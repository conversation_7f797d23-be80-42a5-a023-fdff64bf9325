events {
    worker_connections 4096; # Max connections per worker
    multi_accept on; # Accept multiple connections at once
    use epoll; # Efficient event handling (Linux-specific)
}

http {
    sendfile on; # Efficient file serving
    tcp_nopush on; # Optimize TCP packet sending
    tcp_nodelay on; # Reduce latency
    keepalive_timeout 15; # Keep connections alive briefly
    types_hash_max_size 2048;

    # Logging (optional: disable for performance under high load)
    # access_log /var/log/nginx/access.log main buffer=64k;
    # error_log /var/log/nginx/error.log warn;

    # Gzip compression (optional, reduces bandwidth)
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    server {
        listen 80;
        listen [::]:80;
        root /var/www/html/public;

        add_header X-Frame-Options "SAMEORIGIN";
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options "nosniff";

        ### Config for video content
   	location /cam_archive {
        	# Set the MIME type for MP4 files
        	types {
            		video/mp4 mp4;
        	}

        	# You might also want these settings for serving video files
        	autoindex on;             # Optional: enables directory listing
        	sendfile on;              # Efficient file serving
        	tcp_nopush on;            # Optimizes sending full TCP packets
        	tcp_nodelay on;           # Reduces latency for small packets
        	keepalive_timeout 65;     # Keep connections alive longer for streaming
        
        	# Path to your movies directory
        	# Modify this if your actual file path is different
        	root /var/www/html/public;
    	}
	###

        index index.php;

        charset utf-8;


        location / {
            try_files $uri $uri/ /index.php?$query_string;

        }

        error_page 404 /index.php;

        location ~ \.php$ {
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
            # FastCGI timeout settings
            fastcgi_read_timeout 300s;
            fastcgi_connect_timeout 300s;
            fastcgi_send_timeout 300s;

            include fastcgi_params;
        }

        location ~ /\.(?!well-known).* {
            deny all;
        }
    }

}







