<?php

namespace App\MongoDB;

use <PERSON><PERSON><PERSON>\Mongodb\Connection;

class MongoDBConnection extends Connection
{
    public function __call($method, $parameters)
    {
        if (method_exists($this->db, $method)) {
            return call_user_func_array([$this->db, $method], $parameters);
        }
        if ($method === 'getDoctrineDriver') {
            throw new \BadMethodCallException("Method {$method} is not supported for MongoDB.");
        }
        throw new \BadMethodCallException("Undefined method '{$method}' called.");
    }
}
