<?php

namespace App\Jobs;

use App\Http\Controllers\Api\LocationController;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Services\LocationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class SaveSocialCallLocation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public int $tries = 5;

    public int $timeout = 5 * 60;

    public int $uniqueFor = 3600;
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): string
    {
//        try {
            $get_data = BeforeAdvancedSearchLog::query()
                ->where('search_type', 'get_social_call_location')
                ->where('status', '0')->get();
            foreach ($get_data as $key => $value) {
                $this->jobX($value);
            }
//        } catch (\Throwable $e) {
//            Log::warning('SaveAdvanceSearchLogsAfterJob handler: ',[
//                'message' => $e->getMessage(),
//                'line' => $e->getLine(),
//                'file' => $e->getFile(),
//                'trace' => $e->getTraceAsString()
//            ]);
//        }
        return 'success';
    }

    private function jobX($get_data = []): void
    {
        Log::error('Processing get_data:', ['data' => json_encode($get_data)]);
        BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
            'retry' => 1,
            'status' => 3,
            'name' => 'DEBUG start Processing'
        ]);

        try {

            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'name' => "STEP 00"
            ]);
            $tab_value = $get_data->search_tab;
            usort($tab_value, function ($a, $b) {
                return $b['value'] <=> $a['value'];
            });

            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'name' => "STEP 1"
            ]);
            foreach ($tab_value as $key_tab => $value_tab) {

                $page = 1;
                do {
                    $request = [
                        "per_page" => 100,
                        "page" => $page,
                        "from_Job" => "SaveSocialCallLocation",
                        "pin" => $get_data->search_params['pin']
                    ];
                    $request_prompt = array_merge($get_data->search_params, $request);
                    Log::info('Request parameters:', ['request_prompt' => json_encode($request_prompt)]);
                    $locationService = new LocationService(
                        config('services.loc_api_key'),
                        config('services.loc_base_url')
                    );
                    $response_data['last_page'] = 2;
                    $es = new LocationController($locationService);
                    $request = Request::createFromGlobals();
                    $requestX = $request->merge($request_prompt);
                    $response = $es->getSocialCallLocationInfo($requestX);
                    $response = collect($response)->toArray();
                    $tabs = $response['tabs'] ?? [[
                            'value' => 'get_social_call_location',
                            'name' => 'tab',
                            'label' => 'Trayektoriyalar (Telefon nömrəsi ilə)',
                        ]];

                    $data = $response['original'];
                    $data = json_decode(json_encode($data), true);
                    $response_data = $data['data'];
                    $response_data_X = [];
                    $original_data = $response_data['data'];
                    Log::debug('get_social_call_location response : ', ['data' => json_encode($original_data)]);

                    BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                        'name' => "STEP 2"
                    ]);
                    foreach ($original_data as $key => $value) {

                        if (str_contains("per_page",$value) && is_string($value)){
                            $response_data_X['parent_id'] = $get_data->id;
                            $response_data_X['user_id'] = $get_data->user_id;
                            $response_data_X['hash'] = $get_data->hash;
                            $response_data_X['request'] = collect($requestX)->toArray();
                            $response_data_X['response'] = collect($value)->toArray();
                            $response_data_X['type'] = $value_tab['value'];
                            $response_data_X['tab'] = json_encode($tabs, JSON_UNESCAPED_UNICODE);
                            $response_data_X['created_at'] = now();
                            AdvancedSearchLog::create($response_data_X);
                        }

                    }
                    $page++;

                } while ($response_data['last_page'] >= $page);

                if ($key_tab == count($tab_value) - 1) {
                    BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                        'status' => 1,
                        'retry' => $get_data->retry + 1,
                    ]);
                }
            }
        }catch (\Exception $exception){
            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'name' => $exception->getMessage(),
            ]);
        }

    }

    public function failed(\Exception $exception): void
    {
        info($exception->getMessage());
    }

}
