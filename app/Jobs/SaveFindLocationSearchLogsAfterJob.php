<?php

namespace App\Jobs;

use App\Http\Controllers\Api\ElasticSearchController;
use App\Http\Controllers\Api\LocationController;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Services\LocationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SaveFindLocationSearchLogsAfterJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public int $tries = 5;

    public int $timeout = 5 * 60;

    public int $uniqueFor = 3600;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Execute the job.
     *
     * @return string
     */
    public function handle(): string
    {
//        try {
            $get_data = BeforeAdvancedSearchLog::query()
                ->where('search_type', 'find_location_by_phone_number')
                ->where('status', '0')->get();
            foreach ($get_data as $key => $value) {
                $this->jobX($value);
            }

//        } catch (\Exception $e) {
//            Log::warning('SaveFindLocationSearchLogsAfterJob handler: ' . $e->getMessage());
//        }
        return 'success';
    }

    private function jobX($get_data = []): void
    {
        $get_data->update([
            'retry' => 1,
            'status' => 3,
        ]);
        $tab_value = $get_data->search_tab;
        usort($tab_value, function ($a, $b) {
            return $b['value'] <=> $a['value'];
        });
        foreach ($tab_value as $key_tab => $value_tab) {
            $request = [
                "per_page" => 100,
                "page" => 1,
                "from_Job" => "SaveFindLocationSearchLogsAfterJob",
            ];
            $request_prompt = array_merge($get_data->search_params, $request);
            $locationService = new LocationService();
            $es = new LocationController($locationService);
            $request = Request::createFromGlobals();
            $requestX = $request->merge($request_prompt);
            Log::debug('find_location_by_phone_number $requestX : ', ['data' => json_encode($requestX)]);
            $response = $es->findLocation($requestX);
            $response = collect($response)->toArray();
            $tabs = $response['tabs'] ?? [[
                'value' => 'find_location_by_phone_number',
                'name' => 'tab',
                'label' => 'Kəsişməni axtar (Telefon nömrəsi ilə)',
            ]];
            $data = $response['original'];
            $response_data = json_decode(json_encode($data), true);
            Log::debug('find_location_by_phone_number response : ', ['data' => json_encode($response_data['data'])]);
            foreach ($response_data['data'] as $value) {
                $response_data_X = [
                    'parent_id' => $get_data->id,
                    'user_id' => $get_data->user_id,
                    'hash' => $get_data->hash,
                    'request' => $get_data->search_params,
                    'response' => $value,
                    'type' => $value_tab['value'],
                    'tab' => $tabs,
                    'created_at' => now(),
                ];
                AdvancedSearchLog::create($response_data_X);
            }
            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'status' => 1,
                'retry' => $get_data->retry + 1
            ]);

        }

    }
}
