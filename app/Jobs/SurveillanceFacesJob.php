<?php

namespace App\Jobs;

use Elasticsearch\ClientBuilder;
use Illuminate\Bus\Queueable;
use App\Services\ElasticService;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SurveillanceFacesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param $payload
     * @return void
     */
    public function handle($payload): void
    {
        /**
        // * @var $service ElasticService
         */
        //$service = app(ElasticService::class);

       // dd(config('database.connections.elasticsearch.hosts'));

        $service = ClientBuilder::create()
            ->setHosts(config('database.connections.elasticsearch.hosts'))
            ->setSSLVerification(false)
            ->build();

        $data = [
            'session_name' => $payload['data']['session_name'],
            'camera_id' => $payload['data']['camera_id'],
        ];

        foreach ($payload['data']['faces'] as $face) {

            $face1 = array_merge($face, $data);

            unset($face1['vector']);

             //file_put_contents(public_path('log.txt'), json_encode($face1, JSON_THROW_ON_ERROR), FILE_APPEND);

            // create($index, $id, array $body)

            $index = 'beein_surveillance_faces_3';
            $id = $face['vector_id'];
            $body = $face1;

            $params = [
                'body' => $body,
                "id" => $id,
                'index' => $index
            ];
            //file_put_contents(public_path('log_body.txt'), json_encode($face1, JSON_THROW_ON_ERROR), FILE_APPEND);
            $service->index($params);
           // $service->indices()->create($params);
            //$service->create('beein_surveillance_faces_3', $face['vector_id'], $face1);
        }
    }
}
