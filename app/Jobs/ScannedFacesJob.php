<?php

namespace App\Jobs;

use App\Models\ScannedFaces;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ScannedFacesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */

    protected array $data;

    /**
     * Execute the job.
     *
     * @param $payload
     * @return void
     */
    public function handle($payload): void
    {
        ScannedFaces::create($payload['data']);
    }
}
