<?php

namespace App\Jobs;

use JsonException;
use Illuminate\Contracts\Container\BindingResolutionException;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\LaravelQueueRabbitMQ\Queue\Jobs\RabbitMQJob as BaseJob;

class RabbitMQSurveillanceFacesJob extends BaseJob
{
    /**
     * Fire the job.
     *
     * @return void
     * @throws BindingResolutionException
     * @throws JsonException
     */
    public function fire(): void
    {
        $payload = $this->payload();

        $class = SurveillanceFacesJob::class;

        $method = 'handle';

        ($this->instance = $this->resolve($class))->{$method}($payload);

        $this->delete();
    }

    /**
     * Get the decoded body of the job.
     * @return array
     * @throws JsonException
     */
    public function payload(): array
    {
        return [
            'job' => SurveillanceFacesJob::class,
            'data' => json_decode($this->getRawBody(), true, 512, JSON_THROW_ON_ERROR)
        ];
    }
}
