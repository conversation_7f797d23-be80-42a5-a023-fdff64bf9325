<?php

namespace App\Jobs;

use App\Http\Controllers\Api\LocationController;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Services\LocationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Throwable;

class SaveFindMeetingPlacesLogsAfterJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public int $tries = 5;

    public int $timeout = 5 * 60;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle() :string
    {
//        try {
            $get_data = BeforeAdvancedSearchLog::query()
                ->where('search_type', 'find_meeting_places')
                ->where('status', '0')->get();
            foreach ($get_data as $key => $value) {
                $this->jobX($value);
            }

//        }catch (Throwable $e) {
//            Log::warning('SaveFindMeetingPlacesLogsAfterJob handler: ',[
//                'message' => $e->getMessage(),
//                'line' => $e->getLine(),
//                'file' => $e->getFile(),
//                'trace' => $e->getTraceAsString()
//            ]);
//        }
        return 'success';
    }

    private function jobX($get_data): void
    {
        $get_data->update([
            'retry' => 1,
            'status' => 3,
        ]);

        $tab_value = $get_data->search_tab;

        usort($tab_value, function ($a, $b) {
            return $b['value'] <=> $a['value'];
        });

        foreach ($tab_value as $key_tab => $value_tab) {
            $request = [
                "per_page" => 100,
                "page" => 1,
                "from_Job" => "SaveFindMeetingPlacesLogsAfterJob",
            ];
            $request_prompt = array_merge($get_data->search_params, $request);
            $locationService = new LocationService(
                config('services.loc_api_key'),
                config('services.loc_base_url')
            );
            $es = new LocationController($locationService);
            $request = Request::createFromGlobals();
            $request_X = $request->merge($request_prompt);
            $response = $es->findMeetingPlaces($request_X);
            Log::debug('find_meeting_places response : ', ['data' => json_encode($response)]);
            $response = collect($response)->toArray();
            $tabs = $response['tabs'] ?? [[
                'value' => 'find_meeting_places',
                'name' => 'tab',
                'label' => 'İki şəxsin görüş ehtimalı (Telefon nömrələrinə görə)',
            ]];
            $data = $response['original'];
            $response_data = json_decode(json_encode($data), true);
            $original_data = $response_data['dataMap'];
            foreach ($original_data as $value) {
                $response_data_X = [
                    'parent_id' => $get_data->id,
                    'user_id' => $get_data->user_id,
                    'hash' => $get_data->hash,
                    'request' => $get_data->search_params,
                    'response' => $value,
                    'type' => $value_tab['value'],
                    'tab' => $tabs,
                    'created_at' => now(),
                ];
                AdvancedSearchLog::create($response_data_X);
            }
            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'status' => 1,
                'retry' => $get_data->retry + 1,
            ]);

        }
    }

    public function failed(Throwable $exception): void
    {
        info('SaveFindMeetingPlacesLogsAfterJob failed: ' . $exception->getMessage(), ['message' => $exception->getMessage(), 'line' => $exception->getLine(), 'file' => $exception->getFile(), 'trace' => $exception->getTraceAsString()]);
    }
}
