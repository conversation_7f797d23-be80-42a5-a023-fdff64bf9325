<?php

namespace App\Jobs;

use App\Http\Controllers\Api\LocationController;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Services\LocationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Request;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SaveFindTravelLogsAfterJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    public int $tries = 5;

    public int $timeout = 5 * 60;

    public int $uniqueFor = 3600;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle():string
    {
        try {
            $get_data = BeforeAdvancedSearchLog::query()
                ->where('search_type', 'find_travel')
                ->where('status', '0')->get();
            foreach ($get_data as $key => $value) {
                $this->jobX($value);
            }

        }catch (\Exception $e) {
            Log::warning('SaveFindTravelLogsAfterJob handler: '.$e->getMessage());
        }
        return 'success';
    }

    private function jobX($get_data): void
    {
        $get_data->update([
            'retry' => 1,
            'status' => 3,
        ]);

        $tab_value = $get_data->search_tab;

        usort($tab_value, function ($a, $b) {
            return $b['value'] <=> $a['value'];
        });

        foreach ($tab_value as $key_tab => $value_tab) {
            $request = [
                "per_page" => 100,
                "page" => 1,
                "from_Job" => "SaveFindTravelJobsAfterLog",
            ];
            $request_prompt = array_merge($get_data->search_params, $request);
            $locationService = new LocationService();
            $es = new LocationController($locationService);
            $request = Request::createFromGlobals();
            $request_X = $request->merge($request_prompt);
            $response = $es->getVehicleEntered($request_X);
            $response = collect($response)->toArray();
            $tabs = $response['tabs'] ?? [[
                'value' => 'find_travel',
                'name' => 'tab',
                'label' => 'Maşın nömrəsinə görə trayektoriya',
            ]];
            $data = $response['original']['data'];
            $response_data = json_decode(json_encode($data), true);
            foreach ($response_data as $value) {
                $response_data_X = [
                    'parent_id' => $get_data->id,
                    'user_id' => $get_data->user_id,
                    'hash' => $get_data->hash,
                    'request' => $get_data->search_params,
                    'response' => $value,
                    'type' => $value_tab['value'],
                    'tab' => $tabs,
                    'created_at' => now(),
                ];
                AdvancedSearchLog::create($response_data_X);
            }
            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'status' => 1,
                'retry' => $get_data->retry + 1,
            ]);
        }
    }
}
