<?php

namespace App\Jobs;

use App\Http\Controllers\Api\ElasticSearchController;
use App\Http\Requests\DetailedSearchRequest;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use JetBrains\PhpStorm\ArrayShape;
use Throwable;


class SaveAdvanceSearchLogsAfterJob implements ShouldQueue//, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 5;

    public int $timeout = 5 * 60;

    public int $uniqueFor = 3600;

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return static::class;
    }

    public function handle(): string
    {
        try {
            BeforeAdvancedSearchLog::query()
                ->where('search_type', 'detailed_search')
                //->where('retry',  '0')
                ->where('status', '0')
                ->get()
                ->each(function (BeforeAdvancedSearchLog $item) {
                    $this->createSearchLog($item);
                });


        } catch (Throwable $e) {
            Log::warning('SaveAdvanceSearchLogsAfterJob handler: ',[
                'message' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return 'success';
    }

    /**
     * @throws Exception
     */
    public function createSearchLog(BeforeAdvancedSearchLog $log): void
    {
        if ($log->status == 3 && $log->retry<5) {
            return;
        }

        $log->update([
            'retry' => 1,
            'status' => 3
        ]);

        $tab_value = $log->search_tab;

        usort($tab_value, function ($a, $b) {
            return $b['value'] <=> $a['value'];
        });

        foreach ($tab_value as $key_tab => $value_tab) {
            $page = 1;
            do {
                [
                    'response' => $response,
                    'request' => $request
                ] = $this->createControllerResponse($log, $value_tab, $page);

                $tabs = $response['tabs'];

                $data = $response['data']->toArray($request);

                $pagination = $data['pagination'];
                $response_data = $data['data'];

                foreach ($response_data as $value) {
                    AdvancedSearchLog::create([
                        'parent_id' => $log->id,
                        'user_id' => $log->user_id,
                        'hash' => $log->hash,
                        'request' => $request->all(),
                        'response' => collect($value)->toArray(),
                        'type' => $value_tab['value'],
                        'tab' => json_encode($tabs, JSON_UNESCAPED_UNICODE),
                        'created_at' => now()
                    ]);

                    if ($page >= (250000/10)) {
                        $log->update([
                            'status' => 1,
                            'retry' => $log->retry + 1
                        ]);
                        return;
                    }

                }

                $page++;
            } while ($pagination['lastPage'] >= $page);

            if ($key_tab == count($tab_value) - 1 || $page >= (250000/10)) {
                $log->update([
                    'status' => 1,
                    'retry' => $log->retry + 1
                ]);
            }

        }

    }


    #[ArrayShape(['response' => "array", 'request' => DetailedSearchRequest::class])]
    public function createControllerResponse($get_data, $value_tab, $page): array
    {
        $es = new ElasticSearchController();

        $request = DetailedSearchRequest::createFromGlobals();

        $request->merge(array_merge($get_data->search_params, [
            "per_page" => 100,
            "tab" => $value_tab['value'],
            "page" => $page,
            "from_Job" => "SaveAdvanceSearchLogsAfterJob",
        ]));

        $response = $es->detailedSearchSave($request);

        return [
            'response' => collect($response)->toArray(),
            'request' => $request
        ];
    }

    public function failed(Throwable $exception)
    {
        info('SaveAdvanceSearchLogsAfterJob failed: ' . $exception->getMessage(), ['message' => $exception->getMessage(), 'line' => $exception->getLine(), 'file' => $exception->getFile(), 'trace' => $exception->getTraceAsString()]);
    }
}
