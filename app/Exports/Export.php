<?php
namespace App\Exports;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class Export implements FromCollection,WithHeadings, ShouldAutoSize ,WithChunkReading
{
    use Exportable;

    protected  $data_query = null;
    protected  $data = null;
    protected array $data_header = [];

    /**
     * @param array $heading
     */
    public function __construct(array $heading = [])
    {
//        $this->data_header = $heading;
//        $this->data_query = $data;
        return $this->data_header = $heading;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return $this->data_header;
    }

    /**
     * @param callable $writerType
     * @return Export
     */

    public function setQuery(callable $writerType): Export
    {
        if(is_callable($writerType)) {
            $this->data_query = $writerType();
        }
        return  $this;
    }

    /**
     * @return int
     */
    public function chunkSize(): int
    {
        return 1000;
    }


    /**
     * @return mixed|null
     */
    public function query(): mixed
    {
      return $this->data_query;
    }

    /**
     * @param $writerType
     * @return Collection
     */
    public function setCollection($writerType): Collection
    {
        return new Collection([
            [1, 2, 3],
            [4, 5, 6]
        ]);
    }

    /**
     * @return Collection|null
     */
    public function collection(): ?Collection
    {
        return collect($this->data_query);
    }

    /**
     * @return mixed
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class    => function(AfterSheet $event) {
                $cellRange = 'A1:A5'; // All headers
                $event->sheet
                    ->getDelegate()
                    ->getStyle($cellRange)
                    ->getFont()
                    ->setFont(array(
                        'family'     => 'Arial',
                        'size'       => '25',
                        'bold'       => true
                    ))
                    ->setName('')
                    ->setSize(24);
            },
        ];
    }
}
