<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Person Social",
 *     description="API endpoints for person social media, health, and digital information"
 * )
 */

class PersonSocialController
{
    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/student",
     *     summary="Get person student information",
     *     description="Retrieve student information for a person using their PIN",
     *     operationId="getPersonStudent",
     *     tags={"Person Social"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Student information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Student information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="student_id", type="string", example="STU123456"),
     *                 @OA\Property(property="university", type="string", example="Baku State University"),
     *                 @OA\Property(property="faculty", type="string", example="Computer Science"),
     *                 @OA\Property(property="course", type="integer", example=3),
     *                 @OA\Property(property="enrollment_date", type="string", format="date", example="2020-09-01"),
     *                 @OA\Property(property="graduation_date", type="string", format="date", example="2024-06-30"),
     *                 @OA\Property(property="status", type="string", example="active")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Student information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Student information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonStudent() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/social-call",
     *     summary="Get person social call data",
     *     description="Retrieve social call data for a person using their PIN",
     *     operationId="getPersonSocialCall",
     *     tags={"Person Social"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Social call data retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Social call data retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="call_date", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="caller_number", type="string", example="994501234567"),
     *                     @OA\Property(property="called_number", type="string", example="994507654321"),
     *                     @OA\Property(property="call_duration", type="integer", example=120, description="Duration in seconds"),
     *                     @OA\Property(property="call_type", type="string", enum={"incoming", "outgoing"}, example="outgoing"),
     *                     @OA\Property(property="location", type="string", example="Baku")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Social call data not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Social call data not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonSocialCall() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/facebook-profil",
     *     summary="Get person Facebook profile",
     *     description="Retrieve Facebook profile information for a person using their PIN",
     *     operationId="getPersonFacebookProfile",
     *     tags={"Person Social"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Facebook profile retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Facebook profile retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="facebook_id", type="string", example="100012345678901"),
     *                 @OA\Property(property="profile_url", type="string", example="https://facebook.com/john.doe"),
     *                 @OA\Property(property="display_name", type="string", example="John Doe"),
     *                 @OA\Property(property="profile_picture", type="string", example="https://facebook.com/profile_pic.jpg"),
     *                 @OA\Property(property="location", type="string", example="Baku, Azerbaijan"),
     *                 @OA\Property(property="friends_count", type="integer", example=250),
     *                 @OA\Property(property="last_activity", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Facebook profile not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Facebook profile not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonFacebookProfile() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/telegram-profile",
     *     summary="Get person Telegram profile",
     *     description="Retrieve Telegram profile information for a person using their PIN",
     *     operationId="getPersonTelegramProfile",
     *     tags={"Person Social"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Telegram profile retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Telegram profile retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="telegram_id", type="string", example="123456789"),
     *                 @OA\Property(property="username", type="string", example="@johndoe"),
     *                 @OA\Property(property="display_name", type="string", example="John Doe"),
     *                 @OA\Property(property="phone_number", type="string", example="994501234567"),
     *                 @OA\Property(property="profile_photo", type="string", example="https://telegram.org/photo.jpg"),
     *                 @OA\Property(property="last_seen", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="is_verified", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Telegram profile not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Telegram profile not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonTelegramProfile() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/covid",
     *     summary="Get person COVID information",
     *     description="Retrieve COVID-19 related information for a person using their PIN",
     *     operationId="getPersonCovid",
     *     tags={"Person Social"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="COVID information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="COVID information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="test_results", type="array", @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="test_date", type="string", format="date", example="2024-01-15"),
     *                     @OA\Property(property="test_type", type="string", example="PCR"),
     *                     @OA\Property(property="result", type="string", enum={"positive", "negative"}, example="negative"),
     *                     @OA\Property(property="test_center", type="string", example="Baku Medical Center")
     *                 )),
     *                 @OA\Property(property="vaccination_status", type="string", example="fully_vaccinated"),
     *                 @OA\Property(property="quarantine_history", type="array", @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="start_date", type="string", format="date", example="2020-03-15"),
     *                     @OA\Property(property="end_date", type="string", format="date", example="2020-03-29"),
     *                     @OA\Property(property="reason", type="string", example="Contact tracing")
     *                 ))
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="COVID information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="COVID information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonCovid() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/vaccine",
     *     summary="Get person vaccination records",
     *     description="Retrieve vaccination records for a person using their PIN",
     *     operationId="getPersonVaccine",
     *     tags={"Person Social"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Vaccination records retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Vaccination records retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="vaccine_name", type="string", example="Pfizer-BioNTech"),
     *                     @OA\Property(property="vaccination_date", type="string", format="date", example="2021-06-15"),
     *                     @OA\Property(property="dose_number", type="integer", example=1),
     *                     @OA\Property(property="batch_number", type="string", example="VAC123456"),
     *                     @OA\Property(property="vaccination_center", type="string", example="Baku Vaccination Center"),
     *                     @OA\Property(property="certificate_number", type="string", example="CERT123456789")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Vaccination records not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Vaccination records not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonVaccine() {}
}
