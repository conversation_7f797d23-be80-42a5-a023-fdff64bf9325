<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Location Services",
 *     description="API endpoints for location tracking, geolocation, and movement analysis"
 * )
 */

class LocationController
{
    /**
     * @OA\Post(
     *     path="/api/v1/find-location",
     *     summary="Find location information",
     *     description="Find detailed location information based on coordinates or address",
     *     operationId="findLocationInfo",
     *     tags={"Location Services"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Location search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="latitude", type="number", format="float", example=40.4093, description="Latitude coordinate"),
     *             @OA\Property(property="longitude", type="number", format="float", example=49.8671, description="Longitude coordinate"),
     *             @OA\Property(property="address", type="string", example="Nizami Street, Baku", description="Address to search"),
     *             @OA\Property(property="radius", type="integer", example=1000, description="Search radius in meters"),
     *             @OA\Property(property="include_poi", type="boolean", example=true, description="Include points of interest"),
     *             @OA\Property(property="include_traffic", type="boolean", example=false, description="Include traffic information")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Location information found successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Location information found successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="coordinates", type="object",
     *                     @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                     @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *                 ),
     *                 @OA\Property(property="address", type="object",
     *                     @OA\Property(property="formatted_address", type="string", example="Nizami Street, Baku, Azerbaijan"),
     *                     @OA\Property(property="street", type="string", example="Nizami Street"),
     *                     @OA\Property(property="city", type="string", example="Baku"),
     *                     @OA\Property(property="district", type="string", example="Nasimi"),
     *                     @OA\Property(property="country", type="string", example="Azerbaijan"),
     *                     @OA\Property(property="postal_code", type="string", example="AZ1000")
     *                 ),
     *                 @OA\Property(property="administrative_info", type="object",
     *                     @OA\Property(property="region", type="string", example="Baku"),
     *                     @OA\Property(property="municipality", type="string", example="Baku City"),
     *                     @OA\Property(property="settlement", type="string", example="Baku")
     *                 ),
     *                 @OA\Property(
     *                     property="nearby_poi",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="name", type="string", example="Fountain Square"),
     *                         @OA\Property(property="type", type="string", example="landmark"),
     *                         @OA\Property(property="distance", type="integer", example=250, description="Distance in meters"),
     *                         @OA\Property(property="coordinates", type="object",
     *                             @OA\Property(property="latitude", type="number", format="float", example=40.4075),
     *                             @OA\Property(property="longitude", type="number", format="float", example=49.8679)
     *                         )
     *                     )
     *                 ),
     *                 @OA\Property(property="accuracy", type="integer", example=10, description="Location accuracy in meters"),
     *                 @OA\Property(property="elevation", type="number", format="float", example=28.5, description="Elevation above sea level in meters")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Location not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Location not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="latitude",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"Either coordinates or address is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function findLocationInfo() {}

    /**
     * @OA\Get(
     *     path="/api/v1/find-similarity",
     *     summary="Find location similarity",
     *     description="Find locations with similar characteristics or patterns",
     *     operationId="findSimilarity",
     *     tags={"Location Services"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="reference_lat",
     *         in="query",
     *         description="Reference latitude",
     *         required=true,
     *         @OA\Schema(type="number", format="float", example=40.4093)
     *     ),
     *     @OA\Parameter(
     *         name="reference_lng",
     *         in="query",
     *         description="Reference longitude",
     *         required=true,
     *         @OA\Schema(type="number", format="float", example=49.8671)
     *     ),
     *     @OA\Parameter(
     *         name="similarity_type",
     *         in="query",
     *         description="Type of similarity to find",
     *         required=false,
     *         @OA\Schema(type="string", enum={"demographic", "geographic", "traffic_pattern", "poi_density"}, example="geographic")
     *     ),
     *     @OA\Parameter(
     *         name="radius",
     *         in="query",
     *         description="Search radius in kilometers",
     *         required=false,
     *         @OA\Schema(type="integer", example=10)
     *     ),
     *     @OA\Parameter(
     *         name="limit",
     *         in="query",
     *         description="Maximum number of similar locations to return",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Similar locations found successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Similar locations found successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="reference_location", type="object",
     *                     @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                     @OA\Property(property="longitude", type="number", format="float", example=49.8671),
     *                     @OA\Property(property="address", type="string", example="Nizami Street, Baku")
     *                 ),
     *                 @OA\Property(property="similarity_type", type="string", example="geographic"),
     *                 @OA\Property(property="total_found", type="integer", example=15),
     *                 @OA\Property(
     *                     property="similar_locations",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="location_id", type="string", example="LOC123456"),
     *                         @OA\Property(property="coordinates", type="object",
     *                             @OA\Property(property="latitude", type="number", format="float", example=40.4125),
     *                             @OA\Property(property="longitude", type="number", format="float", example=49.8695)
     *                         ),
     *                         @OA\Property(property="address", type="string", example="Torgovaya Street, Baku"),
     *                         @OA\Property(property="similarity_score", type="number", format="float", example=0.87, description="Similarity score 0-1"),
     *                         @OA\Property(property="distance", type="number", format="float", example=1.2, description="Distance in kilometers"),
     *                         @OA\Property(property="characteristics", type="object",
     *                             @OA\Property(property="population_density", type="string", example="high"),
     *                             @OA\Property(property="commercial_activity", type="string", example="very_high"),
     *                             @OA\Property(property="transportation_access", type="string", example="excellent")
     *                         ),
     *                         @OA\Property(property="match_reasons", type="array", @OA\Items(type="string"), example={"similar_poi_density", "comparable_traffic_patterns"})
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="reference_lat",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The reference lat field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function findSimilarity() {}

    /**
     * @OA\Get(
     *     path="/api/v1/find-meeting-places",
     *     summary="Find meeting places",
     *     description="Find potential meeting places based on movement patterns and location data",
     *     operationId="findMeetingPlacesAnalysis",
     *     tags={"Location Services"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="person_pins",
     *         in="query",
     *         description="Comma-separated list of person PINs",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567,7654321,9876543")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for analysis",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-15")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for analysis",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-15")
     *     ),
     *     @OA\Parameter(
     *         name="min_duration",
     *         in="query",
     *         description="Minimum meeting duration in minutes",
     *         required=false,
     *         @OA\Schema(type="integer", example=30)
     *     ),
     *     @OA\Parameter(
     *         name="max_distance",
     *         in="query",
     *         description="Maximum distance between persons in meters",
     *         required=false,
     *         @OA\Schema(type="integer", example=100)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Meeting places found successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Meeting places found successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="analysis_period", type="string", example="2024-01-15 to 2024-01-15"),
     *                 @OA\Property(property="persons_analyzed", type="array", @OA\Items(type="string"), example={"1234567", "7654321", "9876543"}),
     *                 @OA\Property(property="total_meetings", type="integer", example=5),
     *                 @OA\Property(
     *                     property="meeting_places",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="meeting_id", type="string", example="MEET123456"),
     *                         @OA\Property(property="location", type="object",
     *                             @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                             @OA\Property(property="longitude", type="number", format="float", example=49.8671),
     *                             @OA\Property(property="address", type="string", example="Fountain Square, Baku")
     *                         ),
     *                         @OA\Property(property="start_time", type="string", format="date-time", example="2024-01-15T14:30:00Z"),
     *                         @OA\Property(property="end_time", type="string", format="date-time", example="2024-01-15T16:15:00Z"),
     *                         @OA\Property(property="duration", type="integer", example=105, description="Duration in minutes"),
     *                         @OA\Property(property="participants", type="array", @OA\Items(type="string"), example={"1234567", "7654321"}),
     *                         @OA\Property(property="confidence", type="number", format="float", example=0.92, description="Confidence score 0-1"),
     *                         @OA\Property(property="meeting_type", type="string", enum={"coincidental", "planned", "frequent"}, example="planned"),
     *                         @OA\Property(property="location_type", type="string", example="public_square"),
     *                         @OA\Property(property="frequency_score", type="number", format="float", example=0.3, description="How often this location is used for meetings")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="patterns",
     *                     type="object",
     *                     @OA\Property(property="most_frequent_location", type="string", example="Fountain Square, Baku"),
     *                     @OA\Property(property="average_meeting_duration", type="integer", example=85),
     *                     @OA\Property(property="preferred_time_slots", type="array", @OA\Items(type="string"), example={"14:00-16:00", "19:00-21:00"}),
     *                     @OA\Property(property="meeting_frequency", type="string", example="weekly")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="person_pins",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"At least 2 person PINs are required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function findMeetingPlacesAnalysis() {}
}
