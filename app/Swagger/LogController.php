<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Logs",
 *     description="API endpoints for system logs and audit trails"
 * )
 */

class LogController
{
    /**
     * @OA\Get(
     *     path="/api/v1/logs",
     *     summary="Get system logs",
     *     description="Retrieve system logs with filtering and pagination",
     *     operationId="getLogs",
     *     tags={"Logs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=50)
     *     ),
     *     @OA\Parameter(
     *         name="level",
     *         in="query",
     *         description="Filter by log level",
     *         required=false,
     *         @OA\Schema(type="string", enum={"debug", "info", "warning", "error", "critical"}, example="error")
     *     ),
     *     @OA\Parameter(
     *         name="category",
     *         in="query",
     *         description="Filter by log category",
     *         required=false,
     *         @OA\Schema(type="string", enum={"authentication", "authorization", "search", "export", "system", "security"}, example="authentication")
     *     ),
     *     @OA\Parameter(
     *         name="user_id",
     *         in="query",
     *         description="Filter by user ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for log entries",
     *         required=false,
     *         @OA\Schema(type="string", format="date-time", example="2024-01-15T00:00:00Z")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for log entries",
     *         required=false,
     *         @OA\Schema(type="string", format="date-time", example="2024-01-15T23:59:59Z")
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search in log messages",
     *         required=false,
     *         @OA\Schema(type="string", example="login failed")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Logs retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Logs retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="level", type="string", example="error"),
     *                     @OA\Property(property="category", type="string", example="authentication"),
     *                     @OA\Property(property="message", type="string", example="Failed login attempt <NAME_EMAIL>"),
     *                     @OA\Property(property="user_id", type="integer", nullable=true, example=1),
     *                     @OA\Property(property="user_name", type="string", nullable=true, example="John Doe"),
     *                     @OA\Property(property="ip_address", type="string", example="*************"),
     *                     @OA\Property(property="user_agent", type="string", example="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"),
     *                     @OA\Property(property="request_method", type="string", example="POST"),
     *                     @OA\Property(property="request_url", type="string", example="/api/v1/auth/login"),
     *                     @OA\Property(property="response_code", type="integer", example=401),
     *                     @OA\Property(property="execution_time", type="number", format="float", example=0.25, description="Execution time in seconds"),
     *                     @OA\Property(property="context", type="object", description="Additional context data"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=1250),
     *                 @OA\Property(property="per_page", type="integer", example=50)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to access logs")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/logs/{log}",
     *     summary="Get log entry by ID",
     *     description="Retrieve a specific log entry by its ID",
     *     operationId="getLogById",
     *     tags={"Logs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="log",
     *         in="path",
     *         description="Log ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Log entry retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Log entry retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="level", type="string", example="error"),
     *                 @OA\Property(property="category", type="string", example="authentication"),
     *                 @OA\Property(property="message", type="string", example="Failed login attempt <NAME_EMAIL>"),
     *                 @OA\Property(property="user_id", type="integer", nullable=true, example=1),
     *                 @OA\Property(property="user_name", type="string", nullable=true, example="John Doe"),
     *                 @OA\Property(property="ip_address", type="string", example="*************"),
     *                 @OA\Property(property="user_agent", type="string", example="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"),
     *                 @OA\Property(property="request_method", type="string", example="POST"),
     *                 @OA\Property(property="request_url", type="string", example="/api/v1/auth/login"),
     *                 @OA\Property(property="request_headers", type="object", description="Request headers"),
     *                 @OA\Property(property="request_body", type="object", description="Request body (sensitive data masked)"),
     *                 @OA\Property(property="response_code", type="integer", example=401),
     *                 @OA\Property(property="response_body", type="object", description="Response body"),
     *                 @OA\Property(property="execution_time", type="number", format="float", example=0.25),
     *                 @OA\Property(property="memory_usage", type="integer", example=2048576, description="Memory usage in bytes"),
     *                 @OA\Property(property="context", type="object", description="Additional context data"),
     *                 @OA\Property(property="stack_trace", type="string", nullable=true, example=null, description="Stack trace for errors"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Log entry not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Log entry not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to access logs")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Get(
     *     path="/api/v1/logs/audit-trail",
     *     summary="Get audit trail",
     *     description="Retrieve audit trail for specific actions or entities",
     *     operationId="getAuditTrail",
     *     tags={"Logs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="entity_type",
     *         in="query",
     *         description="Type of entity to audit",
     *         required=false,
     *         @OA\Schema(type="string", enum={"user", "person", "alert", "camera", "hierarchy"}, example="user")
     *     ),
     *     @OA\Parameter(
     *         name="entity_id",
     *         in="query",
     *         description="ID of the entity to audit",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="action",
     *         in="query",
     *         description="Filter by action type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"create", "update", "delete", "view", "login", "logout"}, example="update")
     *     ),
     *     @OA\Parameter(
     *         name="user_id",
     *         in="query",
     *         description="Filter by user who performed the action",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for audit trail",
     *         required=false,
     *         @OA\Schema(type="string", format="date-time", example="2024-01-15T00:00:00Z")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for audit trail",
     *         required=false,
     *         @OA\Schema(type="string", format="date-time", example="2024-01-15T23:59:59Z")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Audit trail retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Audit trail retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="action", type="string", example="update"),
     *                     @OA\Property(property="entity_type", type="string", example="user"),
     *                     @OA\Property(property="entity_id", type="integer", example=1),
     *                     @OA\Property(property="entity_name", type="string", example="John Doe"),
     *                     @OA\Property(property="user_id", type="integer", example=2),
     *                     @OA\Property(property="user_name", type="string", example="Admin User"),
     *                     @OA\Property(property="description", type="string", example="Updated user profile information"),
     *                     @OA\Property(property="old_values", type="object", description="Previous values before change"),
     *                     @OA\Property(property="new_values", type="object", description="New values after change"),
     *                     @OA\Property(property="ip_address", type="string", example="*************"),
     *                     @OA\Property(property="user_agent", type="string", example="Mozilla/5.0..."),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to access audit trail")
     *         )
     *     )
     * )
     */
    public function getAuditTrail() {}

    /**
     * @OA\Get(
     *     path="/api/v1/logs/statistics",
     *     summary="Get log statistics",
     *     description="Retrieve statistics and metrics about system logs",
     *     operationId="getLogStatistics",
     *     tags={"Logs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="period",
     *         in="query",
     *         description="Time period for statistics",
     *         required=false,
     *         @OA\Schema(type="string", enum={"hour", "day", "week", "month"}, example="day")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for statistics",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-15")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for statistics",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-15")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Log statistics retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Log statistics retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total_logs", type="integer", example=15420),
     *                 @OA\Property(property="period", type="string", example="day"),
     *                 @OA\Property(
     *                     property="by_level",
     *                     type="object",
     *                     @OA\Property(property="debug", type="integer", example=5200),
     *                     @OA\Property(property="info", type="integer", example=8500),
     *                     @OA\Property(property="warning", type="integer", example=1200),
     *                     @OA\Property(property="error", type="integer", example=450),
     *                     @OA\Property(property="critical", type="integer", example=70)
     *                 ),
     *                 @OA\Property(
     *                     property="by_category",
     *                     type="object",
     *                     @OA\Property(property="authentication", type="integer", example=2500),
     *                     @OA\Property(property="authorization", type="integer", example=1800),
     *                     @OA\Property(property="search", type="integer", example=4200),
     *                     @OA\Property(property="export", type="integer", example=350),
     *                     @OA\Property(property="system", type="integer", example=3800),
     *                     @OA\Property(property="security", type="integer", example=2770)
     *                 ),
     *                 @OA\Property(
     *                     property="timeline",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:00:00Z"),
     *                         @OA\Property(property="count", type="integer", example=125)
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="top_users",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="user_id", type="integer", example=1),
     *                         @OA\Property(property="user_name", type="string", example="John Doe"),
     *                         @OA\Property(property="log_count", type="integer", example=450)
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="top_errors",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="message", type="string", example="Database connection failed"),
     *                         @OA\Property(property="count", type="integer", example=25),
     *                         @OA\Property(property="last_occurrence", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to access log statistics")
     *         )
     *     )
     * )
     */
    public function getStatistics() {}
}
