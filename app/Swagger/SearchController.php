<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Search",
 *     description="API endpoints for search functionality and operations"
 * )
 */

class SearchController
{
    /**
     * @OA\Post(
     *     path="/api/v1/search/persons",
     *     summary="Search persons",
     *     description="Search for persons using various criteria",
     *     operationId="searchPersons",
     *     tags={"Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="query", type="string", example="John Doe"),
     *             @OA\Property(property="pin", type="string", example="1234567"),
     *             @OA\Property(property="doc_number", type="string", example="AA123456"),
     *             @OA\Property(property="name", type="string", example="John"),
     *             @OA\Property(property="surname", type="string", example="Doe"),
     *             @OA\Property(property="father_name", type="string", example="Michael"),
     *             @OA\Property(property="page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=15)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", example=25),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="John"),
     *                         @OA\Property(property="surname", type="string", example="Doe"),
     *                         @OA\Property(property="father_name", type="string", example="Michael"),
     *                         @OA\Property(property="pin", type="string", example="1234567"),
     *                         @OA\Property(property="doc_number", type="string", example="AA123456"),
     *                         @OA\Property(property="relevance_score", type="number", format="float", example=0.95)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchPersons() {}

    /**
     * @OA\Post(
     *     path="/api/v1/search/blacklist",
     *     summary="Search blacklist entries",
     *     description="Search for blacklist entries using various criteria",
     *     operationId="searchBlacklist",
     *     tags={"Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="query", type="string", example="John Doe"),
     *             @OA\Property(property="name", type="string", example="John"),
     *             @OA\Property(property="surname", type="string", example="Doe"),
     *             @OA\Property(property="document_number", type="string", example="AA123456"),
     *             @OA\Property(property="pin", type="string", example="1234567"),
     *             @OA\Property(property="gender", type="string", enum={"male", "female"}, example="male"),
     *             @OA\Property(property="status", type="integer", enum={0, 1}, example=1),
     *             @OA\Property(property="page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=15)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blacklist search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Blacklist search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", example=10),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="John"),
     *                         @OA\Property(property="surname", type="string", example="Doe"),
     *                         @OA\Property(property="document_number", type="string", example="AA123456"),
     *                         @OA\Property(property="pin", type="string", example="1234567"),
     *                         @OA\Property(property="photo", type="string", example="photos/blacklist_1.jpg"),
     *                         @OA\Property(property="status", type="integer", example=1),
     *                         @OA\Property(property="relevance_score", type="number", format="float", example=0.88)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchBlacklist() {}

    /**
     * @OA\Post(
     *     path="/api/v1/search/face",
     *     summary="Face recognition search",
     *     description="Search for persons using facial recognition technology",
     *     operationId="faceSearch",
     *     tags={"Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Face search data",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"image"},
     *                 @OA\Property(property="image", type="string", format="binary", description="Face image file"),
     *                 @OA\Property(property="threshold", type="number", format="float", example=0.8, description="Similarity threshold (0.0-1.0)"),
     *                 @OA\Property(property="max_results", type="integer", example=10, description="Maximum number of results"),
     *                 @OA\Property(property="search_blacklist", type="boolean", example=true, description="Include blacklist in search"),
     *                 @OA\Property(property="search_persons", type="boolean", example=true, description="Include persons in search")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Face search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Face search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total_matches", type="integer", example=3),
     *                 @OA\Property(property="processing_time", type="number", format="float", example=1.25),
     *                 @OA\Property(
     *                     property="matches",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="type", type="string", enum={"person", "blacklist"}, example="person"),
     *                         @OA\Property(property="name", type="string", example="John"),
     *                         @OA\Property(property="surname", type="string", example="Doe"),
     *                         @OA\Property(property="similarity_score", type="number", format="float", example=0.92),
     *                         @OA\Property(property="confidence", type="string", enum={"high", "medium", "low"}, example="high"),
     *                         @OA\Property(property="photo", type="string", example="photos/person_1.jpg")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Invalid image or processing error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Invalid image format or no face detected")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function faceSearch() {}

    /**
     * @OA\Post(
     *     path="/api/v1/search/global",
     *     summary="Global search across all entities",
     *     description="Perform a comprehensive search across persons, blacklist, and other entities",
     *     operationId="globalSearch",
     *     tags={"Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Global search criteria",
     *         @OA\JsonContent(
     *             required={"query"},
     *             @OA\Property(property="query", type="string", example="John Doe"),
     *             @OA\Property(property="entities", type="array", @OA\Items(type="string"), example={"persons", "blacklist", "cameras"}, description="Entity types to search"),
     *             @OA\Property(property="fuzzy", type="boolean", example=true, description="Enable fuzzy matching"),
     *             @OA\Property(property="page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=20)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Global search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Global search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total_results", type="integer", example=45),
     *                 @OA\Property(property="search_time", type="number", format="float", example=0.85),
     *                 @OA\Property(
     *                     property="results_by_entity",
     *                     type="object",
     *                     @OA\Property(
     *                         property="persons",
     *                         type="object",
     *                         @OA\Property(property="count", type="integer", example=25),
     *                         @OA\Property(property="results", type="array", @OA\Items(type="object"))
     *                     ),
     *                     @OA\Property(
     *                         property="blacklist",
     *                         type="object",
     *                         @OA\Property(property="count", type="integer", example=15),
     *                         @OA\Property(property="results", type="array", @OA\Items(type="object"))
     *                     ),
     *                     @OA\Property(
     *                         property="cameras",
     *                         type="object",
     *                         @OA\Property(property="count", type="integer", example=5),
     *                         @OA\Property(property="results", type="array", @OA\Items(type="object"))
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function globalSearch() {}
}
