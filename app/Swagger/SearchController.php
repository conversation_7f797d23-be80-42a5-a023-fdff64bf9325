<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Search",
 *     description="API endpoints for search functionality including face search, global search, and advanced search operations"
 * )
 */

/**
 * @OA\Schema(
 *     schema="SearchRequest",
 *     type="object",
 *     @OA\Property(
 *         property="query",
 *         type="string",
 *         description="Search query string",
 *         example="John Doe"
 *     ),
 *     @OA\Property(
 *         property="filters",
 *         type="object",
 *         description="Additional search filters",
 *         @OA\Property(property="gender", type="string", enum={"male", "female"}, example="male"),
 *         @OA\Property(property="age_from", type="integer", example=18),
 *         @OA\Property(property="age_to", type="integer", example=65),
 *         @OA\Property(property="status", type="integer", example=1)
 *     ),
 *     @OA\Property(
 *         property="sort_by",
 *         type="string",
 *         description="Field to sort by",
 *         example="created_at"
 *     ),
 *     @OA\Property(
 *         property="sort_order",
 *         type="string",
 *         enum={"asc", "desc"},
 *         description="Sort order",
 *         example="desc"
 *     ),
 *     @OA\Property(
 *         property="page",
 *         type="integer",
 *         description="Page number",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="per_page",
 *         type="integer",
 *         description="Items per page",
 *         example=15
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="FaceSearchRequest",
 *     type="object",
 *     required={"image"},
 *     @OA\Property(
 *         property="image",
 *         type="string",
 *         format="binary",
 *         description="Face image file for search"
 *     ),
 *     @OA\Property(
 *         property="similarity",
 *         type="number",
 *         format="float",
 *         minimum=0.0,
 *         maximum=1.0,
 *         description="Minimum similarity threshold",
 *         example=0.8
 *     ),
 *     @OA\Property(
 *         property="max_results",
 *         type="integer",
 *         minimum=1,
 *         maximum=100,
 *         description="Maximum number of results",
 *         example=10
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="FaceSearchResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Face search completed successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         @OA\Property(
 *             property="similarity_threshold",
 *             type="number",
 *             format="float",
 *             example=0.8
 *         ),
 *         @OA\Property(
 *             property="total_matches",
 *             type="integer",
 *             example=5
 *         ),
 *         @OA\Property(
 *             property="results",
 *             type="array",
 *             @OA\Items(
 *                 type="object",
 *                 @OA\Property(property="person_id", type="integer", example=1),
 *                 @OA\Property(property="name", type="string", example="John Doe"),
 *                 @OA\Property(property="similarity_score", type="number", format="float", example=0.95),
 *                 @OA\Property(property="image_path", type="string", example="faces/person_1.jpg"),
 *                 @OA\Property(property="confidence", type="string", example="high")
 *             )
 *         )
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="GlobalSearchResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Global search completed successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         @OA\Property(
 *             property="query",
 *             type="string",
 *             example="John Doe"
 *         ),
 *         @OA\Property(
 *             property="total_results",
 *             type="integer",
 *             example=25
 *         ),
 *         @OA\Property(
 *             property="persons",
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/Person")
 *         ),
 *         @OA\Property(
 *             property="blacklist",
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/Blacklist")
 *         ),
 *         @OA\Property(
 *             property="cameras",
 *             type="array",
 *             @OA\Items(ref="#/components/schemas/Camera")
 *         )
 *     )
 * )
 */

class SearchController
{
    /**
     * @OA\Post(
     *     path="/api/v1/search/persons",
     *     summary="Search persons",
     *     description="Search for persons using various criteria",
     *     operationId="searchPersons",
     *     tags={"Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="John"),
     *             @OA\Property(property="surname", type="string", example="Doe"),
     *             @OA\Property(property="pin", type="string", example="1234567"),
     *             @OA\Property(property="document_number", type="string", example="AA123456"),
     *             @OA\Property(property="is_sync", type="boolean", example=true),
     *             @OA\Property(property="page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=15)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/Person")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchPersons() {}

    /**
     * @OA\Post(
     *     path="/api/v1/search/blacklist",
     *     summary="Search blacklist",
     *     description="Search for blacklist entries using various criteria",
     *     operationId="searchBlacklist",
     *     tags={"Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="John"),
     *             @OA\Property(property="surname", type="string", example="Doe"),
     *             @OA\Property(property="pin", type="string", example="1234567"),
     *             @OA\Property(property="document_number", type="string", example="AA123456"),
     *             @OA\Property(property="status", type="integer", example=1),
     *             @OA\Property(property="gender", type="string", enum={"male", "female"}, example="male"),
     *             @OA\Property(property="page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=15)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/Blacklist")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchBlacklist() {}

    /**
     * @OA\Post(
     *     path="/api/v1/search/face",
     *     summary="Face search",
     *     description="Search for similar faces using image upload",
     *     operationId="faceSearch",
     *     tags={"Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Face search data",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(ref="#/components/schemas/FaceSearchRequest")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Face search completed successfully",
     *         @OA\JsonContent(ref="#/components/schemas/FaceSearchResponse")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="image",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The image field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function faceSearch() {}
}
