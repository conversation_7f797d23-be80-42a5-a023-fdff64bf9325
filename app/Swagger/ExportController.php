<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Export",
 *     description="API endpoints for data export functionality in various formats"
 * )
 */

class ExportController
{
    /**
     * @OA\Post(
     *     path="/api/v1/export",
     *     summary="Export data",
     *     description="Export data in various formats (Excel, CSV, PDF)",
     *     operationId="exportData",
     *     tags={"Export"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Export configuration",
     *         @OA\JsonContent(
     *             required={"export_type", "format"},
     *             @OA\Property(property="export_type", type="string", enum={"persons", "blacklist", "cameras", "alerts", "search_results"}, example="persons"),
     *             @OA\Property(property="format", type="string", enum={"excel", "csv", "pdf"}, example="excel"),
     *             @OA\Property(property="filters", type="object", description="Filters to apply to the export"),
     *             @OA\Property(property="columns", type="array", @OA\Items(type="string"), example={"name", "surname", "pin", "email"}, description="Columns to include in export"),
     *             @OA\Property(property="date_range", type="object", 
     *                 @OA\Property(property="start_date", type="string", format="date", example="2024-01-01"),
     *                 @OA\Property(property="end_date", type="string", format="date", example="2024-01-31")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Export initiated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Export initiated successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="export_id", type="string", example="EXP123456789"),
     *                 @OA\Property(property="status", type="string", example="processing"),
     *                 @OA\Property(property="estimated_completion", type="string", format="date-time", example="2024-01-15T10:35:00Z"),
     *                 @OA\Property(property="download_url", type="string", nullable=true, example=null),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="export_type",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The export_type field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function export() {}

    /**
     * @OA\Get(
     *     path="/api/v1/export/{exportId}/status",
     *     summary="Get export status",
     *     description="Check the status of an export job",
     *     operationId="getExportStatus",
     *     tags={"Export"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="exportId",
     *         in="path",
     *         description="Export ID",
     *         required=true,
     *         @OA\Schema(type="string", example="EXP123456789")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Export status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Export status retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="export_id", type="string", example="EXP123456789"),
     *                 @OA\Property(property="status", type="string", enum={"processing", "completed", "failed"}, example="completed"),
     *                 @OA\Property(property="progress", type="integer", example=100, description="Progress percentage"),
     *                 @OA\Property(property="total_records", type="integer", example=1500),
     *                 @OA\Property(property="processed_records", type="integer", example=1500),
     *                 @OA\Property(property="download_url", type="string", example="https://api.beein.az/exports/EXP123456789/download"),
     *                 @OA\Property(property="file_size", type="string", example="2.5MB"),
     *                 @OA\Property(property="expires_at", type="string", format="date-time", example="2024-01-22T10:30:00Z"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="completed_at", type="string", format="date-time", example="2024-01-15T10:33:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Export not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Export not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getExportStatus() {}

    /**
     * @OA\Get(
     *     path="/api/v1/export/{exportId}/download",
     *     summary="Download export file",
     *     description="Download the completed export file",
     *     operationId="downloadExport",
     *     tags={"Export"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="exportId",
     *         in="path",
     *         description="Export ID",
     *         required=true,
     *         @OA\Schema(type="string", example="EXP123456789")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="File download initiated",
     *         @OA\MediaType(
     *             mediaType="application/octet-stream",
     *             @OA\Schema(type="string", format="binary")
     *         ),
     *         @OA\Header(
     *             header="Content-Disposition",
     *             description="File attachment header",
     *             @OA\Schema(type="string", example="attachment; filename=export_persons_2024-01-15.xlsx")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Export file not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Export file not found or expired")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function downloadExport() {}

    /**
     * @OA\Get(
     *     path="/api/v1/exports",
     *     summary="Get export history",
     *     description="Retrieve list of user's export history",
     *     operationId="getExportHistory",
     *     tags={"Export"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by export status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"processing", "completed", "failed"}, example="completed")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Export history retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Export history retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="export_id", type="string", example="EXP123456789"),
     *                     @OA\Property(property="export_type", type="string", example="persons"),
     *                     @OA\Property(property="format", type="string", example="excel"),
     *                     @OA\Property(property="status", type="string", example="completed"),
     *                     @OA\Property(property="total_records", type="integer", example=1500),
     *                     @OA\Property(property="file_size", type="string", example="2.5MB"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="expires_at", type="string", format="date-time", example="2024-01-22T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=25),
     *                 @OA\Property(property="per_page", type="integer", example=15)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getExportHistory() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/export/{exportId}",
     *     summary="Delete export",
     *     description="Delete an export and its associated file",
     *     operationId="deleteExport",
     *     tags={"Export"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="exportId",
     *         in="path",
     *         description="Export ID",
     *         required=true,
     *         @OA\Schema(type="string", example="EXP123456789")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Export deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Export deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Export not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Export not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function deleteExport() {}
}
