<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Nebula Search",
 *     description="API endpoints for Nebula advanced search and discovery operations"
 * )
 */

class FinderController
{
    /**
     * @OA\Post(
     *     path="/api/v1/nebula/search",
     *     summary="Nebula advanced search",
     *     description="Perform advanced search using Nebula search engine with AI-powered capabilities",
     *     operationId="nebulaSearch",
     *     tags={"Nebula Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search parameters",
     *         @OA\JsonContent(
     *             required={"query"},
     *             @OA\Property(property="query", type="string", example="suspicious activity near Fountain Square"),
     *             @OA\Property(property="search_type", type="string", enum={"semantic", "keyword", "hybrid", "ai_assisted"}, example="ai_assisted"),
     *             @OA\Property(property="data_sources", type="array", @OA\Items(type="string"), example={"persons", "vehicles", "alerts", "surveillance"}, description="Data sources to search"),
     *             @OA\Property(property="time_range", type="object",
     *                 @OA\Property(property="start_date", type="string", format="date-time", example="2024-01-15T00:00:00Z"),
     *                 @OA\Property(property="end_date", type="string", format="date-time", example="2024-01-15T23:59:59Z")
     *             ),
     *             @OA\Property(property="location_filter", type="object",
     *                 @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                 @OA\Property(property="longitude", type="number", format="float", example=49.8671),
     *                 @OA\Property(property="radius", type="integer", example=1000, description="Radius in meters")
     *             ),
     *             @OA\Property(property="filters", type="object",
     *                 @OA\Property(property="priority", type="string", enum={"low", "medium", "high", "critical"}, example="high"),
     *                 @OA\Property(property="confidence_threshold", type="number", format="float", example=0.7, description="Minimum confidence score 0-1"),
     *                 @OA\Property(property="include_archived", type="boolean", example=false)
     *             ),
     *             @OA\Property(property="ai_options", type="object",
     *                 @OA\Property(property="enable_nlp", type="boolean", example=true, description="Enable natural language processing"),
     *                 @OA\Property(property="enable_pattern_detection", type="boolean", example=true),
     *                 @OA\Property(property="enable_anomaly_detection", type="boolean", example=false),
     *                 @OA\Property(property="context_expansion", type="boolean", example=true, description="Expand search context using AI")
     *             ),
     *             @OA\Property(property="result_options", type="object",
     *                 @OA\Property(property="max_results", type="integer", example=100),
     *                 @OA\Property(property="include_explanations", type="boolean", example=true),
     *                 @OA\Property(property="include_related", type="boolean", example=true),
     *                 @OA\Property(property="group_similar", type="boolean", example=false)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="search_id", type="string", example="SEARCH123456789"),
     *                 @OA\Property(property="query", type="string", example="suspicious activity near Fountain Square"),
     *                 @OA\Property(property="search_type", type="string", example="ai_assisted"),
     *                 @OA\Property(property="total_results", type="integer", example=45),
     *                 @OA\Property(property="search_time", type="number", format="float", example=1.25, description="Search time in seconds"),
     *                 @OA\Property(property="ai_confidence", type="number", format="float", example=0.87, description="AI confidence in search results"),
     *                 @OA\Property(
     *                     property="query_analysis",
     *                     type="object",
     *                     @OA\Property(property="intent", type="string", example="security_investigation"),
     *                     @OA\Property(property="entities", type="array", @OA\Items(type="string"), example={"location:Fountain Square", "activity:suspicious"}),
     *                     @OA\Property(property="keywords", type="array", @OA\Items(type="string"), example={"suspicious", "activity", "fountain", "square"}),
     *                     @OA\Property(property="suggested_filters", type="array", @OA\Items(type="string"), example={"high_priority", "recent_events"})
     *                 ),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="string", example="RESULT123456"),
     *                         @OA\Property(property="type", type="string", enum={"person", "vehicle", "alert", "surveillance", "incident"}, example="alert"),
     *                         @OA\Property(property="title", type="string", example="Suspicious Activity Alert - Fountain Square"),
     *                         @OA\Property(property="description", type="string", example="Multiple individuals observed in suspicious behavior near Fountain Square"),
     *                         @OA\Property(property="relevance_score", type="number", format="float", example=0.92),
     *                         @OA\Property(property="confidence_score", type="number", format="float", example=0.85),
     *                         @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T14:30:00Z"),
     *                         @OA\Property(property="location", type="object",
     *                             @OA\Property(property="latitude", type="number", format="float", example=40.4075),
     *                             @OA\Property(property="longitude", type="number", format="float", example=49.8679),
     *                             @OA\Property(property="address", type="string", example="Fountain Square, Baku")
     *                         ),
     *                         @OA\Property(property="data", type="object", description="Type-specific data"),
     *                         @OA\Property(property="explanation", type="string", example="Matched based on location proximity and activity keywords"),
     *                         @OA\Property(property="related_items", type="array", @OA\Items(type="string"), example={"ALERT789", "PERSON456"})
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="aggregations",
     *                     type="object",
     *                     @OA\Property(property="by_type", type="object",
     *                         @OA\Property(property="alerts", type="integer", example=25),
     *                         @OA\Property(property="persons", type="integer", example=12),
     *                         @OA\Property(property="vehicles", type="integer", example=5),
     *                         @OA\Property(property="surveillance", type="integer", example=3)
     *                     ),
     *                     @OA\Property(property="by_time", type="array",
     *                         @OA\Items(
     *                             type="object",
     *                             @OA\Property(property="hour", type="string", example="14:00"),
     *                             @OA\Property(property="count", type="integer", example=8)
     *                         )
     *                     ),
     *                     @OA\Property(property="by_location", type="array",
     *                         @OA\Items(
     *                             type="object",
     *                             @OA\Property(property="area", type="string", example="Fountain Square"),
     *                             @OA\Property(property="count", type="integer", example=15)
     *                         )
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="suggestions",
     *                     type="object",
     *                     @OA\Property(property="related_queries", type="array", @OA\Items(type="string"), example={"recent alerts in city center", "suspicious persons near landmarks"}),
     *                     @OA\Property(property="filter_suggestions", type="array", @OA\Items(type="string"), example={"Add time filter for last 24 hours", "Include vehicle data"}),
     *                     @OA\Property(property="investigation_leads", type="array", @OA\Items(type="string"), example={"Check CCTV footage from 14:00-16:00", "Review person movement patterns"})
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="query",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The query field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function search() {}

    /**
     * @OA\Get(
     *     path="/api/v1/nebula/search/{searchId}",
     *     summary="Get search results by ID",
     *     description="Retrieve previously executed search results by search ID",
     *     operationId="getNebulaSearchResults",
     *     tags={"Nebula Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="searchId",
     *         in="path",
     *         description="Search ID",
     *         required=true,
     *         @OA\Schema(type="string", example="SEARCH123456789")
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search results retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search results retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="search_id", type="string", example="SEARCH123456789"),
     *                 @OA\Property(property="original_query", type="string", example="suspicious activity near Fountain Square"),
     *                 @OA\Property(property="search_date", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="total_results", type="integer", example=45),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="string", example="RESULT123456"),
     *                         @OA\Property(property="type", type="string", example="alert"),
     *                         @OA\Property(property="title", type="string", example="Suspicious Activity Alert"),
     *                         @OA\Property(property="relevance_score", type="number", format="float", example=0.92),
     *                         @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T14:30:00Z")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="meta",
     *                     type="object",
     *                     @OA\Property(property="current_page", type="integer", example=1),
     *                     @OA\Property(property="total", type="integer", example=45),
     *                     @OA\Property(property="per_page", type="integer", example=20)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Search not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Search not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getSearchResults() {}

    /**
     * @OA\Post(
     *     path="/api/v1/nebula/suggestions",
     *     summary="Get search suggestions",
     *     description="Get AI-powered search suggestions and query completions",
     *     operationId="getNebulaSearchSuggestions",
     *     tags={"Nebula Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Suggestion request",
     *         @OA\JsonContent(
     *             required={"partial_query"},
     *             @OA\Property(property="partial_query", type="string", example="suspicious act"),
     *             @OA\Property(property="context", type="string", enum={"investigation", "monitoring", "analysis", "general"}, example="investigation"),
     *             @OA\Property(property="data_sources", type="array", @OA\Items(type="string"), example={"persons", "alerts"}),
     *             @OA\Property(property="max_suggestions", type="integer", example=10)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Suggestions retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Suggestions retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="partial_query", type="string", example="suspicious act"),
     *                 @OA\Property(
     *                     property="suggestions",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="query", type="string", example="suspicious activity near landmarks"),
     *                         @OA\Property(property="type", type="string", enum={"completion", "related", "popular"}, example="completion"),
     *                         @OA\Property(property="confidence", type="number", format="float", example=0.85),
     *                         @OA\Property(property="description", type="string", example="Search for suspicious activities near important landmarks"),
     *                         @OA\Property(property="estimated_results", type="integer", example=150)
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="popular_queries",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"recent security alerts", "vehicle tracking", "person movement analysis"}
     *                 ),
     *                 @OA\Property(
     *                     property="quick_filters",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="name", type="string", example="Last 24 hours"),
     *                         @OA\Property(property="filter", type="object")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getSuggestions() {}

    /**
     * @OA\Get(
     *     path="/api/v1/nebula/search-history",
     *     summary="Get search history",
     *     description="Retrieve user's search history with pagination",
     *     operationId="getNebulaSearchHistory",
     *     tags={"Nebula Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Filter by start date",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search history retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search history retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="search_id", type="string", example="SEARCH123456789"),
     *                     @OA\Property(property="query", type="string", example="suspicious activity near Fountain Square"),
     *                     @OA\Property(property="search_type", type="string", example="ai_assisted"),
     *                     @OA\Property(property="results_count", type="integer", example=45),
     *                     @OA\Property(property="search_time", type="number", format="float", example=1.25),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=150),
     *                 @OA\Property(property="per_page", type="integer", example=20)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getSearchHistory() {}
}
