<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Server Status",
 *     description="API endpoints for server health monitoring and status checks"
 * )
 */

class ServerStatusController
{
    /**
     * @OA\Get(
     *     path="/api/v1/server-status",
     *     summary="Get overall server status",
     *     description="Retrieve comprehensive server health and status information",
     *     operationId="getServerStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Server status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Server status retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="overall_status", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                 @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T14:30:00Z"),
     *                 @OA\Property(property="uptime", type="integer", example=86400, description="Server uptime in seconds"),
     *                 @OA\Property(property="version", type="string", example="2.0.0"),
     *                 @OA\Property(property="environment", type="string", example="production"),
     *                 @OA\Property(
     *                     property="services",
     *                     type="object",
     *                     @OA\Property(property="database", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                     @OA\Property(property="elasticsearch", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                     @OA\Property(property="redis", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                     @OA\Property(property="storage", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy")
     *                 ),
     *                 @OA\Property(
     *                     property="metrics",
     *                     type="object",
     *                     @OA\Property(property="cpu_usage", type="number", format="float", example=25.5, description="CPU usage percentage"),
     *                     @OA\Property(property="memory_usage", type="number", format="float", example=68.2, description="Memory usage percentage"),
     *                     @OA\Property(property="disk_usage", type="number", format="float", example=45.8, description="Disk usage percentage"),
     *                     @OA\Property(property="active_connections", type="integer", example=150),
     *                     @OA\Property(property="requests_per_minute", type="integer", example=1250)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="Server is unhealthy",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="Server is unhealthy"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="overall_status", type="string", example="unhealthy"),
     *                 @OA\Property(property="failed_services", type="array", @OA\Items(type="string"), example={"database", "elasticsearch"})
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/database",
     *     summary="Get database status",
     *     description="Check the status and health of database connections",
     *     operationId="getDatabaseStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Database status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Database status retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="status", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                 @OA\Property(property="connection_time", type="number", format="float", example=0.025, description="Connection time in seconds"),
     *                 @OA\Property(property="query_time", type="number", format="float", example=0.012, description="Average query time in seconds"),
     *                 @OA\Property(
     *                     property="connections",
     *                     type="object",
     *                     @OA\Property(property="active", type="integer", example=15),
     *                     @OA\Property(property="idle", type="integer", example=5),
     *                     @OA\Property(property="max", type="integer", example=100)
     *                 ),
     *                 @OA\Property(
     *                     property="databases",
     *                     type="object",
     *                     @OA\Property(
     *                         property="postgresql",
     *                         type="object",
     *                         @OA\Property(property="status", type="string", example="healthy"),
     *                         @OA\Property(property="version", type="string", example="13.8"),
     *                         @OA\Property(property="size", type="string", example="2.5GB")
     *                     ),
     *                     @OA\Property(
     *                         property="mongodb",
     *                         type="object",
     *                         @OA\Property(property="status", type="string", example="healthy"),
     *                         @OA\Property(property="version", type="string", example="5.0.9"),
     *                         @OA\Property(property="size", type="string", example="1.8GB")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="Database is unavailable",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="Database is unavailable")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getDatabaseStatus() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/elasticsearch",
     *     summary="Get Elasticsearch status",
     *     description="Check the status and health of the Elasticsearch cluster",
     *     operationId="getElasticsearchStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Elasticsearch status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Elasticsearch status retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="status", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                 @OA\Property(property="cluster_name", type="string", example="beein-cluster"),
     *                 @OA\Property(property="cluster_status", type="string", enum={"green", "yellow", "red"}, example="green"),
     *                 @OA\Property(property="number_of_nodes", type="integer", example=3),
     *                 @OA\Property(property="active_primary_shards", type="integer", example=15),
     *                 @OA\Property(property="active_shards", type="integer", example=30),
     *                 @OA\Property(property="relocating_shards", type="integer", example=0),
     *                 @OA\Property(property="initializing_shards", type="integer", example=0),
     *                 @OA\Property(property="unassigned_shards", type="integer", example=0),
     *                 @OA\Property(property="version", type="string", example="7.17.0"),
     *                 @OA\Property(
     *                     property="indices",
     *                     type="object",
     *                     @OA\Property(property="total", type="integer", example=8),
     *                     @OA\Property(property="health", type="string", example="green"),
     *                     @OA\Property(property="total_size", type="string", example="1.2GB")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="Elasticsearch is unavailable",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="Elasticsearch is unavailable")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getElasticsearchStatus() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/redis",
     *     summary="Get Redis cache status",
     *     description="Check the status and health of the Redis cache server",
     *     operationId="getRedisStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Redis status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Redis status retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="status", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                 @OA\Property(property="version", type="string", example="6.2.7"),
     *                 @OA\Property(property="uptime", type="integer", example=86400, description="Redis uptime in seconds"),
     *                 @OA\Property(property="used_memory", type="string", example="2.5MB"),
     *                 @OA\Property(property="connected_clients", type="integer", example=12),
     *                 @OA\Property(property="total_commands_processed", type="integer", example=1500000),
     *                 @OA\Property(property="keyspace_hits", type="integer", example=950000),
     *                 @OA\Property(property="keyspace_misses", type="integer", example=50000),
     *                 @OA\Property(property="hit_rate", type="number", format="float", example=95.0, description="Cache hit rate percentage"),
     *                 @OA\Property(property="response_time", type="number", format="float", example=0.001, description="Average response time in seconds")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="Redis is unavailable",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="Redis is unavailable")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getRedisStatus() {}
}
