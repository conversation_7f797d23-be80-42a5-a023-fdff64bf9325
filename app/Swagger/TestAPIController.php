<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Testing",
 *     description="API endpoints for testing and development purposes"
 * )
 */

class TestAPIController
{
    /**
     * @OA\Get(
     *     path="/api/v1/test/health",
     *     summary="API health check",
     *     description="Check the health status of the API and its dependencies",
     *     operationId="healthCheck",
     *     tags={"Testing"},
     *     @OA\Response(
     *         response=200,
     *         description="API is healthy",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="API is healthy"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="api_version", type="string", example="1.0.0"),
     *                 @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="uptime", type="integer", example=86400, description="Uptime in seconds"),
     *                 @OA\Property(property="environment", type="string", example="production"),
     *                 @OA\Property(
     *                     property="services",
     *                     type="object",
     *                     @OA\Property(property="database", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                     @OA\Property(property="redis", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                     @OA\Property(property="elasticsearch", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy"),
     *                     @OA\Property(property="storage", type="string", enum={"healthy", "degraded", "unhealthy"}, example="healthy")
     *                 ),
     *                 @OA\Property(
     *                     property="performance",
     *                     type="object",
     *                     @OA\Property(property="memory_usage", type="string", example="45%"),
     *                     @OA\Property(property="cpu_usage", type="string", example="23%"),
     *                     @OA\Property(property="disk_usage", type="string", example="67%"),
     *                     @OA\Property(property="response_time", type="number", format="float", example=0.025, description="Average response time in seconds")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="API is unhealthy",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="API is unhealthy"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="issues", type="array", @OA\Items(type="string"), example={"Database connection failed", "Redis unavailable"})
     *             )
     *         )
     *     )
     * )
     */
    public function healthCheck() {}

    /**
     * @OA\Post(
     *     path="/api/v1/test/echo",
     *     summary="Echo test endpoint",
     *     description="Echo back the request data for testing purposes",
     *     operationId="echoTest",
     *     tags={"Testing"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Data to echo back",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Hello, Beein API!"),
     *             @OA\Property(property="data", type="object", description="Any data structure to echo"),
     *             @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Data echoed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Data echoed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="echoed_data", type="object", description="The original request data"),
     *                 @OA\Property(property="request_info", type="object",
     *                     @OA\Property(property="method", type="string", example="POST"),
     *                     @OA\Property(property="url", type="string", example="/api/v1/test/echo"),
     *                     @OA\Property(property="user_agent", type="string", example="Mozilla/5.0..."),
     *                     @OA\Property(property="ip_address", type="string", example="*************"),
     *                     @OA\Property(property="content_type", type="string", example="application/json")
     *                 ),
     *                 @OA\Property(property="server_timestamp", type="string", format="date-time", example="2024-01-15T10:30:05Z"),
     *                 @OA\Property(property="processing_time", type="number", format="float", example=0.001, description="Processing time in seconds")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function echoTest() {}

    /**
     * @OA\Get(
     *     path="/api/v1/test/performance",
     *     summary="Performance test endpoint",
     *     description="Test API performance with configurable load",
     *     operationId="performanceTest",
     *     tags={"Testing"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="iterations",
     *         in="query",
     *         description="Number of iterations to perform",
     *         required=false,
     *         @OA\Schema(type="integer", example=100, minimum=1, maximum=10000)
     *     ),
     *     @OA\Parameter(
     *         name="delay",
     *         in="query",
     *         description="Delay between iterations in milliseconds",
     *         required=false,
     *         @OA\Schema(type="integer", example=10, minimum=0, maximum=1000)
     *     ),
     *     @OA\Parameter(
     *         name="memory_test",
     *         in="query",
     *         description="Include memory usage test",
     *         required=false,
     *         @OA\Schema(type="boolean", example=false)
     *     ),
     *     @OA\Parameter(
     *         name="database_test",
     *         in="query",
     *         description="Include database performance test",
     *         required=false,
     *         @OA\Schema(type="boolean", example=false)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Performance test completed",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Performance test completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="test_config", type="object",
     *                     @OA\Property(property="iterations", type="integer", example=100),
     *                     @OA\Property(property="delay", type="integer", example=10),
     *                     @OA\Property(property="memory_test", type="boolean", example=false),
     *                     @OA\Property(property="database_test", type="boolean", example=false)
     *                 ),
     *                 @OA\Property(property="results", type="object",
     *                     @OA\Property(property="total_time", type="number", format="float", example=1.25, description="Total execution time in seconds"),
     *                     @OA\Property(property="average_iteration_time", type="number", format="float", example=0.0125, description="Average time per iteration in seconds"),
     *                     @OA\Property(property="min_iteration_time", type="number", format="float", example=0.008),
     *                     @OA\Property(property="max_iteration_time", type="number", format="float", example=0.025),
     *                     @OA\Property(property="iterations_per_second", type="number", format="float", example=80.0),
     *                     @OA\Property(property="memory_usage", type="object",
     *                         @OA\Property(property="start_memory", type="integer", example=52428800, description="Memory usage at start in bytes"),
     *                         @OA\Property(property="end_memory", type="integer", example=54525952, description="Memory usage at end in bytes"),
     *                         @OA\Property(property="peak_memory", type="integer", example=56623104, description="Peak memory usage in bytes"),
     *                         @OA\Property(property="memory_increase", type="integer", example=2097152, description="Memory increase in bytes")
     *                     ),
     *                     @OA\Property(property="database_performance", type="object",
     *                         @OA\Property(property="query_count", type="integer", example=100),
     *                         @OA\Property(property="total_query_time", type="number", format="float", example=0.5),
     *                         @OA\Property(property="average_query_time", type="number", format="float", example=0.005),
     *                         @OA\Property(property="slowest_query_time", type="number", format="float", example=0.015)
     *                     )
     *                 ),
     *                 @OA\Property(property="system_info", type="object",
     *                     @OA\Property(property="php_version", type="string", example="8.2.0"),
     *                     @OA\Property(property="laravel_version", type="string", example="10.0.0"),
     *                     @OA\Property(property="server_load", type="array", @OA\Items(type="number"), example={0.25, 0.30, 0.28}),
     *                     @OA\Property(property="memory_limit", type="string", example="512M")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid test parameters",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=400),
     *             @OA\Property(property="message", type="string", example="Invalid test parameters"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="iterations",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"Iterations must be between 1 and 10000"}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function performanceTest() {}

    /**
     * @OA\Post(
     *     path="/api/v1/test/error",
     *     summary="Error simulation endpoint",
     *     description="Simulate various error conditions for testing error handling",
     *     operationId="errorTest",
     *     tags={"Testing"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Error simulation configuration",
     *         @OA\JsonContent(
     *             required={"error_type"},
     *             @OA\Property(property="error_type", type="string", enum={"validation", "server_error", "timeout", "database_error", "memory_error"}, example="validation"),
     *             @OA\Property(property="error_code", type="integer", example=422, description="HTTP status code to return"),
     *             @OA\Property(property="error_message", type="string", example="Custom error message for testing"),
     *             @OA\Property(property="delay", type="integer", example=0, description="Delay before error in milliseconds")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error simulation",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error (Simulated)"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="test_field",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"This is a simulated validation error for testing purposes"}
     *                 )
     *             ),
     *             @OA\Property(property="simulation_info", type="object",
     *                 @OA\Property(property="simulated", type="boolean", example=true),
     *                 @OA\Property(property="error_type", type="string", example="validation"),
     *                 @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=500,
     *         description="Server error simulation",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=500),
     *             @OA\Property(property="message", type="string", example="Internal Server Error (Simulated)"),
     *             @OA\Property(property="simulation_info", type="object",
     *                 @OA\Property(property="simulated", type="boolean", example=true),
     *                 @OA\Property(property="error_type", type="string", example="server_error"),
     *                 @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function errorTest() {}

    /**
     * @OA\Get(
     *     path="/api/v1/test/database",
     *     summary="Database connectivity test",
     *     description="Test database connectivity and performance",
     *     operationId="databaseTest",
     *     tags={"Testing"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="test_type",
     *         in="query",
     *         description="Type of database test to perform",
     *         required=false,
     *         @OA\Schema(type="string", enum={"connection", "performance", "stress"}, example="connection")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Database test completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Database test completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="test_type", type="string", example="connection"),
     *                 @OA\Property(property="database_info", type="object",
     *                     @OA\Property(property="driver", type="string", example="mysql"),
     *                     @OA\Property(property="version", type="string", example="8.0.32"),
     *                     @OA\Property(property="host", type="string", example="localhost"),
     *                     @OA\Property(property="database", type="string", example="beein_api")
     *                 ),
     *                 @OA\Property(property="connection_test", type="object",
     *                     @OA\Property(property="status", type="string", example="success"),
     *                     @OA\Property(property="connection_time", type="number", format="float", example=0.025, description="Connection time in seconds"),
     *                     @OA\Property(property="ping_time", type="number", format="float", example=0.001, description="Ping time in seconds")
     *                 ),
     *                 @OA\Property(property="performance_test", type="object",
     *                     @OA\Property(property="simple_query_time", type="number", format="float", example=0.002),
     *                     @OA\Property(property="complex_query_time", type="number", format="float", example=0.015),
     *                     @OA\Property(property="insert_test_time", type="number", format="float", example=0.008),
     *                     @OA\Property(property="update_test_time", type="number", format="float", example=0.006)
     *                 ),
     *                 @OA\Property(property="statistics", type="object",
     *                     @OA\Property(property="active_connections", type="integer", example=5),
     *                     @OA\Property(property="max_connections", type="integer", example=151),
     *                     @OA\Property(property="uptime", type="integer", example=86400, description="Database uptime in seconds")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="Database test failed",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="Database test failed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="error", type="string", example="Connection timeout"),
     *                 @OA\Property(property="details", type="string", example="Could not connect to database server")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function databaseTest() {}
}
