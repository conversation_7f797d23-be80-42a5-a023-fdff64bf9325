<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Notifications",
 *     description="API endpoints for notification management and delivery"
 * )
 */

class NotificationController
{
    /**
     * @OA\Get(
     *     path="/api/v1/notifications",
     *     summary="Get user notifications",
     *     description="Retrieve list of notifications for the authenticated user",
     *     operationId="getNotifications",
     *     tags={"Notifications"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by notification status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"read", "unread", "all"}, example="unread")
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="Filter by notification type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"alert", "system", "security", "info"}, example="alert")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Notifications retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Notifications retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Security Alert"),
     *                     @OA\Property(property="message", type="string", example="Suspicious activity detected at Main Entrance"),
     *                     @OA\Property(property="type", type="string", enum={"alert", "system", "security", "info"}, example="alert"),
     *                     @OA\Property(property="priority", type="string", enum={"low", "medium", "high", "critical"}, example="high"),
     *                     @OA\Property(property="is_read", type="boolean", example=false),
     *                     @OA\Property(property="read_at", type="string", format="date-time", nullable=true, example=null),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="data", type="object", description="Additional notification data"),
     *                     @OA\Property(property="action_url", type="string", nullable=true, example="https://app.beein.az/alerts/123"),
     *                     @OA\Property(property="expires_at", type="string", format="date-time", nullable=true, example="2024-01-22T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=45),
     *                 @OA\Property(property="per_page", type="integer", example=20),
     *                 @OA\Property(property="unread_count", type="integer", example=12)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Post(
     *     path="/api/v1/notifications",
     *     summary="Create a notification",
     *     description="Create a new notification for users",
     *     operationId="createNotification",
     *     tags={"Notifications"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Notification data",
     *         @OA\JsonContent(
     *             required={"title", "message", "type"},
     *             @OA\Property(property="title", type="string", example="Security Alert"),
     *             @OA\Property(property="message", type="string", example="Suspicious activity detected at Main Entrance"),
     *             @OA\Property(property="type", type="string", enum={"alert", "system", "security", "info"}, example="alert"),
     *             @OA\Property(property="priority", type="string", enum={"low", "medium", "high", "critical"}, example="high"),
     *             @OA\Property(property="recipients", type="array", @OA\Items(type="integer"), example={1, 2, 3}, description="User IDs to send notification to"),
     *             @OA\Property(property="data", type="object", description="Additional notification data"),
     *             @OA\Property(property="action_url", type="string", example="https://app.beein.az/alerts/123"),
     *             @OA\Property(property="expires_at", type="string", format="date-time", example="2024-01-22T10:30:00Z"),
     *             @OA\Property(property="send_email", type="boolean", example=false, description="Also send as email"),
     *             @OA\Property(property="send_sms", type="boolean", example=false, description="Also send as SMS")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Notification created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Notification created successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Security Alert"),
     *                 @OA\Property(property="message", type="string", example="Suspicious activity detected at Main Entrance"),
     *                 @OA\Property(property="type", type="string", example="alert"),
     *                 @OA\Property(property="priority", type="string", example="high"),
     *                 @OA\Property(property="recipients_count", type="integer", example=3),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The title field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Get(
     *     path="/api/v1/notifications/{notification}",
     *     summary="Get notification by ID",
     *     description="Retrieve a specific notification by its ID",
     *     operationId="getNotificationById",
     *     tags={"Notifications"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="notification",
     *         in="path",
     *         description="Notification ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Notification retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Notification retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Security Alert"),
     *                 @OA\Property(property="message", type="string", example="Suspicious activity detected at Main Entrance"),
     *                 @OA\Property(property="type", type="string", example="alert"),
     *                 @OA\Property(property="priority", type="string", example="high"),
     *                 @OA\Property(property="is_read", type="boolean", example=false),
     *                 @OA\Property(property="read_at", type="string", format="date-time", nullable=true, example=null),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="data", type="object", description="Additional notification data"),
     *                 @OA\Property(property="action_url", type="string", nullable=true, example="https://app.beein.az/alerts/123"),
     *                 @OA\Property(property="expires_at", type="string", format="date-time", nullable=true, example="2024-01-22T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Notification not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Notification not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Put(
     *     path="/api/v1/notifications/{notification}/read",
     *     summary="Mark notification as read",
     *     description="Mark a specific notification as read",
     *     operationId="markNotificationAsRead",
     *     tags={"Notifications"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="notification",
     *         in="path",
     *         description="Notification ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Notification marked as read successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Notification marked as read"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="is_read", type="boolean", example=true),
     *                 @OA\Property(property="read_at", type="string", format="date-time", example="2024-01-15T10:35:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Notification not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Notification not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function markAsRead() {}

    /**
     * @OA\Put(
     *     path="/api/v1/notifications/read-all",
     *     summary="Mark all notifications as read",
     *     description="Mark all notifications for the authenticated user as read",
     *     operationId="markAllNotificationsAsRead",
     *     tags={"Notifications"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="All notifications marked as read successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="All notifications marked as read"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="updated_count", type="integer", example=12),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:35:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function markAllAsRead() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/notifications/{notification}",
     *     summary="Delete a notification",
     *     description="Delete a specific notification",
     *     operationId="deleteNotification",
     *     tags={"Notifications"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="notification",
     *         in="path",
     *         description="Notification ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Notification deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Notification deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Notification not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Notification not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}
}
