<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Vehicles",
 *     description="API endpoints for vehicle search and management"
 * )
 */

class VehicleController
{
    /**
     * @OA\Post(
     *     path="/api/v1/search-car",
     *     summary="Search for vehicles",
     *     description="Search for vehicles using various criteria including license plate, owner information, and vehicle details",
     *     operationId="searchCar",
     *     tags={"Vehicles"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Vehicle search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="license_plate", type="string", example="10-AA-123", description="Vehicle license plate number"),
     *             @OA\Property(property="owner_pin", type="string", example="1234567", description="Owner's PIN"),
     *             @OA\Property(property="owner_name", type="string", example="John Doe", description="Owner's name"),
     *             @OA\Property(property="make", type="string", example="Toyota", description="Vehicle make/brand"),
     *             @OA\Property(property="model", type="string", example="Camry", description="Vehicle model"),
     *             @OA\Property(property="year", type="integer", example=2020, description="Manufacturing year"),
     *             @OA\Property(property="color", type="string", example="White", description="Vehicle color"),
     *             @OA\Property(property="vin", type="string", example="1HGBH41JXMN109186", description="Vehicle identification number"),
     *             @OA\Property(property="engine_number", type="string", example="ENG123456", description="Engine number"),
     *             @OA\Property(property="registration_date_from", type="string", format="date", example="2020-01-01", description="Registration date range start"),
     *             @OA\Property(property="registration_date_to", type="string", format="date", example="2024-01-31", description="Registration date range end")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Vehicle search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Vehicle search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total_results", type="integer", example=5),
     *                 @OA\Property(property="search_time", type="number", format="float", example=0.25, description="Search time in seconds"),
     *                 @OA\Property(
     *                     property="vehicles",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="license_plate", type="string", example="10-AA-123"),
     *                         @OA\Property(property="make", type="string", example="Toyota"),
     *                         @OA\Property(property="model", type="string", example="Camry"),
     *                         @OA\Property(property="year", type="integer", example=2020),
     *                         @OA\Property(property="color", type="string", example="White"),
     *                         @OA\Property(property="vin", type="string", example="1HGBH41JXMN109186"),
     *                         @OA\Property(property="engine_number", type="string", example="ENG123456"),
     *                         @OA\Property(property="registration_date", type="string", format="date", example="2020-01-15"),
     *                         @OA\Property(property="status", type="string", enum={"active", "inactive", "stolen", "impounded"}, example="active"),
     *                         @OA\Property(
     *                             property="owner",
     *                             type="object",
     *                             @OA\Property(property="pin", type="string", example="1234567"),
     *                             @OA\Property(property="name", type="string", example="John"),
     *                             @OA\Property(property="surname", type="string", example="Doe"),
     *                             @OA\Property(property="phone", type="string", example="994501234567"),
     *                             @OA\Property(property="address", type="string", example="Baku, Azerbaijan")
     *                         ),
     *                         @OA\Property(
     *                             property="insurance",
     *                             type="object",
     *                             @OA\Property(property="policy_number", type="string", example="INS123456789"),
     *                             @OA\Property(property="company", type="string", example="ASCO Insurance"),
     *                             @OA\Property(property="valid_until", type="string", format="date", example="2024-12-31"),
     *                             @OA\Property(property="status", type="string", example="active")
     *                         ),
     *                         @OA\Property(
     *                             property="technical_inspection",
     *                             type="object",
     *                             @OA\Property(property="last_inspection", type="string", format="date", example="2023-06-15"),
     *                             @OA\Property(property="next_inspection", type="string", format="date", example="2024-06-15"),
     *                             @OA\Property(property="status", type="string", example="valid")
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="license_plate",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"At least one search criteria is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchCar() {}

    /**
     * @OA\Post(
     *     path="/api/v1/search-car-by-pin",
     *     summary="Search vehicles by owner PIN",
     *     description="Search for all vehicles owned by a specific person using their PIN",
     *     operationId="searchCarByPin",
     *     tags={"Vehicles"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Owner PIN for vehicle search",
     *         @OA\JsonContent(
     *             required={"pin"},
     *             @OA\Property(property="pin", type="string", example="1234567", description="Owner's PIN"),
     *             @OA\Property(property="include_history", type="boolean", example=false, description="Include ownership history"),
     *             @OA\Property(property="status_filter", type="string", enum={"all", "active", "inactive", "stolen"}, example="all", description="Filter by vehicle status")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Vehicles found successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Vehicles found for PIN"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="owner_pin", type="string", example="1234567"),
     *                 @OA\Property(property="total_vehicles", type="integer", example=3),
     *                 @OA\Property(
     *                     property="owner_info",
     *                     type="object",
     *                     @OA\Property(property="name", type="string", example="John"),
     *                     @OA\Property(property="surname", type="string", example="Doe"),
     *                     @OA\Property(property="phone", type="string", example="994501234567"),
     *                     @OA\Property(property="address", type="string", example="Baku, Azerbaijan")
     *                 ),
     *                 @OA\Property(
     *                     property="vehicles",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="license_plate", type="string", example="10-AA-123"),
     *                         @OA\Property(property="make", type="string", example="Toyota"),
     *                         @OA\Property(property="model", type="string", example="Camry"),
     *                         @OA\Property(property="year", type="integer", example=2020),
     *                         @OA\Property(property="color", type="string", example="White"),
     *                         @OA\Property(property="registration_date", type="string", format="date", example="2020-01-15"),
     *                         @OA\Property(property="status", type="string", example="active"),
     *                         @OA\Property(property="ownership_start", type="string", format="date", example="2020-01-15"),
     *                         @OA\Property(property="ownership_end", type="string", format="date", nullable=true, example=null)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="No vehicles found for PIN",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="No vehicles found for the provided PIN")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="pin",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The pin field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchCarByPin() {}

    /**
     * @OA\Get(
     *     path="/api/v1/vehicle/{vehicleId}/history",
     *     summary="Get vehicle history",
     *     description="Retrieve complete history of a vehicle including ownership changes, accidents, and maintenance",
     *     operationId="getVehicleHistory",
     *     tags={"Vehicles"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="vehicleId",
     *         in="path",
     *         description="Vehicle ID or license plate",
     *         required=true,
     *         @OA\Schema(type="string", example="10-AA-123")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Vehicle history retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Vehicle history retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="license_plate", type="string", example="10-AA-123"),
     *                 @OA\Property(property="vin", type="string", example="1HGBH41JXMN109186"),
     *                 @OA\Property(
     *                     property="ownership_history",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="owner_pin", type="string", example="1234567"),
     *                         @OA\Property(property="owner_name", type="string", example="John Doe"),
     *                         @OA\Property(property="ownership_start", type="string", format="date", example="2020-01-15"),
     *                         @OA\Property(property="ownership_end", type="string", format="date", nullable=true, example="2023-06-30"),
     *                         @OA\Property(property="transfer_reason", type="string", example="sale")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="accidents",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="date", type="string", format="date", example="2022-03-15"),
     *                         @OA\Property(property="location", type="string", example="Baku city center"),
     *                         @OA\Property(property="severity", type="string", enum={"minor", "moderate", "severe"}, example="minor"),
     *                         @OA\Property(property="damage_cost", type="number", format="float", example=1500.00),
     *                         @OA\Property(property="police_report", type="string", example="REP123456")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="violations",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="date", type="string", format="date", example="2023-01-10"),
     *                         @OA\Property(property="violation_type", type="string", example="speeding"),
     *                         @OA\Property(property="fine_amount", type="number", format="float", example=50.00),
     *                         @OA\Property(property="location", type="string", example="Highway M1"),
     *                         @OA\Property(property="status", type="string", enum={"paid", "unpaid", "contested"}, example="paid")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="inspections",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="date", type="string", format="date", example="2023-06-15"),
     *                         @OA\Property(property="result", type="string", enum={"passed", "failed", "conditional"}, example="passed"),
     *                         @OA\Property(property="next_due", type="string", format="date", example="2024-06-15"),
     *                         @OA\Property(property="inspection_center", type="string", example="Baku Technical Center")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Vehicle not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Vehicle not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getVehicleHistory() {}
}
