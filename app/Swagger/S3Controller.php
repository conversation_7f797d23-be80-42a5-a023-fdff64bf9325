<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="File Management",
 *     description="API endpoints for file upload, download, and media management operations"
 * )
 */

class S3Controller
{
    /**
     * @OA\Post(
     *     path="/api/v1/files/upload",
     *     summary="Upload file",
     *     description="Upload a file to the system storage",
     *     operationId="uploadFile",
     *     tags={"File Management"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="File upload",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"file"},
     *                 @OA\Property(property="file", type="string", format="binary", description="File to upload"),
     *                 @OA\Property(property="category", type="string", enum={"evidence", "document", "image", "video", "report", "export"}, example="evidence"),
     *                 @OA\Property(property="description", type="string", example="Security camera footage from incident"),
     *                 @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"surveillance", "incident123"}),
     *                 @OA\Property(property="is_confidential", type="boolean", example=true),
     *                 @OA\Property(property="retention_days", type="integer", example=365, description="File retention period in days"),
     *                 @OA\Property(property="related_entity_type", type="string", enum={"person", "vehicle", "alert", "case"}, example="alert"),
     *                 @OA\Property(property="related_entity_id", type="string", example="ALERT123456")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="File uploaded successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="File uploaded successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="file_id", type="string", example="FILE123456789"),
     *                 @OA\Property(property="filename", type="string", example="evidence_video_20240115.mp4"),
     *                 @OA\Property(property="original_filename", type="string", example="camera_footage.mp4"),
     *                 @OA\Property(property="file_size", type="integer", example=52428800, description="File size in bytes"),
     *                 @OA\Property(property="mime_type", type="string", example="video/mp4"),
     *                 @OA\Property(property="category", type="string", example="evidence"),
     *                 @OA\Property(property="file_hash", type="string", example="sha256:abc123def456..."),
     *                 @OA\Property(property="storage_path", type="string", example="evidence/2024/01/15/FILE123456789.mp4"),
     *                 @OA\Property(property="download_url", type="string", example="https://api.beein.az/files/download/FILE123456789"),
     *                 @OA\Property(property="thumbnail_url", type="string", nullable=true, example="https://api.beein.az/files/thumbnail/FILE123456789"),
     *                 @OA\Property(property="is_confidential", type="boolean", example=true),
     *                 @OA\Property(property="retention_until", type="string", format="date", example="2025-01-15"),
     *                 @OA\Property(property="uploaded_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="file",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The file field is required.", "File size exceeds maximum allowed size."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=413,
     *         description="File too large",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=413),
     *             @OA\Property(property="message", type="string", example="File size exceeds maximum allowed size")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function upload() {}

    /**
     * @OA\Get(
     *     path="/api/v1/files/{fileId}/download",
     *     summary="Download file",
     *     description="Download a file by its ID",
     *     operationId="downloadFile",
     *     tags={"File Management"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="fileId",
     *         in="path",
     *         description="File ID",
     *         required=true,
     *         @OA\Schema(type="string", example="FILE123456789")
     *     ),
     *     @OA\Parameter(
     *         name="inline",
     *         in="query",
     *         description="Display file inline instead of downloading",
     *         required=false,
     *         @OA\Schema(type="boolean", example=false)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="File downloaded successfully",
     *         @OA\MediaType(
     *             mediaType="application/octet-stream",
     *             @OA\Schema(type="string", format="binary")
     *         ),
     *         @OA\Header(
     *             header="Content-Disposition",
     *             description="File download disposition",
     *             @OA\Schema(type="string", example="attachment; filename=evidence_video_20240115.mp4")
     *         ),
     *         @OA\Header(
     *             header="Content-Type",
     *             description="File MIME type",
     *             @OA\Schema(type="string", example="video/mp4")
     *         ),
     *         @OA\Header(
     *             header="Content-Length",
     *             description="File size in bytes",
     *             @OA\Schema(type="integer", example=52428800)
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="File not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="File not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Access denied to this file")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function download() {}

    /**
     * @OA\Get(
     *     path="/api/v1/files",
     *     summary="Get file list",
     *     description="Retrieve list of files with filtering and pagination",
     *     operationId="getFiles",
     *     tags={"File Management"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Parameter(
     *         name="category",
     *         in="query",
     *         description="Filter by file category",
     *         required=false,
     *         @OA\Schema(type="string", enum={"evidence", "document", "image", "video", "report", "export"}, example="evidence")
     *     ),
     *     @OA\Parameter(
     *         name="mime_type",
     *         in="query",
     *         description="Filter by MIME type",
     *         required=false,
     *         @OA\Schema(type="string", example="video/mp4")
     *     ),
     *     @OA\Parameter(
     *         name="uploaded_by",
     *         in="query",
     *         description="Filter by uploader user ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=123)
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Filter by upload date from",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="Filter by upload date to",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-31")
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search in filename and description",
     *         required=false,
     *         @OA\Schema(type="string", example="evidence")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Files retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Files retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="file_id", type="string", example="FILE123456789"),
     *                     @OA\Property(property="filename", type="string", example="evidence_video_20240115.mp4"),
     *                     @OA\Property(property="original_filename", type="string", example="camera_footage.mp4"),
     *                     @OA\Property(property="file_size", type="integer", example=52428800),
     *                     @OA\Property(property="mime_type", type="string", example="video/mp4"),
     *                     @OA\Property(property="category", type="string", example="evidence"),
     *                     @OA\Property(property="description", type="string", example="Security camera footage from incident"),
     *                     @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"surveillance", "incident123"}),
     *                     @OA\Property(property="is_confidential", type="boolean", example=true),
     *                     @OA\Property(property="download_count", type="integer", example=5),
     *                     @OA\Property(property="uploaded_by", type="object",
     *                         @OA\Property(property="id", type="integer", example=123),
     *                         @OA\Property(property="name", type="string", example="Əli Məmmədov")
     *                     ),
     *                     @OA\Property(property="uploaded_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="last_accessed", type="string", format="date-time", nullable=true, example="2024-01-15T14:20:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=150),
     *                 @OA\Property(property="per_page", type="integer", example=20)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/files/{fileId}",
     *     summary="Get file information",
     *     description="Retrieve detailed information about a specific file",
     *     operationId="getFileInfo",
     *     tags={"File Management"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="fileId",
     *         in="path",
     *         description="File ID",
     *         required=true,
     *         @OA\Schema(type="string", example="FILE123456789")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="File information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="File information retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="file_id", type="string", example="FILE123456789"),
     *                 @OA\Property(property="filename", type="string", example="evidence_video_20240115.mp4"),
     *                 @OA\Property(property="original_filename", type="string", example="camera_footage.mp4"),
     *                 @OA\Property(property="file_size", type="integer", example=52428800),
     *                 @OA\Property(property="mime_type", type="string", example="video/mp4"),
     *                 @OA\Property(property="category", type="string", example="evidence"),
     *                 @OA\Property(property="description", type="string", example="Security camera footage from incident"),
     *                 @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"surveillance", "incident123"}),
     *                 @OA\Property(property="file_hash", type="string", example="sha256:abc123def456..."),
     *                 @OA\Property(property="storage_path", type="string", example="evidence/2024/01/15/FILE123456789.mp4"),
     *                 @OA\Property(property="is_confidential", type="boolean", example=true),
     *                 @OA\Property(property="retention_until", type="string", format="date", example="2025-01-15"),
     *                 @OA\Property(property="download_count", type="integer", example=5),
     *                 @OA\Property(property="view_count", type="integer", example=12),
     *                 @OA\Property(property="related_entity", type="object",
     *                     @OA\Property(property="type", type="string", example="alert"),
     *                     @OA\Property(property="id", type="string", example="ALERT123456"),
     *                     @OA\Property(property="title", type="string", example="Security Incident Alert")
     *                 ),
     *                 @OA\Property(property="uploaded_by", type="object",
     *                     @OA\Property(property="id", type="integer", example=123),
     *                     @OA\Property(property="name", type="string", example="Əli Məmmədov"),
     *                     @OA\Property(property="department", type="string", example="Security Operations")
     *                 ),
     *                 @OA\Property(property="access_log", type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="action", type="string", enum={"download", "view", "share"}, example="download"),
     *                         @OA\Property(property="user_id", type="integer", example=456),
     *                         @OA\Property(property="user_name", type="string", example="Leyla Əliyeva"),
     *                         @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T14:20:00Z"),
     *                         @OA\Property(property="ip_address", type="string", example="*************")
     *                     )
     *                 ),
     *                 @OA\Property(property="uploaded_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="last_accessed", type="string", format="date-time", nullable=true, example="2024-01-15T14:20:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="File not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="File not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/files/{fileId}",
     *     summary="Delete file",
     *     description="Delete a file from the system",
     *     operationId="deleteFile",
     *     tags={"File Management"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="fileId",
     *         in="path",
     *         description="File ID",
     *         required=true,
     *         @OA\Schema(type="string", example="FILE123456789")
     *     ),
     *     @OA\Parameter(
     *         name="reason",
     *         in="query",
     *         description="Reason for deletion",
     *         required=false,
     *         @OA\Schema(type="string", example="No longer needed for investigation")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="File deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="File deleted successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="file_id", type="string", example="FILE123456789"),
     *                 @OA\Property(property="filename", type="string", example="evidence_video_20240115.mp4"),
     *                 @OA\Property(property="deleted_at", type="string", format="date-time", example="2024-01-15T15:30:00Z"),
     *                 @OA\Property(property="deleted_by", type="string", example="Əli Məmmədov")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="File not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="File not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to delete this file")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}
}
