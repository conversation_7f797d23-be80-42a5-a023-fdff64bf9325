<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Hierarchy Relationships",
 *     description="API endpoints for hierarchy relationships, tree operations, and user queries"
 * )
 */

class HierarchyRelationshipController
{
    /**
     * @OA\Get(
     *     path="/api/v1/users/{user}/hierarchies",
     *     summary="Get user hierarchies",
     *     description="Retrieve all hierarchies that a user belongs to",
     *     operationId="getUserHierarchies",
     *     tags={"Hierarchy Relationships"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="user",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User hierarchies retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="IT Department"),
     *                 @OA\Property(property="description", type="string", example="Information Technology Department"),
     *                 @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="pivot",
     *                     type="object",
     *                     @OA\Property(property="user_id", type="integer", example=1),
     *                     @OA\Property(property="hierarchy_id", type="integer", example=1),
     *                     @OA\Property(property="role", type="string", example="manager"),
     *                     @OA\Property(property="position", type="integer", example=1),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="User not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getUserHierarchies() {}

    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/{hierarchy}/users",
     *     summary="Get hierarchy users",
     *     description="Retrieve all users that belong to a specific hierarchy",
     *     operationId="getHierarchyUsers",
     *     tags={"Hierarchy Relationships"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchy users retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="IT Department"),
     *             @OA\Property(property="description", type="string", example="Information Technology Department"),
     *             @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *             @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(
     *                 property="users",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John"),
     *                     @OA\Property(property="surname", type="string", example="Doe"),
     *                     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                     @OA\Property(property="hierarchy_role", type="string", example="manager"),
     *                     @OA\Property(property="position", type="integer", example=1)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getHierarchyUsers() {}

    /**
     * @OA\Get(
     *     path="/api/v1/users/{user}/hierarchy-paths",
     *     summary="Get user hierarchy paths",
     *     description="Retrieve all hierarchy paths for a user showing the complete organizational structure",
     *     operationId="getUserHierarchyPaths",
     *     tags={"Hierarchy Relationships"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="user",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User hierarchy paths retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="hierarchy_id", type="integer", example=1),
     *                 @OA\Property(property="hierarchy_name", type="string", example="IT Department"),
     *                 @OA\Property(property="user_role", type="string", example="manager"),
     *                 @OA\Property(property="user_position", type="integer", example=1),
     *                 @OA\Property(
     *                     property="path",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Beein Technologies"),
     *                         @OA\Property(property="level", type="integer", example=0)
     *                     ),
     *                     description="Complete hierarchy path from root to current level"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="User not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getUserHierarchyPaths() {}

    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/{hierarchy}/users/role/{role}",
     *     summary="Get users by role in hierarchy",
     *     description="Retrieve all users with a specific role in a hierarchy",
     *     operationId="getUsersByRole",
     *     tags={"Hierarchy Relationships"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="role",
     *         in="path",
     *         description="Role name",
     *         required=true,
     *         @OA\Schema(type="string", example="manager")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Users with specified role retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="John"),
     *                 @OA\Property(property="surname", type="string", example="Doe"),
     *                 @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                 @OA\Property(property="hierarchy_role", type="string", example="manager"),
     *                 @OA\Property(property="position", type="integer", example=1),
     *                 @OA\Property(property="hierarchy_id", type="integer", example=1),
     *                 @OA\Property(property="hierarchy_name", type="string", example="IT Department")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getUsersByRole() {}

    /**
     * @OA\Get(
     *     path="/api/v1/users/{user}/hierarchies/role/{role}",
     *     summary="Get hierarchies by role for user",
     *     description="Retrieve all hierarchies where a user has a specific role",
     *     operationId="getHierarchiesByRole",
     *     tags={"Hierarchy Relationships"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="user",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="role",
     *         in="path",
     *         description="Role name",
     *         required=true,
     *         @OA\Schema(type="string", example="manager")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchies with specified role retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="IT Department"),
     *                 @OA\Property(property="description", type="string", example="Information Technology Department"),
     *                 @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="pivot",
     *                     type="object",
     *                     @OA\Property(property="user_id", type="integer", example=1),
     *                     @OA\Property(property="hierarchy_id", type="integer", example=1),
     *                     @OA\Property(property="role", type="string", example="manager"),
     *                     @OA\Property(property="position", type="integer", example=1)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="User not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getHierarchiesByRole() {}
}
