<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Blacklist",
 *     description="API endpoints for blacklist management and operations"
 * )
 */

/**
 * @OA\Schema(
 *     schema="Blacklist",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Blacklist entry ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Person first name",
 *         example="<PERSON>"
 *     ),
 *     @OA\Property(
 *         property="surname",
 *         type="string",
 *         description="Person last name",
 *         example="Doe"
 *     ),
 *     @OA\Property(
 *         property="father_name",
 *         type="string",
 *         description="Person father's name",
 *         example="<PERSON>"
 *     ),
 *     @OA\Property(
 *         property="birthdate",
 *         type="string",
 *         format="date",
 *         description="Person birth date",
 *         example="1990-01-01"
 *     ),
 *     @OA\Property(
 *         property="document_number",
 *         type="string",
 *         description="Document number",
 *         example="AA123456"
 *     ),
 *     @OA\Property(
 *         property="pin",
 *         type="string",
 *         description="Personal identification number",
 *         example="1234567"
 *     ),
 *     @OA\Property(
 *         property="photo",
 *         type="string",
 *         description="Photo file path",
 *         example="photos/blacklist/john_doe.jpg"
 *     ),
 *     @OA\Property(
 *         property="note",
 *         type="string",
 *         description="Additional notes",
 *         example="Suspected of fraud"
 *     ),
 *     @OA\Property(
 *         property="gender",
 *         type="string",
 *         description="Person gender",
 *         enum={"male", "female"},
 *         example="male"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         description="Blacklist status (1=active, 0=inactive)",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Creation timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Last update timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="BlacklistRequest",
 *     type="object",
 *     required={"name", "surname", "pin"},
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Person first name",
 *         example="John"
 *     ),
 *     @OA\Property(
 *         property="surname",
 *         type="string",
 *         description="Person last name",
 *         example="Doe"
 *     ),
 *     @OA\Property(
 *         property="father_name",
 *         type="string",
 *         description="Person father's name",
 *         example="Michael"
 *     ),
 *     @OA\Property(
 *         property="birthdate",
 *         type="string",
 *         format="date",
 *         description="Person birth date",
 *         example="1990-01-01"
 *     ),
 *     @OA\Property(
 *         property="document_number",
 *         type="string",
 *         description="Document number",
 *         example="AA123456"
 *     ),
 *     @OA\Property(
 *         property="pin",
 *         type="string",
 *         description="Personal identification number",
 *         example="1234567"
 *     ),
 *     @OA\Property(
 *         property="photo",
 *         type="string",
 *         format="binary",
 *         description="Photo file upload"
 *     ),
 *     @OA\Property(
 *         property="note",
 *         type="string",
 *         description="Additional notes",
 *         example="Suspected of fraud"
 *     ),
 *     @OA\Property(
 *         property="gender",
 *         type="string",
 *         description="Person gender",
 *         enum={"male", "female"},
 *         example="male"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         description="Blacklist status (1=active, 0=inactive)",
 *         example=1
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="PaginatedBlacklistResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Blacklist entries retrieved successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/Blacklist")
 *     ),
 *     @OA\Property(
 *         property="links",
 *         type="object",
 *         @OA\Property(property="first", type="string", example="http://api.beein.az/api/v1/blacklist?page=1"),
 *         @OA\Property(property="last", type="string", example="http://api.beein.az/api/v1/blacklist?page=10"),
 *         @OA\Property(property="prev", type="string", example=null),
 *         @OA\Property(property="next", type="string", example="http://api.beein.az/api/v1/blacklist?page=2")
 *     ),
 *     @OA\Property(
 *         property="meta",
 *         type="object",
 *         @OA\Property(property="current_page", type="integer", example=1),
 *         @OA\Property(property="from", type="integer", example=1),
 *         @OA\Property(property="last_page", type="integer", example=10),
 *         @OA\Property(property="per_page", type="integer", example=15),
 *         @OA\Property(property="to", type="integer", example=15),
 *         @OA\Property(property="total", type="integer", example=150)
 *     )
 * )
 */

class BlacklistController
{
    /**
     * @OA\Get(
     *     path="/api/v1/blacklist",
     *     summary="Get list of blacklist entries",
     *     description="Retrieve a paginated list of all blacklist entries",
     *     operationId="getBlacklist",
     *     tags={"Blacklist"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term for name or surname",
     *         required=false,
     *         @OA\Schema(type="string", example="John")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by status",
     *         required=false,
     *         @OA\Schema(type="integer", enum={0, 1}, example=1)
     *     ),
     *     @OA\Parameter(
     *         name="gender",
     *         in="query",
     *         description="Filter by gender",
     *         required=false,
     *         @OA\Schema(type="string", enum={"male", "female"}, example="male")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blacklist entries retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PaginatedBlacklistResponse")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/blacklist/{id}",
     *     summary="Get blacklist entry by ID",
     *     description="Retrieve a specific blacklist entry by its ID",
     *     operationId="getBlacklistById",
     *     tags={"Blacklist"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Blacklist entry ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blacklist entry retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Blacklist entry retrieved successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Blacklist")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Blacklist entry not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Blacklist entry not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Post(
     *     path="/api/v1/blacklist",
     *     summary="Create a new blacklist entry",
     *     description="Create a new blacklist entry with optional photo upload",
     *     operationId="createBlacklistEntry",
     *     tags={"Blacklist"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Blacklist entry data",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 @OA\Property(property="name", type="string", example="John"),
     *                 @OA\Property(property="surname", type="string", example="Doe"),
     *                 @OA\Property(property="father_name", type="string", example="Michael"),
     *                 @OA\Property(property="birthdate", type="string", format="date", example="1990-01-01"),
     *                 @OA\Property(property="document_number", type="string", example="AA123456"),
     *                 @OA\Property(property="pin", type="string", example="1234567"),
     *                 @OA\Property(property="photo", type="string", format="binary", description="Photo file"),
     *                 @OA\Property(property="note", type="string", example="Suspected of fraud"),
     *                 @OA\Property(property="gender", type="string", enum={"male", "female"}, example="male"),
     *                 @OA\Property(property="status", type="integer", example=1)
     *             )
     *         ),
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(ref="#/components/schemas/BlacklistRequest")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Blacklist entry created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Blacklist entry created successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Blacklist")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="pin",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The pin field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Put(
     *     path="/api/v1/blacklist/{id}",
     *     summary="Update a blacklist entry",
     *     description="Update an existing blacklist entry",
     *     operationId="updateBlacklistEntry",
     *     tags={"Blacklist"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Blacklist entry ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Blacklist entry data",
     *         @OA\JsonContent(ref="#/components/schemas/BlacklistRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blacklist entry updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Blacklist entry updated successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Blacklist")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Blacklist entry not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Blacklist entry not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function update() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/blacklist/{id}",
     *     summary="Delete a blacklist entry",
     *     description="Delete an existing blacklist entry",
     *     operationId="deleteBlacklistEntry",
     *     tags={"Blacklist"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Blacklist entry ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blacklist entry deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Blacklist entry deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Blacklist entry not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Blacklist entry not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}
}
