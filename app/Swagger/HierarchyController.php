<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Hierarchies",
 *     description="API endpoints for organizational hierarchy management and user-hierarchy relationships"
 * )
 */

class HierarchyController
{
    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies",
     *     summary="Get list of hierarchies",
     *     description="Retrieve a list of all root hierarchies with their children",
     *     operationId="getHierarchies",
     *     tags={"Hierarchies"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchies retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beein Technologies"),
     *                 @OA\Property(property="description", type="string", example="Main organizational hierarchy"),
     *                 @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="parent",
     *                     type="object",
     *                     nullable=true,
     *                     example=null
     *                 ),
     *                 @OA\Property(
     *                     property="children_recursive",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=2),
     *                         @OA\Property(property="name", type="string", example="IT Department"),
     *                         @OA\Property(property="description", type="string", example="Information Technology Department"),
     *                         @OA\Property(property="parent_id", type="integer", example=1)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Post(
     *     path="/api/v1/hierarchies",
     *     summary="Create a new hierarchy",
     *     description="Create a new organizational hierarchy",
     *     operationId="createHierarchy",
     *     tags={"Hierarchies"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Hierarchy data",
     *         @OA\JsonContent(
     *             required={"name"},
     *             @OA\Property(property="name", type="string", example="IT Department"),
     *             @OA\Property(property="description", type="string", example="Information Technology Department"),
     *             @OA\Property(property="parent_id", type="integer", nullable=true, example=1, description="Parent hierarchy ID (null for root hierarchy)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Hierarchy created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=2),
     *             @OA\Property(property="name", type="string", example="IT Department"),
     *             @OA\Property(property="description", type="string", example="Information Technology Department"),
     *             @OA\Property(property="parent_id", type="integer", example=1),
     *             @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The name field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/{hierarchy}",
     *     summary="Get hierarchy by ID",
     *     description="Retrieve a specific hierarchy with its parent and children",
     *     operationId="getHierarchyById",
     *     tags={"Hierarchies"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchy retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="Beein Technologies"),
     *             @OA\Property(property="description", type="string", example="Main organizational hierarchy"),
     *             @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *             @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(
     *                 property="parent",
     *                 type="object",
     *                 nullable=true,
     *                 example=null
     *             ),
     *             @OA\Property(
     *                 property="children_recursive",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=2),
     *                     @OA\Property(property="name", type="string", example="IT Department"),
     *                     @OA\Property(property="description", type="string", example="Information Technology Department"),
     *                     @OA\Property(property="parent_id", type="integer", example=1)
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="users",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John"),
     *                     @OA\Property(property="surname", type="string", example="Doe"),
     *                     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                     @OA\Property(property="hierarchy_role", type="string", example="manager"),
     *                     @OA\Property(property="position", type="integer", example=1)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Put(
     *     path="/api/v1/hierarchies/{hierarchy}",
     *     summary="Update a hierarchy",
     *     description="Update an existing organizational hierarchy",
     *     operationId="updateHierarchy",
     *     tags={"Hierarchies"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Updated hierarchy data",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="IT Department - Updated"),
     *             @OA\Property(property="description", type="string", example="Updated Information Technology Department"),
     *             @OA\Property(property="parent_id", type="integer", nullable=true, example=1, description="Parent hierarchy ID")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchy updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="IT Department - Updated"),
     *             @OA\Property(property="description", type="string", example="Updated Information Technology Department"),
     *             @OA\Property(property="parent_id", type="integer", example=1),
     *             @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T11:30:00Z")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function update() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/hierarchies/{hierarchy}",
     *     summary="Delete a hierarchy",
     *     description="Delete an existing organizational hierarchy",
     *     operationId="deleteHierarchy",
     *     tags={"Hierarchies"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchy deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Hierarchy deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}

    /**
     * @OA\Post(
     *     path="/api/v1/users/{user}/hierarchies/{hierarchy}",
     *     summary="Assign user to hierarchy",
     *     description="Assign a user to a specific hierarchy with role and position",
     *     operationId="assignUserToHierarchy",
     *     tags={"Hierarchies"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="user",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=false,
     *         description="User hierarchy assignment data",
     *         @OA\JsonContent(
     *             @OA\Property(property="role", type="string", example="manager", description="User role in the hierarchy"),
     *             @OA\Property(property="position", type="integer", example=1, description="User position order in the hierarchy")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User assigned to hierarchy successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="User assigned to hierarchy successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User or hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="User or hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function assignUserToHierarchy() {}

    /**
     * @OA\Put(
     *     path="/api/v1/users/{user}/hierarchies/{hierarchy}",
     *     summary="Update user in hierarchy",
     *     description="Update a user's role and position in a specific hierarchy",
     *     operationId="updateUserInHierarchy",
     *     tags={"Hierarchies"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="user",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Updated user hierarchy data",
     *         @OA\JsonContent(
     *             @OA\Property(property="role", type="string", example="senior_manager", description="Updated user role in the hierarchy"),
     *             @OA\Property(property="position", type="integer", example=2, description="Updated user position order in the hierarchy")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User updated in hierarchy successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="User updated in hierarchy successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User or hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="User or hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function updateUserInHierarchy() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/users/{user}/hierarchies/{hierarchy}",
     *     summary="Remove user from hierarchy",
     *     description="Remove a user from a specific hierarchy",
     *     operationId="removeUserFromHierarchy",
     *     tags={"Hierarchies"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="user",
     *         in="path",
     *         description="User ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="User removed from hierarchy successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="User removed from hierarchy successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="User or hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="User or hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function removeUserFromHierarchy() {}
}
