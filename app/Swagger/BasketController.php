<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Baskets",
 *     description="API endpoints for basket management and saved collections"
 * )
 */

class BasketController
{
    /**
     * @OA\Get(
     *     path="/api/v1/baskets",
     *     summary="Get user baskets",
     *     description="Retrieve list of baskets for the authenticated user",
     *     operationId="getBaskets",
     *     tags={"Baskets"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="Filter by basket type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"persons", "searches", "alerts", "mixed"}, example="persons")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Baskets retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Baskets retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Suspicious Persons"),
     *                     @OA\Property(property="description", type="string", example="Collection of persons flagged for investigation"),
     *                     @OA\Property(property="type", type="string", enum={"persons", "searches", "alerts", "mixed"}, example="persons"),
     *                     @OA\Property(property="items_count", type="integer", example=15),
     *                     @OA\Property(property="is_shared", type="boolean", example=false),
     *                     @OA\Property(property="shared_with", type="array", @OA\Items(type="integer"), example={2, 3}, description="User IDs basket is shared with"),
     *                     @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"investigation", "priority"}, description="Basket tags"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="last_accessed", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=8),
     *                 @OA\Property(property="per_page", type="integer", example=15)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Post(
     *     path="/api/v1/baskets",
     *     summary="Create a new basket",
     *     description="Create a new basket for organizing items",
     *     operationId="createBasket",
     *     tags={"Baskets"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Basket data",
     *         @OA\JsonContent(
     *             required={"name", "type"},
     *             @OA\Property(property="name", type="string", example="Suspicious Persons"),
     *             @OA\Property(property="description", type="string", example="Collection of persons flagged for investigation"),
     *             @OA\Property(property="type", type="string", enum={"persons", "searches", "alerts", "mixed"}, example="persons"),
     *             @OA\Property(property="is_shared", type="boolean", example=false),
     *             @OA\Property(property="shared_with", type="array", @OA\Items(type="integer"), example={2, 3}, description="User IDs to share basket with"),
     *             @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"investigation", "priority"}, description="Basket tags")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Basket created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Basket created successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Suspicious Persons"),
     *                 @OA\Property(property="description", type="string", example="Collection of persons flagged for investigation"),
     *                 @OA\Property(property="type", type="string", example="persons"),
     *                 @OA\Property(property="items_count", type="integer", example=0),
     *                 @OA\Property(property="is_shared", type="boolean", example=false),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The name field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Get(
     *     path="/api/v1/baskets/{basket}",
     *     summary="Get basket by ID",
     *     description="Retrieve a specific basket with its items",
     *     operationId="getBasketById",
     *     tags={"Baskets"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="basket",
     *         in="path",
     *         description="Basket ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="include_items",
     *         in="query",
     *         description="Include basket items in response",
     *         required=false,
     *         @OA\Schema(type="boolean", example=true)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Basket retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Basket retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Suspicious Persons"),
     *                 @OA\Property(property="description", type="string", example="Collection of persons flagged for investigation"),
     *                 @OA\Property(property="type", type="string", example="persons"),
     *                 @OA\Property(property="items_count", type="integer", example=15),
     *                 @OA\Property(property="is_shared", type="boolean", example=false),
     *                 @OA\Property(property="shared_with", type="array", @OA\Items(type="integer"), example={2, 3}),
     *                 @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"investigation", "priority"}),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="items",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="item_type", type="string", example="person"),
     *                         @OA\Property(property="item_id", type="string", example="1234567"),
     *                         @OA\Property(property="item_data", type="object", description="Item-specific data"),
     *                         @OA\Property(property="notes", type="string", example="Person of interest in case #123"),
     *                         @OA\Property(property="added_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Basket not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Basket not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Put(
     *     path="/api/v1/baskets/{basket}",
     *     summary="Update a basket",
     *     description="Update an existing basket",
     *     operationId="updateBasket",
     *     tags={"Baskets"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="basket",
     *         in="path",
     *         description="Basket ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Updated basket data",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Updated Suspicious Persons"),
     *             @OA\Property(property="description", type="string", example="Updated description"),
     *             @OA\Property(property="is_shared", type="boolean", example=true),
     *             @OA\Property(property="shared_with", type="array", @OA\Items(type="integer"), example={2, 3, 4}),
     *             @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"investigation", "priority", "urgent"})
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Basket updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Basket updated successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Updated Suspicious Persons"),
     *                 @OA\Property(property="description", type="string", example="Updated description"),
     *                 @OA\Property(property="is_shared", type="boolean", example=true),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T11:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Basket not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Basket not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function update() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/baskets/{basket}",
     *     summary="Delete a basket",
     *     description="Delete an existing basket and all its items",
     *     operationId="deleteBasket",
     *     tags={"Baskets"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="basket",
     *         in="path",
     *         description="Basket ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Basket deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Basket deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Basket not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Basket not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}

    /**
     * @OA\Post(
     *     path="/api/v1/baskets/{basket}/items",
     *     summary="Add item to basket",
     *     description="Add an item to a specific basket",
     *     operationId="addItemToBasket",
     *     tags={"Baskets"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="basket",
     *         in="path",
     *         description="Basket ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Item data to add to basket",
     *         @OA\JsonContent(
     *             required={"item_type", "item_id"},
     *             @OA\Property(property="item_type", type="string", enum={"person", "search", "alert", "camera"}, example="person"),
     *             @OA\Property(property="item_id", type="string", example="1234567"),
     *             @OA\Property(property="item_data", type="object", description="Additional item data"),
     *             @OA\Property(property="notes", type="string", example="Person of interest in case #123")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Item added to basket successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Item added to basket successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="basket_id", type="integer", example=1),
     *                 @OA\Property(property="item_type", type="string", example="person"),
     *                 @OA\Property(property="item_id", type="string", example="1234567"),
     *                 @OA\Property(property="notes", type="string", example="Person of interest in case #123"),
     *                 @OA\Property(property="added_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Basket not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Basket not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=409,
     *         description="Item already exists in basket",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=409),
     *             @OA\Property(property="message", type="string", example="Item already exists in this basket")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function addItem() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/baskets/{basket}/items/{item}",
     *     summary="Remove item from basket",
     *     description="Remove a specific item from a basket",
     *     operationId="removeItemFromBasket",
     *     tags={"Baskets"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="basket",
     *         in="path",
     *         description="Basket ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="item",
     *         in="path",
     *         description="Basket Item ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Item removed from basket successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Item removed from basket successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Basket or item not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Basket or item not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function removeItem() {}
}
