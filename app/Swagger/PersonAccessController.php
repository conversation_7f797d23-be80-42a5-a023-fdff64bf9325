<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Person Access",
 *     description="API endpoints for person access control, gallery, and administrative functions"
 * )
 */

class PersonAccessController
{
    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/access",
     *     summary="Get tester access to profile",
     *     description="Check if the current user has access to view the person's profile",
     *     operationId="getTesterAccessToProfile",
     *     tags={"Person Access"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Access information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Access information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="has_access", type="boolean", example=true),
     *                 @OA\Property(property="access_level", type="string", example="full"),
     *                 @OA\Property(property="restrictions", type="array", @OA\Items(type="string"), example={}),
     *                 @OA\Property(property="expires_at", type="string", format="date-time", example="2024-12-31T23:59:59Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Access denied",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Access denied to this profile")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getTesterAccessToProfile() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/gallery",
     *     summary="Get person gallery",
     *     description="Retrieve photo gallery for a person using their PIN",
     *     operationId="getPersonGallery",
     *     tags={"Person Access"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Person gallery retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Person gallery retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="photo_id", type="string", example="PHOTO123456"),
     *                     @OA\Property(property="photo_url", type="string", example="https://api.beein.az/photos/person_1234567_001.jpg"),
     *                     @OA\Property(property="photo_type", type="string", example="profile"),
     *                     @OA\Property(property="upload_date", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="source", type="string", example="passport"),
     *                     @OA\Property(property="quality_score", type="number", format="float", example=0.95),
     *                     @OA\Property(property="face_detected", type="boolean", example=true)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person gallery not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Person gallery not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonGallery() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/get-face-video",
     *     summary="Get person face video data",
     *     description="Retrieve face video data for a person using their PIN",
     *     operationId="getPersonFaceVideo",
     *     tags={"Person Access"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Face video data retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Face video data retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="video_url", type="string", example="https://api.beein.az/videos/face_1234567.mp4"),
     *                 @OA\Property(property="video_duration", type="integer", example=30, description="Duration in seconds"),
     *                 @OA\Property(property="video_quality", type="string", example="HD"),
     *                 @OA\Property(property="face_detection_confidence", type="number", format="float", example=0.98),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="file_size", type="integer", example=2048576, description="File size in bytes")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Face video data not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Face video data not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonFaceVideo() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/job",
     *     summary="Get person job information",
     *     description="Retrieve detailed job information for a person using their PIN",
     *     operationId="getPersonJob",
     *     tags={"Person Access"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Job information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Job information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="company_name", type="string", example="Beein Technologies"),
     *                     @OA\Property(property="company_voen", type="string", example="1234567890"),
     *                     @OA\Property(property="position", type="string", example="Software Developer"),
     *                     @OA\Property(property="employment_type", type="string", example="full-time"),
     *                     @OA\Property(property="start_date", type="string", format="date", example="2020-01-15"),
     *                     @OA\Property(property="end_date", type="string", format="date", nullable=true, example=null),
     *                     @OA\Property(property="salary", type="number", format="float", example=2500.00),
     *                     @OA\Property(property="currency", type="string", example="AZN"),
     *                     @OA\Property(property="status", type="string", example="active")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Job information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Job information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonJob() {}

    /**
     * @OA\Post(
     *     path="/api/v1/person/{pin}/update",
     *     summary="Update person via IAMAS",
     *     description="Update person information via IAMAS system using their PIN",
     *     operationId="updatePersonViaIAMAS",
     *     tags={"Person Access"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Update data",
     *         @OA\JsonContent(
     *             @OA\Property(property="force_update", type="boolean", example=false, description="Force update even if recent data exists"),
     *             @OA\Property(property="update_fields", type="array", @OA\Items(type="string"), example={"personal_info", "documents", "addresses"}, description="Specific fields to update")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Person updated successfully via IAMAS",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Person updated successfully via IAMAS"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="updated_fields", type="array", @OA\Items(type="string"), example={"personal_info", "documents"}),
     *                 @OA\Property(property="last_update", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="iamas_response_code", type="string", example="SUCCESS")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person not found in IAMAS",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Person not found in IAMAS system")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="IAMAS update failed",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="IAMAS update failed"),
     *             @OA\Property(property="error_code", type="string", example="IAMAS_ERROR_001")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function updatePersonViaIAMAS() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/update-pin",
     *     summary="Update person PIN",
     *     description="Update or refresh person PIN information",
     *     operationId="updatePersonPin",
     *     tags={"Person Access"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Current person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="PIN updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="PIN updated successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="old_pin", type="string", example="1234567"),
     *                 @OA\Property(property="new_pin", type="string", example="7654321"),
     *                 @OA\Property(property="update_reason", type="string", example="Administrative update"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Person not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function updatePersonPin() {}
}
