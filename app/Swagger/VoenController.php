<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="VOEN",
 *     description="API endpoints for VOEN (company registration) management and operations"
 * )
 */

class VoenController
{
    /**
     * @OA\Get(
     *     path="/api/v1/voen",
     *     summary="Get list of VOEN records",
     *     description="Retrieve a paginated list of all VOEN (company) records with optional filtering",
     *     operationId="getVoenRecords",
     *     tags={"VOEN"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term for company name or VOEN",
     *         required=false,
     *         @OA\Schema(type="string", example="Beein")
     *     ),
     *     @OA\Parameter(
     *         name="voen",
     *         in="query",
     *         description="Filter by specific VOEN number",
     *         required=false,
     *         @OA\Schema(type="string", example="1234567890")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by company status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"active", "inactive", "suspended"}, example="active")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="VOEN records retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="VOEN records retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="voen", type="string", example="1234567890"),
     *                     @OA\Property(property="company_name", type="string", example="Beein Technologies LLC"),
     *                     @OA\Property(property="legal_form", type="string", example="LLC"),
     *                     @OA\Property(property="registration_date", type="string", format="date", example="2020-01-15"),
     *                     @OA\Property(property="address", type="string", example="Baku, Azerbaijan"),
     *                     @OA\Property(property="activity_type", type="string", example="Information Technology"),
     *                     @OA\Property(property="status", type="string", example="active"),
     *                     @OA\Property(property="authorized_capital", type="number", format="float", example=50000.00),
     *                     @OA\Property(property="created_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/voen/{voen}",
     *     summary="Get VOEN record by VOEN number",
     *     description="Retrieve a specific company record by its VOEN number",
     *     operationId="getVoenByNumber",
     *     tags={"VOEN"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="voen",
     *         in="path",
     *         description="VOEN number",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567890")
     *     ),
     *     @OA\Parameter(
     *         name="include",
     *         in="query",
     *         description="Include related data (directors,shareholders)",
     *         required=false,
     *         @OA\Schema(type="string", example="directors,shareholders")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="VOEN record retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="VOEN record retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="voen", type="string", example="1234567890"),
     *                 @OA\Property(property="company_name", type="string", example="Beein Technologies LLC"),
     *                 @OA\Property(property="legal_form", type="string", example="LLC"),
     *                 @OA\Property(property="registration_date", type="string", format="date", example="2020-01-15"),
     *                 @OA\Property(property="address", type="string", example="Baku, Azerbaijan"),
     *                 @OA\Property(property="activity_type", type="string", example="Information Technology"),
     *                 @OA\Property(property="status", type="string", example="active"),
     *                 @OA\Property(property="authorized_capital", type="number", format="float", example=50000.00),
     *                 @OA\Property(property="phone", type="string", example="994123456789"),
     *                 @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                 @OA\Property(property="website", type="string", example="https://beein.az"),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time"),
     *                 @OA\Property(
     *                     property="directors",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="John Doe"),
     *                         @OA\Property(property="position", type="string", example="CEO"),
     *                         @OA\Property(property="pin", type="string", example="1234567"),
     *                         @OA\Property(property="appointment_date", type="string", format="date", example="2020-01-15")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="shareholders",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Jane Smith"),
     *                         @OA\Property(property="pin", type="string", example="7654321"),
     *                         @OA\Property(property="share_percentage", type="number", format="float", example=51.0),
     *                         @OA\Property(property="share_amount", type="number", format="float", example=25500.00)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="VOEN record not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="VOEN record not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Post(
     *     path="/api/v1/voen/search",
     *     summary="Search VOEN records",
     *     description="Search for company records using advanced criteria",
     *     operationId="searchVoenRecords",
     *     tags={"VOEN"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="query", type="string", example="Beein Technologies"),
     *             @OA\Property(property="voen", type="string", example="1234567890"),
     *             @OA\Property(property="company_name", type="string", example="Beein"),
     *             @OA\Property(property="legal_form", type="string", example="LLC"),
     *             @OA\Property(property="activity_type", type="string", example="Information Technology"),
     *             @OA\Property(property="status", type="string", enum={"active", "inactive", "suspended"}, example="active"),
     *             @OA\Property(property="registration_date_from", type="string", format="date", example="2020-01-01"),
     *             @OA\Property(property="registration_date_to", type="string", format="date", example="2024-12-31"),
     *             @OA\Property(property="authorized_capital_min", type="number", format="float", example=10000.00),
     *             @OA\Property(property="authorized_capital_max", type="number", format="float", example=100000.00),
     *             @OA\Property(property="page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=20)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="VOEN search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="VOEN search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", example=15),
     *                 @OA\Property(property="search_time", type="number", format="float", example=0.45),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="voen", type="string", example="1234567890"),
     *                         @OA\Property(property="company_name", type="string", example="Beein Technologies LLC"),
     *                         @OA\Property(property="legal_form", type="string", example="LLC"),
     *                         @OA\Property(property="registration_date", type="string", format="date", example="2020-01-15"),
     *                         @OA\Property(property="status", type="string", example="active"),
     *                         @OA\Property(property="relevance_score", type="number", format="float", example=0.92)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function search() {}

    /**
     * @OA\Get(
     *     path="/api/v1/voen/{voen}/directors",
     *     summary="Get company directors",
     *     description="Retrieve all directors for a specific company by VOEN",
     *     operationId="getVoenDirectors",
     *     tags={"VOEN"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="voen",
     *         in="path",
     *         description="VOEN number",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567890")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Directors retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Directors retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John Doe"),
     *                     @OA\Property(property="position", type="string", example="CEO"),
     *                     @OA\Property(property="pin", type="string", example="1234567"),
     *                     @OA\Property(property="appointment_date", type="string", format="date", example="2020-01-15"),
     *                     @OA\Property(property="termination_date", type="string", format="date", nullable=true, example=null),
     *                     @OA\Property(property="is_active", type="boolean", example=true)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="VOEN record not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="VOEN record not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getDirectors() {}
}
