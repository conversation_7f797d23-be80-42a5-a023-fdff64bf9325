<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Surveillance",
 *     description="API endpoints for surveillance operations and monitoring"
 * )
 */

class SurveillanceController
{
    /**
     * @OA\Get(
     *     path="/api/v1/surveillance/live-feeds",
     *     summary="Get live surveillance feeds",
     *     description="Retrieve list of available live surveillance camera feeds",
     *     operationId="getLiveFeeds",
     *     tags={"Surveillance"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="location",
     *         in="query",
     *         description="Filter by location",
     *         required=false,
     *         @OA\Schema(type="string", example="Main Building")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by camera status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"online", "offline", "maintenance"}, example="online")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Live feeds retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Live feeds retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="camera_id", type="integer", example=1),
     *                     @OA\Property(property="camera_name", type="string", example="Main Entrance Camera"),
     *                     @OA\Property(property="location", type="string", example="Main Building Entrance"),
     *                     @OA\Property(property="stream_url", type="string", example="rtsp://camera1.beein.az:554/stream"),
     *                     @OA\Property(property="thumbnail_url", type="string", example="https://api.beein.az/thumbnails/camera_1.jpg"),
     *                     @OA\Property(property="status", type="string", enum={"online", "offline", "maintenance"}, example="online"),
     *                     @OA\Property(property="resolution", type="string", example="1920x1080"),
     *                     @OA\Property(property="fps", type="integer", example=30),
     *                     @OA\Property(property="coordinates", type="object",
     *                         @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                         @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *                     ),
     *                     @OA\Property(property="capabilities", type="array", @OA\Items(type="string"), example={"pan", "tilt", "zoom", "night_vision"}),
     *                     @OA\Property(property="last_activity", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="recording_status", type="string", enum={"recording", "not_recording", "scheduled"}, example="recording")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getLiveFeeds() {}

    /**
     * @OA\Get(
     *     path="/api/v1/surveillance/recordings",
     *     summary="Get surveillance recordings",
     *     description="Retrieve list of surveillance recordings with filtering options",
     *     operationId="getRecordings",
     *     tags={"Surveillance"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="camera_id",
     *         in="query",
     *         description="Filter by camera ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for recordings",
     *         required=false,
     *         @OA\Schema(type="string", format="date-time", example="2024-01-15T00:00:00Z")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for recordings",
     *         required=false,
     *         @OA\Schema(type="string", format="date-time", example="2024-01-15T23:59:59Z")
     *     ),
     *     @OA\Parameter(
     *         name="event_type",
     *         in="query",
     *         description="Filter by event type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"motion", "face_detection", "alert", "scheduled"}, example="motion")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Recordings retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Recordings retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="recording_id", type="string", example="REC123456789"),
     *                     @OA\Property(property="camera_id", type="integer", example=1),
     *                     @OA\Property(property="camera_name", type="string", example="Main Entrance Camera"),
     *                     @OA\Property(property="start_time", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="end_time", type="string", format="date-time", example="2024-01-15T10:35:00Z"),
     *                     @OA\Property(property="duration", type="integer", example=300, description="Duration in seconds"),
     *                     @OA\Property(property="file_size", type="integer", example=52428800, description="File size in bytes"),
     *                     @OA\Property(property="resolution", type="string", example="1920x1080"),
     *                     @OA\Property(property="event_type", type="string", example="motion"),
     *                     @OA\Property(property="thumbnail_url", type="string", example="https://api.beein.az/thumbnails/rec_123456789.jpg"),
     *                     @OA\Property(property="video_url", type="string", example="https://api.beein.az/recordings/rec_123456789.mp4"),
     *                     @OA\Property(property="download_url", type="string", example="https://api.beein.az/download/rec_123456789"),
     *                     @OA\Property(property="metadata", type="object",
     *                         @OA\Property(property="motion_detected", type="boolean", example=true),
     *                         @OA\Property(property="faces_detected", type="integer", example=2),
     *                         @OA\Property(property="objects_detected", type="array", @OA\Items(type="string"), example={"person", "vehicle"})
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getRecordings() {}

    /**
     * @OA\Post(
     *     path="/api/v1/surveillance/camera/{cameraId}/control",
     *     summary="Control surveillance camera",
     *     description="Send control commands to a surveillance camera (PTZ, recording, etc.)",
     *     operationId="controlCamera",
     *     tags={"Surveillance"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="cameraId",
     *         in="path",
     *         description="Camera ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Camera control commands",
     *         @OA\JsonContent(
     *             required={"command"},
     *             @OA\Property(property="command", type="string", enum={"pan", "tilt", "zoom", "preset", "start_recording", "stop_recording", "snapshot"}, example="pan"),
     *             @OA\Property(property="direction", type="string", enum={"left", "right", "up", "down", "in", "out"}, example="left", description="Direction for pan/tilt/zoom commands"),
     *             @OA\Property(property="speed", type="integer", example=5, description="Speed for movement commands (1-10)"),
     *             @OA\Property(property="preset_id", type="integer", example=1, description="Preset position ID"),
     *             @OA\Property(property="duration", type="integer", example=10, description="Duration for timed commands in seconds")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Camera command executed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Camera command executed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="camera_id", type="integer", example=1),
     *                 @OA\Property(property="command", type="string", example="pan"),
     *                 @OA\Property(property="status", type="string", example="completed"),
     *                 @OA\Property(property="execution_time", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="result", type="object", description="Command-specific result data")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Camera not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Camera not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function controlCamera() {}

    /**
     * @OA\Get(
     *     path="/api/v1/surveillance/events",
     *     summary="Get surveillance events",
     *     description="Retrieve surveillance events and alerts",
     *     operationId="getSurveillanceEvents",
     *     tags={"Surveillance"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="event_type",
     *         in="query",
     *         description="Filter by event type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"motion", "face_detection", "intrusion", "line_crossing", "object_detection"}, example="motion")
     *     ),
     *     @OA\Parameter(
     *         name="severity",
     *         in="query",
     *         description="Filter by event severity",
     *         required=false,
     *         @OA\Schema(type="string", enum={"low", "medium", "high", "critical"}, example="high")
     *     ),
     *     @OA\Parameter(
     *         name="camera_id",
     *         in="query",
     *         description="Filter by camera ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Surveillance events retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Surveillance events retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="event_id", type="string", example="EVT123456789"),
     *                     @OA\Property(property="event_type", type="string", example="motion"),
     *                     @OA\Property(property="severity", type="string", example="high"),
     *                     @OA\Property(property="camera_id", type="integer", example=1),
     *                     @OA\Property(property="camera_name", type="string", example="Main Entrance Camera"),
     *                     @OA\Property(property="location", type="string", example="Main Building Entrance"),
     *                     @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="description", type="string", example="Motion detected in restricted area"),
     *                     @OA\Property(property="confidence", type="number", format="float", example=0.92),
     *                     @OA\Property(property="image_url", type="string", example="https://api.beein.az/events/evt_123456789.jpg"),
     *                     @OA\Property(property="video_url", type="string", nullable=true, example="https://api.beein.az/events/evt_123456789.mp4"),
     *                     @OA\Property(property="status", type="string", enum={"new", "acknowledged", "resolved", "false_positive"}, example="new"),
     *                     @OA\Property(property="metadata", type="object", description="Event-specific metadata")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getSurveillanceEvents() {}
}
