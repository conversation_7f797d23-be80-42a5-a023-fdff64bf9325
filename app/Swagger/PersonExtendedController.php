<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Person Extended",
 *     description="Extended API endpoints for detailed person information and services"
 * )
 */

class PersonExtendedController
{
    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/customs",
     *     summary="Get person customs information",
     *     description="Retrieve customs information for a person using their PIN",
     *     operationId="getPersonCustoms",
     *     tags={"Person Extended"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Customs information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Customs information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="declaration_date", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="declaration_number", type="string", example="CUS123456789"),
     *                     @OA\Property(property="goods_description", type="string", example="Personal belongings"),
     *                     @OA\Property(property="declared_value", type="number", format="float", example=1500.00),
     *                     @OA\Property(property="customs_point", type="string", example="Heydar Aliyev Airport"),
     *                     @OA\Property(property="status", type="string", example="cleared")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Customs information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Customs information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonCustoms() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/vehicle",
     *     summary="Get person vehicle information",
     *     description="Retrieve vehicle information for a person using their PIN",
     *     operationId="getPersonVehicle",
     *     tags={"Person Extended"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Vehicle information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Vehicle information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="license_plate", type="string", example="10-AA-123"),
     *                     @OA\Property(property="make", type="string", example="Toyota"),
     *                     @OA\Property(property="model", type="string", example="Camry"),
     *                     @OA\Property(property="year", type="integer", example=2020),
     *                     @OA\Property(property="color", type="string", example="White"),
     *                     @OA\Property(property="registration_date", type="string", format="date", example="2020-01-15"),
     *                     @OA\Property(property="status", type="string", example="active")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Vehicle information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Vehicle information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonVehicle() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/military",
     *     summary="Get person military service information",
     *     description="Retrieve military service information for a person using their PIN",
     *     operationId="getPersonMilitary",
     *     tags={"Person Extended"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Military service information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Military service information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="service_status", type="string", example="completed"),
     *                 @OA\Property(property="service_start_date", type="string", format="date", example="2018-01-15"),
     *                 @OA\Property(property="service_end_date", type="string", format="date", example="2019-01-15"),
     *                 @OA\Property(property="military_unit", type="string", example="Unit 12345"),
     *                 @OA\Property(property="rank", type="string", example="Private"),
     *                 @OA\Property(property="exemption_reason", type="string", nullable=true, example=null)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Military service information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Military service information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonMilitary() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/mia/fines",
     *     summary="Get person MIA fines",
     *     description="Retrieve Ministry of Internal Affairs fines for a person using their PIN",
     *     operationId="getPersonMIAFines",
     *     tags={"Person Extended"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="MIA fines retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="MIA fines retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="fine_number", type="string", example="FINE123456"),
     *                     @OA\Property(property="violation_date", type="string", format="date", example="2024-01-15"),
     *                     @OA\Property(property="violation_type", type="string", example="Traffic violation"),
     *                     @OA\Property(property="fine_amount", type="number", format="float", example=50.00),
     *                     @OA\Property(property="payment_status", type="string", example="unpaid"),
     *                     @OA\Property(property="due_date", type="string", format="date", example="2024-02-15")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="MIA fines not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="MIA fines not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonMIAFines() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/mia/protocols",
     *     summary="Get person MIA protocols",
     *     description="Retrieve Ministry of Internal Affairs protocols for a person using their PIN",
     *     operationId="getPersonMIAProtocols",
     *     tags={"Person Extended"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="MIA protocols retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="MIA protocols retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="protocol_number", type="string", example="PROT123456"),
     *                     @OA\Property(property="protocol_date", type="string", format="date", example="2024-01-15"),
     *                     @OA\Property(property="violation_description", type="string", example="Administrative violation"),
     *                     @OA\Property(property="officer_name", type="string", example="Officer Smith"),
     *                     @OA\Property(property="location", type="string", example="Baku city center"),
     *                     @OA\Property(property="status", type="string", example="active")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="MIA protocols not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="MIA protocols not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonMIAProtocols() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/mia/driver-license",
     *     summary="Get person driver license information",
     *     description="Retrieve driver license information for a person using their PIN",
     *     operationId="getPersonDriverLicense",
     *     tags={"Person Extended"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Driver license information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Driver license information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="license_number", type="string", example="DL123456789"),
     *                 @OA\Property(property="issue_date", type="string", format="date", example="2020-01-15"),
     *                 @OA\Property(property="expiry_date", type="string", format="date", example="2030-01-15"),
     *                 @OA\Property(property="license_categories", type="array", @OA\Items(type="string"), example={"B", "C"}),
     *                 @OA\Property(property="status", type="string", example="active"),
     *                 @OA\Property(property="penalty_points", type="integer", example=0)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Driver license information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Driver license information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonDriverLicense() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/doctor",
     *     summary="Get person doctor information",
     *     description="Retrieve doctor/medical professional information for a person using their PIN",
     *     operationId="getPersonDoctor",
     *     tags={"Person Extended"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Doctor information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Doctor information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="medical_license", type="string", example="MD123456789"),
     *                 @OA\Property(property="specialization", type="string", example="Cardiology"),
     *                 @OA\Property(property="workplace", type="string", example="Baku Medical Center"),
     *                 @OA\Property(property="license_issue_date", type="string", format="date", example="2015-01-15"),
     *                 @OA\Property(property="license_expiry_date", type="string", format="date", example="2025-01-15"),
     *                 @OA\Property(property="status", type="string", example="active")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Doctor information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Doctor information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonDoctor() {}

    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/tqdk",
     *     summary="Get person TQDK information",
     *     description="Retrieve TQDK (State Examination Center) information for a person using their PIN",
     *     operationId="getPersonTQDK",
     *     tags={"Person Extended"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="TQDK information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="TQDK information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="exam_year", type="integer", example=2020),
     *                     @OA\Property(property="exam_type", type="string", example="University Entrance"),
     *                     @OA\Property(property="total_score", type="number", format="float", example=650.5),
     *                     @OA\Property(property="subject_scores", type="object", description="Scores by subject"),
     *                     @OA\Property(property="rank", type="integer", example=1250),
     *                     @OA\Property(property="exam_date", type="string", format="date", example="2020-06-15")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="TQDK information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="TQDK information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonTQDK() {}
}
