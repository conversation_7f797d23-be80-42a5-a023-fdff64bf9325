<?php

namespace App\Swagger;

/**
 * @OA\Schema(
 *     schema="User",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="User ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="User first name",
 *         example="<PERSON>"
 *     ),
 *     @OA\Property(
 *         property="surname",
 *         type="string",
 *         description="User last name",
 *         example="Doe"
 *     ),
 *     @OA\Property(
 *         property="email",
 *         type="string",
 *         format="email",
 *         description="User email address",
 *         example="<EMAIL>"
 *     ),
 *     @OA\Property(
 *         property="entity_name",
 *         type="string",
 *         description="Organization name",
 *         example="Beein Technologies"
 *     ),
 *     @OA\Property(
 *         property="phone_number",
 *         type="string",
 *         description="User phone number",
 *         example="994501234567"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         description="User status (1=active, 0=inactive)",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="is_system",
 *         type="boolean",
 *         description="Whether user is a system user",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="User creation timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="User last update timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="ErrorResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=400
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Bad Request"
 *     ),
 *     @OA\Property(
 *         property="errors",
 *         type="object",
 *         description="Validation errors (if applicable)",
 *         example={}
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="ValidationErrorResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=422
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Validation Error"
 *     ),
 *     @OA\Property(
 *         property="errors",
 *         type="object",
 *         description="Field-specific validation errors",
 *         @OA\Property(
 *             property="field_name",
 *             type="array",
 *             @OA\Items(type="string"),
 *             example={"The field is required."}
 *         )
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="SuccessResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Operation completed successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         description="Response data"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="PaginationMeta",
 *     type="object",
 *     @OA\Property(
 *         property="current_page",
 *         type="integer",
 *         description="Current page number",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="from",
 *         type="integer",
 *         description="First item number on current page",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="last_page",
 *         type="integer",
 *         description="Last page number",
 *         example=10
 *     ),
 *     @OA\Property(
 *         property="per_page",
 *         type="integer",
 *         description="Number of items per page",
 *         example=15
 *     ),
 *     @OA\Property(
 *         property="to",
 *         type="integer",
 *         description="Last item number on current page",
 *         example=15
 *     ),
 *     @OA\Property(
 *         property="total",
 *         type="integer",
 *         description="Total number of items",
 *         example=150
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="PaginationLinks",
 *     type="object",
 *     @OA\Property(
 *         property="first",
 *         type="string",
 *         description="URL to first page",
 *         example="http://api.beein.az/api/v1/endpoint?page=1"
 *     ),
 *     @OA\Property(
 *         property="last",
 *         type="string",
 *         description="URL to last page",
 *         example="http://api.beein.az/api/v1/endpoint?page=10"
 *     ),
 *     @OA\Property(
 *         property="prev",
 *         type="string",
 *         nullable=true,
 *         description="URL to previous page",
 *         example=null
 *     ),
 *     @OA\Property(
 *         property="next",
 *         type="string",
 *         nullable=true,
 *         description="URL to next page",
 *         example="http://api.beein.az/api/v1/endpoint?page=2"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="FileUploadResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="File uploaded successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         @OA\Property(
 *             property="file_path",
 *             type="string",
 *             description="Uploaded file path",
 *             example="uploads/photos/2024/01/15/photo_123.jpg"
 *         ),
 *         @OA\Property(
 *             property="file_name",
 *             type="string",
 *             description="Original file name",
 *             example="photo.jpg"
 *         ),
 *         @OA\Property(
 *             property="file_size",
 *             type="integer",
 *             description="File size in bytes",
 *             example=1024000
 *         ),
 *         @OA\Property(
 *             property="mime_type",
 *             type="string",
 *             description="File MIME type",
 *             example="image/jpeg"
 *         )
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="DateRange",
 *     type="object",
 *     @OA\Property(
 *         property="from",
 *         type="string",
 *         format="date",
 *         description="Start date",
 *         example="2024-01-01"
 *     ),
 *     @OA\Property(
 *         property="to",
 *         type="string",
 *         format="date",
 *         description="End date",
 *         example="2024-12-31"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="Coordinates",
 *     type="object",
 *     @OA\Property(
 *         property="lat",
 *         type="number",
 *         format="float",
 *         description="Latitude",
 *         example=40.4093
 *     ),
 *     @OA\Property(
 *         property="lng",
 *         type="number",
 *         format="float",
 *         description="Longitude",
 *         example=49.8671
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="AuditLog",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Log entry ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="user_id",
 *         type="integer",
 *         nullable=true,
 *         description="User who performed the action",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="action",
 *         type="string",
 *         description="Action performed",
 *         example="create"
 *     ),
 *     @OA\Property(
 *         property="model_type",
 *         type="string",
 *         description="Model that was affected",
 *         example="App\\Models\\Person"
 *     ),
 *     @OA\Property(
 *         property="model_id",
 *         type="integer",
 *         description="ID of the affected model",
 *         example=123
 *     ),
 *     @OA\Property(
 *         property="changes",
 *         type="object",
 *         description="Changes made to the model",
 *         example={"name": {"old": "John", "new": "Jane"}}
 *     ),
 *     @OA\Property(
 *         property="ip_address",
 *         type="string",
 *         description="IP address of the user",
 *         example="*************"
 *     ),
 *     @OA\Property(
 *         property="user_agent",
 *         type="string",
 *         description="User agent string",
 *         example="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="When the action was performed",
 *         example="2024-01-15T14:30:00Z"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="SystemInfo",
 *     type="object",
 *     @OA\Property(
 *         property="app_name",
 *         type="string",
 *         description="Application name",
 *         example="Beein API"
 *     ),
 *     @OA\Property(
 *         property="app_version",
 *         type="string",
 *         description="Application version",
 *         example="2.0.0"
 *     ),
 *     @OA\Property(
 *         property="php_version",
 *         type="string",
 *         description="PHP version",
 *         example="8.2.0"
 *     ),
 *     @OA\Property(
 *         property="laravel_version",
 *         type="string",
 *         description="Laravel framework version",
 *         example="10.0.0"
 *     ),
 *     @OA\Property(
 *         property="environment",
 *         type="string",
 *         description="Application environment",
 *         example="production"
 *     ),
 *     @OA\Property(
 *         property="timezone",
 *         type="string",
 *         description="Application timezone",
 *         example="Asia/Baku"
 *     ),
 *     @OA\Property(
 *         property="server_time",
 *         type="string",
 *         format="date-time",
 *         description="Current server time",
 *         example="2024-01-15T14:30:00Z"
 *     )
 * )
 */

class CommonSchemas
{
    // This class exists only to hold common schema definitions
    // No actual methods are needed as this is used for documentation only
}
