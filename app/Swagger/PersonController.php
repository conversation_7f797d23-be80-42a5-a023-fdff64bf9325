<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Persons",
 *     description="API endpoints for person management and operations"
 * )
 */

/**
 * @OA\Schema(
 *     schema="Person",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Person ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Person first name",
 *         example="John"
 *     ),
 *     @OA\Property(
 *         property="surname",
 *         type="string",
 *         description="Person last name",
 *         example="Doe"
 *     ),
 *     @OA\Property(
 *         property="father_name",
 *         type="string",
 *         description="Person father's name",
 *         example="<PERSON>"
 *     ),
 *     @OA\Property(
 *         property="pin",
 *         type="string",
 *         description="Personal identification number",
 *         example="1234567"
 *     ),
 *     @OA\Property(
 *         property="doc_type",
 *         type="string",
 *         description="Document type",
 *         example="passport"
 *     ),
 *     @OA\Property(
 *         property="doc_serial_number",
 *         type="string",
 *         description="Document serial number",
 *         example="AA123456"
 *     ),
 *     @OA\Property(
 *         property="doc_number",
 *         type="string",
 *         description="Document number",
 *         example="*********"
 *     ),
 *     @OA\Property(
 *         property="is_sync",
 *         type="boolean",
 *         description="Whether person data is synchronized",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         description="Additional person data",
 *         example={"additional_info": "value"}
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Creation timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Last update timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="PersonRequest",
 *     type="object",
 *     required={"name", "surname", "pin"},
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Person first name",
 *         example="John"
 *     ),
 *     @OA\Property(
 *         property="surname",
 *         type="string",
 *         description="Person last name",
 *         example="Doe"
 *     ),
 *     @OA\Property(
 *         property="father_name",
 *         type="string",
 *         description="Person father's name",
 *         example="Michael"
 *     ),
 *     @OA\Property(
 *         property="pin",
 *         type="string",
 *         description="Personal identification number",
 *         example="1234567"
 *     ),
 *     @OA\Property(
 *         property="doc_type",
 *         type="string",
 *         description="Document type",
 *         example="passport"
 *     ),
 *     @OA\Property(
 *         property="doc_serial_number",
 *         type="string",
 *         description="Document serial number",
 *         example="AA123456"
 *     ),
 *     @OA\Property(
 *         property="doc_number",
 *         type="string",
 *         description="Document number",
 *         example="*********"
 *     ),
 *     @OA\Property(
 *         property="is_sync",
 *         type="boolean",
 *         description="Whether person data is synchronized",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         description="Additional person data",
 *         example={"additional_info": "value"}
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="PaginatedPersonResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Persons retrieved successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/Person")
 *     ),
 *     @OA\Property(
 *         property="links",
 *         type="object",
 *         @OA\Property(property="first", type="string", example="http://api.beein.az/api/v1/persons?page=1"),
 *         @OA\Property(property="last", type="string", example="http://api.beein.az/api/v1/persons?page=10"),
 *         @OA\Property(property="prev", type="string", example=null),
 *         @OA\Property(property="next", type="string", example="http://api.beein.az/api/v1/persons?page=2")
 *     ),
 *     @OA\Property(
 *         property="meta",
 *         type="object",
 *         @OA\Property(property="current_page", type="integer", example=1),
 *         @OA\Property(property="from", type="integer", example=1),
 *         @OA\Property(property="last_page", type="integer", example=10),
 *         @OA\Property(property="per_page", type="integer", example=15),
 *         @OA\Property(property="to", type="integer", example=15),
 *         @OA\Property(property="total", type="integer", example=150)
 *     )
 * )
 */

class PersonController
{
    /**
     * @OA\Get(
     *     path="/api/v1/persons",
     *     summary="Get list of persons",
     *     description="Retrieve a paginated list of all persons",
     *     operationId="getPersons",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term for name or surname",
     *         required=false,
     *         @OA\Schema(type="string", example="John")
     *     ),
     *     @OA\Parameter(
     *         name="is_sync",
     *         in="query",
     *         description="Filter by sync status",
     *         required=false,
     *         @OA\Schema(type="boolean", example=true)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Persons retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PaginatedPersonResponse")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/persons/{id}",
     *     summary="Get person by ID",
     *     description="Retrieve a specific person by their ID",
     *     operationId="getPersonById",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Person ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Person retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Person retrieved successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Person")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Person not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Post(
     *     path="/api/v1/persons",
     *     summary="Create a new person",
     *     description="Create a new person record",
     *     operationId="createPerson",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Person data",
     *         @OA\JsonContent(ref="#/components/schemas/PersonRequest")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Person created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Person created successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Person")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="pin",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The pin field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Put(
     *     path="/api/v1/persons/{id}",
     *     summary="Update a person",
     *     description="Update an existing person record",
     *     operationId="updatePerson",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Person ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Person data",
     *         @OA\JsonContent(ref="#/components/schemas/PersonRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Person updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Person updated successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Person")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Person not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function update() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/persons/{id}",
     *     summary="Delete a person",
     *     description="Delete an existing person record",
     *     operationId="deletePerson",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Person ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Person deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Person deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Person not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}

    /**
     * @OA\Get(
     *     path="/api/v1/jobX/{pin}",
     *     summary="Get person job information by PIN",
     *     description="Retrieve job information for a person using their PIN",
     *     operationId="getPersonJobByPin",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Person job information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Job information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="pin", type="string", example="1234567"),
     *                 @OA\Property(property="job_title", type="string", example="Software Engineer"),
     *                 @OA\Property(property="company", type="string", example="Beein Technologies"),
     *                 @OA\Property(property="employment_status", type="string", example="active")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Person not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getJobByPin() {}
}
