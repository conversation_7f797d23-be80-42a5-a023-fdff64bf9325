<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Persons",
 *     description="API endpoints for person management and search operations"
 * )
 */

class PersonController
{
    /**
     * @OA\Get(
     *     path="/api/v1/persons",
     *     summary="Get list of persons",
     *     description="Retrieve a paginated list of all persons with optional filtering",
     *     operationId="getPersons",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term for name or PIN",
     *         required=false,
     *         @OA\Schema(type="string", example="John")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Persons retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Persons retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John"),
     *                     @OA\Property(property="surname", type="string", example="Doe"),
     *                     @OA\Property(property="father_name", type="string", example="Michael"),
     *                     @OA\Property(property="pin", type="string", example="1234567"),
     *                     @OA\Property(property="doc_type", type="string", example="passport"),
     *                     @OA\Property(property="doc_number", type="string", example="AA123456"),
     *                     @OA\Property(property="is_sync", type="boolean", example=true),
     *                     @OA\Property(property="created_at", type="string", format="date-time")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/persons/{id}",
     *     summary="Get person by ID",
     *     description="Retrieve a specific person by their ID",
     *     operationId="getPersonById",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Person ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Person retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Person retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="John"),
     *                 @OA\Property(property="surname", type="string", example="Doe"),
     *                 @OA\Property(property="father_name", type="string", example="Michael"),
     *                 @OA\Property(property="pin", type="string", example="1234567"),
     *                 @OA\Property(property="doc_type", type="string", example="passport"),
     *                 @OA\Property(property="doc_number", type="string", example="AA123456"),
     *                 @OA\Property(property="is_sync", type="boolean", example=true),
     *                 @OA\Property(property="data", type="object", description="Additional person data"),
     *                 @OA\Property(property="created_at", type="string", format="date-time"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Person not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Get(
     *     path="/api/jobX/{pin}",
     *     summary="Get person job information by PIN",
     *     description="Retrieve job information for a person using their PIN",
     *     operationId="getPersonJobByPin",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Job information retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Job information retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="pin", type="string", example="1234567"),
     *                 @OA\Property(property="company_name", type="string", example="Beein Technologies"),
     *                 @OA\Property(property="position", type="string", example="Software Developer"),
     *                 @OA\Property(property="start_date", type="string", format="date", example="2020-01-15"),
     *                 @OA\Property(property="salary", type="number", format="float", example=2500.00),
     *                 @OA\Property(property="status", type="string", example="active")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Job information not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Job information not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPersonJobByPin() {}

    /**
     * @OA\Post(
     *     path="/api/v1/persons",
     *     summary="Create a new person",
     *     description="Create a new person record",
     *     operationId="createPerson",
     *     tags={"Persons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Person data",
     *         @OA\JsonContent(
     *             required={"name", "surname", "pin"},
     *             @OA\Property(property="name", type="string", example="John"),
     *             @OA\Property(property="surname", type="string", example="Doe"),
     *             @OA\Property(property="father_name", type="string", example="Michael"),
     *             @OA\Property(property="pin", type="string", example="1234567"),
     *             @OA\Property(property="doc_type", type="string", example="passport"),
     *             @OA\Property(property="doc_serial_number", type="string", example="AA"),
     *             @OA\Property(property="doc_number", type="string", example="123456"),
     *             @OA\Property(property="is_sync", type="boolean", example=true),
     *             @OA\Property(property="data", type="object", description="Additional person data")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Person created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Person created successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="John"),
     *                 @OA\Property(property="surname", type="string", example="Doe"),
     *                 @OA\Property(property="pin", type="string", example="1234567")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="pin",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The pin field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}
}
