<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Foreign Citizens",
 *     description="API endpoints for foreign citizen records and immigration data"
 * )
 */

class ForeignCitizenController
{
    /**
     * @OA\Get(
     *     path="/api/v1/foreign-citizens",
     *     summary="Get foreign citizen records",
     *     description="Retrieve list of foreign citizen records with filtering and pagination",
     *     operationId="getForeignCitizens",
     *     tags={"Foreign Citizens"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=50)
     *     ),
     *     @OA\Parameter(
     *         name="nationality",
     *         in="query",
     *         description="Filter by nationality",
     *         required=false,
     *         @OA\Schema(type="string", example="Turkish")
     *     ),
     *     @OA\Parameter(
     *         name="visa_type",
     *         in="query",
     *         description="Filter by visa type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"tourist", "business", "work", "student", "transit", "diplomatic"}, example="business")
     *     ),
     *     @OA\Parameter(
     *         name="entry_purpose",
     *         in="query",
     *         description="Filter by entry purpose",
     *         required=false,
     *         @OA\Schema(type="string", example="business")
     *     ),
     *     @OA\Parameter(
     *         name="entry_date_from",
     *         in="query",
     *         description="Filter by entry date from",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="entry_date_to",
     *         in="query",
     *         description="Filter by entry date to",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-31")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by current status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"in_country", "departed", "overstayed", "deported"}, example="in_country")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Foreign citizen records retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Foreign citizen records retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="passport_number", type="string", example="A12345678"),
     *                     @OA\Property(property="full_name", type="string", example="John Smith"),
     *                     @OA\Property(property="nationality", type="string", example="Turkish"),
     *                     @OA\Property(property="date_of_birth", type="string", format="date", example="1985-03-15"),
     *                     @OA\Property(property="gender", type="string", enum={"male", "female"}, example="male"),
     *                     @OA\Property(property="visa_number", type="string", example="V123456789"),
     *                     @OA\Property(property="visa_type", type="string", example="business"),
     *                     @OA\Property(property="entry_date", type="string", format="date", example="2024-01-15"),
     *                     @OA\Property(property="exit_date", type="string", format="date", nullable=true, example=null),
     *                     @OA\Property(property="visa_expiry", type="string", format="date", example="2024-07-15"),
     *                     @OA\Property(property="entry_point", type="string", example="Heydar Aliyev International Airport"),
     *                     @OA\Property(property="entry_purpose", type="string", example="Business meetings"),
     *                     @OA\Property(property="accommodation_address", type="string", example="Baku, Nizami Street 123"),
     *                     @OA\Property(property="sponsor_info", type="string", nullable=true, example="Beein Technologies MMC"),
     *                     @OA\Property(property="current_status", type="string", example="in_country"),
     *                     @OA\Property(property="days_in_country", type="integer", example=45),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=2500),
     *                 @OA\Property(property="per_page", type="integer", example=50)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to access foreign citizen data")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/foreign-citizens/{foreignCitizen}",
     *     summary="Get foreign citizen by ID",
     *     description="Retrieve detailed information about a specific foreign citizen",
     *     operationId="getForeignCitizenById",
     *     tags={"Foreign Citizens"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="foreignCitizen",
     *         in="path",
     *         description="Foreign Citizen ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Foreign citizen retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Foreign citizen retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="passport_number", type="string", example="A12345678"),
     *                 @OA\Property(property="full_name", type="string", example="John Smith"),
     *                 @OA\Property(property="nationality", type="string", example="Turkish"),
     *                 @OA\Property(property="date_of_birth", type="string", format="date", example="1985-03-15"),
     *                 @OA\Property(property="place_of_birth", type="string", example="Istanbul, Turkey"),
     *                 @OA\Property(property="gender", type="string", example="male"),
     *                 @OA\Property(property="passport_issue_date", type="string", format="date", example="2020-01-15"),
     *                 @OA\Property(property="passport_expiry_date", type="string", format="date", example="2030-01-15"),
     *                 @OA\Property(property="passport_issuing_authority", type="string", example="Turkish Ministry of Interior"),
     *                 @OA\Property(property="visa_number", type="string", example="V123456789"),
     *                 @OA\Property(property="visa_type", type="string", example="business"),
     *                 @OA\Property(property="visa_issue_date", type="string", format="date", example="2024-01-01"),
     *                 @OA\Property(property="visa_expiry", type="string", format="date", example="2024-07-15"),
     *                 @OA\Property(property="visa_issuing_office", type="string", example="Azerbaijan Embassy in Ankara"),
     *                 @OA\Property(property="entry_date", type="string", format="date", example="2024-01-15"),
     *                 @OA\Property(property="exit_date", type="string", format="date", nullable=true, example=null),
     *                 @OA\Property(property="entry_point", type="string", example="Heydar Aliyev International Airport"),
     *                 @OA\Property(property="exit_point", type="string", nullable=true, example=null),
     *                 @OA\Property(property="entry_purpose", type="string", example="Business meetings"),
     *                 @OA\Property(property="accommodation_address", type="string", example="Baku, Nizami Street 123"),
     *                 @OA\Property(property="accommodation_type", type="string", example="hotel"),
     *                 @OA\Property(property="sponsor_info", type="object",
     *                     @OA\Property(property="name", type="string", example="Beein Technologies MMC"),
     *                     @OA\Property(property="voen", type="string", example="1234567890"),
     *                     @OA\Property(property="contact_person", type="string", example="Əli Məmmədov"),
     *                     @OA\Property(property="phone", type="string", example="+994501234567")
     *                 ),
     *                 @OA\Property(property="contact_info", type="object",
     *                     @OA\Property(property="phone", type="string", example="+905551234567"),
     *                     @OA\Property(property="email", type="string", example="<EMAIL>"),
     *                     @OA\Property(property="emergency_contact", type="string", example="+905559876543")
     *                 ),
     *                 @OA\Property(property="current_status", type="string", example="in_country"),
     *                 @OA\Property(property="days_in_country", type="integer", example=45),
     *                 @OA\Property(property="allowed_stay_days", type="integer", example=90),
     *                 @OA\Property(property="remaining_days", type="integer", example=45),
     *                 @OA\Property(
     *                     property="entry_exit_history",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="entry_date", type="string", format="date", example="2024-01-15"),
     *                         @OA\Property(property="exit_date", type="string", format="date", nullable=true, example=null),
     *                         @OA\Property(property="entry_point", type="string", example="Heydar Aliyev International Airport"),
     *                         @OA\Property(property="exit_point", type="string", nullable=true, example=null),
     *                         @OA\Property(property="purpose", type="string", example="Business meetings"),
     *                         @OA\Property(property="duration_days", type="integer", nullable=true, example=null)
     *                     )
     *                 ),
     *                 @OA\Property(property="risk_assessment", type="object",
     *                     @OA\Property(property="risk_level", type="string", enum={"low", "medium", "high"}, example="low"),
     *                     @OA\Property(property="risk_factors", type="array", @OA\Items(type="string"), example={}),
     *                     @OA\Property(property="watch_list_status", type="boolean", example=false)
     *                 ),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Foreign citizen not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Foreign citizen not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Post(
     *     path="/api/v1/foreign-citizens/search",
     *     summary="Search foreign citizens",
     *     description="Search foreign citizens using advanced criteria",
     *     operationId="searchForeignCitizens",
     *     tags={"Foreign Citizens"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="passport_number", type="string", example="A12345678"),
     *             @OA\Property(property="full_name", type="string", example="John Smith"),
     *             @OA\Property(property="nationality", type="string", example="Turkish"),
     *             @OA\Property(property="visa_number", type="string", example="V123456789"),
     *             @OA\Property(property="date_of_birth", type="string", format="date", example="1985-03-15"),
     *             @OA\Property(property="entry_date_from", type="string", format="date", example="2024-01-01"),
     *             @OA\Property(property="entry_date_to", type="string", format="date", example="2024-01-31"),
     *             @OA\Property(property="visa_type", type="string", example="business"),
     *             @OA\Property(property="current_status", type="string", example="in_country"),
     *             @OA\Property(property="sponsor_name", type="string", example="Beein Technologies"),
     *             @OA\Property(property="accommodation_address", type="string", example="Baku"),
     *             @OA\Property(property="page", type="integer", example=1),
     *             @OA\Property(property="per_page", type="integer", example=20)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total_found", type="integer", example=25),
     *                 @OA\Property(property="search_time", type="number", format="float", example=0.35),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="passport_number", type="string", example="A12345678"),
     *                         @OA\Property(property="full_name", type="string", example="John Smith"),
     *                         @OA\Property(property="nationality", type="string", example="Turkish"),
     *                         @OA\Property(property="visa_type", type="string", example="business"),
     *                         @OA\Property(property="entry_date", type="string", format="date", example="2024-01-15"),
     *                         @OA\Property(property="current_status", type="string", example="in_country"),
     *                         @OA\Property(property="relevance_score", type="number", format="float", example=0.95)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function search() {}

    /**
     * @OA\Get(
     *     path="/api/v1/foreign-citizens/analytics",
     *     summary="Get foreign citizen analytics",
     *     description="Retrieve analytics and statistics about foreign citizens",
     *     operationId="getForeignCitizenAnalytics",
     *     tags={"Foreign Citizens"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="period",
     *         in="query",
     *         description="Analysis period",
     *         required=false,
     *         @OA\Schema(type="string", enum={"day", "week", "month", "quarter", "year"}, example="month")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for analytics",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for analytics",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-31")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Analytics retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Analytics retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="period", type="string", example="2024-01-01 to 2024-01-31"),
     *                 @OA\Property(property="total_entries", type="integer", example=1250),
     *                 @OA\Property(property="total_exits", type="integer", example=980),
     *                 @OA\Property(property="currently_in_country", type="integer", example=8500),
     *                 @OA\Property(property="overstayed_visas", type="integer", example=45),
     *                 @OA\Property(
     *                     property="by_nationality",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="nationality", type="string", example="Turkish"),
     *                         @OA\Property(property="count", type="integer", example=450),
     *                         @OA\Property(property="percentage", type="number", format="float", example=36.0)
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="by_visa_type",
     *                     type="object",
     *                     @OA\Property(property="tourist", type="integer", example=650),
     *                     @OA\Property(property="business", type="integer", example=350),
     *                     @OA\Property(property="work", type="integer", example=150),
     *                     @OA\Property(property="student", type="integer", example=75),
     *                     @OA\Property(property="transit", type="integer", example=25)
     *                 ),
     *                 @OA\Property(
     *                     property="by_entry_point",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="entry_point", type="string", example="Heydar Aliyev International Airport"),
     *                         @OA\Property(property="count", type="integer", example=850),
     *                         @OA\Property(property="percentage", type="number", format="float", example=68.0)
     *                     )
     *                 ),
     *                 @OA\Property(property="average_stay_duration", type="number", format="float", example=12.5, description="Average stay duration in days"),
     *                 @OA\Property(
     *                     property="monthly_trends",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="month", type="string", example="2024-01"),
     *                         @OA\Property(property="entries", type="integer", example=1250),
     *                         @OA\Property(property="exits", type="integer", example=980)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getAnalytics() {}
}
