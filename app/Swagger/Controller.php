<?php

namespace App\Swagger;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;

/**
 * @OA\Info(
 *      version="2.0.0",
 *      title="Beein API",
 *      description="Comprehensive API documentation for Beein surveillance and analytics platform. This API provides access to person management, blacklist operations, camera monitoring, search functionality, and various data analytics services.",
 *      termsOfService="https://beein.az/terms",
 *      @OA\Contact(
 *          name="Beein API Support",
 *          email="<EMAIL>",
 *          url="https://beein.az/support"
 *      ),
 *      @OA\License(
 *          name="Apache 2.0",
 *          url="https://www.apache.org/licenses/LICENSE-2.0.html"
 *      )
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="JWT Authorization header using the Bearer scheme. Example: 'Authorization: Bearer {token}'"
 * )
 *
 * @OA\Server(
 *      url=L5_SWAGGER_CONST_HOST,
 *      description="Production API Server"
 * )
 *
 * @OA\Server(
 *      url="http://localhost:8000",
 *      description="Local Development Server"
 * )
 *
 * @OA\Server(
 *      url="https://staging-api.beein.az",
 *      description="Staging API Server"
 * )
 *
 */
class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;
}
