<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Elasticsearch",
 *     description="API endpoints for Elasticsearch operations and advanced search functionality"
 * )
 */

/**
 * @OA\Schema(
 *     schema="ElasticSearchRequest",
 *     type="object",
 *     @OA\Property(
 *         property="query",
 *         type="string",
 *         description="Search query string",
 *         example="John Doe"
 *     ),
 *     @OA\Property(
 *         property="size",
 *         type="integer",
 *         minimum=1,
 *         maximum=1000,
 *         description="Number of results to return",
 *         example=10
 *     ),
 *     @OA\Property(
 *         property="from",
 *         type="integer",
 *         minimum=0,
 *         description="Starting offset for pagination",
 *         example=0
 *     ),
 *     @OA\Property(
 *         property="sort",
 *         type="object",
 *         description="Sort criteria",
 *         example={"created_at": {"order": "desc"}}
 *     ),
 *     @OA\Property(
 *         property="filters",
 *         type="object",
 *         description="Additional filters",
 *         @OA\Property(property="gender", type="string", example="male"),
 *         @OA\Property(property="age_range", type="object", 
 *             @OA\Property(property="min", type="integer", example=18),
 *             @OA\Property(property="max", type="integer", example=65)
 *         ),
 *         @OA\Property(property="date_range", type="object",
 *             @OA\Property(property="from", type="string", format="date", example="2024-01-01"),
 *             @OA\Property(property="to", type="string", format="date", example="2024-12-31")
 *         )
 *     ),
 *     @OA\Property(
 *         property="aggregations",
 *         type="object",
 *         description="Aggregation queries",
 *         example={"by_gender": {"terms": {"field": "gender"}}}
 *     ),
 *     @OA\Property(
 *         property="scroll",
 *         type="boolean",
 *         description="Enable scroll for large result sets",
 *         example=false
 *     ),
 *     @OA\Property(
 *         property="scroll_size",
 *         type="integer",
 *         description="Scroll batch size",
 *         example=1000
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="ElasticSearchResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Search completed successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         @OA\Property(
 *             property="total",
 *             type="integer",
 *             description="Total number of results",
 *             example=150
 *         ),
 *         @OA\Property(
 *             property="max_score",
 *             type="number",
 *             format="float",
 *             description="Maximum relevance score",
 *             example=1.5
 *         ),
 *         @OA\Property(
 *             property="results",
 *             type="array",
 *             description="Search results",
 *             @OA\Items(
 *                 type="object",
 *                 @OA\Property(property="_score", type="number", format="float", example=1.2),
 *                 @OA\Property(property="_source", type="object", description="Document source data")
 *             )
 *         ),
 *         @OA\Property(
 *             property="aggregations",
 *             type="object",
 *             description="Aggregation results",
 *             example={"by_gender": {"buckets": [{"key": "male", "doc_count": 75}, {"key": "female", "doc_count": 75}]}}
 *         ),
 *         @OA\Property(
 *             property="scroll_id",
 *             type="string",
 *             description="Scroll ID for pagination (if scroll enabled)",
 *             example="DXF1ZXJ5QW5kRmV0Y2g..."
 *         )
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="BorderCrossingSearchRequest",
 *     type="object",
 *     @OA\Property(
 *         property="pin",
 *         type="string",
 *         description="Person PIN",
 *         example="1234567"
 *     ),
 *     @OA\Property(
 *         property="date_from",
 *         type="string",
 *         format="date",
 *         description="Start date for search",
 *         example="2024-01-01"
 *     ),
 *     @OA\Property(
 *         property="date_to",
 *         type="string",
 *         format="date",
 *         description="End date for search",
 *         example="2024-12-31"
 *     ),
 *     @OA\Property(
 *         property="border_point",
 *         type="string",
 *         description="Border crossing point",
 *         example="Heydar Aliyev International Airport"
 *     ),
 *     @OA\Property(
 *         property="direction",
 *         type="string",
 *         enum={"entry", "exit"},
 *         description="Crossing direction",
 *         example="entry"
 *     ),
 *     @OA\Property(
 *         property="size",
 *         type="integer",
 *         minimum=1,
 *         maximum=1000,
 *         description="Number of results",
 *         example=50
 *     ),
 *     @OA\Property(
 *         property="from",
 *         type="integer",
 *         minimum=0,
 *         description="Starting offset",
 *         example=0
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="VoenSearchRequest",
 *     type="object",
 *     @OA\Property(
 *         property="voen",
 *         type="string",
 *         description="VOEN number",
 *         example="1234567890"
 *     ),
 *     @OA\Property(
 *         property="company_name",
 *         type="string",
 *         description="Company name",
 *         example="Beein Technologies"
 *     ),
 *     @OA\Property(
 *         property="registration_date_from",
 *         type="string",
 *         format="date",
 *         description="Registration date from",
 *         example="2020-01-01"
 *     ),
 *     @OA\Property(
 *         property="registration_date_to",
 *         type="string",
 *         format="date",
 *         description="Registration date to",
 *         example="2024-12-31"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         enum={"active", "inactive", "suspended"},
 *         description="Company status",
 *         example="active"
 *     ),
 *     @OA\Property(
 *         property="size",
 *         type="integer",
 *         minimum=1,
 *         maximum=1000,
 *         description="Number of results",
 *         example=20
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="SocialAccountSearchRequest",
 *     type="object",
 *     @OA\Property(
 *         property="username",
 *         type="string",
 *         description="Social media username",
 *         example="john_doe"
 *     ),
 *     @OA\Property(
 *         property="platform",
 *         type="string",
 *         enum={"facebook", "instagram", "twitter", "linkedin", "telegram"},
 *         description="Social media platform",
 *         example="instagram"
 *     ),
 *     @OA\Property(
 *         property="followers_min",
 *         type="integer",
 *         description="Minimum followers count",
 *         example=1000
 *     ),
 *     @OA\Property(
 *         property="followers_max",
 *         type="integer",
 *         description="Maximum followers count",
 *         example=100000
 *     ),
 *     @OA\Property(
 *         property="verified",
 *         type="boolean",
 *         description="Account verification status",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="size",
 *         type="integer",
 *         minimum=1,
 *         maximum=1000,
 *         description="Number of results",
 *         example=25
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="ScrollRequest",
 *     type="object",
 *     required={"scroll_id"},
 *     @OA\Property(
 *         property="scroll_id",
 *         type="string",
 *         description="Scroll ID from previous search",
 *         example="DXF1ZXJ5QW5kRmV0Y2g..."
 *     ),
 *     @OA\Property(
 *         property="scroll_timeout",
 *         type="string",
 *         description="Scroll context timeout",
 *         example="10s"
 *     )
 * )
 */

class ElasticSearchController
{
    /**
     * @OA\Post(
     *     path="/api/v1/elastic/search/persons",
     *     summary="Search persons in Elasticsearch",
     *     description="Search for persons using Elasticsearch with advanced query capabilities",
     *     operationId="searchPersonsElastic",
     *     tags={"Elasticsearch"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search criteria",
     *         @OA\JsonContent(ref="#/components/schemas/ElasticSearchRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search completed successfully",
     *         @OA\JsonContent(ref="#/components/schemas/ElasticSearchResponse")
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="size",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The size may not be greater than 1000."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchPersons() {}

    /**
     * @OA\Post(
     *     path="/api/v1/elastic/search/border-crossings",
     *     summary="Search border crossings",
     *     description="Search for border crossing records using Elasticsearch",
     *     operationId="searchBorderCrossings",
     *     tags={"Elasticsearch"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Border crossing search criteria",
     *         @OA\JsonContent(ref="#/components/schemas/BorderCrossingSearchRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Border crossing search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Border crossing search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", example=25),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="pin", type="string", example="1234567"),
     *                         @OA\Property(property="crossing_date", type="string", format="date-time", example="2024-06-15T10:30:00Z"),
     *                         @OA\Property(property="border_point", type="string", example="Heydar Aliyev International Airport"),
     *                         @OA\Property(property="direction", type="string", example="entry"),
     *                         @OA\Property(property="document_type", type="string", example="passport"),
     *                         @OA\Property(property="document_number", type="string", example="AA123456")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchBorderCrossings() {}
}
