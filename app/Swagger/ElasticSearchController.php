<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Elasticsearch",
 *     description="API endpoints for Elasticsearch operations and advanced search"
 * )
 */

class ElasticSearchController
{
    /**
     * @OA\Post(
     *     path="/api/v1/elasticsearch/search",
     *     summary="Elasticsearch general search",
     *     description="Perform advanced search using Elasticsearch across multiple indices",
     *     operationId="elasticsearchSearch",
     *     tags={"Elasticsearch"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Search parameters",
     *         @OA\JsonContent(
     *             required={"query"},
     *             @OA\Property(property="query", type="string", example="John Doe"),
     *             @OA\Property(property="indices", type="array", @OA\Items(type="string"), example={"persons", "blacklist"}, description="Elasticsearch indices to search"),
     *             @OA\Property(property="size", type="integer", example=20, description="Number of results to return"),
     *             @OA\Property(property="from", type="integer", example=0, description="Starting offset for pagination"),
     *             @OA\Property(property="sort", type="array", @OA\Items(type="object"), description="Sort criteria"),
     *             @OA\Property(property="filters", type="object", description="Additional filters"),
     *             @OA\Property(property="highlight", type="boolean", example=true, description="Enable result highlighting")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", example=150),
     *                 @OA\Property(property="max_score", type="number", format="float", example=1.5),
     *                 @OA\Property(property="took", type="integer", example=25, description="Search time in milliseconds"),
     *                 @OA\Property(
     *                     property="hits",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="index", type="string", example="persons"),
     *                         @OA\Property(property="id", type="string", example="1"),
     *                         @OA\Property(property="score", type="number", format="float", example=1.2),
     *                         @OA\Property(property="source", type="object", description="Document source data"),
     *                         @OA\Property(property="highlight", type="object", description="Highlighted fields")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="aggregations",
     *                     type="object",
     *                     description="Search aggregations if requested"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Invalid search query",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=400),
     *             @OA\Property(property="message", type="string", example="Invalid search query")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function search() {}

    /**
     * @OA\Post(
     *     path="/api/v1/elasticsearch/persons",
     *     summary="Search persons in Elasticsearch",
     *     description="Search for persons using Elasticsearch with advanced filtering",
     *     operationId="searchPersonsElastic",
     *     tags={"Elasticsearch"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Person search parameters",
     *         @OA\JsonContent(
     *             @OA\Property(property="query", type="string", example="John Doe"),
     *             @OA\Property(property="name", type="string", example="John"),
     *             @OA\Property(property="surname", type="string", example="Doe"),
     *             @OA\Property(property="pin", type="string", example="1234567"),
     *             @OA\Property(property="doc_number", type="string", example="AA123456"),
     *             @OA\Property(property="fuzzy", type="boolean", example=true, description="Enable fuzzy matching"),
     *             @OA\Property(property="size", type="integer", example=20),
     *             @OA\Property(property="from", type="integer", example=0),
     *             @OA\Property(property="date_range", type="object",
     *                 @OA\Property(property="field", type="string", example="created_at"),
     *                 @OA\Property(property="gte", type="string", format="date", example="2020-01-01"),
     *                 @OA\Property(property="lte", type="string", format="date", example="2024-12-31")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Person search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Person search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", example=25),
     *                 @OA\Property(property="took", type="integer", example=15),
     *                 @OA\Property(
     *                     property="persons",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="John"),
     *                         @OA\Property(property="surname", type="string", example="Doe"),
     *                         @OA\Property(property="pin", type="string", example="1234567"),
     *                         @OA\Property(property="doc_number", type="string", example="AA123456"),
     *                         @OA\Property(property="score", type="number", format="float", example=1.5),
     *                         @OA\Property(property="created_at", type="string", format="date-time")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchPersons() {}

    /**
     * @OA\Post(
     *     path="/api/v1/elasticsearch/border-crossings",
     *     summary="Search border crossing records",
     *     description="Search for border crossing records using Elasticsearch",
     *     operationId="searchBorderCrossings",
     *     tags={"Elasticsearch"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Border crossing search parameters",
     *         @OA\JsonContent(
     *             @OA\Property(property="passport_number", type="string", example="AA123456"),
     *             @OA\Property(property="pin", type="string", example="1234567"),
     *             @OA\Property(property="name", type="string", example="John"),
     *             @OA\Property(property="surname", type="string", example="Doe"),
     *             @OA\Property(property="crossing_type", type="string", enum={"entry", "exit"}, example="entry"),
     *             @OA\Property(property="border_point", type="string", example="Heydar Aliyev Airport"),
     *             @OA\Property(property="date_from", type="string", format="date", example="2024-01-01"),
     *             @OA\Property(property="date_to", type="string", format="date", example="2024-12-31"),
     *             @OA\Property(property="size", type="integer", example=50),
     *             @OA\Property(property="from", type="integer", example=0)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Border crossing search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Border crossing search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", example=12),
     *                 @OA\Property(property="took", type="integer", example=8),
     *                 @OA\Property(
     *                     property="crossings",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="string", example="bc_001"),
     *                         @OA\Property(property="passport_number", type="string", example="AA123456"),
     *                         @OA\Property(property="pin", type="string", example="1234567"),
     *                         @OA\Property(property="name", type="string", example="John"),
     *                         @OA\Property(property="surname", type="string", example="Doe"),
     *                         @OA\Property(property="crossing_type", type="string", example="entry"),
     *                         @OA\Property(property="border_point", type="string", example="Heydar Aliyev Airport"),
     *                         @OA\Property(property="crossing_date", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                         @OA\Property(property="nationality", type="string", example="Azerbaijan"),
     *                         @OA\Property(property="purpose", type="string", example="Tourism")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchBorderCrossings() {}

    /**
     * @OA\Post(
     *     path="/api/v1/elasticsearch/voen",
     *     summary="Search VOEN records in Elasticsearch",
     *     description="Search for company VOEN records using Elasticsearch",
     *     operationId="searchVoenElastic",
     *     tags={"Elasticsearch"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="VOEN search parameters",
     *         @OA\JsonContent(
     *             @OA\Property(property="query", type="string", example="Beein Technologies"),
     *             @OA\Property(property="voen", type="string", example="1234567890"),
     *             @OA\Property(property="company_name", type="string", example="Beein"),
     *             @OA\Property(property="director_name", type="string", example="John Doe"),
     *             @OA\Property(property="activity_type", type="string", example="Information Technology"),
     *             @OA\Property(property="status", type="string", enum={"active", "inactive"}, example="active"),
     *             @OA\Property(property="size", type="integer", example=20),
     *             @OA\Property(property="from", type="integer", example=0)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="VOEN search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="VOEN search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", example=8),
     *                 @OA\Property(property="took", type="integer", example=12),
     *                 @OA\Property(
     *                     property="companies",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="voen", type="string", example="1234567890"),
     *                         @OA\Property(property="company_name", type="string", example="Beein Technologies LLC"),
     *                         @OA\Property(property="legal_form", type="string", example="LLC"),
     *                         @OA\Property(property="registration_date", type="string", format="date", example="2020-01-15"),
     *                         @OA\Property(property="status", type="string", example="active"),
     *                         @OA\Property(property="activity_type", type="string", example="Information Technology"),
     *                         @OA\Property(property="score", type="number", format="float", example=2.1)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchVoen() {}
}
