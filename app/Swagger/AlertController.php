<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Alerts",
 *     description="API endpoints for alert management and monitoring"
 * )
 */

class AlertController
{
    /**
     * @OA\Get(
     *     path="/api/v1/alert",
     *     summary="Get list of alerts",
     *     description="Retrieve a list of all alerts with pagination and filtering",
     *     operationId="getAlerts",
     *     tags={"Alerts"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by alert status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"active", "inactive", "resolved"}, example="active")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Alerts retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Alerts retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="title", type="string", example="Security Alert"),
     *                     @OA\Property(property="description", type="string", example="Suspicious activity detected"),
     *                     @OA\Property(property="type", type="string", example="security"),
     *                     @OA\Property(property="priority", type="string", enum={"low", "medium", "high", "critical"}, example="high"),
     *                     @OA\Property(property="status", type="string", enum={"active", "inactive", "resolved"}, example="active"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=50),
     *                 @OA\Property(property="per_page", type="integer", example=15)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Post(
     *     path="/api/v1/alert",
     *     summary="Create a new alert",
     *     description="Create a new alert for monitoring and notification",
     *     operationId="createAlert",
     *     tags={"Alerts"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Alert data",
     *         @OA\JsonContent(
     *             required={"title", "description", "type", "priority"},
     *             @OA\Property(property="title", type="string", example="Security Alert"),
     *             @OA\Property(property="description", type="string", example="Suspicious activity detected in sector 5"),
     *             @OA\Property(property="type", type="string", example="security"),
     *             @OA\Property(property="priority", type="string", enum={"low", "medium", "high", "critical"}, example="high"),
     *             @OA\Property(property="search_criteria", type="object", description="Search criteria for alert monitoring"),
     *             @OA\Property(property="notification_settings", type="object", description="Notification configuration")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Alert created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Alert created successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Security Alert"),
     *                 @OA\Property(property="description", type="string", example="Suspicious activity detected in sector 5"),
     *                 @OA\Property(property="type", type="string", example="security"),
     *                 @OA\Property(property="priority", type="string", example="high"),
     *                 @OA\Property(property="status", type="string", example="active"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="title",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The title field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Get(
     *     path="/api/v1/alert/{alert}",
     *     summary="Get alert by ID",
     *     description="Retrieve a specific alert by its ID",
     *     operationId="getAlertById",
     *     tags={"Alerts"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="alert",
     *         in="path",
     *         description="Alert ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Alert retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Alert retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Security Alert"),
     *                 @OA\Property(property="description", type="string", example="Suspicious activity detected"),
     *                 @OA\Property(property="type", type="string", example="security"),
     *                 @OA\Property(property="priority", type="string", example="high"),
     *                 @OA\Property(property="status", type="string", example="active"),
     *                 @OA\Property(property="search_criteria", type="object", description="Search criteria configuration"),
     *                 @OA\Property(property="notification_settings", type="object", description="Notification settings"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Alert not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Alert not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Put(
     *     path="/api/v1/alert/{alert}",
     *     summary="Update an alert",
     *     description="Update an existing alert",
     *     operationId="updateAlert",
     *     tags={"Alerts"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="alert",
     *         in="path",
     *         description="Alert ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Updated alert data",
     *         @OA\JsonContent(
     *             @OA\Property(property="title", type="string", example="Updated Security Alert"),
     *             @OA\Property(property="description", type="string", example="Updated description"),
     *             @OA\Property(property="type", type="string", example="security"),
     *             @OA\Property(property="priority", type="string", enum={"low", "medium", "high", "critical"}, example="critical"),
     *             @OA\Property(property="status", type="string", enum={"active", "inactive", "resolved"}, example="active"),
     *             @OA\Property(property="search_criteria", type="object", description="Updated search criteria"),
     *             @OA\Property(property="notification_settings", type="object", description="Updated notification settings")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Alert updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Alert updated successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="title", type="string", example="Updated Security Alert"),
     *                 @OA\Property(property="priority", type="string", example="critical"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T11:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Alert not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Alert not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function update() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/alert/{alert}",
     *     summary="Delete an alert",
     *     description="Delete an existing alert",
     *     operationId="deleteAlert",
     *     tags={"Alerts"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="alert",
     *         in="path",
     *         description="Alert ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Alert deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Alert deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Alert not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Alert not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}

    /**
     * @OA\Get(
     *     path="/api/v1/check-alert-search-result",
     *     summary="Check alert search results",
     *     description="Check and validate alert search results for monitoring",
     *     operationId="checkAlertSearchResult",
     *     tags={"Alerts"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="alert_id",
     *         in="query",
     *         description="Alert ID to check",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="search_type",
     *         in="query",
     *         description="Type of search to check",
     *         required=false,
     *         @OA\Schema(type="string", example="person")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Alert search results checked successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Alert search results checked"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="alert_id", type="integer", example=1),
     *                 @OA\Property(property="matches_found", type="integer", example=5),
     *                 @OA\Property(property="last_check", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="match_id", type="string", example="MATCH123"),
     *                         @OA\Property(property="confidence", type="number", format="float", example=0.95),
     *                         @OA\Property(property="match_type", type="string", example="person"),
     *                         @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:25:00Z")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function checkAlertSearchResult() {}

    /**
     * @OA\Get(
     *     path="/api/v1/check-alert-social-calls",
     *     summary="Check alert social calls",
     *     description="Check social call patterns for alert monitoring",
     *     operationId="checkAlertSocialCalls",
     *     tags={"Alerts"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="alert_id",
     *         in="query",
     *         description="Alert ID to check",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="phone_number",
     *         in="query",
     *         description="Phone number to monitor",
     *         required=false,
     *         @OA\Schema(type="string", example="994501234567")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Alert social calls checked successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Alert social calls checked"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="alert_id", type="integer", example=1),
     *                 @OA\Property(property="calls_monitored", type="integer", example=25),
     *                 @OA\Property(property="suspicious_patterns", type="integer", example=3),
     *                 @OA\Property(property="last_check", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="patterns",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="pattern_type", type="string", example="frequent_calls"),
     *                         @OA\Property(property="phone_number", type="string", example="994501234567"),
     *                         @OA\Property(property="call_count", type="integer", example=15),
     *                         @OA\Property(property="time_period", type="string", example="last_24_hours"),
     *                         @OA\Property(property="risk_level", type="string", enum={"low", "medium", "high"}, example="high")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function checkSocialCall() {}
}
