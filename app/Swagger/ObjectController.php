<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Objects",
 *     description="API endpoints for object and object type management"
 * )
 */

class ObjectController
{
    /**
     * @OA\Get(
     *     path="/api/v1/objects",
     *     summary="Get list of objects",
     *     description="Retrieve a list of all objects with pagination and filtering",
     *     operationId="getObjects",
     *     tags={"Objects"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="type_id",
     *         in="query",
     *         description="Filter by object type ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by object status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"active", "inactive", "maintenance"}, example="active")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Objects retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Objects retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Main Building Camera 01"),
     *                     @OA\Property(property="description", type="string", example="Security camera at main entrance"),
     *                     @OA\Property(property="object_type_id", type="integer", example=1),
     *                     @OA\Property(property="location", type="string", example="Main Building Entrance"),
     *                     @OA\Property(property="coordinates", type="object",
     *                         @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                         @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *                     ),
     *                     @OA\Property(property="status", type="string", enum={"active", "inactive", "maintenance"}, example="active"),
     *                     @OA\Property(property="properties", type="object", description="Object-specific properties"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(
     *                         property="object_type",
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="Security Camera"),
     *                         @OA\Property(property="category", type="string", example="surveillance")
     *                     )
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=150),
     *                 @OA\Property(property="per_page", type="integer", example=15)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Post(
     *     path="/api/v1/objects",
     *     summary="Create a new object",
     *     description="Create a new object with specified type and properties",
     *     operationId="createObject",
     *     tags={"Objects"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Object data",
     *         @OA\JsonContent(
     *             required={"name", "object_type_id"},
     *             @OA\Property(property="name", type="string", example="Main Building Camera 01"),
     *             @OA\Property(property="description", type="string", example="Security camera at main entrance"),
     *             @OA\Property(property="object_type_id", type="integer", example=1),
     *             @OA\Property(property="location", type="string", example="Main Building Entrance"),
     *             @OA\Property(property="coordinates", type="object",
     *                 @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                 @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *             ),
     *             @OA\Property(property="status", type="string", enum={"active", "inactive", "maintenance"}, example="active"),
     *             @OA\Property(property="properties", type="object", description="Object-specific properties based on type")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Object created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Object created successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Main Building Camera 01"),
     *                 @OA\Property(property="description", type="string", example="Security camera at main entrance"),
     *                 @OA\Property(property="object_type_id", type="integer", example=1),
     *                 @OA\Property(property="location", type="string", example="Main Building Entrance"),
     *                 @OA\Property(property="status", type="string", example="active"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The name field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Get(
     *     path="/api/v1/objects/{object}",
     *     summary="Get object by ID",
     *     description="Retrieve a specific object by its ID",
     *     operationId="getObjectById",
     *     tags={"Objects"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="object",
     *         in="path",
     *         description="Object ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Object retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Object retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Main Building Camera 01"),
     *                 @OA\Property(property="description", type="string", example="Security camera at main entrance"),
     *                 @OA\Property(property="object_type_id", type="integer", example=1),
     *                 @OA\Property(property="location", type="string", example="Main Building Entrance"),
     *                 @OA\Property(property="coordinates", type="object",
     *                     @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                     @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *                 ),
     *                 @OA\Property(property="status", type="string", example="active"),
     *                 @OA\Property(property="properties", type="object", description="Object-specific properties"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="object_type",
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Security Camera"),
     *                     @OA\Property(property="category", type="string", example="surveillance"),
     *                     @OA\Property(property="schema", type="object", description="Object type schema definition")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Object not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Object not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Put(
     *     path="/api/v1/objects/{object}",
     *     summary="Update an object",
     *     description="Update an existing object",
     *     operationId="updateObject",
     *     tags={"Objects"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="object",
     *         in="path",
     *         description="Object ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Updated object data",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Updated Camera Name"),
     *             @OA\Property(property="description", type="string", example="Updated description"),
     *             @OA\Property(property="location", type="string", example="Updated location"),
     *             @OA\Property(property="coordinates", type="object",
     *                 @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                 @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *             ),
     *             @OA\Property(property="status", type="string", enum={"active", "inactive", "maintenance"}, example="maintenance"),
     *             @OA\Property(property="properties", type="object", description="Updated object-specific properties")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Object updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Object updated successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Updated Camera Name"),
     *                 @OA\Property(property="status", type="string", example="maintenance"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T11:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Object not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Object not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function update() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/objects/{object}",
     *     summary="Delete an object",
     *     description="Delete an existing object",
     *     operationId="deleteObject",
     *     tags={"Objects"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="object",
     *         in="path",
     *         description="Object ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Object deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Object deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Object not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Object not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}

    /**
     * @OA\Get(
     *     path="/api/v1/object-types",
     *     summary="Get list of object types",
     *     description="Retrieve a list of all object types",
     *     operationId="getObjectTypes",
     *     tags={"Objects"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Object types retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Object types retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Security Camera"),
     *                     @OA\Property(property="description", type="string", example="Surveillance camera for security monitoring"),
     *                     @OA\Property(property="category", type="string", example="surveillance"),
     *                     @OA\Property(property="icon", type="string", example="camera-icon.svg"),
     *                     @OA\Property(property="color", type="string", example="#FF5722"),
     *                     @OA\Property(property="schema", type="object", description="JSON schema for object properties"),
     *                     @OA\Property(property="is_active", type="boolean", example=true),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getObjectTypes() {}

    /**
     * @OA\Post(
     *     path="/api/v1/object-types",
     *     summary="Create a new object type",
     *     description="Create a new object type with schema definition",
     *     operationId="createObjectType",
     *     tags={"Objects"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Object type data",
     *         @OA\JsonContent(
     *             required={"name", "category"},
     *             @OA\Property(property="name", type="string", example="Security Camera"),
     *             @OA\Property(property="description", type="string", example="Surveillance camera for security monitoring"),
     *             @OA\Property(property="category", type="string", example="surveillance"),
     *             @OA\Property(property="icon", type="string", example="camera-icon.svg"),
     *             @OA\Property(property="color", type="string", example="#FF5722"),
     *             @OA\Property(property="schema", type="object", description="JSON schema for object properties"),
     *             @OA\Property(property="is_active", type="boolean", example=true)
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Object type created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Object type created successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Security Camera"),
     *                 @OA\Property(property="category", type="string", example="surveillance"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The name field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function createObjectType() {}

    /**
     * @OA\Get(
     *     path="/api/v1/object-types/{objectType}",
     *     summary="Get object type by ID",
     *     description="Retrieve a specific object type by its ID",
     *     operationId="getObjectTypeById",
     *     tags={"Objects"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="objectType",
     *         in="path",
     *         description="Object Type ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Object type retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Object type retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Security Camera"),
     *                 @OA\Property(property="description", type="string", example="Surveillance camera for security monitoring"),
     *                 @OA\Property(property="category", type="string", example="surveillance"),
     *                 @OA\Property(property="icon", type="string", example="camera-icon.svg"),
     *                 @OA\Property(property="color", type="string", example="#FF5722"),
     *                 @OA\Property(property="schema", type="object", description="JSON schema for object properties"),
     *                 @OA\Property(property="is_active", type="boolean", example=true),
     *                 @OA\Property(property="objects_count", type="integer", example=25, description="Number of objects of this type"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Object type not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Object type not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function showObjectType() {}
}
