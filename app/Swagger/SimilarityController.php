<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Similarity Search",
 *     description="API endpoints for similarity search and face recognition"
 * )
 */

class SimilarityController
{
    /**
     * @OA\Post(
     *     path="/api/v1/similarity-search",
     *     summary="Perform similarity search",
     *     description="Search for similar faces or objects using image comparison",
     *     operationId="similaritySearch",
     *     tags={"Similarity Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Similarity search data",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"image"},
     *                 @OA\Property(
     *                     property="image",
     *                     type="string",
     *                     format="binary",
     *                     description="Image file for similarity search"
     *                 ),
     *                 @OA\Property(
     *                     property="search_type",
     *                     type="string",
     *                     enum={"face", "object", "vehicle"},
     *                     example="face",
     *                     description="Type of similarity search"
     *                 ),
     *                 @OA\Property(
     *                     property="threshold",
     *                     type="number",
     *                     format="float",
     *                     example=0.8,
     *                     description="Similarity threshold (0.0 to 1.0)"
     *                 ),
     *                 @OA\Property(
     *                     property="max_results",
     *                     type="integer",
     *                     example=50,
     *                     description="Maximum number of results to return"
     *                 ),
     *                 @OA\Property(
     *                     property="date_range",
     *                     type="object",
     *                     @OA\Property(property="start_date", type="string", format="date", example="2024-01-01"),
     *                     @OA\Property(property="end_date", type="string", format="date", example="2024-01-31")
     *                 ),
     *                 @OA\Property(
     *                     property="camera_ids",
     *                     type="array",
     *                     @OA\Items(type="integer"),
     *                     example={1, 2, 3},
     *                     description="Specific camera IDs to search"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Similarity search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Similarity search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="search_id", type="string", example="SIM123456789"),
     *                 @OA\Property(property="total_matches", type="integer", example=25),
     *                 @OA\Property(property="search_time", type="number", format="float", example=3.5, description="Search time in seconds"),
     *                 @OA\Property(property="threshold_used", type="number", format="float", example=0.8),
     *                 @OA\Property(
     *                     property="matches",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="match_id", type="string", example="MATCH123"),
     *                         @OA\Property(property="similarity_score", type="number", format="float", example=0.95),
     *                         @OA\Property(property="image_url", type="string", example="https://api.beein.az/images/match_123.jpg"),
     *                         @OA\Property(property="thumbnail_url", type="string", example="https://api.beein.az/thumbnails/match_123.jpg"),
     *                         @OA\Property(property="camera_id", type="integer", example=1),
     *                         @OA\Property(property="camera_name", type="string", example="Main Entrance Camera"),
     *                         @OA\Property(property="location", type="string", example="Main Building Entrance"),
     *                         @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                         @OA\Property(property="coordinates", type="object",
     *                             @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                             @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *                         ),
     *                         @OA\Property(property="metadata", type="object", description="Additional metadata about the match")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="image",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The image field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function similaritySearch() {}

    /**
     * @OA\Get(
     *     path="/api/v1/similarity-search/{searchId}/results",
     *     summary="Get similarity search results",
     *     description="Retrieve results from a previous similarity search",
     *     operationId="getSimilarityResults",
     *     tags={"Similarity Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="searchId",
     *         in="path",
     *         description="Search ID",
     *         required=true,
     *         @OA\Schema(type="string", example="SIM123456789")
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of results per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Parameter(
     *         name="min_score",
     *         in="query",
     *         description="Minimum similarity score filter",
     *         required=false,
     *         @OA\Schema(type="number", format="float", example=0.9)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Similarity search results retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Search results retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="search_id", type="string", example="SIM123456789"),
     *                 @OA\Property(property="search_date", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="total_matches", type="integer", example=25),
     *                 @OA\Property(property="filtered_matches", type="integer", example=15),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="match_id", type="string", example="MATCH123"),
     *                         @OA\Property(property="similarity_score", type="number", format="float", example=0.95),
     *                         @OA\Property(property="image_url", type="string", example="https://api.beein.az/images/match_123.jpg"),
     *                         @OA\Property(property="thumbnail_url", type="string", example="https://api.beein.az/thumbnails/match_123.jpg"),
     *                         @OA\Property(property="camera_name", type="string", example="Main Entrance Camera"),
     *                         @OA\Property(property="location", type="string", example="Main Building Entrance"),
     *                         @OA\Property(property="timestamp", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="pagination",
     *                     type="object",
     *                     @OA\Property(property="current_page", type="integer", example=1),
     *                     @OA\Property(property="total_pages", type="integer", example=2),
     *                     @OA\Property(property="per_page", type="integer", example=20)
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Search results not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Search results not found or expired")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getSimilarityResults() {}

    /**
     * @OA\Post(
     *     path="/api/v1/face-recognition",
     *     summary="Perform face recognition",
     *     description="Recognize faces in an image and identify known persons",
     *     operationId="faceRecognition",
     *     tags={"Similarity Search"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Face recognition data",
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\Schema(
     *                 required={"image"},
     *                 @OA\Property(
     *                     property="image",
     *                     type="string",
     *                     format="binary",
     *                     description="Image file for face recognition"
     *                 ),
     *                 @OA\Property(
     *                     property="confidence_threshold",
     *                     type="number",
     *                     format="float",
     *                     example=0.85,
     *                     description="Recognition confidence threshold"
     *                 ),
     *                 @OA\Property(
     *                     property="max_faces",
     *                     type="integer",
     *                     example=10,
     *                     description="Maximum number of faces to detect"
     *                 ),
     *                 @OA\Property(
     *                     property="include_unknown",
     *                     type="boolean",
     *                     example=true,
     *                     description="Include unrecognized faces in results"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Face recognition completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Face recognition completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="recognition_id", type="string", example="REC123456789"),
     *                 @OA\Property(property="total_faces_detected", type="integer", example=3),
     *                 @OA\Property(property="recognized_faces", type="integer", example=2),
     *                 @OA\Property(property="unknown_faces", type="integer", example=1),
     *                 @OA\Property(property="processing_time", type="number", format="float", example=1.2),
     *                 @OA\Property(
     *                     property="faces",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="face_id", type="string", example="FACE123"),
     *                         @OA\Property(property="bounding_box", type="object",
     *                             @OA\Property(property="x", type="integer", example=100),
     *                             @OA\Property(property="y", type="integer", example=150),
     *                             @OA\Property(property="width", type="integer", example=80),
     *                             @OA\Property(property="height", type="integer", example=100)
     *                         ),
     *                         @OA\Property(property="confidence", type="number", format="float", example=0.92),
     *                         @OA\Property(property="is_recognized", type="boolean", example=true),
     *                         @OA\Property(property="person_pin", type="string", nullable=true, example="1234567"),
     *                         @OA\Property(property="person_name", type="string", nullable=true, example="John Doe"),
     *                         @OA\Property(property="recognition_confidence", type="number", format="float", nullable=true, example=0.88),
     *                         @OA\Property(property="face_image_url", type="string", example="https://api.beein.az/faces/face_123.jpg"),
     *                         @OA\Property(property="attributes", type="object",
     *                             @OA\Property(property="age", type="integer", example=35),
     *                             @OA\Property(property="gender", type="string", example="male"),
     *                             @OA\Property(property="emotion", type="string", example="neutral"),
     *                             @OA\Property(property="glasses", type="boolean", example=false),
     *                             @OA\Property(property="mask", type="boolean", example=false)
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="image",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The image field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function faceRecognition() {}
}
