<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Blacklist",
 *     description="API endpoints for blacklist management and operations"
 * )
 */

/**
 * @OA\Schema(
 *     schema="Blacklist",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Blacklist entry ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Person first name",
 *         example="<PERSON>"
 *     ),
 *     @OA\Property(
 *         property="surname",
 *         type="string",
 *         description="Person last name",
 *         example="Doe"
 *     ),
 *     @OA\Property(
 *         property="father_name",
 *         type="string",
 *         description="Person father's name",
 *         example="<PERSON>"
 *     ),
 *     @OA\Property(
 *         property="birthdate",
 *         type="string",
 *         format="date",
 *         description="Person birth date",
 *         example="1990-01-01"
 *     ),
 *     @OA\Property(
 *         property="document_number",
 *         type="string",
 *         description="Document number",
 *         example="AA123456"
 *     ),
 *     @OA\Property(
 *         property="pin",
 *         type="string",
 *         description="Personal identification number",
 *         example="1234567"
 *     ),
 *     @OA\Property(
 *         property="photo",
 *         type="string",
 *         description="Photo file path",
 *         example="photos/blacklist/john_doe.jpg"
 *     ),
 *     @OA\Property(
 *         property="note",
 *         type="string",
 *         description="Additional notes",
 *         example="Suspected of fraud"
 *     ),
 *     @OA\Property(
 *         property="gender",
 *         type="string",
 *         description="Person gender",
 *         enum={"male", "female"},
 *         example="male"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         description="Blacklist status (1=active, 0=inactive)",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Creation timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Last update timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="BlacklistRequest",
 *     type="object",
 *     required={"name", "surname", "pin"},
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Person first name",
 *         example="John"
 *     ),
 *     @OA\Property(
 *         property="surname",
 *         type="string",
 *         description="Person last name",
 *         example="Doe"
 *     ),
 *     @OA\Property(
 *         property="father_name",
 *         type="string",
 *         description="Person father's name",
 *         example="Michael"
 *     ),
 *     @OA\Property(
 *         property="birthdate",
 *         type="string",
 *         format="date",
 *         description="Person birth date",
 *         example="1990-01-01"
 *     ),
 *     @OA\Property(
 *         property="document_number",
 *         type="string",
 *         description="Document number",
 *         example="AA123456"
 *     ),
 *     @OA\Property(
 *         property="pin",
 *         type="string",
 *         description="Personal identification number",
 *         example="1234567"
 *     ),
 *     @OA\Property(
 *         property="photo",
 *         type="string",
 *         format="binary",
 *         description="Photo file upload"
 *     ),
 *     @OA\Property(
 *         property="note",
 *         type="string",
 *         description="Additional notes",
 *         example="Suspected of fraud"
 *     ),
 *     @OA\Property(
 *         property="gender",
 *         type="string",
 *         description="Person gender",
 *         enum={"male", "female"},
 *         example="male"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         description="Blacklist status (1=active, 0=inactive)",
 *         example=1
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="PaginatedBlacklistResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Blacklist entries retrieved successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/Blacklist")
 *     ),
 *     @OA\Property(
 *         property="links",
 *         type="object",
 *         @OA\Property(property="first", type="string", example="http://api.beein.az/api/v1/blacklist?page=1"),
 *         @OA\Property(property="last", type="string", example="http://api.beein.az/api/v1/blacklist?page=10"),
 *         @OA\Property(property="prev", type="string", example=null),
 *         @OA\Property(property="next", type="string", example="http://api.beein.az/api/v1/blacklist?page=2")
 *     ),
 *     @OA\Property(
 *         property="meta",
 *         type="object",
 *         @OA\Property(property="current_page", type="integer", example=1),
 *         @OA\Property(property="from", type="integer", example=1),
 *         @OA\Property(property="last_page", type="integer", example=10),
 *         @OA\Property(property="per_page", type="integer", example=15),
 *         @OA\Property(property="to", type="integer", example=15),
 *         @OA\Property(property="total", type="integer", example=150)
 *     )
 * )
 */

class BlacklistController
{
    /**
     * @OA\Get(
     *     path="/api/v1/blacklist",
     *     summary="Get list of blacklist entries",
     *     description="Retrieve a paginated list of all blacklist entries",
     *     operationId="getBlacklist",
     *     tags={"Blacklist"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term for name or surname",
     *         required=false,
     *         @OA\Schema(type="string", example="John")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by status",
     *         required=false,
     *         @OA\Schema(type="integer", enum={0, 1}, example=1)
     *     ),
     *     @OA\Parameter(
     *         name="gender",
     *         in="query",
     *         description="Filter by gender",
     *         required=false,
     *         @OA\Schema(type="string", enum={"male", "female"}, example="male")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blacklist entries retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PaginatedBlacklistResponse")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/blacklist/{id}",
     *     summary="Get blacklist entry by ID",
     *     description="Retrieve a specific blacklist entry by its ID",
     *     operationId="getBlacklistById",
     *     tags={"Blacklist"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Blacklist entry ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Blacklist entry retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Blacklist entry retrieved successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Blacklist")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Blacklist entry not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Blacklist entry not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}
}
