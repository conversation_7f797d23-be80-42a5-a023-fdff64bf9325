<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Server Status",
 *     description="API endpoints for monitoring server and service status"
 * )
 */

/**
 * @OA\Schema(
 *     schema="ServerStatus",
 *     type="object",
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Service name",
 *         example="PostgreSQL Database"
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         description="HTTP status code indicating service health",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         description="Status message",
 *         example="Connected"
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         description="Service type",
 *         enum={"API", "DB", "CACHE", "QUEUE", "STORAGE"},
 *         example="DB"
 *     ),
 *     @OA\Property(
 *         property="host",
 *         type="string",
 *         description="Service host (may be masked for security)",
 *         example="*.*.*.28"
 *     ),
 *     @OA\Property(
 *         property="response_time",
 *         type="number",
 *         format="float",
 *         description="Response time in milliseconds",
 *         example=25.5
 *     ),
 *     @OA\Property(
 *         property="last_checked",
 *         type="string",
 *         format="date-time",
 *         description="Last health check timestamp",
 *         example="2024-01-15T14:30:00Z"
 *     ),
 *     @OA\Property(
 *         property="details",
 *         type="object",
 *         description="Additional service details",
 *         @OA\Property(property="version", type="string", example="14.2"),
 *         @OA\Property(property="uptime", type="string", example="15 days, 3 hours"),
 *         @OA\Property(property="connections", type="integer", example=25)
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="AllServerStatusResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Server status retrieved successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/ServerStatus")
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="HealthCheckResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Health check completed"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         @OA\Property(
 *             property="overall_status",
 *             type="string",
 *             enum={"healthy", "degraded", "unhealthy"},
 *             description="Overall system health status",
 *             example="healthy"
 *         ),
 *         @OA\Property(
 *             property="healthy_services",
 *             type="integer",
 *             description="Number of healthy services",
 *             example=5
 *         ),
 *         @OA\Property(
 *             property="failed_services",
 *             type="integer",
 *             description="Number of failed services",
 *             example=0
 *         ),
 *         @OA\Property(
 *             property="total_services",
 *             type="integer",
 *             description="Total number of monitored services",
 *             example=5
 *         ),
 *         @OA\Property(
 *             property="uptime_percentage",
 *             type="number",
 *             format="float",
 *             description="System uptime percentage",
 *             example=99.9
 *         ),
 *         @OA\Property(
 *             property="last_incident",
 *             type="string",
 *             format="date-time",
 *             description="Last incident timestamp",
 *             example="2024-01-10T08:15:00Z"
 *         )
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="MetricsResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Metrics retrieved successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="object",
 *         @OA\Property(
 *             property="average_response_time",
 *             type="number",
 *             format="float",
 *             description="Average response time across all services (ms)",
 *             example=45.2
 *         ),
 *         @OA\Property(
 *             property="fastest_service",
 *             type="object",
 *             @OA\Property(property="name", type="string", example="Redis Cache"),
 *             @OA\Property(property="response_time", type="number", format="float", example=2.1)
 *         ),
 *         @OA\Property(
 *             property="slowest_service",
 *             type="object",
 *             @OA\Property(property="name", type="string", example="Elasticsearch"),
 *             @OA\Property(property="response_time", type="number", format="float", example=120.5)
 *         ),
 *         @OA\Property(
 *             property="uptime_percentage",
 *             type="number",
 *             format="float",
 *             description="Overall uptime percentage",
 *             example=99.95
 *         ),
 *         @OA\Property(
 *             property="total_requests_24h",
 *             type="integer",
 *             description="Total requests in last 24 hours",
 *             example=125000
 *         ),
 *         @OA\Property(
 *             property="error_rate_24h",
 *             type="number",
 *             format="float",
 *             description="Error rate percentage in last 24 hours",
 *             example=0.05
 *         )
 *     )
 * )
 */

class ServerStatusController
{
    /**
     * @OA\Get(
     *     path="/api/v1/server-status",
     *     summary="Get all server statuses",
     *     description="Retrieve the status of all monitored services and servers",
     *     operationId="getAllServerStatuses",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Server statuses retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/AllServerStatusResponse")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="Some services are unavailable",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="Some services are unavailable"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/ServerStatus")
     *             )
     *         )
     *     )
     * )
     */
    public function getAllStatuses() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/postgresql",
     *     summary="Get PostgreSQL database status",
     *     description="Check the status and health of the PostgreSQL database",
     *     operationId="getPostgreSqlStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="PostgreSQL status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="PostgreSQL status retrieved"),
     *             @OA\Property(property="data", ref="#/components/schemas/ServerStatus")
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="PostgreSQL is unavailable",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="PostgreSQL is unavailable"),
     *             @OA\Property(property="data", ref="#/components/schemas/ServerStatus")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getPostgreSqlStatus() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/mongodb",
     *     summary="Get MongoDB database status",
     *     description="Check the status and health of the MongoDB database",
     *     operationId="getMongoDbStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="MongoDB status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="MongoDB status retrieved"),
     *             @OA\Property(property="data", ref="#/components/schemas/ServerStatus")
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="MongoDB is unavailable",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="MongoDB is unavailable")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getMongoDbStatus() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/backend",
     *     summary="Get backend server status",
     *     description="Check the status and health of the backend API server",
     *     operationId="getBackendStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Backend server status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Backend server status retrieved"),
     *             @OA\Property(property="data", ref="#/components/schemas/ServerStatus")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getBackendStatus() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/health",
     *     summary="Get overall system health",
     *     description="Get comprehensive health check results for the entire system",
     *     operationId="getSystemHealth",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="System health retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/HealthCheckResponse")
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="System is degraded or unhealthy",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="System health is degraded"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="overall_status", type="string", example="degraded"),
     *                 @OA\Property(property="healthy_services", type="integer", example=3),
     *                 @OA\Property(property="failed_services", type="integer", example=2),
     *                 @OA\Property(property="total_services", type="integer", example=5)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getSystemHealth() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/metrics",
     *     summary="Get system performance metrics",
     *     description="Retrieve detailed performance metrics and statistics",
     *     operationId="getSystemMetrics",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="period",
     *         in="query",
     *         description="Time period for metrics",
     *         required=false,
     *         @OA\Schema(type="string", enum={"1h", "24h", "7d", "30d"}, example="24h")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="System metrics retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/MetricsResponse")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getSystemMetrics() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/elasticsearch",
     *     summary="Get Elasticsearch status",
     *     description="Check the status and health of the Elasticsearch cluster",
     *     operationId="getElasticsearchStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Elasticsearch status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Elasticsearch status retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/ServerStatus"),
     *                     @OA\Schema(
     *                         @OA\Property(
     *                             property="cluster_details",
     *                             type="object",
     *                             @OA\Property(property="cluster_name", type="string", example="beein-cluster"),
     *                             @OA\Property(property="status", type="string", example="green"),
     *                             @OA\Property(property="number_of_nodes", type="integer", example=3),
     *                             @OA\Property(property="active_primary_shards", type="integer", example=15),
     *                             @OA\Property(property="active_shards", type="integer", example=30),
     *                             @OA\Property(property="relocating_shards", type="integer", example=0),
     *                             @OA\Property(property="initializing_shards", type="integer", example=0),
     *                             @OA\Property(property="unassigned_shards", type="integer", example=0)
     *                         )
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="Elasticsearch is unavailable",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="Elasticsearch is unavailable")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getElasticsearchStatus() {}

    /**
     * @OA\Get(
     *     path="/api/v1/server-status/redis",
     *     summary="Get Redis cache status",
     *     description="Check the status and health of the Redis cache server",
     *     operationId="getRedisStatus",
     *     tags={"Server Status"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Redis status retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Redis status retrieved"),
     *             @OA\Property(
     *                 property="data",
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/ServerStatus"),
     *                     @OA\Schema(
     *                         @OA\Property(
     *                             property="cache_details",
     *                             type="object",
     *                             @OA\Property(property="used_memory", type="string", example="2.5MB"),
     *                             @OA\Property(property="connected_clients", type="integer", example=12),
     *                             @OA\Property(property="total_commands_processed", type="integer", example=1500000),
     *                             @OA\Property(property="keyspace_hits", type="integer", example=950000),
     *                             @OA\Property(property="keyspace_misses", type="integer", example=50000),
     *                             @OA\Property(property="hit_rate", type="number", format="float", example=95.0)
     *                         )
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=503,
     *         description="Redis is unavailable",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=503),
     *             @OA\Property(property="message", type="string", example="Redis is unavailable")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getRedisStatus() {}
}
