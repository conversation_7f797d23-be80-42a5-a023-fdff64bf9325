<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Cameras",
 *     description="API endpoints for camera management and monitoring"
 * )
 */

/**
 * @OA\Schema(
 *     schema="Camera",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Camera ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="camera_id",
 *         type="string",
 *         description="Unique camera identifier",
 *         example="CAM001"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Camera name",
 *         example="Front Door Camera"
 *     ),
 *     @OA\Property(
 *         property="object_id",
 *         type="integer",
 *         description="Associated object ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="camera_type_id",
 *         type="integer",
 *         description="Camera type ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="ip_address",
 *         type="string",
 *         description="Camera IP address",
 *         example="*************"
 *     ),
 *     @OA\Property(
 *         property="port",
 *         type="integer",
 *         description="Camera port",
 *         example=8080
 *     ),
 *     @OA\Property(
 *         property="username",
 *         type="string",
 *         description="Camera username",
 *         example="admin"
 *     ),
 *     @OA\Property(
 *         property="password",
 *         type="string",
 *         description="Camera password (write-only)",
 *         example="password123"
 *     ),
 *     @OA\Property(
 *         property="active",
 *         type="boolean",
 *         description="Camera active status",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="stream_url",
 *         type="string",
 *         description="Camera stream URL",
 *         example="rtsp://*************:554/stream"
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         description="Creation timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         description="Last update timestamp",
 *         example="2024-01-01T00:00:00.000000Z"
 *     ),
 *     @OA\Property(
 *         property="type",
 *         ref="#/components/schemas/CameraType"
 *     ),
 *     @OA\Property(
 *         property="object",
 *         ref="#/components/schemas/Object"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="CameraType",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Camera type ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Camera type name",
 *         example="IP Camera"
 *     ),
 *     @OA\Property(
 *         property="description",
 *         type="string",
 *         description="Camera type description",
 *         example="High-definition IP surveillance camera"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="Object",
 *     type="object",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         description="Object ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Object name",
 *         example="Main Building"
 *     ),
 *     @OA\Property(
 *         property="address",
 *         type="string",
 *         description="Object address",
 *         example="123 Main Street, Baku"
 *     ),
 *     @OA\Property(
 *         property="gps",
 *         type="object",
 *         description="GPS coordinates",
 *         @OA\Property(property="lat", type="number", format="float", example=40.4093),
 *         @OA\Property(property="lng", type="number", format="float", example=49.8671)
 *     ),
 *     @OA\Property(
 *         property="active",
 *         type="boolean",
 *         description="Object active status",
 *         example=true
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="CameraRequest",
 *     type="object",
 *     required={"camera_id", "name", "object_id", "camera_type_id"},
 *     @OA\Property(
 *         property="camera_id",
 *         type="string",
 *         description="Unique camera identifier",
 *         example="CAM001"
 *     ),
 *     @OA\Property(
 *         property="name",
 *         type="string",
 *         description="Camera name",
 *         example="Front Door Camera"
 *     ),
 *     @OA\Property(
 *         property="object_id",
 *         type="integer",
 *         description="Associated object ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="camera_type_id",
 *         type="integer",
 *         description="Camera type ID",
 *         example=1
 *     ),
 *     @OA\Property(
 *         property="ip_address",
 *         type="string",
 *         description="Camera IP address",
 *         example="*************"
 *     ),
 *     @OA\Property(
 *         property="port",
 *         type="integer",
 *         description="Camera port",
 *         example=8080
 *     ),
 *     @OA\Property(
 *         property="username",
 *         type="string",
 *         description="Camera username",
 *         example="admin"
 *     ),
 *     @OA\Property(
 *         property="password",
 *         type="string",
 *         description="Camera password",
 *         example="password123"
 *     ),
 *     @OA\Property(
 *         property="active",
 *         type="boolean",
 *         description="Camera active status",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="stream_url",
 *         type="string",
 *         description="Camera stream URL",
 *         example="rtsp://*************:554/stream"
 *     )
 * )
 */

/**
 * @OA\Schema(
 *     schema="PaginatedCameraResponse",
 *     type="object",
 *     @OA\Property(
 *         property="success",
 *         type="boolean",
 *         example=true
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="integer",
 *         example=200
 *     ),
 *     @OA\Property(
 *         property="message",
 *         type="string",
 *         example="Cameras retrieved successfully"
 *     ),
 *     @OA\Property(
 *         property="data",
 *         type="array",
 *         @OA\Items(ref="#/components/schemas/Camera")
 *     ),
 *     @OA\Property(
 *         property="links",
 *         type="object",
 *         @OA\Property(property="first", type="string", example="http://api.beein.az/api/v1/cameras?page=1"),
 *         @OA\Property(property="last", type="string", example="http://api.beein.az/api/v1/cameras?page=10"),
 *         @OA\Property(property="prev", type="string", example=null),
 *         @OA\Property(property="next", type="string", example="http://api.beein.az/api/v1/cameras?page=2")
 *     ),
 *     @OA\Property(
 *         property="meta",
 *         type="object",
 *         @OA\Property(property="current_page", type="integer", example=1),
 *         @OA\Property(property="from", type="integer", example=1),
 *         @OA\Property(property="last_page", type="integer", example=10),
 *         @OA\Property(property="per_page", type="integer", example=15),
 *         @OA\Property(property="to", type="integer", example=15),
 *         @OA\Property(property="total", type="integer", example=150)
 *     )
 * )
 */

class CameraController
{
    /**
     * @OA\Get(
     *     path="/api/v1/cameras",
     *     summary="Get list of cameras",
     *     description="Retrieve a paginated list of all cameras",
     *     operationId="getCameras",
     *     tags={"Cameras"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="object_id",
     *         in="query",
     *         description="Filter by object ID",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="active",
     *         in="query",
     *         description="Filter by active status",
     *         required=false,
     *         @OA\Schema(type="boolean", example=true)
     *     ),
     *     @OA\Parameter(
     *         name="search",
     *         in="query",
     *         description="Search term for camera name or ID",
     *         required=false,
     *         @OA\Schema(type="string", example="Front Door")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Cameras retrieved successfully",
     *         @OA\JsonContent(ref="#/components/schemas/PaginatedCameraResponse")
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Get(
     *     path="/api/v1/cameras/{id}",
     *     summary="Get camera by ID",
     *     description="Retrieve a specific camera by its ID",
     *     operationId="getCameraById",
     *     tags={"Cameras"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Camera ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="include",
     *         in="query",
     *         description="Include related data (type,object)",
     *         required=false,
     *         @OA\Schema(type="string", example="type,object")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Camera retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Camera retrieved successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Camera")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Camera not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Camera not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Post(
     *     path="/api/v1/cameras",
     *     summary="Create a new camera",
     *     description="Create a new camera record",
     *     operationId="createCamera",
     *     tags={"Cameras"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Camera data",
     *         @OA\JsonContent(ref="#/components/schemas/CameraRequest")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Camera created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Camera created successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Camera")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="camera_id",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The camera_id field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Put(
     *     path="/api/v1/cameras/{id}",
     *     summary="Update a camera",
     *     description="Update an existing camera record",
     *     operationId="updateCamera",
     *     tags={"Cameras"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Camera ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Camera data",
     *         @OA\JsonContent(ref="#/components/schemas/CameraRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Camera updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Camera updated successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/Camera")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Camera not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Camera not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function update() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/cameras/{id}",
     *     summary="Delete a camera",
     *     description="Delete an existing camera record",
     *     operationId="deleteCamera",
     *     tags={"Cameras"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="Camera ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Camera deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Camera deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Camera not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Camera not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}
}
