<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Customs",
 *     description="API endpoints for customs and border control operations"
 * )
 */

class CustomsController
{
    /**
     * @OA\Get(
     *     path="/api/v1/person/{pin}/customs",
     *     summary="Get person customs records",
     *     description="Retrieve customs records for a specific person by PIN",
     *     operationId="getPersonCustomsRecords",
     *     tags={"Customs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         description="Person PIN",
     *         required=true,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for customs records",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for customs records",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-31")
     *     ),
     *     @OA\Parameter(
     *         name="customs_type",
     *         in="query",
     *         description="Filter by customs operation type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"import", "export", "transit", "personal"}, example="import")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Customs records retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Customs records retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="person_pin", type="string", example="1234567"),
     *                 @OA\Property(property="total_records", type="integer", example=15),
     *                 @OA\Property(
     *                     property="customs_records",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="declaration_id", type="string", example="DECL123456789"),
     *                         @OA\Property(property="declaration_date", type="string", format="date", example="2024-01-15"),
     *                         @OA\Property(property="customs_office", type="string", example="Baku International Airport"),
     *                         @OA\Property(property="operation_type", type="string", enum={"import", "export", "transit", "personal"}, example="import"),
     *                         @OA\Property(property="transport_mode", type="string", enum={"air", "sea", "land", "rail"}, example="air"),
     *                         @OA\Property(property="origin_country", type="string", example="Turkey"),
     *                         @OA\Property(property="destination_country", type="string", example="Azerbaijan"),
     *                         @OA\Property(property="total_value", type="number", format="float", example=2500.00, description="Total value in AZN"),
     *                         @OA\Property(property="currency", type="string", example="AZN"),
     *                         @OA\Property(property="duty_paid", type="number", format="float", example=375.00),
     *                         @OA\Property(property="vat_paid", type="number", format="float", example=450.00),
     *                         @OA\Property(property="status", type="string", enum={"cleared", "pending", "inspection", "detained"}, example="cleared"),
     *                         @OA\Property(
     *                             property="goods",
     *                             type="array",
     *                             @OA\Items(
     *                                 type="object",
     *                                 @OA\Property(property="description", type="string", example="Electronic devices"),
     *                                 @OA\Property(property="hs_code", type="string", example="8517.12.00"),
     *                                 @OA\Property(property="quantity", type="integer", example=5),
     *                                 @OA\Property(property="unit", type="string", example="pieces"),
     *                                 @OA\Property(property="unit_value", type="number", format="float", example=500.00),
     *                                 @OA\Property(property="total_value", type="number", format="float", example=2500.00)
     *                             )
     *                         ),
     *                         @OA\Property(property="inspection_required", type="boolean", example=false),
     *                         @OA\Property(property="risk_assessment", type="string", enum={"low", "medium", "high"}, example="low"),
     *                         @OA\Property(property="processing_time", type="integer", example=45, description="Processing time in minutes"),
     *                         @OA\Property(property="officer_id", type="string", example="OFF123"),
     *                         @OA\Property(property="notes", type="string", nullable=true, example="Standard clearance procedure")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="summary",
     *                     type="object",
     *                     @OA\Property(property="total_declarations", type="integer", example=15),
     *                     @OA\Property(property="total_value", type="number", format="float", example=37500.00),
     *                     @OA\Property(property="total_duties_paid", type="number", format="float", example=5625.00),
     *                     @OA\Property(property="most_common_goods", type="string", example="Electronic devices"),
     *                     @OA\Property(property="most_used_office", type="string", example="Baku International Airport"),
     *                     @OA\Property(property="average_processing_time", type="integer", example=38, description="Minutes")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Person not found or no customs records",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="No customs records found for this person")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to access customs data")
     *         )
     *     )
     * )
     */
    public function searchByPin() {}

    /**
     * @OA\Get(
     *     path="/api/v1/customs/declarations",
     *     summary="Get customs declarations",
     *     description="Retrieve customs declarations with filtering and pagination",
     *     operationId="getCustomsDeclarations",
     *     tags={"Customs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=50)
     *     ),
     *     @OA\Parameter(
     *         name="declaration_id",
     *         in="query",
     *         description="Filter by declaration ID",
     *         required=false,
     *         @OA\Schema(type="string", example="DECL123456789")
     *     ),
     *     @OA\Parameter(
     *         name="person_pin",
     *         in="query",
     *         description="Filter by person PIN",
     *         required=false,
     *         @OA\Schema(type="string", example="1234567")
     *     ),
     *     @OA\Parameter(
     *         name="customs_office",
     *         in="query",
     *         description="Filter by customs office",
     *         required=false,
     *         @OA\Schema(type="string", example="Baku International Airport")
     *     ),
     *     @OA\Parameter(
     *         name="operation_type",
     *         in="query",
     *         description="Filter by operation type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"import", "export", "transit", "personal"}, example="import")
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by declaration status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"cleared", "pending", "inspection", "detained"}, example="cleared")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for declarations",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for declarations",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-31")
     *     ),
     *     @OA\Parameter(
     *         name="min_value",
     *         in="query",
     *         description="Minimum declaration value",
     *         required=false,
     *         @OA\Schema(type="number", format="float", example=1000.00)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Customs declarations retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Customs declarations retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="declaration_id", type="string", example="DECL123456789"),
     *                     @OA\Property(property="declaration_date", type="string", format="date", example="2024-01-15"),
     *                     @OA\Property(property="person_pin", type="string", example="1234567"),
     *                     @OA\Property(property="person_name", type="string", example="Əli Məmmədov"),
     *                     @OA\Property(property="customs_office", type="string", example="Baku International Airport"),
     *                     @OA\Property(property="operation_type", type="string", example="import"),
     *                     @OA\Property(property="transport_mode", type="string", example="air"),
     *                     @OA\Property(property="origin_country", type="string", example="Turkey"),
     *                     @OA\Property(property="destination_country", type="string", example="Azerbaijan"),
     *                     @OA\Property(property="total_value", type="number", format="float", example=2500.00),
     *                     @OA\Property(property="currency", type="string", example="AZN"),
     *                     @OA\Property(property="duty_paid", type="number", format="float", example=375.00),
     *                     @OA\Property(property="vat_paid", type="number", format="float", example=450.00),
     *                     @OA\Property(property="status", type="string", example="cleared"),
     *                     @OA\Property(property="risk_assessment", type="string", example="low"),
     *                     @OA\Property(property="processing_time", type="integer", example=45),
     *                     @OA\Property(property="goods_count", type="integer", example=3, description="Number of different goods in declaration"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=2500),
     *                 @OA\Property(property="per_page", type="integer", example=50)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to access customs data")
     *         )
     *     )
     * )
     */
    public function getDeclarations() {}

    /**
     * @OA\Get(
     *     path="/api/v1/customs/analytics",
     *     summary="Get customs analytics",
     *     description="Retrieve customs analytics and statistics",
     *     operationId="getCustomsAnalytics",
     *     tags={"Customs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="period",
     *         in="query",
     *         description="Analysis period",
     *         required=false,
     *         @OA\Schema(type="string", enum={"day", "week", "month", "quarter", "year"}, example="month")
     *     ),
     *     @OA\Parameter(
     *         name="start_date",
     *         in="query",
     *         description="Start date for analytics",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-01")
     *     ),
     *     @OA\Parameter(
     *         name="end_date",
     *         in="query",
     *         description="End date for analytics",
     *         required=false,
     *         @OA\Schema(type="string", format="date", example="2024-01-31")
     *     ),
     *     @OA\Parameter(
     *         name="customs_office",
     *         in="query",
     *         description="Filter by specific customs office",
     *         required=false,
     *         @OA\Schema(type="string", example="Baku International Airport")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Customs analytics retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Customs analytics retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="period", type="string", example="2024-01-01 to 2024-01-31"),
     *                 @OA\Property(property="total_declarations", type="integer", example=15420),
     *                 @OA\Property(property="total_value", type="number", format="float", example=125000000.00, description="Total value in AZN"),
     *                 @OA\Property(property="total_duties_collected", type="number", format="float", example=18750000.00),
     *                 @OA\Property(property="total_vat_collected", type="number", format="float", example=22500000.00),
     *                 @OA\Property(
     *                     property="by_operation_type",
     *                     type="object",
     *                     @OA\Property(property="import", type="object",
     *                         @OA\Property(property="count", type="integer", example=8500),
     *                         @OA\Property(property="value", type="number", format="float", example=75000000.00),
     *                         @OA\Property(property="percentage", type="number", format="float", example=60.0)
     *                     ),
     *                     @OA\Property(property="export", type="object",
     *                         @OA\Property(property="count", type="integer", example=4200),
     *                         @OA\Property(property="value", type="number", format="float", example=35000000.00),
     *                         @OA\Property(property="percentage", type="number", format="float", example=28.0)
     *                     ),
     *                     @OA\Property(property="transit", type="object",
     *                         @OA\Property(property="count", type="integer", example=1850),
     *                         @OA\Property(property="value", type="number", format="float", example=12000000.00),
     *                         @OA\Property(property="percentage", type="number", format="float", example=9.6)
     *                     ),
     *                     @OA\Property(property="personal", type="object",
     *                         @OA\Property(property="count", type="integer", example=870),
     *                         @OA\Property(property="value", type="number", format="float", example=3000000.00),
     *                         @OA\Property(property="percentage", type="number", format="float", example=2.4)
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="by_transport_mode",
     *                     type="object",
     *                     @OA\Property(property="air", type="integer", example=6500),
     *                     @OA\Property(property="sea", type="integer", example=4200),
     *                     @OA\Property(property="land", type="integer", example=3800),
     *                     @OA\Property(property="rail", type="integer", example=920)
     *                 ),
     *                 @OA\Property(
     *                     property="top_countries",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="country", type="string", example="Turkey"),
     *                         @OA\Property(property="declarations", type="integer", example=3500),
     *                         @OA\Property(property="value", type="number", format="float", example=28000000.00)
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="processing_efficiency",
     *                     type="object",
     *                     @OA\Property(property="average_processing_time", type="integer", example=42, description="Minutes"),
     *                     @OA\Property(property="inspection_rate", type="number", format="float", example=15.5, description="Percentage"),
     *                     @OA\Property(property="clearance_rate", type="number", format="float", example=94.2, description="Percentage"),
     *                     @OA\Property(property="detention_rate", type="number", format="float", example=2.1, description="Percentage")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     ),
     *     @OA\Response(
     *         response=403,
     *         description="Forbidden - Insufficient permissions",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=403),
     *             @OA\Property(property="message", type="string", example="Insufficient permissions to access customs analytics")
     *         )
     *     )
     * )
     */
    public function getAnalytics() {}
}
