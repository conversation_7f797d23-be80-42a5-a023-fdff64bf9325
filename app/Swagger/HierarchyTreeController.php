<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Hierarchy Trees",
 *     description="API endpoints for hierarchy tree operations, ancestors, descendants, and tree structures"
 * )
 */

class HierarchyTreeController
{
    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/{hierarchy}/tree",
     *     summary="Get hierarchy tree",
     *     description="Retrieve the complete hierarchy tree structure for a specific hierarchy",
     *     operationId="getHierarchyTree",
     *     tags={"Hierarchy Trees"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchy tree retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="Beein Technologies"),
     *             @OA\Property(property="description", type="string", example="Main organizational hierarchy"),
     *             @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *             @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(
     *                 property="parent",
     *                 type="object",
     *                 nullable=true,
     *                 example=null
     *             ),
     *             @OA\Property(
     *                 property="children_recursive",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=2),
     *                     @OA\Property(property="name", type="string", example="IT Department"),
     *                     @OA\Property(property="description", type="string", example="Information Technology Department"),
     *                     @OA\Property(property="parent_id", type="integer", example=1),
     *                     @OA\Property(
     *                         property="children_recursive",
     *                         type="array",
     *                         @OA\Items(
     *                             type="object",
     *                             @OA\Property(property="id", type="integer", example=3),
     *                             @OA\Property(property="name", type="string", example="Development Team"),
     *                             @OA\Property(property="parent_id", type="integer", example=2)
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getHierarchyTree() {}

    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/tree-users/all",
     *     summary="Get all hierarchy trees with users",
     *     description="Retrieve all hierarchy trees with their associated users",
     *     operationId="getHierarchyTreeWithUsersAll",
     *     tags={"Hierarchy Trees"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="All hierarchy trees with users retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beein Technologies"),
     *                 @OA\Property(property="description", type="string", example="Main organizational hierarchy"),
     *                 @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="users",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=1),
     *                         @OA\Property(property="name", type="string", example="John"),
     *                         @OA\Property(property="surname", type="string", example="Doe"),
     *                         @OA\Property(property="email", type="string", format="email", example="<EMAIL>")
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="children_recursive",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=2),
     *                         @OA\Property(property="name", type="string", example="IT Department"),
     *                         @OA\Property(
     *                             property="users",
     *                             type="array",
     *                             @OA\Items(
     *                                 type="object",
     *                                 @OA\Property(property="id", type="integer", example=2),
     *                                 @OA\Property(property="name", type="string", example="Jane"),
     *                                 @OA\Property(property="surname", type="string", example="Smith"),
     *                                 @OA\Property(property="email", type="string", format="email", example="<EMAIL>")
     *                             )
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getHierarchyTreeWithUsersAll() {}

    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/{hierarchy}/tree-users",
     *     summary="Get hierarchy tree with users",
     *     description="Retrieve a specific hierarchy tree with all associated users and their roles",
     *     operationId="getHierarchyTreeWithUsers",
     *     tags={"Hierarchy Trees"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchy tree with users retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="Beein Technologies"),
     *             @OA\Property(property="description", type="string", example="Main organizational hierarchy"),
     *             @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *             @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(
     *                 property="users",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John"),
     *                     @OA\Property(property="surname", type="string", example="Doe"),
     *                     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                     @OA\Property(property="role", type="string", example="manager"),
     *                     @OA\Property(property="position", type="integer", example=1)
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="children_recursive",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=2),
     *                     @OA\Property(property="name", type="string", example="IT Department"),
     *                     @OA\Property(
     *                         property="users",
     *                         type="array",
     *                         @OA\Items(
     *                             type="object",
     *                             @OA\Property(property="id", type="integer", example=2),
     *                             @OA\Property(property="name", type="string", example="Jane"),
     *                             @OA\Property(property="surname", type="string", example="Smith"),
     *                             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                             @OA\Property(property="role", type="string", example="developer"),
     *                             @OA\Property(property="position", type="integer", example=2)
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getHierarchyTreeWithUsers() {}

    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/{hierarchy}/descendants",
     *     summary="Get hierarchy descendants",
     *     description="Retrieve all descendant hierarchies (children, grandchildren, etc.) of a specific hierarchy",
     *     operationId="getHierarchyDescendants",
     *     tags={"Hierarchy Trees"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchy descendants retrieved successfully",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=2),
     *                 @OA\Property(property="name", type="string", example="IT Department"),
     *                 @OA\Property(property="description", type="string", example="Information Technology Department"),
     *                 @OA\Property(property="parent_id", type="integer", example=1),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(
     *                     property="descendants",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=3),
     *                         @OA\Property(property="name", type="string", example="Development Team"),
     *                         @OA\Property(property="parent_id", type="integer", example=2)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getHierarchyDescendants() {}

    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/{hierarchy}/ancestors",
     *     summary="Get hierarchy ancestors",
     *     description="Retrieve all ancestor hierarchies (parent, grandparent, etc.) of a specific hierarchy",
     *     operationId="getHierarchyAncestors",
     *     tags={"Hierarchy Trees"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=3)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Hierarchy ancestors retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=2),
     *             @OA\Property(property="name", type="string", example="IT Department"),
     *             @OA\Property(property="description", type="string", example="Information Technology Department"),
     *             @OA\Property(property="parent_id", type="integer", example=1),
     *             @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *             @OA\Property(
     *                 property="ancestors",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beein Technologies"),
     *                 @OA\Property(property="parent_id", type="integer", nullable=true, example=null),
     *                 @OA\Property(
     *                     property="ancestors",
     *                     type="object",
     *                     nullable=true,
     *                     example=null
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getHierarchyAncestors() {}

    /**
     * @OA\Get(
     *     path="/api/v1/hierarchies/{hierarchy}/users/all",
     *     summary="Get all users in hierarchy tree",
     *     description="Retrieve all users in the complete hierarchy tree (including all descendants)",
     *     operationId="getAllUsersInHierarchyTree",
     *     tags={"Hierarchy Trees"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="hierarchy",
     *         in="path",
     *         description="Hierarchy ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="All users in hierarchy tree retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="hierarchy",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Beein Technologies"),
     *                 @OA\Property(property="description", type="string", example="Main organizational hierarchy")
     *             ),
     *             @OA\Property(
     *                 property="users",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="John"),
     *                     @OA\Property(property="surname", type="string", example="Doe"),
     *                     @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *                     @OA\Property(property="hierarchy_role", type="string", example="manager"),
     *                     @OA\Property(property="position", type="integer", example=1),
     *                     @OA\Property(property="hierarchy_id", type="integer", example=1),
     *                     @OA\Property(property="hierarchy_name", type="string", example="Beein Technologies")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="children",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(
     *                         property="hierarchy",
     *                         type="object",
     *                         @OA\Property(property="id", type="integer", example=2),
     *                         @OA\Property(property="name", type="string", example="IT Department")
     *                     ),
     *                     @OA\Property(
     *                         property="users",
     *                         type="array",
     *                         @OA\Items(
     *                             type="object",
     *                             @OA\Property(property="id", type="integer", example=2),
     *                             @OA\Property(property="name", type="string", example="Jane"),
     *                             @OA\Property(property="hierarchy_role", type="string", example="developer")
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Hierarchy not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Hierarchy not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function getAllUsersInHierarchyTree() {}
}
