<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Saved Data",
 *     description="API endpoints for saved data management and quick access"
 * )
 */

class SavedDataController
{
    /**
     * @OA\Get(
     *     path="/api/v1/saved-data",
     *     summary="Get saved data",
     *     description="Retrieve list of saved data items for the authenticated user",
     *     operationId="getSavedData",
     *     tags={"Saved Data"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=20)
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="Filter by data type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"search", "person", "alert", "report", "query"}, example="search")
     *     ),
     *     @OA\Parameter(
     *         name="category",
     *         in="query",
     *         description="Filter by category",
     *         required=false,
     *         @OA\Schema(type="string", example="investigations")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Saved data retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Saved data retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Suspicious Activity Search"),
     *                     @OA\Property(property="description", type="string", example="Search query for suspicious activities in downtown area"),
     *                     @OA\Property(property="type", type="string", enum={"search", "person", "alert", "report", "query"}, example="search"),
     *                     @OA\Property(property="category", type="string", example="investigations"),
     *                     @OA\Property(property="data", type="object", description="Saved data content"),
     *                     @OA\Property(property="metadata", type="object", description="Additional metadata"),
     *                     @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"urgent", "downtown"}, description="Data tags"),
     *                     @OA\Property(property="is_shared", type="boolean", example=false),
     *                     @OA\Property(property="shared_with", type="array", @OA\Items(type="integer"), example={2, 3}, description="User IDs shared with"),
     *                     @OA\Property(property="access_count", type="integer", example=15, description="Number of times accessed"),
     *                     @OA\Property(property="last_accessed", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=45),
     *                 @OA\Property(property="per_page", type="integer", example=20)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Post(
     *     path="/api/v1/saved-data",
     *     summary="Save data",
     *     description="Save data for quick access later",
     *     operationId="saveData",
     *     tags={"Saved Data"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Data to save",
     *         @OA\JsonContent(
     *             required={"name", "type", "data"},
     *             @OA\Property(property="name", type="string", example="Suspicious Activity Search"),
     *             @OA\Property(property="description", type="string", example="Search query for suspicious activities in downtown area"),
     *             @OA\Property(property="type", type="string", enum={"search", "person", "alert", "report", "query"}, example="search"),
     *             @OA\Property(property="category", type="string", example="investigations"),
     *             @OA\Property(property="data", type="object", description="Data content to save"),
     *             @OA\Property(property="metadata", type="object", description="Additional metadata"),
     *             @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"urgent", "downtown"}),
     *             @OA\Property(property="is_shared", type="boolean", example=false),
     *             @OA\Property(property="shared_with", type="array", @OA\Items(type="integer"), example={2, 3})
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Data saved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Data saved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Suspicious Activity Search"),
     *                 @OA\Property(property="description", type="string", example="Search query for suspicious activities in downtown area"),
     *                 @OA\Property(property="type", type="string", example="search"),
     *                 @OA\Property(property="category", type="string", example="investigations"),
     *                 @OA\Property(property="is_shared", type="boolean", example=false),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"The name field is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Get(
     *     path="/api/v1/saved-data/{savedData}",
     *     summary="Get saved data by ID",
     *     description="Retrieve a specific saved data item by its ID",
     *     operationId="getSavedDataById",
     *     tags={"Saved Data"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="savedData",
     *         in="path",
     *         description="Saved Data ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Saved data retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Saved data retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Suspicious Activity Search"),
     *                 @OA\Property(property="description", type="string", example="Search query for suspicious activities in downtown area"),
     *                 @OA\Property(property="type", type="string", example="search"),
     *                 @OA\Property(property="category", type="string", example="investigations"),
     *                 @OA\Property(property="data", type="object", description="Saved data content"),
     *                 @OA\Property(property="metadata", type="object", description="Additional metadata"),
     *                 @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"urgent", "downtown"}),
     *                 @OA\Property(property="is_shared", type="boolean", example=false),
     *                 @OA\Property(property="shared_with", type="array", @OA\Items(type="integer"), example={2, 3}),
     *                 @OA\Property(property="access_count", type="integer", example=15),
     *                 @OA\Property(property="last_accessed", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Saved data not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Saved data not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Put(
     *     path="/api/v1/saved-data/{savedData}",
     *     summary="Update saved data",
     *     description="Update an existing saved data item",
     *     operationId="updateSavedData",
     *     tags={"Saved Data"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="savedData",
     *         in="path",
     *         description="Saved Data ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Updated saved data",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="Updated Suspicious Activity Search"),
     *             @OA\Property(property="description", type="string", example="Updated description"),
     *             @OA\Property(property="category", type="string", example="investigations"),
     *             @OA\Property(property="data", type="object", description="Updated data content"),
     *             @OA\Property(property="metadata", type="object", description="Updated metadata"),
     *             @OA\Property(property="tags", type="array", @OA\Items(type="string"), example={"urgent", "downtown", "updated"}),
     *             @OA\Property(property="is_shared", type="boolean", example=true),
     *             @OA\Property(property="shared_with", type="array", @OA\Items(type="integer"), example={2, 3, 4})
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Saved data updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Saved data updated successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Updated Suspicious Activity Search"),
     *                 @OA\Property(property="description", type="string", example="Updated description"),
     *                 @OA\Property(property="is_shared", type="boolean", example=true),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T11:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Saved data not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Saved data not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function update() {}

    /**
     * @OA\Delete(
     *     path="/api/v1/saved-data/{savedData}",
     *     summary="Delete saved data",
     *     description="Delete an existing saved data item",
     *     operationId="deleteSavedData",
     *     tags={"Saved Data"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="savedData",
     *         in="path",
     *         description="Saved Data ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Saved data deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Saved data deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Saved data not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Saved data not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function destroy() {}
}
