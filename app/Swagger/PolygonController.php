<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Polygons",
 *     description="API endpoints for polygon management and geographic area operations"
 * )
 */

class PolygonController
{
    /**
     * @OA\Get(
     *     path="/api/v1/polygons",
     *     summary="Get polygons",
     *     description="Retrieve list of polygons with pagination",
     *     operationId="getPolygons",
     *     tags={"Polygons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="Filter by polygon type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"zone", "district", "restricted_area", "surveillance_area"}, example="zone")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Polygons retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Polygons retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Baku City Center Zone"),
     *                     @OA\Property(property="description", type="string", example="Central business district of Baku"),
     *                     @OA\Property(property="type", type="string", example="zone"),
     *                     @OA\Property(property="color", type="string", example="#FF5722"),
     *                     @OA\Property(property="opacity", type="number", format="float", example=0.7),
     *                     @OA\Property(property="is_active", type="boolean", example=true),
     *                     @OA\Property(property="area", type="number", format="float", example=15.5, description="Area in square kilometers"),
     *                     @OA\Property(property="perimeter", type="number", format="float", example=18.2, description="Perimeter in kilometers"),
     *                     @OA\Property(property="coordinates_count", type="integer", example=25, description="Number of coordinate points"),
     *                     @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                     @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="meta",
     *                 type="object",
     *                 @OA\Property(property="current_page", type="integer", example=1),
     *                 @OA\Property(property="total", type="integer", example=25),
     *                 @OA\Property(property="per_page", type="integer", example=15)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function index() {}

    /**
     * @OA\Post(
     *     path="/api/v1/polygons",
     *     summary="Create polygon",
     *     description="Create a new polygon area",
     *     operationId="createPolygon",
     *     tags={"Polygons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Polygon data",
     *         @OA\JsonContent(
     *             required={"name", "type", "coordinates"},
     *             @OA\Property(property="name", type="string", example="Baku City Center Zone"),
     *             @OA\Property(property="description", type="string", example="Central business district of Baku"),
     *             @OA\Property(property="type", type="string", enum={"zone", "district", "restricted_area", "surveillance_area"}, example="zone"),
     *             @OA\Property(property="color", type="string", example="#FF5722"),
     *             @OA\Property(property="opacity", type="number", format="float", example=0.7),
     *             @OA\Property(property="is_active", type="boolean", example=true),
     *             @OA\Property(
     *                 property="coordinates",
     *                 type="array",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                     @OA\Property(property="longitude", type="number", format="float", example=49.8671),
     *                     @OA\Property(property="order", type="integer", example=1)
     *                 ),
     *                 description="Array of coordinate points defining the polygon"
     *             ),
     *             @OA\Property(property="metadata", type="object", description="Additional polygon metadata")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Polygon created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=201),
     *             @OA\Property(property="message", type="string", example="Polygon created successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Baku City Center Zone"),
     *                 @OA\Property(property="type", type="string", example="zone"),
     *                 @OA\Property(property="area", type="number", format="float", example=15.5),
     *                 @OA\Property(property="perimeter", type="number", format="float", example=18.2),
     *                 @OA\Property(property="coordinates_count", type="integer", example=25),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="coordinates",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"At least 3 coordinate points are required to form a polygon."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function store() {}

    /**
     * @OA\Get(
     *     path="/api/v1/polygons/{polygon}",
     *     summary="Get polygon by ID",
     *     description="Retrieve a specific polygon by its ID with full coordinate data",
     *     operationId="getPolygonById",
     *     tags={"Polygons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="polygon",
     *         in="path",
     *         description="Polygon ID",
     *         required=true,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Polygon retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Polygon retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="name", type="string", example="Baku City Center Zone"),
     *                 @OA\Property(property="description", type="string", example="Central business district of Baku"),
     *                 @OA\Property(property="type", type="string", example="zone"),
     *                 @OA\Property(property="color", type="string", example="#FF5722"),
     *                 @OA\Property(property="opacity", type="number", format="float", example=0.7),
     *                 @OA\Property(property="is_active", type="boolean", example=true),
     *                 @OA\Property(property="area", type="number", format="float", example=15.5),
     *                 @OA\Property(property="perimeter", type="number", format="float", example=18.2),
     *                 @OA\Property(
     *                     property="coordinates",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                         @OA\Property(property="longitude", type="number", format="float", example=49.8671),
     *                         @OA\Property(property="order", type="integer", example=1)
     *                     )
     *                 ),
     *                 @OA\Property(property="center_point", type="object",
     *                     @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                     @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *                 ),
     *                 @OA\Property(property="bounding_box", type="object",
     *                     @OA\Property(property="north", type="number", format="float", example=40.4200),
     *                     @OA\Property(property="south", type="number", format="float", example=40.3986),
     *                     @OA\Property(property="east", type="number", format="float", example=49.8800),
     *                     @OA\Property(property="west", type="number", format="float", example=49.8542)
     *                 ),
     *                 @OA\Property(property="metadata", type="object", description="Additional polygon metadata"),
     *                 @OA\Property(property="created_at", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                 @OA\Property(property="updated_at", type="string", format="date-time", example="2024-01-15T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Polygon not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Polygon not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function show() {}

    /**
     * @OA\Post(
     *     path="/api/v1/cartesian-distance",
     *     summary="Calculate cartesian distance within polygon",
     *     description="Calculate distances and analyze movement patterns within a polygon area",
     *     operationId="cartesianDistancePolygon",
     *     tags={"Polygons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Distance calculation parameters",
     *         @OA\JsonContent(
     *             required={"polygon_id"},
     *             @OA\Property(property="polygon_id", type="integer", example=1),
     *             @OA\Property(property="start_date", type="string", format="date-time", example="2024-01-15T00:00:00Z"),
     *             @OA\Property(property="end_date", type="string", format="date-time", example="2024-01-15T23:59:59Z"),
     *             @OA\Property(property="person_pins", type="array", @OA\Items(type="string"), example={"1234567", "7654321"}, description="Specific persons to analyze"),
     *             @OA\Property(property="vehicle_plates", type="array", @OA\Items(type="string"), example={"10AB123", "90XY456"}, description="Specific vehicles to analyze"),
     *             @OA\Property(property="analysis_type", type="string", enum={"movement", "density", "patterns", "interactions"}, example="movement"),
     *             @OA\Property(property="time_interval", type="integer", example=3600, description="Time interval for analysis in seconds")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Distance analysis completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Distance analysis completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="polygon_info", type="object",
     *                     @OA\Property(property="id", type="integer", example=1),
     *                     @OA\Property(property="name", type="string", example="Baku City Center Zone"),
     *                     @OA\Property(property="area", type="number", format="float", example=15.5)
     *                 ),
     *                 @OA\Property(property="analysis_period", type="string", example="2024-01-15T00:00:00Z to 2024-01-15T23:59:59Z"),
     *                 @OA\Property(property="total_data_points", type="integer", example=1250),
     *                 @OA\Property(property="unique_entities", type="integer", example=45, description="Number of unique persons/vehicles"),
     *                 @OA\Property(
     *                     property="movement_analysis",
     *                     type="object",
     *                     @OA\Property(property="total_distance_covered", type="number", format="float", example=125.5, description="Total distance in kilometers"),
     *                     @OA\Property(property="average_speed", type="number", format="float", example=15.2, description="Average speed in km/h"),
     *                     @OA\Property(property="max_speed", type="number", format="float", example=65.0),
     *                     @OA\Property(property="entry_points", type="integer", example=8),
     *                     @OA\Property(property="exit_points", type="integer", example=7),
     *                     @OA\Property(property="dwell_time_average", type="integer", example=45, description="Average time spent in polygon in minutes")
     *                 ),
     *                 @OA\Property(
     *                     property="density_analysis",
     *                     type="object",
     *                     @OA\Property(property="peak_density_time", type="string", format="time", example="14:30:00"),
     *                     @OA\Property(property="peak_density_count", type="integer", example=25),
     *                     @OA\Property(property="low_density_time", type="string", format="time", example="03:15:00"),
     *                     @OA\Property(property="average_density", type="number", format="float", example=8.5)
     *                 ),
     *                 @OA\Property(
     *                     property="hotspots",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="location", type="object",
     *                             @OA\Property(property="latitude", type="number", format="float", example=40.4093),
     *                             @OA\Property(property="longitude", type="number", format="float", example=49.8671)
     *                         ),
     *                         @OA\Property(property="activity_score", type="number", format="float", example=0.85),
     *                         @OA\Property(property="visit_count", type="integer", example=45),
     *                         @OA\Property(property="average_dwell_time", type="integer", example=25, description="Minutes")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Polygon not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=404),
     *             @OA\Property(property="message", type="string", example="Polygon not found")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function cartesianDistancePolygon() {}

    /**
     * @OA\Post(
     *     path="/api/v1/cartesian-distance-export",
     *     summary="Export cartesian distance analysis",
     *     description="Export polygon distance analysis results to various formats",
     *     operationId="cartesianDistancePolygonExport",
     *     tags={"Polygons"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Export parameters",
     *         @OA\JsonContent(
     *             required={"polygon_id", "format"},
     *             @OA\Property(property="polygon_id", type="integer", example=1),
     *             @OA\Property(property="format", type="string", enum={"excel", "csv", "pdf", "json"}, example="excel"),
     *             @OA\Property(property="start_date", type="string", format="date-time", example="2024-01-15T00:00:00Z"),
     *             @OA\Property(property="end_date", type="string", format="date-time", example="2024-01-15T23:59:59Z"),
     *             @OA\Property(property="include_charts", type="boolean", example=true),
     *             @OA\Property(property="include_raw_data", type="boolean", example=false)
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Export completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Export completed successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="export_id", type="string", example="EXP123456789"),
     *                 @OA\Property(property="download_url", type="string", example="https://api.beein.az/downloads/polygon_analysis_123456789.xlsx"),
     *                 @OA\Property(property="file_size", type="integer", example=2048576, description="File size in bytes"),
     *                 @OA\Property(property="expires_at", type="string", format="date-time", example="2024-01-22T10:30:00Z")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function cartesianDistancePolygonExport() {}
}
