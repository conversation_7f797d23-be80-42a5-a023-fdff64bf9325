<?php

namespace App\Swagger;

/**
 * @OA\Tag(
 *     name="Social Media",
 *     description="API endpoints for social media search and monitoring"
 * )
 */

class SocialMediaController
{
    /**
     * @OA\Post(
     *     path="/api/v1/search-facebook",
     *     summary="Search Facebook profiles",
     *     description="Search for Facebook profiles using various criteria",
     *     operationId="searchFacebook",
     *     tags={"Social Media"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Facebook search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", example="<PERSON> Doe", description="Full name or partial name"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>", description="Email address"),
     *             @OA\Property(property="phone", type="string", example="994501234567", description="Phone number"),
     *             @OA\Property(property="location", type="string", example="Baku, Azerbaijan", description="Location/city"),
     *             @OA\Property(property="age_range", type="object",
     *                 @OA\Property(property="min", type="integer", example=25),
     *                 @OA\Property(property="max", type="integer", example=35)
     *             ),
     *             @OA\Property(property="education", type="string", example="Baku State University", description="Educational institution"),
     *             @OA\Property(property="workplace", type="string", example="Beein Technologies", description="Current or past workplace"),
     *             @OA\Property(property="relationship_status", type="string", enum={"single", "married", "divorced", "widowed"}, example="married"),
     *             @OA\Property(property="profile_picture_search", type="boolean", example=false, description="Include profile picture analysis")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Facebook search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Facebook search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total_results", type="integer", example=15),
     *                 @OA\Property(property="search_time", type="number", format="float", example=2.5),
     *                 @OA\Property(
     *                     property="profiles",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="facebook_id", type="string", example="100012345678901"),
     *                         @OA\Property(property="profile_url", type="string", example="https://facebook.com/john.doe"),
     *                         @OA\Property(property="display_name", type="string", example="John Doe"),
     *                         @OA\Property(property="profile_picture", type="string", example="https://facebook.com/profile_pic.jpg"),
     *                         @OA\Property(property="location", type="string", example="Baku, Azerbaijan"),
     *                         @OA\Property(property="work", type="string", example="Software Engineer at Beein Technologies"),
     *                         @OA\Property(property="education", type="string", example="Baku State University"),
     *                         @OA\Property(property="relationship_status", type="string", example="Married"),
     *                         @OA\Property(property="friends_count", type="integer", example=250),
     *                         @OA\Property(property="mutual_friends", type="integer", example=5),
     *                         @OA\Property(property="last_activity", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                         @OA\Property(property="account_created", type="string", format="date", example="2015-03-20"),
     *                         @OA\Property(property="privacy_level", type="string", enum={"public", "friends", "private"}, example="friends"),
     *                         @OA\Property(property="verification_status", type="boolean", example=false),
     *                         @OA\Property(property="match_confidence", type="number", format="float", example=0.85, description="Confidence score for the match")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="name",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"At least one search criteria is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchFacebook() {}

    /**
     * @OA\Post(
     *     path="/api/v1/search-instagram",
     *     summary="Search Instagram profiles",
     *     description="Search for Instagram profiles using various criteria",
     *     operationId="searchInstagram",
     *     tags={"Social Media"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Instagram search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="username", type="string", example="johndoe123", description="Instagram username"),
     *             @OA\Property(property="full_name", type="string", example="John Doe", description="Full name"),
     *             @OA\Property(property="bio_keywords", type="array", @OA\Items(type="string"), example={"developer", "baku", "photography"}, description="Keywords to search in bio"),
     *             @OA\Property(property="location", type="string", example="Baku, Azerbaijan", description="Location tags"),
     *             @OA\Property(property="hashtags", type="array", @OA\Items(type="string"), example={"#baku", "#azerbaijan", "#tech"}, description="Hashtags to search"),
     *             @OA\Property(property="follower_range", type="object",
     *                 @OA\Property(property="min", type="integer", example=100),
     *                 @OA\Property(property="max", type="integer", example=10000)
     *             ),
     *             @OA\Property(property="account_type", type="string", enum={"personal", "business", "creator"}, example="personal"),
     *             @OA\Property(property="verified_only", type="boolean", example=false, description="Search only verified accounts")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Instagram search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Instagram search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total_results", type="integer", example=8),
     *                 @OA\Property(property="search_time", type="number", format="float", example=1.8),
     *                 @OA\Property(
     *                     property="profiles",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="instagram_id", type="string", example="123456789"),
     *                         @OA\Property(property="username", type="string", example="johndoe123"),
     *                         @OA\Property(property="full_name", type="string", example="John Doe"),
     *                         @OA\Property(property="profile_picture", type="string", example="https://instagram.com/profile_pic.jpg"),
     *                         @OA\Property(property="bio", type="string", example="Software Developer from Baku 🇦🇿"),
     *                         @OA\Property(property="website", type="string", example="https://johndoe.dev"),
     *                         @OA\Property(property="followers_count", type="integer", example=1250),
     *                         @OA\Property(property="following_count", type="integer", example=350),
     *                         @OA\Property(property="posts_count", type="integer", example=89),
     *                         @OA\Property(property="account_type", type="string", example="personal"),
     *                         @OA\Property(property="is_verified", type="boolean", example=false),
     *                         @OA\Property(property="is_private", type="boolean", example=false),
     *                         @OA\Property(property="last_post_date", type="string", format="date-time", example="2024-01-14T15:30:00Z"),
     *                         @OA\Property(property="account_created", type="string", format="date", example="2018-05-15"),
     *                         @OA\Property(property="match_confidence", type="number", format="float", example=0.92)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="username",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"At least one search criteria is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchInstagram() {}

    /**
     * @OA\Post(
     *     path="/api/v1/search-telegram",
     *     summary="Search Telegram profiles",
     *     description="Search for Telegram profiles and channels using various criteria",
     *     operationId="searchTelegram",
     *     tags={"Social Media"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Telegram search criteria",
     *         @OA\JsonContent(
     *             @OA\Property(property="username", type="string", example="johndoe", description="Telegram username (without @)"),
     *             @OA\Property(property="phone_number", type="string", example="994501234567", description="Phone number"),
     *             @OA\Property(property="display_name", type="string", example="John Doe", description="Display name"),
     *             @OA\Property(property="bio_keywords", type="array", @OA\Items(type="string"), example={"developer", "baku"}, description="Keywords in bio"),
     *             @OA\Property(property="search_type", type="string", enum={"users", "channels", "groups", "all"}, example="users", description="Type of search"),
     *             @OA\Property(property="language", type="string", example="az", description="Content language"),
     *             @OA\Property(property="member_range", type="object",
     *                 @OA\Property(property="min", type="integer", example=10),
     *                 @OA\Property(property="max", type="integer", example=50000)
     *             ),
     *             @OA\Property(property="last_seen", type="string", enum={"recently", "within_week", "within_month", "long_time_ago"}, example="recently")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Telegram search completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="status", type="integer", example=200),
     *             @OA\Property(property="message", type="string", example="Telegram search completed"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total_results", type="integer", example=12),
     *                 @OA\Property(property="search_time", type="number", format="float", example=1.2),
     *                 @OA\Property(
     *                     property="results",
     *                     type="array",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="telegram_id", type="string", example="123456789"),
     *                         @OA\Property(property="username", type="string", example="johndoe"),
     *                         @OA\Property(property="display_name", type="string", example="John Doe"),
     *                         @OA\Property(property="phone_number", type="string", example="994501234567"),
     *                         @OA\Property(property="bio", type="string", example="Software Developer from Baku"),
     *                         @OA\Property(property="profile_photo", type="string", example="https://telegram.org/photo.jpg"),
     *                         @OA\Property(property="type", type="string", enum={"user", "channel", "group"}, example="user"),
     *                         @OA\Property(property="is_verified", type="boolean", example=false),
     *                         @OA\Property(property="is_premium", type="boolean", example=false),
     *                         @OA\Property(property="last_seen", type="string", format="date-time", example="2024-01-15T10:30:00Z"),
     *                         @OA\Property(property="member_count", type="integer", nullable=true, example=null, description="For channels/groups"),
     *                         @OA\Property(property="language", type="string", example="az"),
     *                         @OA\Property(property="match_confidence", type="number", format="float", example=0.88)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=422),
     *             @OA\Property(property="message", type="string", example="Validation Error"),
     *             @OA\Property(
     *                 property="errors",
     *                 type="object",
     *                 @OA\Property(
     *                     property="username",
     *                     type="array",
     *                     @OA\Items(type="string"),
     *                     example={"At least one search criteria is required."}
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=false),
     *             @OA\Property(property="status", type="integer", example=401),
     *             @OA\Property(property="message", type="string", example="Unauthorized")
     *         )
     *     )
     * )
     */
    public function searchTelegram() {}
}
