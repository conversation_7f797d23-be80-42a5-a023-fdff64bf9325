<?php

namespace App\Swagger;

use App\Http\Controllers\Controller;
use App\Http\Requests\LocationRequests\FindCarRequest;
use App\Http\Requests\LocationRequests\FindIntervalSimilarityRequest;
use App\Http\Requests\LocationRequests\FindLocationRequest;
use App\Http\Requests\LocationRequests\FindMeetingPlacesRequest;
use App\Http\Requests\LocationRequests\FindPhoneSimilarityRequest;
use App\Http\Requests\LocationRequests\NativeAndStrangersRequest;
use App\Http\Requests\LocationRequests\SearchByAreaRequest;
use App\Services\PreLocationService;
use App\Traits\ApiResponsible;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class PreLocationController extends Controller
{
    use ApiResponsible;
    private PreLocationService $preLocationService;

    public function __construct(PreLocationService $preLocationService){
        $this->preLocationService = $preLocationService;
    }
    /**
     * @OA\Post(
     *     path="pre-location/find-location",
     *     tags={"Location"},
     *     summary="Find Location by Phone Number",
     *     description="Finds locations based on a phone number, date range, and optional polygon area.",
     *     operationId="findLocation",
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"phone_number", "start_time", "end_time", "radius"},
     *             @OA\Property(
     *                 property="phone_number",
     *                 type="string",
     *                 description="The phone number to search for."
     *             ),
     *             @OA\Property(
     *                 property="start_time",
     *                 type="string",
     *                 format="date-time",
     *                 description="Start time for the search period."
     *             ),
     *             @OA\Property(
     *                 property="end_time",
     *                 type="string",
     *                 format="date-time",
     *                 description="End time for the search period."
     *             ),
     *             @OA\Property(
     *                 property="radius",
     *                 type="number",
     *                 description="Search radius in meters."
     *             ),
     *             @OA\Property(
     *                 property="polygon",
     *                 type="array",
     *                 description="Optional polygon area to refine the search.",
     *                 nullable=true,
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of a point in the polygon."),
     *                     @OA\Property(property="lng", type="number", description="Longitude of a point in the polygon.")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="work_name",
     *                 type="string",
     *                 description="Optional work name."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Location search request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the location search service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during search",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="message", type="string", description="Detailed error message")
     *         )
     *     )
     * )
     */

    public function findLocation(FindLocationRequest $request): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/find-car",
     *     tags={"Location"},
     *     summary="Find Car by License Plate",
     *     description="Finds a car based on its license plate number, date range, and optional polygon area.",
     *     operationId="findCar",
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"car_number", "start", "end", "response_count", "small_radius", "big_radius", "delta_time"},
     *             @OA\Property(
     *                 property="car_number",
     *                 type="string",
     *                 description="The car's license plate number."
     *             ),
     *             @OA\Property(
     *                 property="start",
     *                 type="string",
     *                 format="date-time",
     *                 description="Start time for the search period."
     *             ),
     *             @OA\Property(
     *                 property="end",
     *                 type="string",
     *                 format="date-time",
     *                 description="End time for the search period."
     *             ),
     *             @OA\Property(
     *                 property="response_count",
     *                 type="integer",
     *                 description="The number of results to retrieve."
     *             ),
     *             @OA\Property(
     *                 property="small_radius",
     *                 type="number",
     *                 description="Small search radius in meters."
     *             ),
     *             @OA\Property(
     *                 property="big_radius",
     *                 type="number",
     *                 description="Big search radius in meters."
     *             ),
     *             @OA\Property(
     *                 property="delta_time",
     *                 type="integer",
     *                 description="Time difference threshold in seconds."
     *             ),
     *             @OA\Property(
     *                 property="polygon",
     *                 type="array",
     *                 description="Optional polygon area to refine the search.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of a point in the polygon."),
     *                     @OA\Property(property="lng", type="number", description="Longitude of a point in the polygon.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Car search request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the car search service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during search",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="msg", type="string", description="Detailed error message")
     *         )
     *     )
     * )
     */

    public function findCar(FindCarRequest $request): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/find-meeting-places",
     *     tags={"Location"},
     *     summary="Find Meeting Places by Phone Numbers",
     *     description="Finds meeting places based on provided phone numbers, date range, and optional polygon area.",
     *     operationId="findMeetingPlaces",
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"number1", "number2", "start", "end", "radius", "delta_time"},
     *             @OA\Property(
     *                 property="number1",
     *                 type="integer",
     *                 description="The first phone number to search for."
     *             ),
     *             @OA\Property(
     *                 property="number2",
     *                 type="array",
     *                 @OA\Items(type="integer"),
     *                 description="An array of additional phone numbers to search for."
     *             ),
     *             @OA\Property(
     *                 property="start",
     *                 type="string",
     *                 format="date-time",
     *                 description="Start time for the search period."
     *             ),
     *             @OA\Property(
     *                 property="end",
     *                 type="string",
     *                 format="date-time",
     *                 description="End time for the search period."
     *             ),
     *             @OA\Property(
     *                 property="radius",
     *                 type="number",
     *                 description="Search radius in kilometers."
     *             ),
     *             @OA\Property(
     *                 property="delta_time",
     *                 type="integer",
     *                 description="Time difference threshold in seconds."
     *             ),
     *             @OA\Property(
     *                 property="break_time",
     *                 type="integer",
     *                 description="Break time in seconds, defaults to 800."
     *             ),
     *             @OA\Property(
     *                 property="polygon",
     *                 type="array",
     *                 description="Optional polygon area to refine the search.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of a point in the polygon."),
     *                     @OA\Property(property="lng", type="number", description="Longitude of a point in the polygon.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Meeting places search request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the meeting places search service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during search",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="message", type="string", description="Detailed error message"),
     *             @OA\Property(property="line", type="string", description="Error line number and file")
     *         )
     *     )
     * )
     */

    public function findMeetingPlaces(FindMeetingPlacesRequest $request): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/find-interval-similarity",
     *     tags={"Location"},
     *     summary="Find Interval Similarity",
     *     description="Finds interval similarity based on provided points or polygons.",
     *     operationId="findIntervalSimilarity",
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"response_count", "min_match"},
     *             @OA\Property(
     *                 property="response_count",
     *                 type="integer",
     *                 description="Number of responses to return."
     *             ),
     *             @OA\Property(
     *                 property="min_match",
     *                 type="integer",
     *                 description="Minimum matches required."
     *             ),
     *             @OA\Property(
     *                 property="points",
     *                 type="array",
     *                 description="Array of points to check for interval similarity.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of the point."),
     *                     @OA\Property(property="lon", type="number", description="Longitude of the point."),
     *                     @OA\Property(property="radius", type="number", description="Radius in kilometers.")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="polygons",
     *                 type="array",
     *                 description="Array of polygons to check for interval similarity.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="polygon", type="array",
     *                         @OA\Items(
     *                             type="object",
     *                             @OA\Property(property="lat", type="number", description="Latitude of a polygon point."),
     *                             @OA\Property(property="lng", type="number", description="Longitude of a polygon point.")
     *                         )
     *                     ),
     *                     @OA\Property(property="start", type="string", format="date-time", description="Start time of the polygon."),
     *                     @OA\Property(property="end", type="string", format="date-time", description="End time of the polygon."),
     *                     @OA\Property(property="radius", type="number", description="Radius in kilometers.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Interval similarity search request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the interval similarity search service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during search",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="Validation error message")
     *         )
     *     )
     * )
     */

    public function findIntervalSimilarity(FindIntervalSimilarityRequest $request): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/phone-similarity/{pin}",
     *     tags={"Location"},
     *     summary="Get Social Call Location with Others",
     *     description="Retrieves social call locations based on the provided phone number and date range.",
     *     operationId="getSocialCallLocationWithOthersNew",
     *     security={{ "bearerAuth": {} }},
     *     @OA\Parameter(
     *         name="pin",
     *         in="path",
     *         required=false,
     *         description="Phone number in the format 994XXXXXXXXX.",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"pin"},
     *             @OA\Property(
     *                 property="pin",
     *                 type="string",
     *                 description="Phone number in the format 994XXXXXXXXX."
     *             ),
     *             @OA\Property(
     *                 property="from",
     *                 type="string",
     *                 format="date-time",
     *                 description="Start date for the search."
     *             ),
     *             @OA\Property(
     *                 property="to",
     *                 type="string",
     *                 format="date-time",
     *                 description="End date for the search."
     *             ),
     *             @OA\Property(
     *                 property="top",
     *                 type="integer",
     *                 description="Number of top results to return."
     *             ),
     *             @OA\Property(
     *                 property="min_radius",
     *                 type="number",
     *                 description="Minimum radius in meters."
     *             ),
     *             @OA\Property(
     *                 property="max_radius",
     *                 type="number",
     *                 description="Maximum radius in meters."
     *             ),
     *             @OA\Property(
     *                 property="delta_time",
     *                 type="integer",
     *                 description="Delta time for processing."
     *             ),
     *             @OA\Property(
     *                 property="polygon",
     *                 type="array",
     *                 description="Polygon for location filtering.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of the polygon point."),
     *                     @OA\Property(property="lng", type="number", description="Longitude of the polygon point.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful search request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the social call location search service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during search",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="msg", type="string", description="Detailed error message")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="Validation error message")
     *         )
     *     )
     * )
     */

    public function getSocialCallLocationWithOthersNew(FindPhoneSimilarityRequest $request, string $pin = null): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/search-by-area",
     *     tags={"Location"},
     *     summary="Search by Area",
     *     description="Retrieves location data within a specified area defined by a polygon and time range.",
     *     operationId="searchByArea",
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"start_time", "end_time"},
     *             @OA\Property(
     *                 property="start_time",
     *                 type="string",
     *                 format="date-time",
     *                 description="Start time for the search."
     *             ),
     *             @OA\Property(
     *                 property="end_time",
     *                 type="string",
     *                 format="date-time",
     *                 description="End time for the search."
     *             ),
     *             @OA\Property(
     *                 property="polygon",
     *                 type="array",
     *                 description="Polygon for area filtering.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of the polygon point."),
     *                     @OA\Property(property="lng", type="number", description="Longitude of the polygon point.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful search request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the area search service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during search",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="msg", type="string", description="Detailed error message")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="Validation error message")
     *         )
     *     )
     * )
     */

    public function searchByArea(SearchByAreaRequest $request): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/phone-trajectory",
     *     tags={"Location"},
     *     summary="Get Social Call Location Info",
     *     description="Retrieves trajectory information for a given phone number within a specified date range.",
     *     operationId="getSocialCallLocationInfo",
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"pin"},
     *             @OA\Property(
     *                 property="pin",
     *                 type="array",
     *                 @OA\Items(type="integer", description="Phone numbers in 994XXXXXXXXX format.")
     *             ),
     *             @OA\Property(
     *                 property="from",
     *                 type="string",
     *                 format="date-time",
     *                 description="Start date for the search."
     *             ),
     *             @OA\Property(
     *                 property="to",
     *                 type="string",
     *                 format="date-time",
     *                 description="End date for the search."
     *             ),
     *             @OA\Property(
     *                 property="per_page",
     *                 type="integer",
     *                 description="Number of results per page."
     *             ),
     *             @OA\Property(
     *                 property="page",
     *                 type="integer",
     *                 description="Page number for pagination."
     *             ),
     *             @OA\Property(
     *                 property="polygon",
     *                 type="array",
     *                 description="Polygon for area filtering.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of the polygon point."),
     *                     @OA\Property(property="lng", type="number", description="Longitude of the polygon point.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the location service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during retrieval",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="msg", type="string", description="Detailed error message")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="Validation error message")
     *         )
     *     )
     * )
     */

    public function getSocialCallLocationInfo(Request $request): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/native-and-strangers",
     *     tags={"Location"},
     *     summary="Find Native and Strangers",
     *     description="Retrieves information about natives and strangers within a specified area and time range.",
     *     operationId="nativeAndStrangers",
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"start_time", "end_time", "type", "frequency_count"},
     *             @OA\Property(
     *                 property="point",
     *                 type="array",
     *                 @OA\Items(type="number", description="Coordinates for point filtering (longitude and latitude).")
     *             ),
     *             @OA\Property(
     *                 property="polygon",
     *                 type="array",
     *                 description="Polygon for area filtering.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of the polygon point."),
     *                     @OA\Property(property="lng", type="number", description="Longitude of the polygon point.")
     *                 )
     *             ),
     *             @OA\Property(
     *                 property="radius",
     *                 type="number",
     *                 description="Search radius in meters."
     *             ),
     *             @OA\Property(
     *                 property="type",
     *                 type="string",
     *                 description="Type of search (e.g., native, stranger)."
     *             ),
     *             @OA\Property(
     *                 property="frequency_count",
     *                 type="integer",
     *                 description="Count of frequencies to search."
     *             ),
     *             @OA\Property(
     *                 property="start_time",
     *                 type="string",
     *                 format="date-time",
     *                 description="Start time for the search."
     *             ),
     *             @OA\Property(
     *                 property="end_time",
     *                 type="string",
     *                 format="date-time",
     *                 description="End time for the search."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the location service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during retrieval",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="msg", type="string", description="Detailed error message")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="Validation error message")
     *         )
     *     )
     * )
     */

    public function nativeAndStrangers(NativeAndStrangersRequest $request): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/find-car-trajectory",
     *     tags={"Location"},
     *     summary="Get Vehicle Entry Information",
     *     description="Retrieves vehicle entry information based on car number and a specified time range.",
     *     operationId="getVehicleEntered",
     *     security={{ "bearerAuth": {} }},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"carNumber", "from", "to"},
     *             @OA\Property(
     *                 property="carNumber",
     *                 type="string",
     *                 description="The car number to search for."
     *             ),
     *             @OA\Property(
     *                 property="from",
     *                 type="string",
     *                 format="date-time",
     *                 description="Start time for the search."
     *             ),
     *             @OA\Property(
     *                 property="to",
     *                 type="string",
     *                 format="date-time",
     *                 description="End time for the search."
     *             ),
     *             @OA\Property(
     *                 property="polygon",
     *                 type="array",
     *                 description="Polygon for area filtering.",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="lat", type="number", description="Latitude of the polygon point."),
     *                     @OA\Property(property="lng", type="number", description="Longitude of the polygon point.")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful request ID",
     *         @OA\JsonContent(
     *             @OA\Property(
     *                 property="searchId",
     *                 type="string",
     *                 description="The search ID returned from the location service."
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Error during retrieval",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="msg", type="string", description="Detailed error message")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="Validation error message")
     *         )
     *     )
     * )
     */
    public function getVehicleEntered(Request $request): JsonResponse
    {

    }

    /**
     * @OA\Get(
     *     path="/pre-location/check-status",
     *     tags={"Location"},
     *     summary="Check Status of a Search Request",
     *     description="Checks the status of a previous search request using its search ID.",
     *     operationId="checkStatus",
     *     security={{ "bearerAuth": {} }},
     *     @OA\Parameter(
     *         name="searchID",
     *         in="query",
     *         required=true,
     *         description="The search ID of the request to check.",
     *         @OA\Schema(
     *             type="string"
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful response with status information",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", description="Current status of the search request."),
     *             @OA\Property(property="message", type="string", description="Additional message related to the status.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Search ID not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", description="Error message"),
     *             @OA\Property(property="msg", type="string", description="Detailed error message")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", description="Validation error message")
     *         )
     *     )
     * )
     */

    public function checkStatus(Request $request): JsonResponse
    {

    }
}
