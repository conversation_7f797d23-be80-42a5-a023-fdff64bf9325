<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use MongoDB\BSON\UTCDateTime;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;
use Jensse<PERSON>\Mongodb\Eloquent\SoftDeletes;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class BlacklistSimilarly extends Model
{
    use HasFactory, SoftDeletes, HybridRelations;

    /**
     * @var string $connection
     */
    protected $connection = 'mongodb';

    /**
     * @var string $table
     */
    protected $table = 'blacklist_similarlies';

    /**
     * @var string[] $fillable
     */
    protected $guarded = ['_id'];

    protected $fillable = ["_id", "bucket_name", "face_name", "frame_name",
        "camera_name", "date", "blacklist_id", "name", "surname", "similarity"];


    protected $dates = [
        'birthdate'
    ];

//    public function getBirthdateAttribute($value): string
//    {
//        return $value ? Carbon::parse($value)->format('d/m/Y'): '';
//    }

    /**
     * @param $query
     * @param $name
     * @return mixed
     */
    public function scopeName($query, $name): mixed
    {
        return $query->when($name, function ($query, $name) {
            $query->where('name', 'like', '%' . $name . '%');
        });
    }

    /**
     * @param $query
     * @param $surname
     * @return mixed
     */
    public function scopeSurname($query, $surname): mixed
    {
        return $query->when($surname, function ($query, $surname) {
            $query->where('surname', 'like', '%' . $surname . '%');
        });
    }

    /**
     * @param $query
     * @param $fatherName
     * @return mixed
     */
    public function scopeFatherName($query, $fatherName): mixed
    {
        return $query->when($fatherName, function ($query, $fatherName) {
            $query->where('father_name', 'like', '%' . $fatherName . '%');
        });
    }

    /**
     * @param $query
     * @param $birthdate
     * @return mixed
     */
    public function scopeBirthdate($query, $birthdate): mixed
    {
        return $query->when($birthdate, function ($query, $birthdate) {
            $query->where('birthdate', 'like', '%' . $birthdate . '%');
        });
    }

    /**
     * @param $query
     * @param $pin
     * @return mixed
     */
    public function scopePin($query, $pin): mixed
    {
        return $query->when($pin, function ($query, $pin) {
            return $query->where('pin', $pin);
        });
    }

    /**
     * @param $query
     * @param $gender
     * @return mixed
     */
    public function scopeGender($query, $gender): mixed
    {
        return $query->when($gender, function ($query, $gender) {
            return $query->where('gender', (int) $gender);
        });
    }

    /**
     * @param $query
     * @param $alignmentError
     * @return mixed
     */
    public function scopeAlignmentError($query, $alignmentError): mixed
    {
        return $query->when($alignmentError, function ($query, $alignmentError) {
            return $query->where('alignment_error', $alignmentError);
        });
    }

    /**
     * @param $query
     * @param $cameraIds
     * @return mixed
     */
    public function scopeCameras($query, $cameraIds): mixed
    {
        return $query->when($cameraIds, function ($query, $cameraIds) {
            return $query->whereIn('camera_id', $cameraIds);
        });
    }

    /**
     * @param $query
     * @param null $fromDate
     * @param null $toDate
     * @return mixed
     * @throws \Exception
     */
    public function scopeBetweenDate($query, $fromDate = null, $toDate = null): mixed
    {
        return $query->when(isset($fromDate, $toDate), function ($query) use ($fromDate, $toDate) {
            return $query->whereBetween('created_at', [
                new UTCDateTime(Carbon::parse($fromDate)),
                new UTCDateTime(Carbon::parse($toDate))
            ]);
        });
    }

    public function blacklist(): BelongsTo|\Jenssegers\Mongodb\Relations\BelongsTo
    {
        return $this->belongsTo(Blacklist::class, 'blacklist_id', 'id');
    }
}
