<?php

namespace App\Models;

use App\Traits\SearchTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @method static create(string[] $array)
 * @method static where(string $string, string $permission)
 * @method whereNull(string $string)
 * @method select(string $string, string $string1, string $string2, string $string3)
 * @method get()
 * @method search(string $string, mixed $get)
 * @method orSearch(string $string, mixed $get)
 * @method static findOrFail(int $id)
 * @method orderBy(string $string, string $string1)
 */
class Permission extends \Spatie\Permission\Models\Permission
{
    use HasFactory,SearchTrait;

    /**
     * @var string $table
     */
    protected $table = 'permissions';



  //  protected $with = ['parent'];

    /**
     * @var string[] $fillable
     */
    protected $fillable = [
        'id',
        'parent_id',
        'name',
        'translation_name',
        'guard_name'
    ];

    /**
     * @var string[] $hidden
     */
    protected $hidden = ['guard_name'];

    /**
     * @var string[] $casts
     */
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];


    public function parent(): BelongsTo
    {
        return $this->belongsTo(Permission::class, 'parent_id', 'id');
    }

    public function child(): HasMany
    {
        return $this->hasMany(Permission::class, 'parent_id', 'id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(Permission::class, 'parent_id', 'id');
    }

}
