<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;
use Jenssegers\Mongodb\Helpers\QueriesRelationships;
use MongoDB\BSON\UTCDateTime;

/**
 * @method static create(array $userData)
 */
class LogHistory extends Model
{
    use HybridRelations, QueriesRelationships;

    /**
     * @var string[] $guarded
     */
    protected $guarded = ['_id'];

    /**
     * @var string $connection
     */
    protected $connection = 'mongodb';

    /**
     * @var string $table
     */
    protected $table = 'log_history';

    public $timestamps = false;
}
