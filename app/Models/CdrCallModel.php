<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CdrCallModel extends Model
{
    use HasFactory;
    protected $connection = 'clickhouse';

    protected $table;

    protected $keyType = 'string';


    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if (in_array(config('app.env'), ['development', 'local'])) {
            $this->table = 'packet_app.calls_dist';
        } else {
            $this->table = 'calls_dist';
        }
    }

    protected $casts = [
        'attributes' => 'json',
    ];
}
