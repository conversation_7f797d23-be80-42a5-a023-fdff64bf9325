<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\Permission\Traits\HasRoles;

/**
 * @method static create(string[] $array)
 */
class Role extends \Spatie\Permission\Models\Role
{
    use HasFactory, HasRoles;


    protected $connection =  'pgsql';


  //  public string $guard_name = 'api';

    /**
     * @var string $table
     */
    protected $table = 'roles';

    /**
     * @var string[] $fillable
     */
    protected $fillable = [
        'id',
        'name',
        'guard_name'
    ];

    /**
     * @var string[] $casts
     */
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
        'updated_at' => 'datetime:Y-m-d H:i:s',
    ];


    /**
     * @var string[] $hidden
     */
    protected $hidden = ['guard_name'];
}
