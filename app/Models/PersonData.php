<?php


namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
use Jen<PERSON>gers\Mongodb\Eloquent\Model;
use Jenssegers\Mongodb\Helpers\QueriesRelationships;

/**
 * @property mixed $_id
 * @property mixed $data
 */
class PersonData extends Model
{
    use HybridRelations, QueriesRelationships;

    /**
     * @var string[] $guarded
     */
    protected $guarded = ['_id'];

    /**
     * @var string $connection
     */
    protected $connection = 'mongodb';

    /**
     * @var string $table
     */
    protected $table = 'person_data';

    public function setPinAttribute($value): void
    {
        $this->attributes['pin'] = strtoupper($value);
    }
}
