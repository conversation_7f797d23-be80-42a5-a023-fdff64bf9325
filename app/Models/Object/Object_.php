<?php

namespace App\Models\Object;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Jen<PERSON>gers\Mongodb\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;

class Object_ extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * @var string $table
     */
    protected $table = 'objects';

    /**
     * @var string[] $fillable
     */
    protected $fillable = [
        'object_type_id',
        'name',
        'address',
        'gps',
        'active',
        'parent_id'
    ];

    /**
     * @var string[] $casts
     */
    protected $casts = [
        'gps' => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function type(): BelongsTo
    {
        return $this->belongsTo(ObjectType::class, 'object_type_id');
    }


    /**
     * @return hasMany
     */
    public  function children(): HasMany {

        return $this->hasMany(Object_::class, 'parent_id', 'id');
    }


    /**
     * @return BelongsTo
     */
    public  function parent(): BelongsTo
    {
        return $this->belongsTo(Object_::class, 'parent_id');
    }


    public function getAllChildren ()
    {
        $sections = new Collection();

        foreach ($this->children as $section) {
            $sections->push($section);
            $sections = $sections->merge($section->getAllChildren());
        }

        return $sections;
    }

}
