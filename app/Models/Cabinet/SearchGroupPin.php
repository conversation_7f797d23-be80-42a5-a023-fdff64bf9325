<?php

namespace App\Models\Cabinet;

use App\Models\Person;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class SearchGroupPin extends Model
{
    use HasFactory, SoftDeletes;

    protected $connection = 'pgsql';

    protected $guarded = ['id'];

    public function group(): BelongsTo
    {
        return $this->belongsTo(SearchGroup::class, 'group_id', 'id');
    }

    public function person(): BelongsTo
    {
        return $this->belongsTo(Person::class, 'pin', 'pin');
    }
}
