<?php

namespace App\Models\Cabinet;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class SearchGroup extends Model
{
    use HasFactory, SoftDeletes;

    protected $connection = 'pgsql';

    protected $guarded = ['id'];

    public function groupPins(): HasMany
    {
        return $this->hasMany(SearchGroupPin::class, 'group_id', 'id');
    }
}
