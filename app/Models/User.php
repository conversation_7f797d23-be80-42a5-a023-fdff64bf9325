<?php

namespace App\Models;

use App\Models\Social\BookMark;
use App\Traits\SearchTrait;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasPermissions;
use Spatie\Permission\Traits\HasRoles;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;
use Illuminate\Support\Facades\Cache;

/**
 * @property mixed $roles
 * @property array|mixed $permissions
 * @property mixed $id
 * @property mixed $password
 * @property mixed $status
 * @property mixed $is_system
 * @property bool|mixed $gray_list
 * @property mixed $name
 * @property mixed $surname
 * @property mixed $hierarchies
 */
class User extends Authenticatable implements JWTSubject
{
    protected $connection = 'pgsql';
    use HasA<PERSON>Token<PERSON>, HasFactory, Notifiable, HasRoles, SearchTrait;

    /**
     * @var string $guard_name
     */
    protected string $guard_name = 'api';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'surname',
        'email',
        'password',
        'entity_name',
        'phone_number',
        'status',
        'is_system',
//        'is_full_access'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
//        'is_full_access' => 'boolean'
    ];


    protected $appends = [
        'gray_list',
        'tester_list',
        'profile_view',
        'root_user'
    ];

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier(): mixed
    {
        return $this->getKey();
    }
    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims(): array
    {
        return [];
    }


    public function getGrayListAttribute(): bool
    {
        $this->setRelation('permissions', $this->getAllPermissions());
        return $this->checkPermissionTo('permission:users:gray_list', 'api');
    }
    public function getTesterListAttribute(): bool
    {
        $this->setRelation('permissions', $this->getAllPermissions());
        return $this->checkPermissionTo('permission:users:tester-assign', 'api');
    }
    public function getProfileViewAttribute(): bool
    {
        $this->setRelation('permissions', $this->getAllPermissions());
        return $this->checkPermissionTo('permission:profile-view', 'api');
    }

    public function getRootUserAttribute(): bool
    {
        return (bool)$this->is_system;
    }

    public function mobile_devices(): HasMany
    {
        return $this->hasMany(UserMobileDevice::class);
    }

    public function bookmarks(): HasMany
    {
        return $this->hasMany(Bookmark::class);
    }

    public function bookmarksOfType($type): MorphToMany
    {
        return $this->morphedByMany($type, 'bookmarkable', 'bookmarks');
    }

//    protected static function booted(): void
//    {
//        static::updated(function ($user) {
//            if ($user->isDirty('password')) {
//                Cache::forget('password_check_' . $user->id);
//            }
//            if ($user->isDirty('is_full_access')) {
//                Cache::forget('user_full_access_' . $user->id);
//            }
//        });
//    }

    /**
     * @return BelongsToMany
     */
    public function hierarchies(): BelongsToMany
    {
        return $this->belongsToMany(Hierarchy::class)
            ->withPivot('role', 'position')
            ->withTimestamps()
            ->orderBy('position');
    }

    /**
     * @param $role
     * @return Collection
     */
    public function hierarchiesByRole($role): Collection
    {
        return $this->hierarchies()->wherePivot('role', $role)->get();
    }

    /**
     * @param $hierarchyId
     * @return bool
     */
    public function belongsToHierarchy($hierarchyId): bool
    {
        return $this->hierarchies()->where('hierarchy_id', $hierarchyId)->exists();
    }

    /**
     * @param $hierarchyId
     * @return null
     */
    public function getRoleInHierarchy($hierarchyId)
    {
        $hierarchy = $this->hierarchies()->where('hierarchy_id', $hierarchyId)->first();
        return $hierarchy ? $hierarchy->pivot->role : null;
    }

}
