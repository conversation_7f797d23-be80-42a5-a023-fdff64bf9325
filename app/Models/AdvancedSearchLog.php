<?php

namespace App\Models;

//use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
//use <PERSON><PERSON><PERSON>\Mongodb\Helpers\QueriesRelationships;
//use PhpClickHouseLaravel\BaseModel AS Model;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;
use <PERSON><PERSON><PERSON>\Mongodb\Helpers\QueriesRelationships;

/**
 * @method static select(string $string)
 * @method static create(array $response_data_X)
 */
class AdvancedSearchLog extends Model
{


    use HybridRelations, QueriesRelationships;

    /**
     * @var string[] $guarded
     */
    protected $guarded = ['_id'];

    /**
     * @var string $connection
     */
    protected $connection = 'mongodb';

    /**
     * @var string $table
     */
    protected $table = 'advance_search_log';


    /**
     * @var string $connection
     */
   // protected $connection = 'clickhouse_save';
    /**
     * @var string $table
     */
   // protected $table = 'advance_search_log';
    /**
     * @var string[] $casts
     */
    protected $casts = [
        'created_at' => 'datetime:Y-m-d H:i:s',
//        'request' => 'array',
//        'response' => 'array',
//        'tabs' => 'array',
    ];




}
