<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class CameraType extends Model
{
    use HasFactory;

    /**
     * @var string $table
     */
    protected $table = 'camera_types';

    /**
     * @var string[]
     */
    protected $fillable = ['name', 'active'];

    /**
     * @return HasMany
     */
    public function cameras(): HasMany
    {
        return $this->hasMany(Camera::class);
    }
}
