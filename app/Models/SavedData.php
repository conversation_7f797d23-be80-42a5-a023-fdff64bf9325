<?php


namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
use <PERSON><PERSON><PERSON>\Mongodb\Helpers\QueriesRelationships;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * @property mixed $type
 * @method static where(array $array)
 */
class SavedData extends Model
{
    use HasFactory, HybridRelations, QueriesRelationships;

    public const PERSON = 'person';
    public const CUSTOM = 'custom';

    /**
     * @var array|string[] $types
     */
    public static array $types = [
        self::PERSON,
        self::CUSTOM
    ];

    protected $primaryKey = "_id";

    /**
     * @var string $connection
     */
    protected $connection = 'mongodb';

    /**
     * @var string $table
     */
    protected $table = 'saved_data';

    /**
     * @var string[] $guarded
     */
    protected $guarded = ['_id'];

    /**
     * @param $query
     * @param $userId
     * @return mixed
     */
    public function scopeUserId($query, $userId): mixed
    {
        return $query->when($userId, function ($query, $userId) {
            $query->where('user_id', $userId);
        });
    }

    /**
     * @param $query
     * @param $type
     * @return mixed
     */
    public function scopeType($query, $type): mixed
    {
        return $query->when($type, function ($query, $type) {
            $query->where('type', $type);
        });
    }

    /**
     * @param $query
     * @param $params
     * @return mixed
     */
    public function scopeCustomFilter($query, $params): mixed
    {
        return $query->when(
            isset($params['type']) && $params['type'] === self::CUSTOM,
            static function ($query)
            use ($params) {

                if (isset($params['name'])) {
                    $query->where('data.name', 'like', '%' . $params['name'] . '%');
                }

                if (isset($params['surname'])) {
                    $query->where('data.surname', 'like', '%' . $params['surname'] . '%');
                }

                if (isset($params['passport_id'])) {
                    $query->where('data.document_number', 'like', '%' . $params['passport_id'] . '%');
                }

                if (isset($params['similarity'])) {
                    $query->where('data.data.distance', '>=', (float) $params['similarity']);
                }

                if (isset($params['similar_percent'])) {
                    $query->where('data.data.similar_percent', '>=', (float) $params['similar_percent']);
                }

                if (isset($params['citizen'])) {
                    $query->where('data.vetendashligi', 'like', '%' . $params['citizen'] . '%');
                }

                if (isset($params['precinct'])) {
                    $query->where('data.data.menteqe', 'like', '%' . $params['precinct'] . '%');
                }

                if (isset($params['min_count'])) {
                    $query->where('data.number_of_people', '>=', (int) $params['min_count']);
                }
            });
    }

    public function scopePersonFilter($query, $params)
    {
        return $query->when(
            isset($params['type']) && $params['type'] === self::PERSON,
            static function ($query)
            use ($params) {

                if (isset($params['name'])) {
                    $query->where('data.name', 'like', '%' . $params['name'] . '%');
                }

                if (isset($params['surname'])) {
                    $query->where('data.surname', 'like', '%' . $params['surname'] . '%');
                }

                if (isset($params['father_name'])) {
                    $query->where('data.father_name', 'like', '%' . $params['father_name'] . '%');
                }

                if (isset($params['pin'])) {
                    $query->where('data.pin', 'like', '%' . $params['pin'] . '%');
                }

                if (isset($params['doc_number'])) {
                    $query->where('data.doc_number', 'like', '%' . $params['doc_number'] . '%');
                }

                if (isset($params['birthdate'])) {
                    $query->where('data.birthdate', 'like', '%' . $params['birthdate'] . '%');
                }

                if (isset($params['sex'])) {

                    $gender = intToSex((int) $params['sex']);

                    if ($gender !== null) {
                        $query->where('data.sex', $gender);
                    }
                }
            });
    }
}
