<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Repository\PersonRepository;
use App\Services\PersonService;

class GrayList extends Model
{
    use HasFactory;

    protected $connection = 'pgsql';

    protected $table = 'graylist';

    protected $fillable = [
        'pin',
        'note',
        'status',
    ];

    public function personInfo(){
        $person_service = new PersonService(new PersonRepository(new Person()));
        $person_info = $person_service->getGallery($this->pin);
        return $person_info;
    }
}
