<?php

namespace App\Models;

use App\Models\Object\Object_;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @method static paginate(int $param)
 * @method static findOrFail(int $id)
 * @method static create(array $validated)
 * @method static objectIds(array|\Illuminate\Contracts\Foundation\Application|\Illuminate\Http\Request|string|null $request)
 */
class Camera extends Model
{
    use HasFactory;

    /**
     * @var string $table
     */
    protected $table = 'cameras';

    /**
     * @var string[] $fillable
     */
    protected $guarded = ['id'];

    /**
     * @return BelongsTo
     */
    public function type(): BelongsTo
    {
        return $this->belongsTo(CameraType::class, 'camera_type_id', 'id');
    }

    /**
     * @return BelongsTo
     */
    public function object(): BelongsTo
    {
        return $this->belongsTo(Object\Object_::class, 'object_id', 'id');
    }


    public function scopeCameraIds($query, $cameraIds) {

        if(!empty($cameraIds))
            return $query->whereIn('camera_id', $cameraIds);
    }

    public function scopeObjectIds($query, $objectIds) {

        if(!empty($objectIds))
            return $query->whereIn('object_id', $objectIds);

    }

    public function scopeObjectId($query, ?int $objectId) {

        if($objectId) {
            $nestedObjectIds = Object_::find($objectId)?->getAllChildren()->pluck('id')->toArray();
            $nestedObjectIds = array_merge($nestedObjectIds ?? [], [$objectId]);
            return $query->whereIn('object_id', $nestedObjectIds);
        }

    }


    public function scopeObjectTypeIds($query, $objectTypeIds) {

        if(!empty($objectTypeIds))
            return $query->whereHas('object', function($q) use ($objectTypeIds) {
                 $q->whereIn('object_type_id', $objectTypeIds);
        });
    }

    public function scopeObjectTypeId($query, $objectTypeId) {

        if($objectTypeId)
            return $query->whereHas('object', function($q) use ($objectTypeId) {
                 $q->where('object_type_id', $objectTypeId);
        });
    }

    public function scopeCameraTypeIds($query, $cameraTypeIds) {

        if(!empty($cameraTypeIds))
            return $query->whereIn('camera_type_id', $cameraTypeIds);
    }


    public function scopeIsActive($query, $isActive) {

        if ($isActive) {
            return $query->where('is_active', $isActive);
        }
    }
}
