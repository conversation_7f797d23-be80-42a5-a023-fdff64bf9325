<?php

namespace App\Models;

use App\Traits\SearchTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method static create(mixed $order)
 */
class FoodOrder extends Model
{
    use HasFactory,SearchTrait;

    protected $guarded = [];


    protected $fillable = [
        'channel',
        'name',
        'email',
        'phone',
        'address',
        'order_date',
        'data',
    ];

}
