<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @method static insert(array $data)
 * @method static whereNull(string $string)
 * @method static whereIn(string $string, mixed $ids)
 */
class MissingPeople extends Model
{
    use HasFactory;

    protected $table = 'missing_people';

    protected $fillable = [
        'fin',
        'is_sync',
        'is_lock',
        'synced_at',
        'locked_at',
        'created_at',
    ];

    public $timestamps = false;

    protected $casts = [
        'synced_at' => 'datetime',
        'locked_at' => 'datetime',
        'created_at' => 'datetime',
    ];


    protected static function boot(): void
    {
        parent::boot();
        static::creating(fn($model) => $model->forceFill([
            'created_at' => $model->freshTimestamp(),
        ]));
    }


//    public function getFinAttribute($value): string
//    {
//      return  $this->attributes['fin'] = strtoupper($value);
//    }
//    public function getIsSyncAttribute($value): bool
//    {
//        return (bool) $value;
//    }
//
//    public function getIsLockAttribute($value): bool
//    {
//        return (bool) $value;
//    }
//
//    public function setIsSyncAttribute($value): void
//    {
//        $this->attributes['is_sync'] = (int) $value;
//    }
//
//    public function setIsLockAttribute($value): void
//    {
//        $this->attributes['is_lock'] = (int) $value;
//    }
//
//    public function setFinAttribute($value): void
//    {
//        $this->attributes['fin'] = strtoupper($value);
//    }


}
