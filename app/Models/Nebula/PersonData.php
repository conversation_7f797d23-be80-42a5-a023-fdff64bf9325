<?php

namespace App\Models\Nebula;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\HybridRelations;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;
use Jensse<PERSON>\Mongodb\Helpers\QueriesRelationships;

/**
 * @property mixed $_id
 * @property mixed $data
 */
class PersonData extends Model
{
    use HybridRelations, QueriesRelationships;

    /**
     * @var string[] $guarded
     */
    protected $guarded = ['_id'];

    /**
     * @var string $connection
     */
    protected $connection = 'mongodb';

    /**
     * @var string $table
     */
    protected $table = 'person_data';
}
