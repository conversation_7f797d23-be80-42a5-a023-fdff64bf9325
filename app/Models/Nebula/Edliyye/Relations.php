<?php

namespace App\Models\Nebula\Edliyye;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Relations extends Model
{
    use HasFactory;

    protected $connection =  'pgsql';

    protected $table = 'edliyye.relations';

    protected $with = ['relationType', 'edliyyeRelationType', 'edliyyeAction'];

    public function relationType(): BelongsTo
    {
        return $this->belongsTo(RelationTypes::class, 'relation_id', 'id');
    }

    public function edliyyeRelationType(): BelongsTo
    {
        return $this->belongsTo(EdliyyeRelationTypes::class, 'edliyye_relation_id', 'id');
    }

    public function edliyyeAction(): BelongsTo
    {
        return $this->belongsTo(EdliyyeActions::class, 'edliyye_action_id', 'id');
    }

}
