<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GsmCallModel extends Model
{
    use HasFactory;
    protected $connection;

    protected $table = 'radius_events.dist_c_ipdr_log';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if (in_array(config('app.env'), ['development', 'local'])) {
            $this->connection = 'clickhouse';
        } else {
            $this->connection = 'clickhouse_radius';
        }
    }

    protected $casts = [
        'attributes' => 'json',
    ];
}
