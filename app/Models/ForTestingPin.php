<?php

namespace App\Models;

use App\Repository\PersonRepository;
use App\Services\PersonService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property mixed $pin
 */
class ForTestingPin extends Model
{
    use HasFactory;

    protected $connection = "pgsql";

    protected $table = "for_testing_pins";

    protected $guarded = [];

    protected $appends = ['person_info'];

    public function personInfo(): array
    {
        $person_service = new PersonService(new PersonRepository(new Person()));
        return $person_service->getGallery($this->pin);
    }
}
