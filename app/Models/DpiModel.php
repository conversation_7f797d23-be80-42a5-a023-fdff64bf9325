<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DpiModel extends Model
{
    use HasFactory;
    protected $connection;

    protected $table = 'radius_events.dist_dpi_5tuples_log';

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if (in_array(config('app.env'), ['development', 'local'])) {
            $this->connection = 'clickhouse';
        } else {
            $this->connection = 'clickhouse_radius';
        }
    }

}
