<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;
use <PERSON><PERSON>gers\Mongodb\Eloquent\HybridRelations;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use MongoDB\BSON\UTCDateTime;

/**
 * @property mixed $id
 * @property mixed $name
 * @property mixed $surname
 * @property mixed $photo
 * @property mixed $similarities
 */
class Blacklist extends Model
{
    use HasFactory, HybridRelations;

    /**
     * @var string $connection
     */
    protected $connection = 'pgsql';

    /**
     * @var string $table
     */
    protected $table = 'blacklists';

    /**
     * @var string[] $fillable
     */
    protected $guarded = ['id'];

    /**
     * @var string[] $dates
     */
    protected $dates = [
        'birthdate' => 'date'
    ];

    /**
     * @var string[] $fillable
     */
    protected $fillable = [
        'name',
        'surname',
        'father_name',
        'birthdate',
        'document_number',
        'pin',
        'photo',
        'note',
        'gender',
        'status',
    ];

    public function getHumanizeBirthdateAttribute(): string
    {
        return $this->birthdate ? Carbon::parse($this->birthdate)->format('d/m/Y'): '';
    }

    /**
     * @param $query
     * @param $name
     * @return mixed
     */
    public function scopeName($query, $name): mixed
    {
        return $query->when($name, function ($query, $name) {
            $query->where(DB::raw('lower(name)'), 'like', '%' .Str::lower($name). '%');
        });
    }

    /**
     * @param $query
     * @param $surname
     * @return mixed
     */
    public function scopeSurname($query, $surname): mixed
    {
        return $query->when($surname, function ($query, $surname) {
            $query->where(DB::raw('lower(surname)'), 'like', '%' .Str::lower($surname). '%');
        });
    }

    /**
     * @param $query
     * @param $fatherName
     * @return mixed
     */
    public function scopeFatherName($query, $fatherName): mixed
    {
        return $query->when($fatherName, function ($query, $fatherName) {
            $query->where(DB::raw('lower(father_name)'), 'like', '%' .Str::lower($fatherName). '%');
        });
    }

    /**
     * @param $query
     * @param $birthdate
     * @return mixed
     */
    public function scopeBirthdate($query, $birthdate): mixed
    {
        return $query->when($birthdate, function ($query, $birthdate) {
            $query->where(DB::raw('lower(birthdate)'), 'like', '%' .Str::lower($birthdate). '%');
        });
    }

    /**
     * @param $query
     * @param $pin
     * @return mixed
     */
    public function scopePin($query, $pin): mixed
    {
        return $query->when($pin, function ($query, $pin) {
            $query->where(DB::raw('lower(pin)'), '=', Str::lower($pin));
        });
    }

    /**
     * @param $query
     * @param $gender
     * @return mixed
     */
    public function scopeGender($query, $gender): mixed
    {
        return $query->when($gender, function ($query, $gender) {
            $query->where('gender', $gender);
        });
    }

    /**
     * @param $query
     * @param null $fromDate
     * @param null $toDate
     * @return mixed
     * @throws \Exception
     */
    public function scopeBetweenDate($query, $fromDate = null, $toDate = null): mixed
    {
        return $query->when(isset($fromDate, $toDate), function ($query) use ($fromDate, $toDate) {
            return $query->whereBetween('created_at', [
                Carbon::parse($fromDate)->format('Y-m-d H:i:s'),
                Carbon::parse($toDate)->format('Y-m-d H:i:s')
            ]);
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function similarities(): HasMany
    {
        return $this->hasMany(BlacklistSimilarly::class, 'blacklist_id', 'id');
    }
}
