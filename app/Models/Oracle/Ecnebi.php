<?php

namespace App\Models\Oracle;

/**
 * @property mixed $id
 */
class Ecnebi extends Customs
{
    /**
     * @var string $connection
     */
    protected $connection = "oracle";

    /**
     * @var string $table
     */
    protected $table = 'ECNEBI2020';

    /**
     * @return string
     */
    public function getImageAttribute(): string
    {
        return config('servers.backend').'/handled_images/' . $this->id . '.jpg';
    }
}
