<?php

namespace App\Models\Social;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class FacebookFriend extends Model
{
    use HasFactory;

    protected $table = 'social_hub.facebook_friends';

    public function account(): HasOne
    {
        return $this->hasOne(Account::class, 'id', 'friends_account_id');
    }

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }


}
