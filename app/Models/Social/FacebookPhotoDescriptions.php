<?php

namespace App\Models\Social;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FacebookPhotoDescriptions extends Model
{
    use HasFactory;

    protected $table = 'facebook.description_table';

    public function account()
    {
        return $this->hasOne(Account::class,'id', $this->account_id);
    }

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }


}
