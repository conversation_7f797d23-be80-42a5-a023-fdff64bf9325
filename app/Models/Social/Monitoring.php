<?php

namespace App\Models\Social;

use App\Enums\FollowingType;
use App\Traits\SearchTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Monitoring extends Model
{
    use HasFactory, SearchTrait;

    protected $table = 'social_hub.monitoring';

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }

    public function posts(): HasMany
    {
        return $this->hasMany(MonitoringPost::class);
    }

    public function comments(): HasMany
    {
        return $this->hasMany(MonitoringComment::class);
    }

    public function following(): HasOne
    {
        return $this->hasOne(Following::class, 'account_id')
            ->where('following_type', FollowingType::PAGE);
    }
}
