<?php

namespace App\Models\Social;

use App\Enums\FollowingType;
use App\Traits\SearchTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MonitoringPost extends Model
{
    use HasFactory, SearchTrait;

    protected $table = 'social_hub.monitoring_post';

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'publisher_account_id', 'id');
    }

    public function following_account(): BelongsTo
    {
        return $this->belongsTo(Following::class, 'monitoring_id', 'account_id')
            ->where('following_type', FollowingType::PAGE);
    }
}
