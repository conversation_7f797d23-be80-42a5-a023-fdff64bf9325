<?php

namespace App\Models\Social;

use App\Enums\FollowingType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserPost extends Model
{
    use HasFactory;

    protected $table = 'social_hub.users_post';

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function following_account(): BelongsTo
    {
        return $this->belongsTo(Following::class, 'account_id', 'account_id')
            ->where('following_type', FollowingType::ACCOUNT);
    }
}
