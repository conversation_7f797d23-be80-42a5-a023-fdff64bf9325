<?php

namespace App\Models\Social;

use App\Enums\FollowingType;
use App\Scope\SocialHubScope\FollowingByUserScope;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Following extends Model
{
    use HasFactory;

    protected $table = 'social_hub.followings';



    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }

    protected $casts = [
        'following_type' => FollowingType::class,
    ];

    protected $guarded = ['id'];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted(): void
    {
        static::addGlobalScope(new FollowingByUserScope());
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function pages(): BelongsTo
    {
        return $this->belongsTo(SocialPage::class, 'account_id');
    }

}
