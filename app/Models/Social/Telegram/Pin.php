<?php

namespace App\Models\Social\Telegram;


use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Pin extends Model
{
    use HasFactory;

    protected $table = 'facebook.tg_pins';

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }

    public function photo(): HasMany
    {
        return $this->hasMany(Photo::class);
    }




}
