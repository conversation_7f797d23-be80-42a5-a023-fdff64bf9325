<?php

namespace App\Models\Social\Telegram;


use App\Traits\SearchTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PhotoDescription extends Model
{
    use HasFactory,SearchTrait;

    protected $table = 'facebook.tg_description_table';

    protected $appends = ['photo_url'];

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }


    public function getPhotoUrlAttribute(): string
    {
        return config('servers.frontend').'/telegram-pics/' . $this->attributes['path_col'];
    }

}
