<?php

namespace App\Models\Social\Telegram;


use App\Traits\SearchTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Account extends Model
{
    use HasFactory,SearchTrait;

    protected $table = 'facebook.tg_accounts';

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }

    public function mainPhoto(): HasOne
    {
        return $this->hasOne(Photo::class,'account_id','id');
    }

}
