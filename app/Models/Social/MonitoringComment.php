<?php

namespace App\Models\Social;

use App\Traits\SearchTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MonitoringComment extends Model
{
    use HasFactory, SearchTrait;

    protected $table = 'social_hub.monitoring_coments';

    protected $connection;

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);

        if(config('app.type') != 'locale')
        {
            $this->connection = 'pgsql_social';
        }else{
            $this->connection = 'pgsql';
        }
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class, 'commentator_account_id', 'id');
    }

    public function posts(): BelongsTo
    {
        return $this->belongsTo(MonitoringPost::class, 'posts_id');
    }

}
