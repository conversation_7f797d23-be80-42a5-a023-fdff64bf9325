<?php

namespace App\DTO;

class MovementRouteDTO
{
    private array $payload;

    public function __construct(array $payload)
    {
        $this->payload = $payload;
    }

    public static function fromArray(array $data): self
    {
        return new self($data);
    }

    private function processAzParkingData(array $azParkingData): array
    {
        $result = [];
        foreach ($azParkingData['data']['data'] as $item) {
            if (isset($item['address'])) {
                $result[] = [
                    'type' => 'azparking',
                    'status' => $item['status'] ?? null,
                    'user' => $item['user'] ?? null,
                    'area' => $item['area'] ?? null,
                    'park' => $item['park'] ?? null,
                    'amount' => $item['amount'] ?? null,
                    'car_number' => $item['car']['label'] ?? null,
                    'address' => $item['address'],
                    'start_date' => $item['start_date'] ?? null,
                    'end_date' => $item['end_date'] ?? null,
                    'created_at' => $item['created_at'] ?? null,
                ];
            }
        }
        return $result;
    }

    private function processDinApiData(array $dinApiData): array
    {
        $result = [];
        if (isset($dinApiData['data']) && is_array($dinApiData['data'])) {
            foreach ($dinApiData['data'] as $item) {
                $result[] = [
                    'type' => 'din-api-result',
                    'vehicleNumber' => $item['vehicleNumber'] ?? null,
                    'cameraNumber' => $item['cameraNumber'] ?? null,
                    'cameraName' => $item['cameraName'] ?? null,
                   'insertDate' => isset($item['insertDate']) 
                 ? \Carbon\Carbon::parse($item['insertDate'])->format('Y-m-d\TH:i:s') 
                   : null,
                   
                    'image' => $item['image'] ?? null,
                    'cameraPointer' => $item['cameraPointer'] ?? null,
                    'icon' => $item['icon'] ?? null
                ];
            }
        }
        return $result;
    }

    private function groupSimilarData(array $data): array
    {
        $grouped = [];
        $tempGroups = [];

        foreach ($data as $item) {
            $key = '';
            if ($item['type'] === 'azparking') {
                $key = $item['park']['label'] ?? '';
            } elseif ($item['type'] === 'din-api-result') {
                $key = 'camera_' . $item['cameraNumber'];
            }

            if (!isset($tempGroups[$key])) {
                $tempGroups[$key] = [];
            }
            $tempGroups[$key][] = $item;
        }

        foreach ($tempGroups as $items) {
            $grouped[] = $items;
        }

        return $grouped;
    }

    public function toArray(): array
    {
        $azParkingData = $this->payload['azparking-data'] ?? [];
        $dinApiData = $this->payload['din-api-result'] ?? [];
        $processedAzParkingData = $this->processAzParkingData($azParkingData);
        $processedDinApiData = $this->processDinApiData($dinApiData);
        $allData = array_merge($processedAzParkingData, $processedDinApiData);
        usort($allData, function ($a, $b) {
            $dateA = $a['type'] === 'azparking'
                ? $a['end_date']
                : ($a['insertDate'] ?? null);
            $dateB = $b['type'] === 'azparking'
                ? $b['end_date']
                : ($b['insertDate'] ?? null);
            return strtotime($dateB) <=> strtotime($dateA);
        });
        $groupedData = $this->groupSimilarData($allData);
        unset($this->payload['azparking-data'], $this->payload['din-api-result']);
        return $groupedData;
    }
}
