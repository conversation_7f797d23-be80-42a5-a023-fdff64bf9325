<?php

namespace App\Http\Middleware;

use App\Models\GrayList;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class GraylistMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $graylist = GrayList::where('pin', $request->pin)->orWhere('pin', $request->fin)->first();
        if ($graylist && Auth::user()->gray_list!=true) {
            return response()->json([
                'status' => 401,
                'message' => 'Məlumatlara baxmaq üçün icazəniz yoxdur',
                'data' => []
            ], 401);
        }
        return $next($request);
    }
}
