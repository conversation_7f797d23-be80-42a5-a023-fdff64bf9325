<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RestrictRefererAndOrigin
{
    /**
     * List of allowed Referers and Origins.
     */


    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $referer = $request->headers->get('referer');
        $origin = $request->headers->get('origin');
        $allowedOrigins = config('cors.allowed_origins');
        $allowedReferer = config('cors.allowed_referer');

        /**
         * Parse the allowed origins and referrers to get the host.
         * TODO: added $allowedOrigins for Production
         */
        $allowedOrigins = array_map(function ($origin) {
            return parse_url($origin, PHP_URL_HOST);
        }, $allowedOrigins);

        $allowedReferer = array_map(function ($referer) {
            return parse_url($referer, PHP_URL_HOST);
        }, $allowedReferer);

        if (!config('cors.app_referer') || !config('cors.app_cors')) {
            return $next($request);
        }

        if (
            ($referer && !in_array(parse_url($referer, PHP_URL_HOST), $allowedReferer)) ||
            ($origin && !in_array(parse_url($origin, PHP_URL_HOST), $allowedOrigins))
        ) {
            return response('Unauthorized missing referer or origin', 403);
        }

        return $next($request);
    }
}
