<?php

namespace App\Http\Middleware;

use App\Traits\ApiResponsible;
use App\Models\User;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class HeaderBasedAuthentication
{
    use ApiResponsible;
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (\Illuminate\Http\Response|RedirectResponse) $next
     * @return JsonResponse
     */
    public function handle(Request $request, Closure $next)
    {
//        if ($request->hasHeader('Authorization')) {
//            return $this->errorResponse(
//                Response::HTTP_UNAUTHORIZED,
//                'Authorization header is not allowed'
//            );
//        }
        $userID = $request->header(config('services.user_header_key', 'x-user-id'));
        if (!$userID) {
            return $this->errorResponse(
                Response::HTTP_UNAUTHORIZED,
                'X-User-ID header is required'
            );
        }
        if (Auth::id() != $userID) {
            $user = User::find($userID);
            if (!$user) {
                return $this->errorResponse(
                    Response::HTTP_UNAUTHORIZED,
                    'Invalid user ID'
                );
            }
            Auth::login($user);
            return $next($request);
        }
        return $next($request);
    }
}
