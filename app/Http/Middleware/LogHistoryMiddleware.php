<?php

namespace App\Http\Middleware;

use App\Models\LogHistory;
use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use MongoDB\BSON\UTCDateTime;


class LogHistoryMiddleware
{
    protected Request $newRequest;

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param \Closure(Request): (Response|RedirectResponse) $next
     * @return \Illuminate\Http\JsonResponse
     */
    public function handle(Request $request, Closure $next)
    {

        if (env('LOG_SAVER') == 'mongo') {
            return $next($request);
        }

        $this->newRequest = clone $request;

        if (!empty($request->password)) {
            $this->newRequest->merge(['password' => '*****']);
        }
        if (!empty($request->password_confirmation)) {
            $this->newRequest->merge(['password_confirmation' => '*****']);
        }
        if (!empty($request->current_password)) {
            $this->newRequest->merge(['current_password' => '*****']);
        }
        if (!empty($request->new_password)) {
            $this->newRequest->merge(['new_password' => '*****']);
        }
        if (!empty($request->new_password_confirmation)) {
            $this->newRequest->merge(['new_password_confirmation' => '*****']);
        }
        if (!empty($request->email)) {
            $maskedMail = substr($request->email, 0, 5) . str_repeat('*', strlen($request->email) - 10) . substr($request->email, -5);
            $this->newRequest->merge(['email' => $maskedMail]);
        }

        $user =  auth('api')->user();

        $userData = [
            'date' => date("Y-m-d H:i:s"),
            'user_id' => $user?->id ?? '',
            'ip' => IP(),
            'user_name' => ($user?->name ?? '') . ' ' . ($user?->surname ?? ''),
            'url' => $request->url(),
            'method' => $request->method(),
            'body'   => $this->newRequest->all(),
            'created_at' => now(),
            'updated_at' => now(),
            'status' => '200',
            'message' => 'success'
        ];

        if($user?->id != 1 && auth('api')->check()) {
            try {

                LogHistory::create($userData);

                sendRequestLogToAudit($userData);

            } catch (\Exception $e) {
                $userData['message'] = 'error';
                $userData['status'] = $e->getCode();
                $userData['body'] = $e->getMessage();
                $userData['line'] = $e->getLine();
                $userData['file'] = $e->getFile();
                $userData['trace'] = $e->getTraceAsString();
                $userData['user_id'] = $user?->id ?? '';
                $userData['data'] = $this->newRequest->all();
                $userData['method'] = $request->method();
                $userData['url'] = $request->url();
                $userData['ip'] = IP();
                $userData['created_at'] = now();
                $userData['updated_at'] = now();
                LogHistory::create($userData);

            }
        }


        return $next($request);
    }
}
