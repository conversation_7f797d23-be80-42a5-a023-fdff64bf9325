<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class MiddlewareWithoutJWT
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param  \Closure(Request): (Response|RedirectResponse)  $next
     * @return JsonResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $devKey = $request->header('Dev-Key');
        $configDevKey = config('services.dev.key');
        if (($devKey !== $configDevKey) || !$devKey || !$configDevKey  && (!$request->wantsJson())) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }
        return $next($request);
    }
}
