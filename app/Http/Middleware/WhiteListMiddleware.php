<?php

namespace App\Http\Middleware;

use App\Models\WhiteList;
use Closure;
use Illuminate\Http\Request;

class WhiteListMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $while_list = WhiteList::where('pin', $request->pin)->orWhere('pin', $request->fin)->first();
        if ($while_list) {
            return response()->json([
                'status' => 404,
                'message' => 'Məlumat tapılmadı',
                'data' => []
            ], 404);
        }
        return $next($request);
    }
}
