<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
class CheckPasswordCache
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse) $next
     * @return JsonResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        if($user){
            $cacheKey = 'password_check_' . $user->id;
            $cacheResult = Cache::get($cacheKey);
            if(!$cacheResult){
                $expiryDays = config('services.password.expiry_days');
                $daysSinceChange = $user->password_changed_at
                    ? Carbon::parse($user->password_changed_at)->diffInDays(Carbon::parse(config('services.password.check_date', now())))
                    : null;
                if ($daysSinceChange === null || $daysSinceChange < $expiryDays) {
                    return response()->json([
                        'message' => 'Parolu dəyişməlisiniz.',
                    ], 403);
                }
                Cache::put($cacheKey, true, now()->addDays());
            }
        }
        return $next($request);
    }
}
