<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AIBlacklistVerification
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->header('x-api-key');

        $validToken = '98NsaUWXzfwPbPNqeZVe6G93I3DlLPfdsXsPmfdx9MAAcaWhx305DdUVuqRmsKCZl5qSZ';

        if ($token !== $validToken) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        return $next($request);
    }
}
