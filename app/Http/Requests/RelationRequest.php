<?php

namespace App\Http\Requests;

use App\Services\RelationService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RelationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'search_type' => 'required|string|' . Rule::in([
                RelationService::BASIC_SEARCH,
                RelationService::SHORTEST_SEARCH,
                RelationService::ALL_SHORTEST_SEARCH,
            ]),
            'value' => 'required|string',
            'second_value' => 'required_unless:search_type,' . RelationService::BASIC_SEARCH . '|string',
            'filter_type' => 'required|string|' .
                Rule::in([
                    'pin',
                    'phone',
                    'vehicle',
                    'company',
                ]),
            'second_filter_type' => 'required_unless:search_type,' . RelationService::BASIC_SEARCH . '|string|' .
                Rule::in([
                    'pin',
                    'phone',
                    'vehicle',
                    'company',
                ]),
            'relation_data' => 'array',
            'relation_level' => 'integer|min:0|max:4',
        ];
    }
}
