<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class DpiBetweenNumbersRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'phoneFirst' => [
                'required',
                'regex:/^994\d{9}$/',
            ],
            'phoneSecond' => [
                'required',
                'regex:/^994\d{9}$/',
            ],
            'from' => [
                'nullable',
                'date_format:Y-m-d H:i:s',
            ],
            'to' => [
                'nullable',
                'date_format:Y-m-d H:i:s',
            ],
        ];
    }

    public function messages()
    {
        return [
            'phoneFirst.required' => 'The first phone number is required.',
            'phoneFirst.regex' => 'The first phone number must start with 994 and contain 9 digits after it.',

            'phoneSecond.required' => 'The second phone number is required.',
            'phoneSecond.regex' => 'The second phone number must start with 994 and contain 9 digits after it.',

            'from.date_format' => 'The from date must be in the format Y-m-d H:i:s.',

            'to.date_format' => 'The to date must be in the format Y-m-d H:i:s.',
        ];
    }

    protected function prepareForValidation()
    {
        $phone = $this->input('phone');

        if ($phone) {
            if (!Str::startsWith($phone, '994')) {
                $phone = '994' . ltrim($phone, '0');
                $this->merge(['phone' => $phone]);
            }
        }

        if (!$this->has('from') || empty($this->input('from'))) {
            $this->merge(['from' => Carbon::now()->format('Y-m-d H:i:s')]);
        }

        if (!$this->has('to') || empty($this->input('to'))) {
            $this->merge(['to' => Carbon::now()->subMonth()->format('Y-m-d H:i:s')]);
        }
    }

}
