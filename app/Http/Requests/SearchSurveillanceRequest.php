<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @property mixed $similarity
 * @property mixed $max_count
 * @property mixed $photo
 * @property mixed $camera_ids
 * @property mixed $date_to
 * @property mixed $date_from
 * @property mixed $shapes
 */
class SearchSurveillanceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'photo' => 'nullable',
            'similarity' => 'required|numeric',
            'max_count' => 'required|integer|min:1|max:100',
            'camera_ids' => 'sometimes|array',
            'camera_ids.*' => 'sometimes|integer',
            "date_from" => 'sometimes|date_format:d.m.Y H:i',
            "date_to" => 'sometimes|date_format:d.m.Y H:i',
            'shapes' => 'array'
        ];
    }
}
