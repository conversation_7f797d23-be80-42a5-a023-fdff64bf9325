<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CameraRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'object_id' => 'required|integer',
            'camera_type_id' => 'required|integer',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:255',
            'address' => 'required|string|max:255',
            'rtsp' => 'string|max:500',
            'url' => 'required|string|max:255',
            'image_url' => 'string|max:255',
            'ip_address' => 'required|ip',
            'lat' => 'numeric|string|max:255',
            'lng' => 'numeric|string|max:255',
            'is_active' =>'required|boolean',
            'tpu_server'=>'required|string|max:255',
        ];
    }
}
