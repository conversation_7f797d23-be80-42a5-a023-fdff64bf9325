<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CameraArchiveRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'camera_id' => 'required|integer|exists:cameras,camera_id',
            'date' => 'required|date|date_format:Y/m/d',
            'fromTime' => 'required',
            'toTime' => 'required'
        ];
    }
}
