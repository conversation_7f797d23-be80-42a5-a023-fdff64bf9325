<?php

namespace App\Http\Requests\Cabinet;

use Illuminate\Foundation\Http\FormRequest;

class SearchGroupRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|min:3|max:200',
            'file' => 'nullable|file|mimes:csv',
            'type' => 'nullable',
            'pins' => 'nullable'
        ];
    }

    public function messages()
    {

        return [
            'name.min' => 'Qrup adı ən az 3 simvoldan ibarət olmalıdır',
            'name.max' => 'Qrup adı ən çox 200 simvoldan ibarət olmalıdır',
            'file.required' => 'Fayl yüklənməyib.',
            'file.mimes' => 'Ancaq CSV faylı ola bilər.',
            'file.max' => '<PERSON><PERSON>ın ölçü<PERSON>ü maksimum 2MB olmalıdır.',
        ];
    }
}
