<?php

namespace App\Http\Requests\SavedData;

use Illuminate\Foundation\Http\FormRequest;

class SavedDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => 'required',
            'name' => 'string',
            'surname' => 'string',
            'father_name' => 'string',
            'passport_id' => 'string',
            'min_count' => 'string',
            'similarity' => 'string',
            'similar_percent' => 'string',
            'citizen' => 'string',
            'precinct' => 'string',
            'birthdate' => 'string',
            'pin' => 'string',
            'sex' => 'string',
            'order_column' => 'string',
            'order_type' => 'string',
            'page' => 'integer',
            'per_page' => 'integer'
        ];
    }
}
