<?php

namespace App\Http\Requests\SavedData;

use App\Models\SavedData;
use Illuminate\Validation\Rule;
use Illuminate\Foundation\Http\FormRequest;

class ToggleSavedDataRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => ['required', Rule::in(SavedData::$types)],
            'data_id' => 'required',
            'data' => 'required'
        ];
    }
}
