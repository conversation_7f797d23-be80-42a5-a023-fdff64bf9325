<?php

namespace App\Http\Requests\LocationRequests;

use Illuminate\Foundation\Http\FormRequest;

class NativeAndStrangersRequest extends FormRequest
{
    protected $locale;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->locale = $this->input('lang', 'az');
        if (!in_array($this->locale, ['az', 'en'])) {
            $this->locale = 'az';
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'point.lat' => 'nullable|numeric|required_without:polygon',
            'point.lng' => 'nullable|numeric|required_without:polygon',
            'polygon.*.lat' => 'nullable|numeric|required_without:point.lat',
            'polygon.*.lng' => 'nullable|numeric|required_without:point.lng',
            'type' => 'required|string',
            'start_time' => 'required|date_format:Y-m-d H:i:s',
            'end_time' => 'required|date_format:Y-m-d H:i:s|after_or_equal:start_time',
            'frequency_count' => 'required|integer',
            'radius' => 'required',
            'topK' => 'required|integer',
            'search_head_name' => 'required|string',
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        if ($this->locale === 'en') {
            return $this->englishMessages();
        }
        return $this->azerbaijaniMessages();
    }

    /**
     * Azerbaijani validation messages.
     *
     * @return array
     */
    private function azerbaijaniMessages(): array
    {
        return [
            'point.lat.required_without' => 'Point enliyini daxil edin və ya polygon məlumatını daxil edin.',
            'point.lng.required_without' => 'Point uzunluğunu daxil edin və ya polygon məlumatını daxil edin.',
            'polygon.*.lat.required_without' => 'Polygon enliyini daxil edin və ya point məlumatını daxil edin.',
            'polygon.*.lng.required_without' => 'Polygon uzunluq dəyərini daxil edin və ya point məlumatını daxil edin.',
            'point.lat.numeric' => 'Point enlik dəyəri rəqəm olmalıdır.',
            'point.lng.numeric' => 'Point uzunluq dəyəri rəqəm olmalıdır.',
            'polygon.*.lat.numeric' => 'Polygon enlik dəyəri rəqəm olmalıdır.',
            'polygon.*.lng.numeric' => 'Polygon uzunluq dəyəri rəqəm olmalıdır.',
            'type.required' => 'Tip sahəsi tələb olunur.',
            'type.string' => 'Tip sahəsi mətn olmalıdır.',
            'start_time.required' => 'Başlama vaxtı sahəsi tələb olunur.',
            'start_time.date_format' => 'Başlama vaxtı sahəsi düzgün formatda olmalıdır: Y-m-d H:i:s.',
            'end_time.required' => 'Bitmə vaxtı sahəsi tələb olunur.',
            'end_time.date_format' => 'Bitmə vaxtı sahəsi düzgün formatda olmalıdır: Y-m-d H:i:s.',
            'end_time.after_or_equal' => 'Bitmə vaxtı başlama vaxtından sonra və ya ona bərabər olmalıdır.',
            'frequency_count.required' => 'Tezlik sayı sahəsi tələb olunur.',
            'frequency_count.integer' => 'Tezlik sayı sahəsi tam ədəd olmalıdır.',
            'radius.required' => 'Radius sahəsi tələb olunur.',
            'topK.required' => 'Top K sahəsi tələb olunur.',
            'topK.integer' => 'Top K sahəsi tam ədəd olmalıdır.',
            'search_head_name.required' => 'Axtarışın adı mütləqdir.',
            'search_head_name.string' => 'Axtarışın adı mətn olmalıdır.',
        ];
    }

    /**
     * English validation messages.
     *
     * @return array
     */
    private function englishMessages(): array
    {
        return [
            'point.lat.required_without' => 'Please provide point latitude or polygon data.',
            'point.lng.required_without' => 'Please provide point longitude or polygon data.',
            'polygon.*.lat.required_without' => 'Please provide polygon latitude or point latitude.',
            'polygon.*.lng.required_without' => 'Please provide polygon longitude or point longitude.',
            'point.lat.numeric' => 'Point latitude must be a number.',
            'point.lng.numeric' => 'Point longitude must be a number.',
            'polygon.*.lat.numeric' => 'Polygon latitude must be a number.',
            'polygon.*.lng.numeric' => 'Polygon longitude must be a number.',
            'type.required' => 'Type field is required.',
            'type.string' => 'Type field must be a string.',
            'start_time.required' => 'Start time field is required.',
            'start_time.date_format' => 'Start time field must be in the format: Y-m-d H:i:s.',
            'end_time.required' => 'End time field is required.',
            'end_time.date_format' => 'End time field must be in the format: Y-m-d H:i:s.',
            'end_time.after_or_equal' => 'End time must be after or equal to start time.',
            'frequency_count.required' => 'Frequency count field is required.',
            'frequency_count.integer' => 'Frequency count must be an integer.',
            'radius.required' => 'Radius field is required.',
            'topK.required' => 'Top K field is required.',
            'topK.integer' => 'Top K must be an integer.'
        ];
    }
}
