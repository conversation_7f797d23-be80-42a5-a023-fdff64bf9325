<?php

namespace App\Http\Requests\LocationRequests;

use Illuminate\Foundation\Http\FormRequest;

class FindIntervalSimilarityRequest extends FormRequest
{
    protected $locale;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->locale = $this->input('lang', 'az');
        if (!in_array($this->locale, ['az', 'en'])) {
            $this->locale = 'az';
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'response_count' => 'required|integer|min:1',
            'min_match' => 'required|integer|min:2',
            'points.*.lat' => 'nullable|numeric|between:-90,90',
            'points.*.lon' => 'nullable|numeric|between:-180,180',
            'points.*.start' => 'required|date_format:Y-m-d H:i:s',
            'points.*.end' => 'required|date_format:Y-m-d H:i:s|after_or_equal:points.*.start',
            'points.*.radius' => 'required|numeric|min:1',
            'polygons.*.polygon.*.lat' => 'nullable|numeric|between:-90,90',
            'polygons.*.polygon.*.lon' => 'nullable|numeric|between:-180,180',
            'polygons.*.start' => 'date_format:Y-m-d H:i:s',
            'polygons.*.end' => 'date_format:Y-m-d H:i:s|after_or_equal:polygon.*.start',
            'polygons.*.radius' => 'numeric|min:1',
            'search_head_name' => 'required|string',
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        if ($this->locale === 'en') {
            return $this->englishMessages();
        }
        return $this->azerbaijaniMessages();
    }

    /**
     * Azerbaijani validation messages.
     *
     * @return array
     */
    private function azerbaijaniMessages(): array
    {
        return [
            'response_count.required' => 'Cavab sayı mütləqdir və boş ola bilməz.',
            'response_count.integer' => 'Cavab sayı tam ədəd olmalıdır.',
            'response_count.min' => 'Cavab sayı ən azı 1 olmalıdır.',
            'min_match.required' => 'Minimal uyğunluq sayı mütləqdir və boş ola bilməz.',
            'min_match.integer' => 'Minimal uyğunluq sayı tam ədəd olmalıdır.',
            'min_match.min' => 'Minimal uyğunluq sayı ən azı 2 olmalıdır.',
            'points.*.lat.required' => 'Enlik mütləqdir və boş ola bilməz.',
            'points.*.lat.numeric' => 'Enlik rəqəm olmalıdır.',
            'points.*.lat.between' => 'Enlik -90 və 90 arasında olmalıdır.',
            'points.*.lon.required' => 'Uzunluq mütləqdir və boş ola bilməz.',
            'points.*.lon.numeric' => 'Uzunluq rəqəm olmalıdır.',
            'points.*.lon.between' => 'Uzunluq -180 və 180 arasında olmalıdır.',
            'points.*.start.required' => 'Başlama vaxtı mütləqdir və boş ola bilməz.',
            'points.*.start.date_format' => 'Başlama vaxtı YYYY-MM-DD HH:MM:SS formatında olmalıdır.',
            'points.*.end.required' => 'Bitmə vaxtı mütləqdir və boş ola bilməz.',
            'points.*.end.date_format' => 'Bitmə vaxtı YYYY-MM-DD HH:MM:SS formatında olmalıdır.',
            'points.*.end.after_or_equal' => 'Bitmə vaxtı başlama vaxtından sonra və ya ona bərabər olmalıdır.',
            'points.*.radius.required' => 'Radius mütləqdir və boş ola bilməz.',
            'points.*.radius.numeric' => 'Radius rəqəm olmalıdır.',
            'points.*.radius.min' => 'Radius mənfi və ya sıfır ola bilməz',
            'polygons.*.polygon.*.lat.numeric' => 'Enlik məlumatı rəqəm olmalıdır.',
            'polygons.*.polygon.*.lat.between' => 'Enlik məlumatı -90 ilə 90 arasında olmalıdır.',
            'polygons.*.polygon.*.lon.numeric' => 'Uzunluq məlumatı rəqəm olmalıdır.',
            'polygons.*.polygon.*.lon.between' => 'Uzunluq məlumatı -180 ilə 180 arasında olmalıdır.',
            'polygons.*.start.date_format' => 'Başlama tarix formatı Y-m-d H:i:s olmalıdır.',
            'polygons.*.end.date_format' => 'Bitmə tarix formatı Y-m-d H:i:s olmalıdır.',
            'polygons.*.end.after_or_equal' => 'Bitmə tarix başlama tarixindən sonra və ya ona bərabər olmalıdır.',
            'polygons.*.radius.numeric' => 'Radius məlumatı rəqəm olmalıdır.',
            'polygons.*.radius.min' => 'Radius minimum 1 olmalıdır.',
            'search_head_name.required' => 'Axtarışın adı mütləqdir.',
            'search_head_name.string' => 'Axtarışın adı mətn olmalıdır.',
        ];
    }

    /**
     * English validation messages.
     *
     * @return array
     */
    private function englishMessages(): array
    {
        return [
            'response_count.required' => 'Response count is required and cannot be empty.',
            'response_count.integer' => 'Response count must be an integer.',
            'response_count.min' => 'Response count must be at least 1.',
            'min_match.required' => 'Minimum match count is required and cannot be empty.',
            'min_match.integer' => 'Minimum match count must be an integer.',
            'min_match.min' => 'Minimum match count must be at least 2.',
            'points.*.lat.required' => 'Latitude is required and cannot be empty.',
            'points.*.lat.numeric' => 'Latitude must be a number.',
            'points.*.lat.between' => 'Latitude must be between -90 and 90.',
            'points.*.lon.required' => 'Longitude is required and cannot be empty.',
            'points.*.lon.numeric' => 'Longitude must be a number.',
            'points.*.lon.between' => 'Longitude must be between -180 and 180.',
            'points.*.start.required' => 'Start time is required and cannot be empty.',
            'points.*.start.date_format' => 'Start time must be in the format YYYY-MM-DD HH:MM:SS.',
            'points.*.end.required' => 'End time is required and cannot be empty.',
            'points.*.end.date_format' => 'End time must be in the format YYYY-MM-DD HH:MM:SS.',
            'points.*.end.after_or_equal' => 'End time must be after or equal to start time.',
            'points.*.radius.required' => 'Radius is required and cannot be empty.',
            'points.*.radius.numeric' => 'Radius must be a number.',
            'points.*.radius.min' => 'Radius cannot be negative or zero.',
            'polygons.*.polygon.*.lat.numeric' => 'Latitude must be a number.',
            'polygons.*.polygon.*.lat.between' => 'Latitude must be between -90 and 90.',
            'polygons.*.polygon.*.lon.numeric' => 'Longitude must be a number.',
            'polygons.*.polygon.*.lon.between' => 'Longitude must be between -180 and 180.',
            'polygons.*.start.date_format' => 'Start date format must be Y-m-d H:i:s.',
            'polygons.*.end.date_format' => 'End date format must be Y-m-d H:i:s.',
            'polygons.*.end.after_or_equal' => 'End date must be after or equal to start date.',
            'polygons.*.radius.numeric' => 'Radius must be a number.',
            'polygons.*.radius.min' => 'Radius must be at least 1.',
        ];
    }
}
