<?php

namespace App\Http\Requests\LocationRequests;

use Illuminate\Foundation\Http\FormRequest;

class FindMeetingPlacesRequest extends FormRequest
{
    protected $locale;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->locale = $this->input('lang', 'az');
        if (!in_array($this->locale, ['az', 'en'])) {
            $this->locale = 'az';
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'number1' => 'required|regex:/^(994)[1-9][0-9]{8}$/',
            'number2' => 'required',
            'number2.*' => 'required|regex:/^(994)[1-9][0-9]{8}$/',
            'polygon.*.lat' => 'nullable',
            'polygon.*.lng' => 'nullable',
            'start' => 'required|date_format:Y-m-d H:i:s',
            'end' => 'required|date_format:Y-m-d H:i:s',
            'radius' => 'required|numeric|min:1',
            'delta_time' => 'required|numeric|min:1',
            'search_head_name' => 'required|string',
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        if ($this->locale === 'en') {
            return $this->englishMessages();
        }
        return $this->azerbaijaniMessages();
    }

    /**
     * Azerbaijani validation messages.
     *
     * @return array
     */
    private function azerbaijaniMessages(): array
    {
        return [
            'number1.required' => 'Birinci telefon nömrəsi mütləqdir və boş ola bilməz.',
            'number1.regex' => 'Birinci telefon nömrəsi 994XXXXXXXXX formatında olmalıdır.',
            'number2.required' => 'Digər telefon nömrələri mütləqdir və boş ola bilməz.',
            'number2.regex' => 'Digər telefon nömrələri 994XXXXXXXXX formatında olmalıdır.',
            'start.required' => 'Başlama vaxtı mütləqdir və boş ola bilməz.',
            'start.date_format' => 'Başlama vaxtı YYYY-MM-DD HH:MM:SS formatında olmalıdır.',
            'end.required' => 'Bitmə vaxtı mütləqdir və boş ola bilməz.',
            'end.date_format' => 'Bitmə vaxtı YYYY-MM-DD HH:MM:SS formatında olmalıdır.',
            'radius.required' => 'Radius mütləqdir və boş ola bilməz.',
            'radius.numeric' => 'Radius rəqəm olmalıdır.',
            'radius.min' => 'Radius mənfi və ya sıfır ola bilməz.',
            'delta_time.required' => 'Zaman fərqi mütləqdir və boş ola bilməz.',
            'delta_time.numeric' => 'Zaman fərqi rəqəm olmalıdır.',
            'delta_time.min' => 'Zaman fərqi mənfi və ya sıfır ola bilməz.',
            'search_head_name.required' => 'Axtarışın adı mütləqdir.',
            'search_head_name.string' => 'Axtarışın adı mətn olmalıdır.',
        ];
    }

    /**
     * English validation messages.
     *
     * @return array
     */
    private function englishMessages(): array
    {
        return [
            'number1.required' => 'First phone number is required and cannot be empty.',
            'number1.regex' => 'First phone number must be in the format 994XXXXXXXXX.',
            'number2.required' => 'Other phone numbers are required and cannot be empty.',
            'number2.regex' => 'Other phone numbers must be in the format 994XXXXXXXXX.',
            'start.required' => 'Start time is required and cannot be empty.',
            'start.date_format' => 'Start time must be in the format YYYY-MM-DD HH:MM:SS.',
            'end.required' => 'End time is required and cannot be empty.',
            'end.date_format' => 'End time must be in the format YYYY-MM-DD HH:MM:SS.',
            'radius.required' => 'Radius is required and cannot be empty.',
            'radius.numeric' => 'Radius must be a number.',
            'radius.min' => 'Radius cannot be negative or zero.',
            'delta_time.required' => 'Delta time is required and cannot be empty.',
            'delta_time.numeric' => 'Delta time must be a number.',
            'delta_time.min' => 'Delta time cannot be negative or zero.'
        ];
    }
}
