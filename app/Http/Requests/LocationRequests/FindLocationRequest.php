<?php

namespace App\Http\Requests\LocationRequests;

use Illuminate\Foundation\Http\FormRequest;

class FindLocationRequest extends FormRequest
{
    protected $locale;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    protected function prepareForValidation(): void
    {
        $this->locale = $this->input('lang', 'az');
        if ($this->locale !== 'az' && $this->locale !== 'en') {
            $this->locale = 'az';
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'phone_number' => 'required|regex:/^(994)[1-9][0-9]{8}$/',
            'start_time' => 'required|date_format:Y-m-d H:i:s',
            'end_time' => 'required|date_format:Y-m-d H:i:s',
//            'radius' => 'required|numeric|min:1',
            'search_head_name' => 'required|string',
        ];
    }

    public function messages(): array
    {
        if ($this->locale === 'en') {
            return $this->englishMessages();
        }
        return $this->azerbaijaniMessages();
    }

    private function azerbaijaniMessages(): array
    {
        return [
            'phone_number.required' => 'Telefon nömrəsi mütləqdir və boş ola bilməz.',
            'phone_number.regex' => 'Telefon nömrəsi 994XXXXXXXXX formatında olmalıdır.',
            'start_time.required' => 'Başlama vaxtı mütləqdir və boş ola bilməz.',
            'start_time.date_format' => 'Başlama vaxtı YYYY-MM-DD HH:MM:SS formatında olmalıdır.',
            'end_time.required' => 'Bitmə vaxtı mütləqdir və boş ola bilməz.',
            'end_time.date_format' => 'Bitmə vaxtı YYYY-MM-DD HH:MM:SS formatında olmalıdır.',
//            'radius.required' => 'Radius mütləqdir və boş ola bilməz.',
//            'radius.numeric' => 'Radius rəqəm olmalıdır.',
//            'radius.min' => 'Radius mənfi və ya sıfır ola bilməz.',
            'search_head_name.required' => 'Axtarışın adı mütləqdir.',
            'search_head_name.string' => 'Axtarışın adı mətn olmalıdır.',
        ];
    }

    private function englishMessages(): array
    {
        return [
            'phone_number.required' => 'Phone number is required and cannot be empty.',
            'phone_number.regex' => 'Phone number must be in the format 994XXXXXXXXX.',
            'start_time.required' => 'Start time is required and cannot be empty.',
            'start_time.date_format' => 'Start time must be in the format YYYY-MM-DD HH:MM:SS.',
            'end_time.required' => 'End time is required and cannot be empty.',
            'end_time.date_format' => 'End time must be in the format YYYY-MM-DD HH:MM:SS.',
//            'radius.required' => 'Radius is required and cannot be empty.',
//            'radius.numeric' => 'Radius must be a number.',
//            'radius.min' => 'Radius cannot be negative or zero.',
        ];
    }
}
