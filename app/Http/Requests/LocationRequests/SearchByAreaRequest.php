<?php

namespace App\Http\Requests\LocationRequests;

use Illuminate\Foundation\Http\FormRequest;

class SearchByAreaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'polygon' => 'required|array|min:4',
            'polygon.*.lat' => 'required|numeric|between:-90,90',
            'polygon.*.lng' => 'required|numeric|between:-180,180',
            'start_time' => 'required|date|before:end_time',
            'end_time' => 'required|date|after:start_time',
            'radius' => 'required|numeric|min:0',
            'search_head_name' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'polygon.required' => 'Polygon məlumatı mütləqdir və boş ola bilməz.',
            'polygon.array' => 'Polygon məlumatı array olmalıdır.',
            'polygon.min' => 'Polygon məlumatı ən azı 4 nöqtədən ibarət olmalıdır.',
            'polygon.*.lat.required' => 'Hər bir nöqtənin lat (eniş) məlumatı mütləqdir.',
            'polygon.*.lat.numeric' => 'Lat (eniş) məlumatı rəqəm olmalıdır.',
            'polygon.*.lat.between' => 'Lat (eniş) məlumatı -90 ilə 90 arasında olmalıdır.',
            'polygon.*.lng.required' => 'Hər bir nöqtənin lng (uzunluq) məlumatı mütləqdir.',
            'polygon.*.lng.numeric' => 'Lng (uzunluq) məlumatı rəqəm olmalıdır.',
            'polygon.*.lng.between' => 'Lng (uzunluq) məlumatı -180 ilə 180 arasında olmalıdır.',
            'start_time.required' => 'Başlama vaxtı mütləqdir.',
            'start_time.date' => 'Başlama vaxtı düzgün tarix formatında olmalıdır.',
            'start_time.before' => 'Başlama vaxtı bitmə vaxtından əvvəl olmalıdır.',
            'end_time.required' => 'Bitmə vaxtı mütləqdir.',
            'end_time.date' => 'Bitmə vaxtı düzgün tarix formatında olmalıdır.',
            'end_time.after' => 'Bitmə vaxtı başlama vaxtından sonra olmalıdır.',
            'radius.required' => 'Radius məlumatı mütləqdir.',
            'radius.numeric' => 'Radius məlumatı rəqəm olmalıdır.',
            'radius.min' => 'Radius məlumatı 0-dan az ola bilməz.',
            'search_head_name.required' => 'Axtarışın adı mütləqdir.',
            'search_head_name.string' => 'Axtarışın adı mətn olmalıdır.',
        ];
    }
}
