<?php

namespace App\Http\Requests;

use App\Services\RelationService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RelationByDateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'value' => 'required|string',
            'date_to' => 'required',
            'date_from' => 'required',
            'filter_type' => 'required|string|' .
                Rule::in([
                    'pin',
                    'phone',
                    'vehicle',
                    'company',
                ]),
            'relation_data' => 'array|required|' .
                Rule::in([
                    'works_at',
                    'was_in_camera',
                    'gave_attorney',
                    'called',
                    'was_in_cellid',
                    'vehicle_was_in_camera',
                ]),
        ];
    }
}
