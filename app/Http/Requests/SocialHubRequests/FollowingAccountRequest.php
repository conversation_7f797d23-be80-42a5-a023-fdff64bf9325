<?php

namespace App\Http\Requests\SocialHubRequests;

use Illuminate\Foundation\Http\FormRequest;

class FollowingAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'account_id' => 'required|numeric|min:1'
        ];
    }
}
