<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class BlacklistRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'photo' => 'required|string',
            'name' => 'required|string',
            'surname' => 'required|string',
            'document_number' => 'nullable|string|unique:blacklists',
            'pin' => 'nullable|string|min:7|max:7|unique:blacklists',
            'father_name' => 'nullable|string',
            'birthdate' => 'nullable|date',
            'gender' => 'nullable|numeric|min:1|max:2',
            'note' => 'nullable|string'
        ];
    }

    public function messages()
    {

        return [
            'pin.min' => 'FİN nömrə 7 simvoldan ibarət olmalıdır',
            'pin.max' => 'FİN nömrə 7 simvoldan ibarət olmalıdır',
        ];
    }
}
