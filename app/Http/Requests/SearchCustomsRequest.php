<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

/**
 * @property mixed $search_type
 */
class SearchCustomsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'photo' => 'nullable|string',
            'similarity' => 'required|numeric',
            'max_count' => 'required|integer|min:1|max:100',
            //    'name' => 'nullable|string',
            //    'surname' => 'nullable|string',
            //    'pin' => 'nullable|string|min:7|max:7',
            //    'father_name' => 'nullable|string',
            //    'birthdate' => 'nullable|date',
            //    'gender' => 'nullable|numeric|min:1|max:2',
        ];
    }
}
