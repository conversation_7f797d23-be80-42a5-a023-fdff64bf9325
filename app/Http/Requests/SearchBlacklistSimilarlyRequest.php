<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchBlacklistSimilarlyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'string',
            'surname' => 'string',
            'father_name' => 'string',
            'birthdate' => 'string',
            'pin' => 'string',
            'from_date' => 'string',
            'to_date' => 'string',
            'object_types' => 'array',
            'objects' => 'array',
            'gender' => 'integer',
            'cameras' => 'array'
        ];
    }
}
