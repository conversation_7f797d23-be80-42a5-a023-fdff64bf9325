<?php

namespace App\Http\Requests;

use App\Models\ForTestingPin;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * @property mixed $search_type
 */
class NewSearchSimilarityRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
         return [
            'photo' => 'nullable',
            'similarity' => 'required|numeric',
            'search_type' => 'required|in:1,2,3',
            'max_count' => 'required|integer|min:1|max:100',
            'do_face_detection' => 'boolean',
            'name' => 'nullable|string',
            'surname' => 'nullable|string',
            'pin' => 'nullable|string',
            'father_name' => 'nullable|string',
            'birthdate' => 'nullable|date',
            'gender' => 'nullable|numeric|min:1|max:2',
        ];

    }
}
