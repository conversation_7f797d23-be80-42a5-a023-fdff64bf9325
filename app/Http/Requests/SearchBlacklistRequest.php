<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class SearchBlacklistRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'string',
            'surname' => 'string',
            'father_name' => 'string',
            'birthdate' => 'string',
            'pin' => 'string',
            'gender' => 'numeric',
            'from_date' => 'string',
            'to_date' => 'string'
        ];
    }
}
