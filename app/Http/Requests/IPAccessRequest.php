<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class IPAccessRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id' => 'required|exists:users,id',
            'ip_address' => ['required', 'ip'],
            'is_full_access' => 'boolean'
        ];
    }

    public function messages(): array
    {
        return [
            'user_id.required' => 'User ID is required',
            'user_id.exists' => 'Selected user does not exist',
            'ip_address.required' => 'IP address is required',
            'ip_address.ip' => 'Invalid IP address format',
            'is_full_access.boolean' => 'Full access must be true or false'
        ];
    }
}
