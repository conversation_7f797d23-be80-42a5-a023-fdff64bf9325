<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StorePolygonRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|string',
            'description' => 'nullable|string',
            'user_id' => 'required|exists:users,id',
            'shapes' => 'required|array',
            'shapes.*.shapeType' => 'required|string|in:circle,polygon,rectangle',
            'shapes.*.coordinates' => 'required|array',
            'shapes.*.area' => 'required_if:shapes.*.shapeType,rectangle|numeric',
            'shapes.*.radius' => 'required_if:shapes.*.shapeType,circle|numeric'
        ];
    }
}
