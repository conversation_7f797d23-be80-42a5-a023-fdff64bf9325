<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PermissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'name' => 'required|unique:permissions,name',
            'parent_id' => 'nullable|exists:permissions,id',
            'translation_name' => 'required',
        ];
    }
}
