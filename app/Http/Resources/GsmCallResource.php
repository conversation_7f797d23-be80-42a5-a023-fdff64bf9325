<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class GsmCallResource extends JsonResource
{

    protected bool $permissionSound;


    public function setPermissionSound(bool $permissionSound): self
    {
        $this->permissionSound = $permissionSound;
        return $this;
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request)
    {
        $array = parent::toArray($request);

        $array = Arr::except($array, ['caller_start_event_time', 'called_start_event_time']);
        if (in_array(config('app.env'), ['development', 'local'])) {
            return array_merge($array, [
                'has_sound' => true
            ]);
        }
        return array_merge($array, [
            'has_sound' => $this->permissionSound && $this->checkGsmSound($this->caller_trace_id),
        ]);
//        return array_merge(parent::toArray($array), [
//            'has_sound' => $this->permissionSound && $this->checkGsmSound($this->caller_trace_id),
//        ]);
    }


    private function checkGsmSound($traceId): bool
    {
        $url = config('servers.gsm_audio_bucket') . "/" . $traceId . ".mp3";

        try {
            $path = parse_url($url, PHP_URL_PATH);
            $extension = pathinfo($path, PATHINFO_EXTENSION);

            if (!$extension || strtolower($extension) !== 'mp3') {
                return false;
            }

            $response = Http::timeout(10)->withOptions([
                'stream' => true,
                'verify' => false,
                'allow_redirects' => true,
            ])->get($url);

            if ($response->successful()) {
                $contentType = $response->header('Content-Type');

                if ($contentType) {
                    if (
                        stripos($contentType, 'audio') !== false ||
                        stripos($contentType, 'application/octet-stream') !== false ||
                        stripos($contentType, 'binary/octet-stream') !== false
                    ) {
                        return true;
                    }
                }
            }

            if (in_array(config('app.env'), ['development', 'local'])) {
                return true;
            }
            return false;

        } catch (\Exception $e) {
            return false;
        }
    }

}
