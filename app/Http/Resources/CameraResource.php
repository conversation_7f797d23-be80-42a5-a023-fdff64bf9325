<?php

namespace App\Http\Resources;

use App\Http\Resources\Object\ObjectListResource;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $id
 * @property mixed $name
 * @property mixed $address
 * @property string $ip_address
 * @property mixed $created_at
 * @property mixed $type
 * @property mixed $object
 * @property mixed $url
 * @property mixed $lat
 * @property mixed $lng
 * @property mixed $rtsp
 * @property mixed $camera_id
 * @property mixed $description
 * @property mixed $image_url
 * @property mixed $is_active
 */
class CameraResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArrayOld($request): array
    {
        //if ($request->is_new==1){
        $url = url('/api/v1/get-file/show-camera-video/' . $this->id . '/playlist.m3u8');
        $image_url = url('/api/v1/get-file/show-camera-image/' . $this->id);
        ///  }
        //  else{
        //      $url = $this->url;
        //     $image_url = $this->image_url;
        //  }
        //Hide : image_url ,rtsp ,url
        return [
            'id' => $this->id,
            'camera_id' => $this->camera_id,
            'name' => $this->name,
            'description' => $this->description,
            'url' => $url,
//            'rtsp' => $this->rtsp,
            'address' => $this->address,
            'gps' => [
                'lat' => (float)$this->lat,
                'lng' => (float)$this->lng,
            ],
            'ip_address' => "0.0.0.0", // $this->ip_address,
            'is_active' => $this->is_active,
            'image_url' => $image_url,
            'type' => new CameraTypeResource($this->type),
            'object' => new ObjectListResource($this->object),
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d H:i:s'),
            'breadcrumb' => $this->breadcrumb,
            'is_faced_enabled' => $this->is_faced_enabled
        ];
    }
    public function toArray($request): array
    {
        $url = url('/api/v1/get-file/show-camera-video/' . $this->id . '/playlist.m3u8');
        $image_url = url('/api/v1/get-file/show-camera-image/' . $this->id . '.jpg');
        return [
            'id' => $this->id,
            'camera_id' => $this->camera_id,
            'name' => $this->name,
            'description' => $this->description,
            'url' => $url,
            'rtsp' => $this->rtsp,
            'address' => $this->address,
            'gps' => [
                'lat' => (float) $this->lat,
                'lng' => (float)  $this->lng,
            ],
            'ip_address' => $this->ip_address,
            'is_active' => $this->is_active,
            'image_url' => $image_url,
            'type' => new CameraTypeResource($this->type),
            'object' => new ObjectListResource($this->object),
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d H:i:s'),
            'breadcrumb' => $this->breadcrumb,
            'tpu_server' => $this->tpu_server,
            'is_faced_enabled' => $this->is_faced_enabled
        ];
    }
}
