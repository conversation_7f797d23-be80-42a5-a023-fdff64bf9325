<?php

namespace App\Http\Resources;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $photos
 * @property mixed $best_fin
 * @property mixed $id
 * @property mixed $fins
 * @property mixed $type
 */
class SimilarFaceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        if ($request->has('camera_id') && $request->get('camera_id') !== null) {
            $photos = array_filter($this->photos, static function ($item) use ($request) {
                return (int) $item['camera_id'] === (int) $request->get('camera_id');
            });
        } else {
            $photos = $this->photos;
        }

        $photos = array_values($photos);

        $pins = $this->fins;

        $pinData = [];

        foreach ($pins as $key => $value) {
            $pinData[] = [
                'pin' => $key,
                'url' => config('similar_image_host').$key.'.png',
                'alignment_error' => $value
            ];
        }

        return [
            'id' => $this->id,
            'type' => $this->type,
            'best_fin' => $this->best_fin,
            'similar' => $pinData,
            'photo' => $photos[0] ?? []
        ];
    }
}
