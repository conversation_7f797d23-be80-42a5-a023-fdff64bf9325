<?php

namespace App\Http\Resources\Social;

use App\Models\Social\Account;
use Illuminate\Http\Resources\Json\JsonResource;

class FacebookAccountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'nationality' => $this->nationality,
            'account' => AccountResource::make($this->account)
        ];
    }
}
