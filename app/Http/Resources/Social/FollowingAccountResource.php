<?php

namespace App\Http\Resources\Social;

use Illuminate\Http\Resources\Json\JsonResource;

class FollowingAccountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'account_id' => $this->account_id,
            'social_media_id' => $this->social_media_id,
            'type' => $this->type,
            'title' => $this->title,
            'following_params' => $this->following_params,
            'user_ids' => $this->user_ids,
            'enigma_phones' => $this->enigma_phones,
            'url' => $this->url,
            'result_pins' => $this->result_pins,
            'status' => $this->status,
            'deleted_at' => $this->deleted_at,
            'created_at' => $this->created_at->toDateTimeString(),
            'updated_at' => $this->updated_at->toDateTimeString(),
            'following_type' => $this->following_type,
            'account' => AccountResource::make($this->whenLoaded('account')),
            'page' => SocialPageResource::make($this->whenLoaded('pages'))
        ];
    }
}
