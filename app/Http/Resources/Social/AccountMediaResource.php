<?php

namespace App\Http\Resources\Social;

use Illuminate\Http\Resources\Json\JsonResource;

class AccountMediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'account_id' => $this->account_id,
            'platform_id' => $this->platform_id,
            'media_url' => $this->media_url,
            'photo_path' => $this->getPhotoUrl($this->photo_path),
            'old_photo_path' => $this->getPhotoUrl($this->old_photo_path),
        ];
    }

    /**
     * Get the photo URL.
     *
     * @param  mixed  $media
     * @return string
     */
    protected function getPhotoUrl($photo): string
    {
        return url(config('servers.s3_bucket_server').'/all-pics/' . $photo);
    }

}
