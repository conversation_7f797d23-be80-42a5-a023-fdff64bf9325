<?php

namespace App\Http\Resources\Social;

use Illuminate\Http\Resources\Json\JsonResource;

class LicensePlateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "plate_number" => $this->plate_number ?? null,
            "photo_path" => $this->photo_path ? getSocialHubPhotoUrl($this->photo_path) : null,
            'account' => $this->account ? AccountResource::make($this->account) : []
        ];
    }
}
