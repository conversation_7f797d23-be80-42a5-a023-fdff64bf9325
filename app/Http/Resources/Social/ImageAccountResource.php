<?php

namespace App\Http\Resources\Social;

use Illuminate\Http\Resources\Json\JsonResource;

class ImageAccountResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $media = '';
        if (!empty($this->media)) {
            $media = $this->getPhotoUrl($this->media);
        }

        return [
            'id' => $this->id,
            'platform_id' => $this->platform_id,
            'username' => $this->username,
            'name' => $this->name,
            'surname' => $this->surname,
            'link' => $this->link,
            'nationality' => $this->nationality,
            'checked' => $this->checked,
            'is_following' => $this->is_following,
            'following_id' => $this->following_id,
            'media' => $media,
            'media_url' => $this->mediaNotProcessed?->media_url ?? "",
            'posts' => $this->whenLoaded($this->posts_count === null, $this->posts),
            'posts_count' => $this->when($this->posts_count !== null, $this->posts_count),
            'comments_count' => $this->when($this->comments_count !== null, $this->comments_count),
        ];
    }

    /**
     * Get the photo URL.
     *
     * @param  mixed  $media
     * @return string
     */
    protected function getPhotoUrl($media): string
    {
        return url(config('servers.s3_bucket_server').'/all-pics/' . $media);
    }

}
