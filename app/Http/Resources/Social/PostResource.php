<?php

namespace App\Http\Resources\Social;

use Illuminate\Http\Resources\Json\JsonResource;

class PostResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $media = '';
        if (!empty($this->media)) {
            $media = getSocialHubPhotoUrl($this->media->photo_path);
        }

        return [
            'id' => $this->id,
            'platform_id' => $this->platform_id,
            'source_type' => $this->source_type,
            'account_id' => $this->account_id,
            'page_id' => $this->page_id,
            'context' => $this->context,
            'comment_count' => $this->comment_count,
            'link' => $this->link,
            'publish_time' => $this->publish_time,
            'media' => $media,
            'page' => $this->whenLoaded('page', SocialPageResource::make($this->page)),
            'account' => $this->whenLoaded('account', AccountResource::make($this->account))
        ];
    }
}
