<?php

namespace App\Http\Resources\Social;

use Illuminate\Http\Resources\Json\JsonResource;

class DescriptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "id"=> $this->id,
            "eng"=> $this->eng,
            "aze"=> $this->aze,
            "media" => MediaResource::make($this->media)
        ];
    }
}
