<?php

namespace App\Http\Resources\GsmCalls;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
class GsmCallsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $array = parent::toArray($request);

        $array = Arr::except($array, ['caller_start_event_time', 'called_start_event_time']);

        return array_merge($array, [
            'has_sound' => false,
        ]);
    }
}