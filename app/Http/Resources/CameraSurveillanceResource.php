<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Object\ObjectSurveillanceResource;

/**
 * @property mixed $id
 * @property mixed $name
 * @property mixed $address
 * @property mixed $created_at
 * @property mixed $type
 * @property mixed $object
 * @property mixed $url
 * @property mixed $lat
 * @property mixed $lng
 * @property mixed $ip_address
 */
class CameraSurveillanceResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'ip_address' => $this->ip_address,
            'object' => new ObjectSurveillanceResource($this->object)
        ];
    }
}
