<?php

namespace App\Http\Resources\Person;

use Illuminate\Http\Resources\Json\JsonResource;

class PersonFinesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'status' => $this->resource['status'],
            'message' => $this->resource['message'],
            'data' => $this->resource['data']['punish'] ?? [],
        ];
    }
}
