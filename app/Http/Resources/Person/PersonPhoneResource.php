<?php

namespace App\Http\Resources\Person;

use Illuminate\Http\Resources\Json\JsonResource;

class PersonPhoneResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     * @throws \JsonException
     */
    public function toArray($request)
    {

        $data = $this->resource['data'];
        $decodedData = is_string($data) ? json_decode($data, true, 512, JSON_THROW_ON_ERROR) : $data;

        $infoList = PersonPhoneListResource::collection(
            $decodedData['infoList'] ?? []
        )->toArray($request);

        usort($infoList, function($a, $b) {
            return $b['used_phone'] <=> $a['used_phone'];
        });

        return [
            'status' => $this->resource['status'],
            'message' => $this->resource['message'],
            'data' => $infoList,
        ];
//        return [
//            'status' => $this->resource['status'],
//            'message' => $this->resource['message'],
//            'data' => PersonPhoneListResource::collection(
//                json_decode($this->resource['data'], true, 512, JSON_THROW_ON_ERROR)['infoList'] ?? []
//            ),
//        ];
    }
}
