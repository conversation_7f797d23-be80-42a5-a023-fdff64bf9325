<?php

namespace App\Http\Resources\Person;

use App\Models\Person;
use App\Services\PersonService;
use App\Services\S3Service;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class RelationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $img_path = S3Service::endpoint() . '/' .PersonService::getImgPath($this->resource['pin'], $this->resource['docType'] ?? 'old');


        $array_id_old = null;
        $array_id_new = null;
        if (isset($this->resource['pin'])) {
            $array_id_old = S3Service::endpoint() . '/' . PersonService::getImgPath($this->resource['pin'], 'old');
            $array_id_new = S3Service::endpoint() . '/' . PersonService::getImgPath($this->resource['pin'], 'new');
            $file_headers_old = @get_headers($array_id_old, true);
            $file_headers_new = @get_headers($array_id_new, true);
            $response_old = ($file_headers_old && isset($file_headers_old[0])) ? $file_headers_old[0] : null;
            $array_id_old = (!str_contains($response_old, '200')) ? null : $array_id_old;
            $response_new = ($file_headers_new && isset($file_headers_new[0])) ? $file_headers_new[0] : null;
            $array_id_new = (!str_contains($response_new, '200')) ? null : $array_id_new;

            $array_id_new = str_ireplace(env('AWS_FILE_URL'), env('AWS_FILE_CLIENT_URL'), $array_id_new);
            $array_id_old = str_ireplace(env('AWS_FILE_URL'), env('AWS_FILE_CLIENT_URL'), $array_id_old);

        }

        $img_path_new = str_ireplace(env('AWS_FILE_URL'), env('AWS_FILE_CLIENT_URL'), $img_path);


        $person= DB::table('people')->where('pin', $this->resource['pin'])->first();

        return [
            "relType" => $this->resource['relType'],
            "citizen" => $this->resource['citizen'],
            "name" => $this->resource['Name'],
            "surname" => $this->resource['Surname'],
            "patronymic" => $this->resource['Patronymic'],
            'photo_old_method' => getHTTPResponseStatusCode($img_path) != 200 ? str_replace('new', 'old', $img_path_new) : $img_path_new,
            "photo" => $array_id_new ?? $array_id_old,
            "image" => personImagePath($person?->doc_serial_number ?? "", $this->resource['pin']),
            "birthPlace" => $this->resource['BirthPlace'],
            "birthDate" => $this->resource['birthDate'],
            "militaryStatus" => $this->resource['MilitaryStatus'],
            "issueDate" => $this->resource['issueDate'],
            "martialStatus" => $this->resource['martialStatus'],
            "pin" => $this->resource['pin'],
            "docType" => $person?->doc_number??null,
            "docSerialNumber" => $person?->doc_serial_number??null,
            "gender" => $this->resource['gender'],
            "bloodType" => $this->resource['bloodtype'],
            "eyecolor" => $this->resource['eyecolor'],
            "height" => $this->resource['height']
        ];
    }
}
