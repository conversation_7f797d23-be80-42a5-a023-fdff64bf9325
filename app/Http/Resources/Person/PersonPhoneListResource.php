<?php

namespace App\Http\Resources\Person;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PersonPhoneListResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request): array|\JsonSerializable|Arrayable
    {



        if (DB::table('pin_phone')->where('pin', $this['Pin'])->exists()) {
            $used_phone = DB::table('pin_phone')->where('pin', $this['Pin'])->first();
            $phone_vaccine_reg = Str::startsWith($used_phone->vaccine_reg, '0') ? substr($used_phone->vaccine_reg, 1) : $used_phone->vaccine_reg;
            $this['used_phone'] = ((int)$phone_vaccine_reg == (int)$this['Phone']);
            $this['vaccine_reg'] = (int)$phone_vaccine_reg;
        }

        return [
            "Phone"          => $this['Typeid']==1 ? findCityByCode($this['Phone'])['phone'] : $this['Phone'],
            "City"           => $this['Typeid']==1 ? findCityByCode($this['Phone'])['city'] : '',
            "IMSI"           => $this['IMSI'],
            "Pasport"        => $this['Pasport'],
            "Pin"            => $this['Pin'],
            "Adres_Iamas"    => $this['Adres_Iamas'],
            "Ad_Iamas"       => $this['Ad_Iamas'],
            "Adres_Operator" => $this['Adres_Operator'],
            "Ad_operator"    => $this['Ad_operator'],
            "DataAktivacii"  => $this['DataAktivacii'],
            "ContactPhone"   => $this['ContactPhone'],
            "Typeid"         => $this['Typeid'],
            "TypeName"       => $this['TypeName'],
            "used_phone"     => $this['used_phone'] ?? false,
            "vaccine_reg"    => $this['vaccine_reg'] ?? 0,
        ];
    }
}
