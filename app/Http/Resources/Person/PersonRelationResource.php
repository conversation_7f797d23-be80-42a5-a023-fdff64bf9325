<?php

namespace App\Http\Resources\Person;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PersonRelationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'status' => $this->resource['status'],
            'message' => $this->resource['message'],
            'data' => RelationResource::collection( $this->resource['data']['RelationData'] ?? []),
        ];
    }
}
