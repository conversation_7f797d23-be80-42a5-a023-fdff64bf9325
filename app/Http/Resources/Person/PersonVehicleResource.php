<?php

namespace App\Http\Resources\Person;

use Illuminate\Http\Resources\Json\JsonResource;

class PersonVehicleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'status' => 200,
            'message' => 'OK',
            'data' => [
//                'request' => $this->resource['vehicles']['request'] ?? [],
                'result' => PersonVehicleListResource::collection(
//                    $this->resource['vehicles']['result']['vehicleInfoForSocial'] ?? []
                    $this->lstVehicle ?? []
                ),
//                'service' => 'EHDISService'
                'service' => 'DTX Service'
            ],
        ];
    }
}
