<?php

namespace App\Http\Resources\Person;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

/**
 * @property mixed $id
 * @property mixed $name
 * @property mixed $surname
 * @property mixed father_name
 * @property mixed $pin
 * @property mixed $image
 * @property mixed $is_saved
 * @property mixed $doc_serial_number
 */
class PersonListResource extends JsonResource
{

    /**
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
         return [
             'id' => $this->id,
             'name' => $this->name,
             'surname' => $this->surname,
             'father_name' => $this->father_name,
             'pin' => $this->pin,
             'birthdate' => $this->birthdate ?? '',
             'photo' => $this->syncImg(),
             'image' => personImagePath($this->doc_serial_number, $this->pin),
             'is_saved' => $this->is_saved ?? false,
             'graylist' => $this->check_graylist()
         ];
    }
}
