<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PolygonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user' => [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'surname' => $this->user->surname,
            ],
            'name' => $this->name,
            'description' => $this->description,
            'shapes' => ShapeResource::collection($this->whenLoaded('shapes')),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at
        ];
    }
}
