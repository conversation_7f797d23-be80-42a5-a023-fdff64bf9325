<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ShapeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'polygon_id' => $this->polygon_id,
            'type' => $this->type,
            'coordinates' => json_decode($this->coordinates),
            'area' => $this->area,
            'radius' => $this->radius,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
