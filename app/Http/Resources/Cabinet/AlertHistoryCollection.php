<?php

namespace App\Http\Resources\Cabinet;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class AlertHistoryCollection extends ResourceCollection
{
    private array $pagination;

    public function __construct($resource)
    {
        $this->pagination = [
            'total' => (int) $resource->total(),
            'perPage' => (int) $resource->perPage(),
            'currentPage' => $resource->currentPage(),
            'lastPage' => $resource->lastPage(),
            'path'=>$resource->path(),
            'prev_page_url'=>$resource->previousPageUrl(),
            'current_page_url' => $resource->url($resource->currentPage()),
            'next_page_url'=>$resource->nextPageUrl(),
            'from' => $resource->firstItem(),
            'to' => $resource->lastItem(),
        ];


        $resource = $resource->getCollection(); // Necessary to remove meta and links

        parent::__construct($resource);
    }
    /**
     * Transform the resource collection into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'paginate' => $this->pagination,
            'data' => $this->collection,

        ];
    }
}
