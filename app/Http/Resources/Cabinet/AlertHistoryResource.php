<?php

namespace App\Http\Resources\Cabinet;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class AlertHistoryResource extends JsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        return [
            'id' => $this->_id,
            'title' => $this->alert_title,
            'content' => $this->alert_content,
            'alert_history_id' => $this->alert_history_id,
            'status' => $this->status,
            'created_at' => Carbon::parse($this->created_at)->format('H:i:s Y-m-d'),
        ];
    }
}
