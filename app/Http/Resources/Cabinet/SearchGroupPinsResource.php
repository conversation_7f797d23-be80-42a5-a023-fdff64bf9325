<?php

namespace App\Http\Resources\Cabinet;

use App\Http\Resources\Person\PersonListResource;
use App\Http\Resources\Person\PersonResource;
use Illuminate\Http\Resources\Json\JsonResource;

class SearchGroupPinsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return PersonListResource::make($this->whenLoaded('person'));
    }
}
