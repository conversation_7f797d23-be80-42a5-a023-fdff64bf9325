<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

/**
 * @property mixed $id
 * @property mixed $name
 * @property mixed $surname
 * @property mixed $father_name
 * @property mixed $pin
 * @property mixed $similarity
 * @property mixed $image
 * @property mixed $is_saved
 * @property mixed $doc_serial_number
 */
class SearchSimilarityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'surname' => $this->surname,
            'father_name' => $this->father_name,
            'pin' => $this->pin,
            'similarity' => $this->similarity,
            'img' => $this->getSyncImg(),
            'image' => personImagePath($this->doc_serial_number, $this->pin),
            'is_saved' => $this->is_saved ?? false,
            'birthDate' => $this->birthdate ?? '',
            'graylist' => $this->check_graylist(),
            'is_type' => $this->is_type ?? 'person',
        ];
    }

    /**
     * @return mixed
     */
    public function getSyncImg()
    {
        return ""; // $this->syncImg();
    }

    private function check_graylist()
    {
        return false;
    }
}
