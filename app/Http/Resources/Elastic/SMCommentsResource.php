<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class SMCommentsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $item = $this->resource['_source'];
     
        return [
            'platform_name' => $item['platform_name'] ?? '',
            'type' => 'comment',


            'account' => [
                'id'          => $item['account_id'] ?? '',
                'pin'          => $item['account_pin'] ?? '',
                'name'          => $item['account_name'] ?? '',
                'surname'          => $item['account_surname'] ?? '',
                'username'          => $item['account_username'] ?? '',
                'link'          => $item['account_link'] ?? '',
                'photo_path' => !empty($item['account_photo_path']) ? env('SOCIAL_MEDIA_PHOTO_PATH').'all-pics/'.$item['account_photo_path'] : '',
            ],

            "post" => [
                'id' => $item['post_id'] ?? '',
                'page_id' => $item['post_page_id'] ?? '',
                'source_type' => $item['post_source_type'] ?? '',
                'comment_count' => $item['post_comment_count'] ?? '',
                'context' => $item['post_context'] ?? '',
                'publish_time' => $item['post_publish_time'] ?? '',
                'link'    => $item['post_link'] ?? '',
                'photo_path' => !empty($item['post_photo_path']) ? env('SOCIAL_MEDIA_PHOTO_PATH').'all-pics/'.$item['post_photo_path'] : '',
            ],
            "comment" => [
                'id' => $item['comment_id'] ?? '',
                'context' => $item['comment_context'] ?? '',
                'publish_time'  => $item['comment_publish_time'] ?? '',
                'link' => $item['comment_link'] ?? '',
            ],
            'person' => [
                'pin' => '',
                'name' => '',
                'surname' => '',
                'father_name' => '',
                'img' => '',

            ],
        ];
    }
}