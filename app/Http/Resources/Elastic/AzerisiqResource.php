<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class AzerisiqResource extends JsonResource
{

    public function toArray($request)
    {
        $data = $this->resource['_source'];
        return [
            "subid"       => $data['subid'] ?? '',
            'type'        => $data['type'] ?? '',
            "name"        => $data['name'] ?? '',
            "surname"     => $data['surname'] ?? '',
            "father_name" => $data['father_name'] ?? '',
            "phone"     => $data['contact'] ?? '',
            "rayon"       => $data['rayon'] ?? '',
            "matrix"      => $data['matrix'] ?? '',
            "street"      => $data['street'] ?? '',
            "full_adress" => $data['full_adress'] ?? '',
            "more" => [],
        ];
    }
}
