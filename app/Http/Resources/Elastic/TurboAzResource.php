<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class TurboAzResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = $this->resource['_source'];
        return [
            "elan_no"=> $data['data']['elan_no'],
            "phone"=> trim($data['data']['seller_phone_number'], '{}"'),
            "seller_ip"=> $data['data']['seller_ip'],
            "seller_name"=> $data['data']['seller_name'],
            "seller_email"=> $data['data']['seller_email'],
            "region"=> $data['data']['region'],
            "marka"=> $data['data']['marka'],
            "ban_type"=> $data['data']['ban_type'],
            "colour"=> $data['data']['colour'],
            "description"=> $data['data']['description'],
            "create_date"=> $data['data']['create_date'],
            "last_update_date"=> $data['data']['last_update_date'],
            "expire_date"=> $data['data']['expire_date'],
            "more" => [
                "id"=> $data['data']['id'],
                "elan_url"=> $data['data']['elan_url'],
                "production_date"=> $data['data']['production_date'],
                "owners"=> $data['data']['owners'],
                "is_damaged"=> $data['data']['is_damaged'],
                "horse_power"=> $data['data']['horse_power'],
                "price"=> $data['data']['price'],
                "is_coloured"=> $data['data']['is_coloured'],
                "model"=> $data['data']['model'],
                "gearbox"=> $data['data']['gearbox'],
                "fuel_type"=> $data['data']['fuel_type'],
                "mileage"=> $data['data']['mileage'],
                "drivetrain"=> $data['data']['drivetrain'],
                "assemble_market"=> $data['data']['assemble_market'],
                "equipments"=> $data['data']['equipments'],
                "engine_size"=> $data['data']['engine_size'],
                "view_count"=> $data['data']['view_count'],
                "images"=> $data['data']['images'],

            ]

        ];
    }
}
