<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class SMAccountsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $item = $this->resource['_source'];
        return [
            'platform_name' => $item['platform_name'] ?? '',
            'account' => [
                'id' => $item['id'] ?? '',
                'name' => $item['name'] ?? '',
                'surname' => $item['surname'] ?? '',
                'username' => $item['username'] ?? '',
                'nationality' => $item['nationality'] ?? '',
                'link' => $item['link'] ?? '',
                'photo_path' => !empty($item['photo_path']) ? env('SOCIAL_MEDIA_PHOTO_PATH').'all-pics/'.$item['photo_path'] : '',
            ],
            'person' => [
                'pin' => '',
                'name' => '',
                'surname' => '',
                'father_name' => '',
                'img' => '',

            ],
        ];
    }
}