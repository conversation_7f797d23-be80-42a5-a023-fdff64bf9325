<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class BeeinContactResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = $this->resource['_source'];

        $number = $data['phone'];

        return [
            "phone"=>$number,
            "name"=>$data['name'],
            "surname"=>$data['surname'],
            "father_name"=>$data['father_name'],
            "birth_date"=>$data['birth_date'],
            "email"=>$data['email'],
            "city"=>$data['city'],
            "address"=>$data['address'],
            "pin"=> '',
            "doc_number"=>$data['doc_number'],
            "note"=>$data['note'],
            "source"=>str_replace('mobcontact', 'internet', $data['source']),
            "more" => [],
  

        ];
    }
}
