<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\ResourceCollection;

class Taxi_189_DataCollection extends ResourceCollection
{
    private array $pagination;
    private array $filters; // Add filters as a private property

    public function __construct($resource, array $filters = [])
    {
        // Initialize filters
        $this->filters = $filters;

        // Set up pagination details
        $this->pagination = [
            'total' => $resource->total(),
            'perPage' => (int)$resource->perPage(),
            'currentPage' => $resource->currentPage(),
            'lastPage' => $resource->lastPage(),
            "path" => $resource->path(),
            "prev_page_url" => $resource->previousPageUrl(),
            "current_page_url" => $resource->url($resource->currentPage()),
            "next_page_url" => $resource->nextPageUrl(),
            'from' => $resource->firstItem(),
            'to' => $resource->lastItem(),
        ];

        // Remove meta and links by getting the collection
        $resource = $resource->getCollection();

        // Call the parent constructor
        parent::__construct($resource);
    }

    /**
     * Transform the resource collection into an array.
     *
     * @param Request $request
     * @return array|Arrayable|\JsonSerializable
     */
    public function toArray($request): array|\JsonSerializable|Arrayable
    {
        return [
            'data' => $this->collection,
            'filters' => $this->filters, // Include filters in the array output
            'pagination' => $this->pagination,
        ];
    }
}
