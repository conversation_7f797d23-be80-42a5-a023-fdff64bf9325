<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class AzersuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
  
        $data = $this->resource['_source'];
        return [
            "abonent_kodu"=> $data['abonent_kodu'] ?? '',
            "muqavile_no"=> $data['muqavile_no'] ?? '',
            "ad_soyad"=> $data['ad_soyad'] ?? '',
            "ata_adi"=> $data['ata_adi'] ?? '',
            "unvan"=> $data['unvan'] ?? '',
            "voen_no"=> $data['voen_no'] ?? '',
        ];


    }
}
