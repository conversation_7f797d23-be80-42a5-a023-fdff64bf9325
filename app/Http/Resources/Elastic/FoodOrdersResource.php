<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class FoodOrdersResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = $this->resource['_source'];
        $data = $data['data'];

        $number = $data['phone']??'';
        if (strpos($number, '+994') === 0) {
            $number = substr($number, 4); // +994 prefiksindən sonra qalan nömrəni al
        }        
        // Nömrəni formatla: (0xx) xxx-xx-xx
        $number = sprintf('994%s%s%s%s',
            substr($number, 0, 2),   // (0xx)
            substr($number, 2, 3),   // xxx
            substr($number, 5, 2),   // xx
            substr($number, 7, 2)    // xx
        );

        return[
            "name"=> $data['name'] ?? '',
            "phone"=> $number ?? '',
            "email"=> $data['email'] ?? '',
            "address"=> $data['address'] ?? '',
            "more" => [
                "birthday"=> $data['data']['birthday'] ?? '',
                "gender"=> $data['data']['gender'] ?? '',
                "channel"=> $data['channel'] ?? '',
                "order_date"=> $data['order_date'] ?? '',
                "detail_info"=> $data['data'] ?? '',
            ]
        ];
    }
}
