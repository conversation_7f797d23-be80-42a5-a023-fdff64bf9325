<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class PersonsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $person = $this->resource['_source'];
        return [
            'pin' => $person['pin'] ?? '',
            'doc_number' => $person['doc_number'] ?? '',
            'name' => $person['name'] ?? '',
            'surname' => $person['surname'] ?? '',
            'father_name' => $person['father_name'] ?? '',
            'height' => $person['height'] ?? '',
            'age' => isset($person['birthday']) ? Carbon::parse($person['birthday'])->age : '',
            'birthday' => isset($person['birthday']) ? date('d.m.Y', strtotime($person['birthday'])) : '',
            // 'marital_status' => $person['marital_status'] == 1 ? 'Evli' : 'Subay',
            'marital_status' => $person['marital_status'] ?? '',

            'eye_color' => $person['eye_color'] ?? '',
            'blood_group' => $person['blood_group'] ?? '',
            'country' => $person['country'] ?? '',
            'city' => $person['city'] ?? '',
            'address' => $person['address'] ?? '',

            // 'img' => isset($person['image']) ? $person['image'] : '',
            $doc_type = isset($person['doc_number']) ? (substr($person['doc_number'],0,2)=='AA' ? '' : 'AZE') : '',
            'img_new' =>personImagePath($doc_type ?? '', $person['pin'] ?? ''),
            // 'img' => isset($person['image']) ? env('S3_BEEIN_LOC').preg_replace('~^.+/api/imgs_~', '/api/imgs_', $person['image']) : '',
            'danger_level' =>$person['danger_level'] ?? 0,
            'labels' => $person['labels'] ?? [],
            // 'graylist' => false,
        ];
    }
}
