<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class DsmfResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
  
          $data = $this->resource['_source']['data'];
        return [
            "CON_NUMBER"=> $data['CON_NUMBER'],
            "DOCTYPE"=> $data['DOCTYPE'],
            "DOC_SERIES"=> $data['DOC_SERIES'],
            "DOC_NUMBER"=> $data['DOC_NUMBER'],
            "PIN"=> $data['PIN'],
            "SURNAME"=> $data['SURNAME'],
            "NAME"=> $data['NAME'],
            "PATRONYMIC"=> $data['PATRONYMIC'],
            "GENDER"=> $data['GENDER'],
            "GENDER_TEXT"=> $data['GENDER_TEXT'],
            "BIRTH_DATE"=> $data['BIRTH_DATE'],
            "DOC_ISS_DATE"=> $data['DOC_ISS_DATE'],
            "DOC_EXPIRE_DATE"=> $data['DOC_EXPIRE_DATE'],
            "DOCISSORG"=> $data['DOCISSORG'],
            "ADDRESS_PIN"=> $data['ADDRESS_PIN'],
            "MOBILE_PHONE"=> $data['MOBILE_PHONE'],
            "EMPLOYER_TPN"=> $data['EMPLOYER_TPN'],
            "EMPLOYER_IRN"=> $data['EMPLOYER_IRN'],
            "EMPTYPE"=> $data['EMPTYPE'],
            "EMPTYPE_ID"=> $data['EMPTYPE_ID'],

            "SSN"=> $data['SSN'],
            "SSN_REG_DATE"=> $data['SSN_REG_DATE'],
            "EMPLOYER_NAME"=> $data['EMPLOYER_NAME'],
            "EMPLOYER_LEG_ADDRESS"=> $data['EMPLOYER_LEG_ADDRESS'],
            "EMPLOYER_CONTACTNUMBER"=> $data['EMPLOYER_CONTACTNUMBER'],
            "WORKPLACE"=> $data['WORKPLACE'],
            "POSITION"=> $data['POSITION'],

            "POSITION_MANUAL"=> $data['POSITION_MANUAL'],
            "CON_PERIOD_TYPE_ID"=> $data['CON_PERIOD_TYPE_ID'],
            "CON_PERIOD_TYPE_TEXT"=> $data['CON_PERIOD_TYPE_TEXT'],
            "WORK_CASUAL_TYPE"=> $data['WORK_CASUAL_TYPE'],
            "WORK_CASUAL_TYPE_TEXT"=> $data['WORK_CASUAL_TYPE_TEXT'],
            "WORKPLACE_TYPE_TEXT"=> $data['WORKPLACE_TYPE_TEXT'],
            "WORKPLACE_TYPE"=> $data['WORKPLACE_TYPE'],
            "INVALIDATION_ID"=> $data['INVALIDATION_ID'],
            "INVALIDATION_TEXT"=> $data['EMPLOYEINVALIDATION_TEXTR_NAME'],
      
            "more"=> [

            ]

        ];
        

        
    }
}
