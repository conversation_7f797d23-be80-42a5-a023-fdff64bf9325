<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\ResourceCollection;

class Taxi_189_DriversCollection extends ResourceCollection
{
    private array $pagination;
    private array $filters; 

    public function __construct($resource, array $filters = [])
    {
        $this->filters = $filters;

        $this->pagination = [
            'total' => $resource->total(),
            'perPage' => (int)$resource->perPage(),
            'currentPage' => $resource->currentPage(),
            'lastPage' => $resource->lastPage(),
            "path" => $resource->path(),
            "prev_page_url" => $resource->previousPageUrl(),
            "current_page_url" => $resource->url($resource->currentPage()),
            "next_page_url" => $resource->nextPageUrl(),
            'from' => $resource->firstItem(),
            'to' => $resource->lastItem(),
        ];

        $resource = $resource->getCollection();

        parent::__construct($resource);
    }

    /**
     * Transform the resource collection into an array.
     *
     * @param Request $request
     * @return array|Arrayable|\JsonSerializable
     */
    public function toArray($request): array|\JsonSerializable|Arrayable
    {
        return [
            'data' => $this->collection,
            'filters' => $this->filters, 
            'pagination' => $this->pagination,
        ];
    }
}

