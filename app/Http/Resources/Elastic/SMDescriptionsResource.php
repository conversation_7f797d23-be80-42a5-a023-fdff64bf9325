<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class SMDescriptionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $item = $this->resource['_source'];
        return [
            'platform_name' => $item['platform_name'] ?? '',
            'type' => 'description',
            'account' => [
                'id'          => $item['account_id'] ?? '',
                'pin'          => $item['account_pin'] ?? '',
                'name'          => $item['account_name'] ?? '',
                'surname'          => $item['account_surname'] ?? '',
                'username'          => $item['account_username'] ?? '',
                'link'          => $item['account_link'] ?? '',
                'photo_path' => !empty($item['account_photo_path']) ? env('SOCIAL_MEDIA_PHOTO_PATH').'all-pics/'.$item['account_photo_path'] : '',
            ],
            "description" => [
                'id' => $item['desc_id'] ?? '',
                'az' => $item['desc_aze'] ?? '',
                'en'    => $item['desc_eng'] ?? '',
                'photo_path' => !empty($item['desc_photo_path']) ? env('SOCIAL_MEDIA_PHOTO_PATH').'all-pics/'.$item['desc_photo_path'] : '',
            ],
            'person' => [
                'pin' => '',
                'name' => '',
                'surname' => '',
                'father_name' => '',
                'img' => '',

            ],
        ];
    }
}