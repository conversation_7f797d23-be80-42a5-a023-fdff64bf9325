<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class BorderCrossingEcnebiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = $this->resource['_source'];

        return [
            'document_number' => $data['doc_number'] ?? '',
            'given_name' => $data['name'] ?? '',
            'surname' => $data['surname'] ?? '',
            'birth_date' => $data['birthdate'] ?? '',
            'direction' => $data['direction'] === 'Girish' ? 'Arrival'  : 'Exit',
            'menteqe' => $data['point'] ?? '',
            'vetendashligi' => $data['citizenship'] ?? '',
            'crossing_date' => $data['crossing_date'] ?? '',
            'image' => $data['image'] ?? '',


        ];
    }
}
