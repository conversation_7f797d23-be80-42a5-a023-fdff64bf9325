<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class AvtovagzalResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = $this->resource['_source'];
        return [
            'id' => $data['id'] ?? '',
            'passenger_name' => $data['passenger_name'] ?? '',
            'passenger_phone' => $data['passenger_phone'] ?? '',
            'passenger_email' => $data['passenger_email'] ?? '',
            'ticket_phone' => $data['tickets_phone'] ?? '',
            'tickets_pin' => $data['tickets_pin'] ?? '',
            'tickets_name' => $data['tickets_name'] ?? '',
            'tickets_foreign_doc' => $data['tickets_foreign_doc'] ?? '',
            'tickets_foreign_country' => $data['tickets_foreign_country'] ?? '',
            'tickets_dqn' =>$data['tickets_dqn'] ?? '',
            'tickets_trip_name' =>$data['tickets_trip_name'] ?? '',

            'more' => [
                'passenger_gender' => $data['passenger_gender'] ?? '',
                'passenger_active' => $data['passenger_active'] ?? '',
                'tickets_doc_number' => $data['tickets_doc_number'] ?? '',
                'tickets_foreign_citizen' => $data['tickets_foreign_citizen'] ?? '',
                'tickets_trip_day' => $data['tickets_trip_day'] ?? '',
                'tickets_sale_date' => $data['tickets_sale_date'] ?? '',
                'tickets_time_start' => $data['tickets_time_start'] ?? '',
                'tickets_number' => $data['tickets_number'] ?? '',
                'tickets_duration' => $data['tickets_duration'] ?? '',
                'tickets_sub_trip' => $data['tickets_sub_trip'] ?? '', 
                'tickets_place' => $data['tickets_place'] ?? '',
                'tickets_seat' => $data['tickets_seat'] ?? '',
                'tickets_price' => $data['tickets_price'] ?? '',
                'tickets_ticket_type' => $data['tickets_ticket_type'] ?? '',
                'tickets_payment_status' => $data['tickets_payment_status'] ?? '',
            ],

        ];
    }
}
