<?php

namespace App\Http\Resources\Elastic;

use Illuminate\Http\Resources\Json\JsonResource;

class TapAzResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = $this->resource['_source'];
        $number = $data['data']['mobile_number'] ?? '';
        if (strlen($number) === 8) {
            $formattedNumber = sprintf('994%s%s%s%s',
                substr($number, 0, 2),   // 0xx
                substr($number, 2, 3),   // xxx
                substr($number, 5, 2),   // xx
                substr($number, 7, 1)    // x
            );
        }
        elseif (strlen($number) === 9) {
            $formattedNumber = sprintf('994%s%s%s%s',
                substr($number, 0, 2),   // 0xx
                substr($number, 2, 3),   // xxx
                substr($number, 5, 2),   // xx
                substr($number, 7, 2)    // x
            );
        } else {
            // Nömrə düzgün uzunluqda deyil
            $formattedNumber = $data['data']['mobile_number']?? '';
        }

        return [
            "phone"=> $formattedNumber ?? '',
            "seller_name"=> $data['data']['seller_name'] ?? '',
            "seller_email"=> $data['data']['seller_email'] ?? '',
            "elan_header"=> $data['data']['elan_header'] ?? '',
            "elan_desc"=> $data['data']['elan_desc'] ?? '',
            "region"=> $data['data']['region'] ?? '',
            "category"=> $data['data']['category'] ?? '',
            "ip"=> $data['data']['ip'] ?? '',
            "created_date"=> $data['data']['created_date'] ?? '',
            "last_update_date"=> $data['data']['last_update_date'] ?? '',
            "expire_date"=> $data['data']['expire_date'] ?? '',
            "more" => [
                "id"=> $data['data']['id'] ?? '',
                "elan_no"=> $data['data']['elan_no'] ?? '',
                "elan_url"=> $data['data']['elan_url'] ?? '',
                "image_url"=> $data['data']['image_url'] ?? '',
                "elan_attributes" => $data['data']['elan_attributes'] ?? '',
                "elan_price"=> $data['data']['elan_price'] ?? '',
                "view_count"=> $data['data']['view_count'] ?? '',
            ]
            

        ];
    }
}
