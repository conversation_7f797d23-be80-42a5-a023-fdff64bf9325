<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $direction
 * @property mixed $id
 * @property mixed $document_number
 * @property mixed $pin
 * @property mixed $distance
 * @property mixed $given_name
 * @property mixed $menteqe
 * @property mixed $vetendashligi
 * @property mixed $crossing_date
 * @property mixed $birth_date
 * @property mixed $image
 * @property mixed $surname
 */
class SearchLocalCustomsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'passport_id' => $this->document_number,
            'pin' => $this->pin,
            'distance' => $this->distance,
            'name' => $this->given_name,
            'surname' => $this->surname,
            'image' => $this->image,
            'birthdate' => $this->birth_date,
            'crossing_date' => $this->crossing_date,
            'direction' => $this->direction,
            'direction_status' => convertDirectionStatusToInt($this->direction),
            'citizenship' => $this->vetendashligi,
            'point' => $this->menteqe,
        ];
    }
}
