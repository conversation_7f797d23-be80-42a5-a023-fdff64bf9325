<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $label
 */
class ForeignCitizenshipCountriesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            // 'id' => $this->ID,
            'label' => $this->label,
            'value'=>$this->label,
        ];
    }
}
