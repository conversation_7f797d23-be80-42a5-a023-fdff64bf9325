<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class RelationTwoPersonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'person_fin' => $this['pin'],
            'person_name' => $this['name'],
            'person_surname' => $this['surname'],
            'person_fatherName' => $this['father_name'],
            'person_type' => $this['doc_serial_number'] == 'AZE' ? 'old' : 'new',
            'photo' => $this->syncImg(),
            'image' => $this->generateImageUrlAttribute(),
            'birthDate' => Carbon::parse($this->birthdate)->format("d-m-Y") ?? ''
        ];
    }
}
