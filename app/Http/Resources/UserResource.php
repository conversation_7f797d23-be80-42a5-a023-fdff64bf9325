<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Cache;

/**
 * @property mixed $id
 * @property mixed $name
 * @property mixed $surname
 * @property mixed $created_at
 * @property mixed $roles
 * @property mixed $entity_name
 * @property mixed $phone_number
 * @property mixed $status
 * @property mixed $email
 * @property mixed $is_system
 * @property mixed $gray_list
 * @property mixed $tester_list
 * @property mixed $profile_view
 */
class UserResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'surname' => $this->surname,
            'email' => $this->email,
            'entity_name' => $this->entity_name,
            'phone_number' => $this->phone_number,
            'status' => $this->status,
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d H:i:s'),
            'roles' => RoleResource::collection($this->roles),
            'root_user' => (bool) $this->is_system,
            'access_gray_list' => (bool) $this->gray_list,
            'access_tester_list' => (bool) $this->tester_list,
            'access_profile_view' => (bool) $this->profile_view,
            'has_work' => Cache::has('user_work_'.$this->id)
        ];
    }
}
