<?php

namespace App\Http\Resources\BlackList;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $name
 * @property mixed $surname
 * @property mixed $father_name
 * @property mixed $birthdate
 * @property mixed $document_number
 * @property mixed $pin
 * @property mixed $photo
 * @property mixed $gender
 */
class BlacklistResource extends JsonResource
{

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'surname' => $this->surname,
            'father_name' => $this->father_name,
            'birthdate' => $this->humanize_birthdate,
            'document_number' => $this->document_number,
            'pin'  => $this->pin,
            'photo' => env('APP_URL') . "/api/v1/get-file/show-blacklist-image/" . basename($this->photo),
            'gender' => $this->gender,
            'note' => $this->note,
            'status' => $this->status,
        ];
    }
}
