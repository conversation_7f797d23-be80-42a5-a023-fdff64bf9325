<?php

namespace App\Http\Resources\BlackList;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $id
 * @property mixed $birthdate
 * @property mixed $document_number
 * @property mixed $gender
 * @property mixed $name
 * @property mixed $surname
 * @property mixed $father_name
 * @property mixed $pin
 * @property mixed $photo
 * @property mixed $similarities
 * @property mixed $note
 */
class ShowBlacklistResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'birthdate' => $this->birthdate,
            'document_number' => $this->document_number,
            'gender' => $this->gender,
            'name' => $this->name,
            'surname' => $this->surname,
            'father_name' => $this->father_name,
            'pin' => $this->pin,
            'photo' => $this->photo,
            'note' => $this->note,
            'similarities' => BlacklistSimilarlyResource::collection($this->similarities->sortByDesc('created_at')),
        ];
    }
}
