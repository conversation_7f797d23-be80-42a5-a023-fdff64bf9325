<?php

namespace App\Http\Resources\BlackList;

use App\Services\SurveillanceService;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

class NewBlackListSimilarityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            '_id' => $this->_id,
            'blacklist_id' => $this->blacklist_id,
            'date' => $this->date,
            'bucket_name' => $this->bucket_name,
            'name' => $this->name,
            'surname' => $this->surname,
            'face_name' => $this->face_name ? $this->addImagePath($this->face_name) : '',
            'frame_name' => $this->frame_name ? $this->addImagePath($this->frame_name) : '',
            'created_at' => Carbon::parse($this->created_at)->format("Y-m-d H:i:s"),
            'blacklist' => BlacklistResource::make($this->blacklist)
        ];
    }

    public function addImagePath($image): string
    {
        return 'http://10.15.50.11:8580/surveillance-photos/' . $image;
    }


}
