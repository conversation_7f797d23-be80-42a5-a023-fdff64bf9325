<?php

namespace App\Http\Resources\BlackList;

use App\Services\SurveillanceService;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;


/**
 * @property mixed $_id
 * @property mixed $name
 * @property mixed $surname
 * @property mixed $father_name
 * @property mixed $pin
 * @property mixed $birthdate
 * @property mixed $camera_id
 * @property mixed $camera_ip
 * @property mixed $camera_name
 * @property mixed $distance
 * @property mixed $face_coordinates
 * @property mixed $timestamp
 * @property mixed $similarity_photo
 * @property mixed $photo
 * @property mixed $created_at
 * @property mixed $updated_at
 */
class BlacklistSimilarlyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            '_id' => $this->_id,
            'name' => $this->name,
            'surname' => $this->surname,
            'father_name' => $this->father_name,
            'pin' => $this->pin,
            'birthdate' => Carbon::parse($this->birthdate)->format("d-m-Y"),
//            'birthdate' => ($this->birthdate != null || $this->birthdate != '') ? Carbon::parse($this->birthdate)->format("Y-m-d H:i:s") : '',
            'camera' => [
                'id' => $this->camera_id,
                'ip' => $this->camera_ip,
                'name' => $this->camera_name,
            ],
            'distance' => $this->distance,
            'face_coordinates' => $this->face_coordinates,
            'timestamp' => Carbon::createFromTimestamp($this->timestamp)->format("d/m/Y H:i:s"),
            'photo' => $this->photo,
            'similarity_photo' => $this->similarity_photo,
            'origin_photo' => $this->similarity_photo ? SurveillanceService::filterOriginPhoto(
                Str::afterLast($this->similarity_photo, '/')
            ) : '',
            'created_at' => Carbon::parse($this->created_at)->format("Y-m-d H:i:s")
        ];
    }
}
