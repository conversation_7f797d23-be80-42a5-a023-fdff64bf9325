<?php

namespace App\Http\Resources\Basket;

use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $_id
 * @property mixed $type
 * @property mixed $data
 * @property mixed $user_id
 */
class BasketResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            '_id' => $this->_id,
            'user_id' => $this->user_id,
            'type' => $this->type,
            'data' => $this->data,
        ];
    }
}
