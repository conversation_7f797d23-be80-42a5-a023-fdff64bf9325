<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class LogHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            "date"=> $this->date,
            "user_id"=> $this->user_id,
            "ip"=> $this->ip,
            "user_name"=> $this->user_name.' '.$this->user_surname,
            "url"=> $this->url,
            "method"=> $this->method,
            "body"=> $this->body,
            "status"=> $this->status,
            "message"=> $this->message,
        ];
    }
}
