<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $id
 * @property mixed $name
 * @property mixed $guard_name
 * @property mixed $created_at
 * @property mixed $parent_id
 * @property mixed $translation_name
 * @property mixed $parent
 * @property mixed $child
 */
class PermissionResource extends JsonResource
{
    /**
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'parent_id' => $this->parent_id,
            'name' => $this->name,
            'translation_name' => $this->translation_name,
            'parent' => $this->parent,
            'child' =>$this->child,
            'children' =>$this->child,
            'guard_name' => $this->guard_name,
            'created_at' => Carbon::parse($this->created_at)->format('Y-m-d H:i:s')
        ];
    }
}
