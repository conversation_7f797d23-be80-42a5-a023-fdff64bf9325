<?php

namespace App\Http\Resources\ForeignCitizen;

use App\Models\SavedData;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

/**
 * @property mixed $_id
 * @property mixed $name
 * @property mixed $surname
 * @property mixed $birthday
 * @property mixed $document_number
 * @property mixed $vetendashligi
 * @property mixed $number_of_people
 */
class ForeignCitizenRecordsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $userId = Auth::id();

        return [
            '_id' => $this->_id,
            'name' => $this->name,
            'surname' => $this->surname,
            'birthday' => $this->birthday,
            'document_number' => $this->document_number,
            'vetendashligi' => $this->vetendashligi,
            'data_count' => $this->number_of_people,
            'saved' => in_array($userId, $value['saved_users'] ?? [], true),
            'is_saved' => SavedData::where(['data_id' => $this->_id->__toString(), 'user_id' => $userId])->exists()
        ];
    }
}
