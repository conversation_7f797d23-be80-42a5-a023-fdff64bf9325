<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Services\SurveillanceService;
use Illuminate\Http\Resources\Json\JsonResource;
/**
 * @property mixed $_source
 */
class SurveillanceFacesResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'session_name' => $this['_source']['session_name'],
            'camera' => CameraSurveillanceResource::make($this['camera']),
            'vector_id' => $this['_source']['vector_id'],
            'alignment_error' => $this['_source']['alignment_error'],
            'distance' => $this['distance'],
            "timestamp" => Carbon::createFromTimestamp($this['_source']['timestamp'])->format('d.m.Y, H:i'),
            'photo' => SurveillanceService::deepFacePhotoEndpoint() . $this['_source']['photo'],
            'origin_photo' => SurveillanceService::filterOriginPhoto($this['_source']['photo']),
            'face_coordinates' => $this['_source']['face_coordinates'] ?? null,
        ];
    }
}
