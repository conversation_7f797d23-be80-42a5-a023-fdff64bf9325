<?php

namespace App\Http\Resources\SocialCalls;

use Illuminate\Http\Resources\Json\ResourceCollection;

class SocialCallsCollection extends ResourceCollection
{
    private $pagination;

    public function __construct($resource)
    {
        $this->pagination = [
            'total' => $resource->total(),
            'perPage' => (int)$resource->perPage(),
            'currentPage' => $resource->currentPage(),
            'lastPage' => $resource->lastPage(),
            "path"=>$resource->path(),
            "prev_page_url"=>$resource->previousPageUrl(),
            "current_page_url" => $resource->url($resource->currentPage()),
            "next_page_url"=>$resource->nextPageUrl(),
            'from' => $resource->firstItem(),
            'to' => $resource->lastItem(),
        ];


        $resource = $resource->getCollection(); // Necessary to remove meta and links

        parent::__construct($resource);
    }
    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'data' => $this->collection,
            'pagination' => $this->pagination,
        ];
    }
}
