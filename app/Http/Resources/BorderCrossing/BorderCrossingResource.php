<?php

namespace App\Http\Resources\BorderCrossing;

use Illuminate\Http\Resources\Json\JsonResource;

class BorderCrossingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $image = $this->pin ? env('BACKEND_SERVER').'/handled_citizen_images/'.$this->id.'.jpg' : env('BACKEND_SERVER').'/handled_images/'.$this->id.'.jpg';
        return [
            "id" => $this->id,
            "pin"           => $this->pin,
            "name" =>  $this->given_name,
            "surname" =>  $this->surname,
            "father_name" =>  $this->patronymic,
            "doc_number"    => $this->document_number,
            "crossing_date" =>  $this->crossing_date,
            "birthdate"    =>  $this->birth_date,
            "direction" => $this->direction == 'Girish' ? 'Arrival'  : 'Exit',
            "point"        =>  $this->menteqe,
            "citizenship" =>  $this->vetendashligi,
            "image" => $image,
        ];

 
    }
}
