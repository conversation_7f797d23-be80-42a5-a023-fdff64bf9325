<?php

namespace App\Http\Resources\Object;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property mixed $gps
 * @property mixed $id
 * @property mixed $name
 * @property mixed $address
 * @property mixed $object_type_id
 * @property mixed $type
 * @property mixed $children
 */
class ObjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'address' => $this->address,
            'gps' => $this->gps,
            'type_id' => $this->object_type_id,
            'type' =>  new ObjectTypesListResource($this->type),
            'parent' => $this->parent ?? null,
            'isHasChild' => (bool) $this->children->count()
        ];
    }
}
