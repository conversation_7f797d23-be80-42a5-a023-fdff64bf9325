<?php

namespace App\Http\Controllers\Api\Drill;

use App\Http\Controllers\Controller;
use App\Models\SearchRequests;
use App\Services\Drill\SearchHistoryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
//use Illuminate\Http\Client\Pool;

class SearchHistoryController extends Controller
{
    private mixed $authorizationHeader;
    protected SearchHistoryService $filterService;

    public function __construct(SearchHistoryService $filterService)
    {
        $this->filterService = $filterService;
        $username = config('services.drill.username');
        $password = config('services.drill.password');
        $this->authorizationHeader = 'Basic ' . base64_encode("{$username}:{$password}");
    }

    public function resultList(Request $request): JsonResponse
    {
        $searchRequests = SearchRequests::where('is_deleted', false)
            ->where('operation_type', '!=', 'ELASTIC_SEARCH')
            ->where('created_by', auth('api')->user()?->id)
            ->where(function ($query) {
                $query->whereJsonContains('result->has_data', true)
                    ->orWhereJsonContains('result->azparking-data->has_data', true)
                    ->orWhereJsonContains('result->din-api-result->has_data', true);
            })
            ->latest('created_at')
            ->paginate(request('per_page', 100), [
                'id', 'operation_type', 'operation_result_name', 'operation_status', 'result', 'created_at', 'updated_at', 'created_by'
            ]);
        $decodedRequests = $searchRequests->getCollection()->map(function ($item) {
            $item->result = json_decode($item->result, true);
            $operationTypeNames = [
                'ELASTIC_SEARCH' => 'Elastik axtarış',
                'PHONE_SIMILARITY_SEARCH@CAR_BY_PHONE' => 'Maşın nömrəsinə görə oxşarlıq',
                'PHONE_SIMILARITY_SEARCH@FIND_INTERSECTED_LOCATION_BY_PHONE' => 'Telefon nömrəsinə görə kəsişmə',
                'PHONE_SIMILARITY_SEARCH@INTERVAL_SIMILARITY_BY_LOCATIONS' => 'Səfər ehtimalı',
                'PHONE_SIMILARITY_SEARCH@NATIVE_AND_STRANGER' => 'Yerlilər və özgələr',
                'PHONE_SIMILARITY_SEARCH@PHONE_BY_PHONE' => 'Telefon nömrəsinə görə oxşarlıq',
                'PHONE_SIMILARITY_SEARCH@SEARCH_ON_AREA' => 'Ərazi üzrə axtarış',
                'PHONE_SIMILARITY_SEARCH@TWO_NUMBER_SIMILARITY' => 'Görüş üzrə',
                'SEARCH_CAR_TRAJECTORY' => 'Maşın nömrəsinə görə trayektoriya',
                'SEARCH_PHONE_NUMBER_TRAJECTORY' => 'Telefon nömrəsinə görə trayektoriya',
                'USER_SAVE' => 'Yadda saxlanılan nəticələr'
            ];
            if (isset($operationTypeNames[$item->operation_type])) {
                $item->operation_type = $operationTypeNames[$item->operation_type];
            }
            return $item;
        });
        $searchRequests->setCollection($decodedRequests);
        return response()->json([
            'status' => 'Success',
            'data' => $searchRequests,
            'code' => 200,
            'message' => 'response ok'
        ]);
    }

    public function resultShow(Request $request): JsonResponse
    {
        $searchID = $request->input('id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 1000);
        $response = $this->filterService->getResult($searchID, $page, $perPage);
        return response()->json($response);
    }
    public function filterResult(Request $request): JsonResponse
    {
        $filterModel = $request->except(['columns','requestID','polygon','work_name','per_page']);
        $polygon = $request->input('polygon');
        $columns = $request->input('columns');
        $modifiedFilterModel = [];
        foreach ($filterModel as $key => $value) {
            $modifiedFilterModel["response.$key"] = $value;
        }
        $path = $this->filterService->getSearchRequest($request->input('requestID'));
        $path = json_decode($path->result, true);
        $query = null;
        if (is_array($path)) {
            if (array_key_exists('file', $path)) {
                if(!$path['has_data']){
                    return $this->filterService->generateErrorResponse($path);
                }
                $query = $this->filterService->buildApacheDrillQuery($path['file'],$modifiedFilterModel, $polygon,$columns);
            } else {
                foreach ($path as $key => $value) {
                    if (is_array($value) && $value['has_data']) {
                        $query = $this->filterService->buildApacheDrillQuery($value['file'], $modifiedFilterModel, $polygon, $columns,$key);
                    }
                }
            }
        }
        $client = new Client();
        $response = $client->post(config('services.drill_query_url'), [
            'headers' => [
                'Content-Type' => 'application/json',
                'Authorization' => $this->authorizationHeader
            ],
            'json' => [
                'queryType' => 'SQL',
                'query' => $query
            ]
        ]);
        $data = json_decode($response->getBody(), true);
        $originalData = [
            'queryId' => $data['queryId'],
            'columns' => $data['columns'],
            'metadata' => $data['metadata'],
            'attemptedAutoLimit' => $data['attemptedAutoLimit'],
            'queryState' => $data['queryState'],
            'rows' => []
        ];
        if($polygon == null){
            foreach ($data['rows'] as $key => $value) {
                if (isset($value['coordinates'])) {
                    $decodedSignals = json_decode($value['coordinates'], true);
                    $processedSignals = [];
                    if (is_array($decodedSignals)) {
                        foreach ($decodedSignals as $signal) {
                                $processedSignals[] = $signal;
                        }
                    }
                    $value['coordinates'] = $processedSignals;
                }
                $originalData['rows'][] = $value;
            }
            if (isset($originalData['rows']) && is_array($originalData['rows'])) {
                if(count($originalData['rows'])>1){
                    foreach ($originalData['rows'] as &$row) {
                        if (isset($row['coordinates'])) {
                            $decodedCoordinates = $row['coordinates'];
                            if (is_array($decodedCoordinates) && !empty($decodedCoordinates)) {
                                $singleObjectArray = [$decodedCoordinates[0]];
                                $row['coordinates'] = $singleObjectArray;
                            } else {
                                $row['coordinates'] = null;
                            }
                        }
                    }
                    unset($row);
                }
            }
            return response()->json([
                'original' => $originalData
            ]);
        }

        foreach ($data['rows'] as $key => $value) {
            if (isset($value['coordinates'])) {
                $decodedSignals = json_decode($value['coordinates'], true);
                $processedSignals = [];
                if (is_array($decodedSignals)) {
                    foreach ($decodedSignals as $signal) {
                        foreach ($signal as $s) {
                            $processedSignals[] = json_decode($s, true);
                        }
                    }
                }
                $value['coordinates'] = $processedSignals;
            }
            $originalData['rows'][] = $value;
        }
        if (isset($originalData['rows']) && is_array($originalData['rows'])) {
            if(count($originalData['rows'])>1){
                foreach ($originalData['rows'] as &$row) {
                    if (isset($row['coordinates'])) {
                        $decodedCoordinates = $row['coordinates'];
                        if (is_array($decodedCoordinates) && !empty($decodedCoordinates)) {
                            $singleObjectArray = [$decodedCoordinates[0]];
                            $row['coordinates'] = $singleObjectArray;
                        } else {
                            $row['coordinates'] = null;
                        }
                    }
                }
                unset($row);
            }
        }
        return response()->json([
                'original' => $originalData
        ]);
    }
    public function intersectResult(Request $request): JsonResponse
    {
        $validatedData = $request->validate([
            'result_id' => 'required|array|max:2',
        ]);
        $resultIds = $validatedData['result_id'];
        $columnName = $request->input('column_name','phone');
        $sqlQuery = $this->filterService->generateIntersectQuery($resultIds,$columnName);
        $sqlQuery = $sqlQuery . ' LIMIT 1000';
        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => $this->authorizationHeader
        ])->post(config('services.drill_query_url'), [
            'queryType' => 'SQL',
            'query' => $sqlQuery
        ]);
        $responseData = $response->json();
        return response()->json($responseData);
    }
    public function saveIntersectedResult(Request $request): JsonResponse
    {
        $resultIds = $request->input('result_id');
        $columnName = $request->input('column_name','phone');
        $sqlQuery = $this->filterService->generateIntersectQuery($resultIds,$columnName);
        $correlationID = (string)$request->input('correlationID', 'correlation-id');
        $operationResultName = $request->input('operation_result_name', 'RESULT NAME');
        $tableName = generateULID();
        $insertSQL = "insert into public.search_requests (id, correlation_id, operation_type, operation_status, operation_result_name, created_by, journey) values (?, ?, 'USER_SAVE', 300, ?, ?, '[]'::jsonb)";
        $results = DB::connection('saved_results')->insert($insertSQL, [
            $tableName,
            $correlationID,
            $operationResultName,
            auth('api')->user()?->id
        ]);
        if ($results) {
            $beforeStartingJourney = "UPDATE public.search_requests
                                        SET journey = journey || jsonb_build_object(
                                            'code', null,
                                            'date', now(),
                                            'kind', 'LOG',
                                            'message', 'Processing started'
                                        ),
                                        updated_at = now()
                                        WHERE id = ?";
            DB::connection('saved_results')->update($beforeStartingJourney, [
                $tableName
            ]);
            $sqlQuery = "CREATE TABLE dfs.tmp.`/{$tableName}` AS (" . $sqlQuery ." LIMIT 1000 " . ")";
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => $this->authorizationHeader
            ])->post(config('services.drill_query_url'), [
                'queryType' => 'SQL',
                'query' => $sqlQuery
            ]);
            $data = $response->json()['rows'];
            $numberOfRecordsWritten = $data[0]['Number of records written'] ?? null;
            if ($numberOfRecordsWritten !== null) {
                $successfulProcessUpdateJourney = "UPDATE public.search_requests
                                        SET journey = journey || jsonb_build_object(
                                            'code', 'SUCCESS',
                                            'date', now(),
                                            'kind', 'STAGE_RESULT',
                                            'message', 'Stage: 1 processed successfully'
                                        ),
                                        updated_at = now(),
                                        operation_status = 200
                                        WHERE id = '$tableName'";
                DB::connection('saved_results')->update($successfulProcessUpdateJourney);
                $hasDataOrNot = ($numberOfRecordsWritten != 0);
                $hasDataValue = $hasDataOrNot ? 'true' : 'false';
                $finalResultJourney = "UPDATE public.search_requests
                                        SET result = jsonb_build_object(
                                            'file', '/datas3/saved-results/users-saves/{$tableName}/*',
                                            'has_data', {$hasDataValue}
                                        ),
                                        updated_at = now(),
                                        journey = journey || jsonb_build_object(
                                            'code' , null,
                                            'date' , now(),
                                            'kind', 'LOG',
                                            'message', 'Processing finished'
                                        )
                                        WHERE id = '{$tableName}'";
                DB::connection('saved_results')->update($finalResultJourney);
                return response()->json(['saved_records'=>$numberOfRecordsWritten]);
            }
            return response()->json('Nəticə yoxdur');
        }
        return response()->json();
    }

    public function getCommonColumns(Request $request): JsonResponse{
        $validatedData = $request->validate([
            'result_id' => 'required|array|max:2',
        ]);
        $resultIds = $validatedData['result_id'];
        $response = $this->filterService->getCommonColumns($resultIds);
        return response()->json($response);
    }

    public function deleteResults(Request $request): JsonResponse
    {
        $resultIds = $request->input('result_id');
        $updatedCount = $this->filterService->deleteResults($resultIds);
        if ($updatedCount > 0) {
            return response()->json([
                'status' => 'Success',
                'message' => "$updatedCount axtarış nəticəsi uğurla silindi.",
                'code' => 200
            ]);
        }
        return response()->json([
            'status' => 'Error',
            'message' => 'Nəticələr silinmədi.',
            'code' => 404
        ]);
    }
}
