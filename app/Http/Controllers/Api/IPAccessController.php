<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\IPAccessService;
use App\Models\IPAccess;
use App\Http\Requests\IPAccessRequest;
use Illuminate\Http\JsonResponse;

class IPAccessController extends Controller
{
    private IPAccessService $ipAccessService;
    public function __construct(IPAccessService $ipAccessService)
    {
        $this->ipAccessService = $ipAccessService;
    }

    public function index(): JsonResponse
    {
        $ipAccesses = $this->ipAccessService->getAllIpAccess();
        return response()->json(['data' => $ipAccesses]);
    }
    public function userIpAccess(int $userId): JsonResponse
    {
        $data = $this->ipAccessService->getIpAccessByUser($userId);
        return response()->json(['data' => $data]);
    }
    public function store(IPAccessRequest $request)
    {
        $ipAccess = $this->ipAccessService->create($request->validated());
        return response()->json([
            'message' => 'IP access record created successfully',
            'data' => $ipAccess
        ], 201);
    }
    public function update(IPAccessRequest $request, IPAccess $ipAccess): JsonResponse
    {
        $ipAccess = $this->ipAccessService->update($ipAccess, $request->validated());
        return response()->json([
            'message' => 'IP access record updated successfully',
            'data' => $ipAccess
        ]);
    }
    public function destroy(IPAccess $ipAccess): JsonResponse
    {
        $this->ipAccessService->delete($ipAccess);
        return response()->json([
            'message' => 'IP access record deleted successfully'
        ]);
    }
}
