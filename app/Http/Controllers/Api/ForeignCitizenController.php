<?php

namespace App\Http\Controllers\Api;

use App\Models\ForeignCitizen;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Resources\ForeignCitizen\ForeignCitizenResource;
use App\Http\Resources\ForeignCitizen\ShowForeignCitizenResource;
use App\Http\Resources\ForeignCitizen\ForeignCitizenRecordsResource;

class ForeignCitizenController extends Controller
{
    use ApiResponsible;

    /**
     * @OA\Get  (
     *      path="/foreign-citizen?page=1",
     *      operationId="foreign-citizen",
     *      tags={"Xarici vətəndaşlar"},
     *      summary="Xarici vətəndaşlar servisi",
     *      description="Xarici vətəndaşlar",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @return JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function index (): JsonResponse
    {
        $aggregations = ['data' => ['$exists' => 'true', '$ne' => []]];
        $filterConditions = [];
        $page = (int) request()->get('page', 1);
        $perPage = (int) request()->get('per_page', 10);
        $orderType = request()->input('order_type') ?? 'desc';
        $minCount = (int) request()->input('min_count', 0);
        $mongoOrderType = (string) $orderType == 'asc' ? 1 : -1;

        if (request()->get('name')) {
            $aggregations['name'] = ['$regex' => request()->get('name'), '$options' => 'i'];
        }

        if (request()->get('surname')) {
            $aggregations['surname'] = ['$regex' => request()->get('surname'), '$options' => 'i'];
        }

        if (request()->get('passport_id')) {
            $aggregations['document_number'] = request()->get('passport_id');
        }

        if (request()->get('citizen')) {
            $aggregations['vetendashligi'] = request()->get('citizen');
        }

        if (request()->get('precinct')) {
            $aggregations['data.menteqe'] = ['$eq' => request()->get('precinct')];

            $filterConditions['$and'][]['$eq'] = [
                '$$d.menteqe',
                request()->get('precinct')
            ];
        }

        if (request()->get('similar_percent')) {
            $aggregations['data.similar_percent'] = ['$lte' => (int)request()->get('similar_percent')];
            $filterConditions['$and'][]['$lte'] = [
                '$$d.similar_percent',
                (int)request()->get('similar_percent')
            ];
        }

        if (request()->get('similarity')) {
            $aggregations['data.distance'] = ['$gt' => (float)request()->get('similarity', 0)];
            $filterConditions['$and'][]['$gt'] = [
                '$$d.distance',
                (float)request()->get('similarity')
            ];
        }

        if (request()->has('saved')) {
            $aggregations['saved_users'] = ['$in' => [Auth::id()]];
        }

        $bodyAggregate = [
            [
                '$match' => $aggregations,
            ],
            [
                '$project' => [
                    'filtered_data' => [
                        '$filter' => [
                            'input' => '$data',
                            'as' => 'd',
                            'cond' => $filterConditions
                        ]
                    ],
                    '_id' => 1,
                    'id' => 1,
                    'name' => 1,
                    'surname' => 1,
                    'birthday' => 1,
                    'number_of_people' => 1,
                    'document_number' => 1,
                    'vetendashligi' => 1,
                    'menteqe' => 1,
                    'direction' => 1,
                    'crossing_date' => 1,
                ]
            ],
            [
                '$match' => [
                    '$expr' => ['$gte' => [['$size' => '$filtered_data'], $minCount]]
                ],
            ],
            [
                '$limit' => 1000
            ],
            [
                '$sort' => [
                    request()->input('order_column') ?? '_id' => $mongoOrderType
                ]
            ],
            [
                '$facet' => [
                    'paginatedResults' => [
                        ['$skip' => ($page - 1) * $perPage],
                        ['$limit' => $perPage],

                    ],
                    'totalCount' => [
                        ['$count' => 'count'],
                    ]
                ]
            ],
        ];

        $collection = $this->query($aggregations, $bodyAggregate);

//        $data = $collection->paginate($perPage, ['*'], 'page', $page);

        $items = $collection[0]['paginatedResults'] ?? [];
        $total = count($items) == 0 ? 0 : $collection[0]['totalCount'][0]['count'] ?? 0;

        $data = new LengthAwarePaginator($items, $total, $perPage, $page);

        return ForeignCitizenResource::collection($data)->response();
    }

    /**
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Psr\Container\ContainerExceptionInterface
     * @throws \Psr\Container\NotFoundExceptionInterface
     */
    public function showSimilarities(string $id): JsonResponse
    {
        /**
         * @var $data ForeignCitizen
         */
        $data = ForeignCitizen::query()->findOrFail($id);

        $filteredData = array_filter($data->data, static function ($value) {
            if (
                ($value['similar_percent'] <= (int) request()->get('similar_percent', 0)) &&
                ((float) (request()->get('similarity') ?? 0) < (float)$value['distance']) &&
                (request()->get('precinct') == '' || request()->get('precinct') == $value['menteqe'])
            ) {
                return $value;
            }
        });

        $data->data = array_values($filteredData);

        return ShowForeignCitizenResource::make($data)->response();
    }

    /**
     * @param string $docNumber
     * @return \Illuminate\Http\JsonResponse
     */
    public function showRecords(string $docNumber): JsonResponse
    {
        $data = ForeignCitizen::query()
            ->where('document_number', $docNumber)
            ->get();

        return ForeignCitizenRecordsResource::collection($data)->response();
    }

    /**
     * @param array $aggregations
     * @param array $bodyAggregate
     * @return mixed
     */
    private function query(array $aggregations, array $bodyAggregate): mixed
    {
        return ForeignCitizen::query()
            ->when(count($aggregations) > 0, function ($q) use ($bodyAggregate) {
                return $q->raw(function ($collection) use ($bodyAggregate) {
                    return $collection->aggregate($bodyAggregate);
                });
            });
    }
}
