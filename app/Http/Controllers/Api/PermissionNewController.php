<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\PermissionUpdateRequest;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\PermissionRequest;
use App\Http\Resources\PermissionResource;
use Spatie\Permission\PermissionRegistrar;
use Symfony\Component\HttpFoundation\Response;

class PermissionNewController extends Controller
{

    public function __construct()
    {
        $this->middleware('permission:permissions', ['only' => ['index']]);
        $this->middleware('permission:permissions:read', ['only' => ['show']]);
        $this->middleware('permission:permissions:create', ['only' => ['store']]);
        $this->middleware('permission:permissions:update', ['only' => ['update']]);
        $this->middleware('permission:permissions:delete', ['only' => ['destroy']]);
    }


    public function index(Request $request): JsonResponse
    {
        /** @var $permissions Permission */
        $permissions = Permission::query();
        $permissions->select('id','parent_id','name as translation_name','translation_name as name','created_at');
        $permissions->with('parent');
        $permissions->whereNull('parent_id');
        $permissions->with('children', function ($query) {
            $query->select('id','parent_id','name as translation_name','translation_name as name','created_at');
            $query->setEagerLoads([]);
            $query->with('parent');
        });

        if ($request->filled('search')) {
            $permissions->search('name', $request->get('search'));
            $permissions->orSearch('translation_name', $request->get('search'));
        }

        $permissions->orderBy('id', 'DESC');
        $permissions->whereNull('parent_id');

        $permissions = $permissions->paginate(99999);

        //if children is empty, remove key from array

        $permissions = $permissions->toArray();
        $permissions['data'] = array_map(function($permission) {
            if (empty($permission['children'])) {
                unset($permission['children']);
            }
            return $permission;
        }, $permissions['data']);

        return response()->json($permissions);
    }

    public function store(PermissionRequest $request): PermissionResource
    {
        $validated = $request->validated();

        $permission = Permission::create($validated);

        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        return new PermissionResource($permission);
    }

    public function show(int $id): PermissionResource
    {

        return new PermissionResource(Permission::findOrFail($id));
    }

    public function update(PermissionUpdateRequest $request, int $id): PermissionResource
    {

        $validated = $request->validated();

        $permission = Permission::findOrFail($id);

        $permission->update($validated);

        $permission->fresh();

        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        return new PermissionResource($permission);
    }

    public function destroy(int $id): JsonResponse
    {
        $permission = Permission::findOrFail($id);
        $permission->delete();
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        return response()->json([], Response::HTTP_NO_CONTENT);
    }
}
