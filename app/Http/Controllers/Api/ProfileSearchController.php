<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Person;
use App\Models\ProfileSearchHistory;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class ProfileSearchController extends Controller
{
    protected ProfileSearchHistory $model;

    public function __construct(ProfileSearchHistory $model)
    {
        $this->model = $model;
    }


    public function index(): JsonResponse
    {
        $data = $this->model->query();
        $data->selectRaw('MAX(id) AS id,COUNT(1) AS count,"pin","user_id", DATE(created_at)');

        if (auth('api')->user()->is_system !== '1') {
            $data->where('user_id', auth('api')->id());
        }
        $data->orderBy('id', 'desc');
        $data->groupBy(['pin', 'user_id',DB::Raw('DATE(created_at)')]);


        $data = $data->paginate(request()->get('per_page', 10));
        $data = $data->through(function ($item) {
            $item->user = User::query()->select('id', 'name','surname','email')
                ->where('id',$item->user_id)
                ->first();
            $item->user?->setAppends([]);
            $item->person = Person::query()->select('id', 'name','surname','father_name')
                ->where("pin",$item->pin)->first();
            $item->person?->setAppends([]);
            return $item;
        });


        return response()->json($data );


    }
}
