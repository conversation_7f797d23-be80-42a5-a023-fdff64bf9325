<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CheckServiceTest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\File;
use JetBrains\PhpStorm\ArrayShape;
use Symfony\Component\Process\Process;

class TestApiController extends Controller
{
    public function allTestFiles(): JsonResponse
    {
        $basePath = base_path('tests/Feature');
        $folders = File::directories($basePath);
        $response = [];

        foreach ($folders as $folder) {
            $folderName = basename($folder);

            if (ctype_upper($folderName[0])) {
                $files = File::files($folder);
                $fileNames = array_filter(
                    array_map(fn($file) => $file->getFilename(), $files),
                    fn($fileName) => str_ends_with($fileName, '.php')
                );

                $fileNames = array_map(
                    fn($fileName) => pathinfo($fileName, PATHINFO_FILENAME),
                    $fileNames
                );

                $response[$folderName] = $fileNames;
            }
        }


        return response()->json($response);
    }

    public function runTests(Request $request): JsonResponse
    {
//        $basePath = base_path('tests/Feature');
        $basePath = base_path('tests/Feature');
        $selectedFolders = $request->input('folders', ['Profile', 'Analytic']);

        if (empty($selectedFolders)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Please specify at least one folder.'
            ], 400);
        }

        $testFiles = [];

        foreach ($selectedFolders as $folder) {
            $folderPath = "$basePath/$folder";
            if (File::isDirectory($folderPath)) {
                $filesInFolder = collect(File::allFiles($folderPath))
                    ->filter(fn($file) => str_ends_with($file->getFilename(), 'Test.php'))
                    ->map(fn($file) => $file->getPathname())
                    ->toArray();
                $testFiles = array_merge($testFiles, $filesInFolder);
            }
        }

        if (empty($testFiles)) {
            return response()->json([
                'status' => 'error',
                'message' => 'No test files found in the specified folders.'
            ], 400);
        }

        $testResults = [];
        $summary = [
            'passed' => 0,
            'failed' => 0,
            'skipped' => 0,
            'total' => 0,
        ];

        $x = 0;
        $errorOutput = '';
        foreach ($testFiles as $testFilePath) {
            $output = '';

            $process = Process::fromShellCommandline(env('PHP_PATH', 'php') . " vendor/bin/phpunit --testdox $testFilePath");
            $process->setWorkingDirectory(base_path());
            $process->setTimeout(300);
            $process->run(function ($type, $buffer) use (&$output, &$errorOutput) {
                if ($type === Process::OUT) {
                    $output .= $buffer;
                } else {
                    $errorOutput .= $buffer;
                }
            });

            $parsedResults = $this->parseTestResults($output, $testFilePath, $process->isSuccessful());

            $summary['passed'] += $parsedResults['summary']['passed'];
            $summary['failed'] += $parsedResults['summary']['failed'];
            $summary['skipped'] += $parsedResults['summary']['skipped'];
            $summary['total'] += $parsedResults['summary']['total'];

            $relativePath = strstr($testFilePath, 'tests/');

            $testResults[] = [
                'file' => $relativePath,
                'results' => $parsedResults['details'],
            ];

        }

        CheckServiceTest::query()->create([
            'status' => 'success',
            'summary' => $summary,
            'details' => $testResults,
            'errorOutput' => $errorOutput,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return response()->json([
            'status' => 'success',
            'summary' => $summary,
            'details' => $testResults,
            'errorOutput' => $errorOutput
        ]);
    }

    #[ArrayShape(['summary' => "int[]", 'details' => "array"])]
    private function parseTestResults(string $output, string $testFilePath, $success = true): array
    {
        $lines = explode("\n", $output);
        $details = [];
        $summary = [
            'passed' => 0,
            'failed' => 0,
            'skipped' => 0,
        ];

        $className = $this->getTestClassName($testFilePath);
        $methodsDescriptions = $this->getTestMethodDescriptions($className);


        if (!$success) {

            $firstDescription = [
                'description' => 'No description available.',
                'api' => 'No API specified.'
            ];

            if (!empty($methodsDescriptions)) {
                $firstKey = array_key_first($methodsDescriptions);
                $firstDescription = $methodsDescriptions[$firstKey] ?? [
                        'description' => 'No description available.',
                        'api' => 'No API specified.'
                    ];
            }

            $details[] = [
                'status' => 'failed',
                'message' => "Error",
                'api' => $firstDescription['api'],
                'description' => $firstDescription['description'],
            ];

            $summary['failed']++;
        } else {

            foreach ($lines as $line) {
                $line = trim($line);


                $firstDescription = null;
                if (!empty($methodsDescriptions)) {
                    $firstKey = array_key_first($methodsDescriptions);
                    $firstDescription = $methodsDescriptions[$firstKey] ?? [
                            'description' => 'No description available.',
                            'api' => 'No API specified.'
                        ];
                }

                if (str_starts_with($line, '✔')) {
                    $descriptionData = $firstDescription ?? [
                            'description' => 'No description available.',
                            'api' => 'No API specified.'
                        ];

                    $details[] = [
                        'status' => 'passed',
                        'message' => $line,
                        'api' => $descriptionData['api'],
                        'description' => $descriptionData['description'],
                    ];
                    $summary['passed']++;
                }

                if (str_starts_with($line, '✖')) {
                    $testMethod = $this->extractTestMethodName($line);
                    $descriptionData = $methodsDescriptions[$testMethod] ?? [
                            'description' => 'No description available.',
                            'api' => 'No API specified.'
                        ];

                    $details[] = [
                        'status' => 'failed',
                        'message' => $line,
                        'api' => $descriptionData['api'],
                        'description' => $descriptionData['description'],
                    ];
                    $summary['failed']++;
                }
            }
        }

        $summary['total'] = $summary['passed'] + $summary['failed'] + $summary['skipped'];

        return [
            'summary' => $summary,
            'details' => $details,
        ];
    }

    private function getTestClassName(string $filePath): string
    {
        return 'Tests\\Feature\\' . str_replace(['/', '.php'], ['\\', ''], str_replace(base_path('tests/Feature/'), '', $filePath));
    }

    private function getTestMethodDescriptions(string $className): array
    {
        $descriptions = [];

        if (class_exists($className)) {
            $reflectionClass = new \ReflectionClass($className);
            foreach ($reflectionClass->getMethods(\ReflectionMethod::IS_PUBLIC) as $method) {
                if (str_starts_with($method->getName(), 'test')) {
                    $docComment = $method->getDocComment();
                    $descData = [
                        'description' => 'No description available.',
                        'api' => 'No API specified.',
                    ];

                    if ($docComment) {
                        if (preg_match('/@description\s+(.+)/', $docComment, $descMatches)) {
                            $descData['description'] = trim($descMatches[1]);
                        }
                        if (preg_match('/@api\s+(.+)/', $docComment, $apiMatches)) {
                            $descData['api'] = trim($apiMatches[1]);
                        }
                    }

                    $descriptions[$method->getName()] = $descData;
                }
            }
        }

        return $descriptions;
    }

    private function extractTestMethodName(string $line): string
    {
        if (preg_match('/::(\w+)\(\)/', $line, $matches)) {
            return $matches[1];
        }
        return '';
    }

    public function getLastInfo(): JsonResponse
    {

        $rows = CheckServiceTest::query()->latest()->first();

        return response()->json($rows);

    }
}
