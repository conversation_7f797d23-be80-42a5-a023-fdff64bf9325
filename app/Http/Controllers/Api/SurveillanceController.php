<?php

namespace App\Http\Controllers\Api;

use Exception;
use Carbon\Carbon;
use App\Models\Camera;
use App\Models\Object\Object_;
use App\Traits\ApiResponsible;
use App\Services\CameraService;
use App\Services\ElasticService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use App\Http\Controllers\Controller;
use Illuminate\Pagination\Paginator;
use App\Services\SurveillanceService;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Http\Requests\SearchSurveillanceRequest;
use App\Http\Resources\SurveillanceFacesResource;
use App\Http\Resources\SurveillanceFacesResource_v3;
use Illuminate\Support\Facades\Http;

class SurveillanceController extends Controller
{
    use ApiResponsible;

    public function __construct(
        protected SurveillanceService $surveillanceService,
        protected CameraService $cameraService
    ) {

    }

    /**
     * @OA\Post  (
     *      path="/surveillance/search",
     *      operationId="get-surveillance-search",
     *      tags={"Bənzər şəxslər"},
     *      summary="Bənzər şəxslərin siyahı üzrə axtarılması servisi",
     *      description="Bənzər şəxslərin siyahı üzrə axtarılması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="photo",
     *          description="Şəkil",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="max_count",
     *          description="Maksimim say",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="similarity",
     *          description="Oxşarlıq",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *       @OA\Parameter (
     *          name="date_from",
     *          description="Date From",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="date_to",
     *          description="Date To",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="camera_ids",
     *          description="Kamera ID-lər",
     *          in="query",
     *          @OA\Schema(
     *              type="array",
     *              @OA\Items(type="integer")
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param SearchSurveillanceRequest $request
     * @return JsonResponse
     */
    public function search(SearchSurveillanceRequest $request)
    {
        $perPage = (int) $request->input('per_page', 15);

        $objects = Object_::query()->when($request->input('object_types'), static function ($query, $objectTypes) {
            $query->whereIn('object_type_id', string_to_int_from_array($objectTypes));
        })->when($request->input('objects'), static function($query, $objects) {
            $query->whereIn('id', string_to_int_from_array($objects));
        })->get();

        $objectIds = $objects->pluck('id')->toArray();

        $cameras = Camera::query()->when($request->input('camera_ids'), static function($query, $cameraIds) {
            $query->whereIn('camera_id', string_to_int_from_array($cameraIds));
        })->when($objectIds, static function ($query, $objectIds) {
            $query->whereIn('object_id', string_to_int_from_array($objectIds));
        })->get();

        $cameraIds = $cameras->pluck('camera_id')->toArray();


        try {
            return $this->surveillanceService->search($request->all(), $cameraIds);
        } catch (Exception $exception) {
            return null;
        }

        if (!is_array($collection) || !isset($collection['data'])) {
            return $this->successResponse(200, $collection['message'] ?? 'Nothing found');
        }

        $uuids = array_combine(
            array_column($collection['data'], 'uuid'),
            array_column($collection['data'], 'distance')
        );

        /**
         * @var $service ElasticService
         */
        $service = app(ElasticService::class);

        $body = [
            "query" => [
                "bool" => [
                    "must" => [
                        [
                            'bool' => [
                                'must' => [
                                    [
                                        'terms' => [
                                            '_id' => array_keys($uuids)
                                        ],
                                    ]
                                ],
                            ],
                        ]
                    ]
                ]
            ]
        ];


        if ($request->has('distance')) {
            $body["sort"][] = [
                "alignment_error" => $request->input('distance') === 'asc' ? 'asc' : 'desc'
            ];
        }

        if ($request->has('timestamp')) {
            $body["sort"][] = [
                "timestamp" => $request->input('timestamp') === 'asc' ? 'asc' : 'desc'
            ];
        }

        $body["from"] = (Paginator::resolveCurrentPage()-1) * $perPage;
        $body["size"] = $perPage;

        $response = $service->searchQuery('beein_surveillance_faces_3', $body);

        $collection = $response['result'];

        $total = $response['total'];

        if (empty($collection)) {
            return $this->successResponse(200, 'Nothing found');
        }

        $sources = array_column($collection, '_source');
        $cameraIds = array_column($sources, 'camera_id');

        $cameras = $this->cameraService
            ->findByCameraIds(array_unique($cameraIds))
            ->groupBy('camera_id');

        $collection = $this->surveillanceService->filterElasticListCollection($collection, $cameras, $uuids);

        $pagination = $request->input('pagination');

        if ($pagination !== "false") {
            $collection = $this->paginate($collection, $perPage, null, [] , $total);
        }

        return SurveillanceFacesResource::collection($collection)->response();
    }

    /**
     * @param \App\Http\Requests\SearchSurveillanceRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function searchAll(SearchSurveillanceRequest $request)
    {
        if (in_array(config('app.env'), ['development', 'local'])) {

            $mockdata = public_path('mocks/surveillance-search.json');
            $mockdata = json_decode(file_get_contents($mockdata), true);

            return $mockdata;
        }
        //return response()->json($request->all());

        $objects = Object_::query()->when($request->input('object_types'), static function ($query, $objectTypes) {
            $query->whereIn('object_type_id', string_to_int_from_array($objectTypes));
        })->when($request->input('objects'), static function($query, $objects) {
            $query->whereIn('id', string_to_int_from_array($objects));
        })->get();

        $objectIds = $objects->pluck('id')->toArray();

        $cameras = Camera::query()->when($request->input('camera_ids'), static function($query, $cameraIds) {
            $query->whereIn('camera_id', string_to_int_from_array($cameraIds));
        })->when($objectIds, static function ($query, $objectIds) {
            $query->whereIn('object_id', string_to_int_from_array($objectIds));
        })->get();

        $cameraIds = $cameras->pluck('camera_id')->toArray();
        try {
            $collection = $this->surveillanceService->search($request->all(), $cameraIds, true);
           // return $collection;

        } catch (Exception $exception) {
            return null;
           // return $this->errorResponse($exception->getCode(), 'AI Error : ' . $exception->getMessage());
        }

        if (!is_array($collection) || !isset($collection['data'])) {
            return $this->successResponse(200, $collection['message'] ?? 'Nothing found ...');
        }

        $uuids = array_combine(
            array_column($collection['data'], 'uuid'),
            array_column($collection['data'], 'distance')
        );

        /**
         * @var $service ElasticService
         */
        $service = app(ElasticService::class);

        $body = [
            "query" => [
                "bool" => [
                    "must" => [
                        [
                            'bool' => [
                                'must' => [
                                    [
                                        'terms' => [
                                            '_id' => array_keys($uuids)
                                        ],
                                    ]
                                ],
                            ],
                        ]
                    ]
                ]
            ],
            "size" => 1000
        ];

        //    if (!empty($cameraIds)) {
        //        $body['query']['bool']['must'][0]['bool']['must'][] = [
        //            'terms' => [
        //                'camera_id' => $cameraIds
        //            ],
        //        ];
        //    }
        //
        $dateFrom = $request->date_from;
        $dateTo = $request->date_to;

        // if ($dateFrom != '' && $dateTo != '') {
            $body['query']['bool']['must'][0]['bool']['must'][] = [
                'range' => [
                    'timestamp' => [
                        'gte' => Carbon::createFromFormat('d/m/Y H:i', date("d/m/Y H:i", strtotime($dateFrom)))->timestamp,
                        'lte' => Carbon::createFromFormat('d/m/Y H:i', date("d/m/Y H:i", strtotime($dateTo)))->timestamp,
                    ]
                ],
            ];
        // }

       // return response()->json($body);

        $response = $service->searchQuery('beein_surveillance_faces_3', $body);

        $collection = $response['result'] ?? [];


        // $total = $response['total'];

        if (empty($collection)) {
            return $this->successResponse(200, 'Nothing found ....');
        }

        $sources = array_column($collection, '_source');
        $cameraIds = array_column($sources, 'camera_id');

        $cameras = $this->cameraService
            ->findByCameraIds(array_unique($cameraIds))
            ->groupBy('camera_id');

        $collection = $this->surveillanceService->filterElasticListCollection($collection, $cameras, $uuids);

        if ($request->has('distance')) {
            if ($request->input('distance') === 'asc') {
                usort($collection, static function ($a, $b)
                {
                    if (abs($a['distance'] - $b['distance']) <= 0.0001)
                    {
                        return 0;
                    }

                    return $a['distance'] >= $b['distance'];
                });
            } else {
                usort($collection, static function ($a, $b)
                {
                    if (abs($a['distance'] - $b['distance']) <= 0.0001)
                    {
                        return 0;
                    }

                    return $a['distance'] <= $b['distance'];
                });
            }
        }


        usort($collection, function($a, $b) {
            return $b['_source']['timestamp'] <=> $a['_source']['timestamp'];
        });
        if ($request->has('timestamp')) {
            if ($request->input('timestamp') === 'asc') {
                usort($collection, static function ($a, $b)
                {
                    if (abs($a['_source']['timestamp'] - $b['_source']['timestamp']) <= 0.0001)
                    {
                        return 0;
                    }

                    return $a['_source']['timestamp'] >= $b['_source']['timestamp'];
                });
            } else {
                usort($collection, static function ($a, $b)
                {
                    if (abs($a['_source']['timestamp'] - $b['_source']['timestamp']) <= 0.0001)
                    {
                        return 0;
                    }

                    return $a['_source']['timestamp'] <= $b['_source']['timestamp'];
                });
            }
        }

        $pagination = $request->input('pagination');

        if ($pagination !== "false") {
            $collection = $this->paginateAll($collection, $request->per_page ?? 15, null, []);
        }

        return SurveillanceFacesResource::collection($collection)->response();
    }

    public function searchAll_v3(SearchSurveillanceRequest $request)
    {
        try {
            $collection = Http::put(config('servers.gpu_api_v3') . 'surveillance/search_v3', [
                "photo" => base64_encode(file_get_contents($request->image)),
                'from_time' => Carbon::createFromFormat('d/m/Y H:i', date("d/m/Y H:i", strtotime($request->date_from)))->timestamp,
                'till_time' => Carbon::createFromFormat('d/m/Y H:i', date("d/m/Y H:i", strtotime($request->date_to)))->timestamp,
                "similarity" => $request->similarity,
                "max_count" => $request->max_count
            ])->json();
        } catch (Exception $exception) {
            return null;
        }

        if (empty($collection)) {
            return $this->successResponse(200, 'Nothing found ....');
        }

        if ($request->has('distance')) {
            if ($request->input('distance') === 'asc') {
                usort($collection, static function ($a, $b)
                {
                    if (abs($a['distance'] - $b['distance']) <= 0.0001)
                    {
                        return 0;
                    }

                    return $a['distance'] >= $b['distance'];
                });
            } else {
                usort($collection, static function ($a, $b)
                {
                    if (abs($a['distance'] - $b['distance']) <= 0.0001)
                    {
                        return 0;
                    }

                    return $a['distance'] <= $b['distance'];
                });
            }
        }

        $pagination = $request->input('pagination');

        // if ($pagination !== "false") {
        $collection = $this->paginateAll($collection, $request->per_page ?? 15, null, []);
        // }

        return SurveillanceFacesResource_v3::collection(collect($collection['data'])->paginate(10))->response();
    }


    /**
     * @OA\Get  (
     *      path="/surveillance/show/{sessionId}",
     *      operationId="get-surveillance-show",
     *      tags={"Bənzər şəxslər daxili"},
     *      summary="Bənzər şəxslərin daxili servisi",
     *      description="Bənzər şəxslərin daxili servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="sessionId",
     *          description="Sessiya id",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param $sessionId
     * @return JsonResponse
     */
    public function show($sessionId): JsonResponse
    {
        /**
         * @var $service ElasticService
         */
        $service = app(ElasticService::class);

        $body = [
            "query" => [
               "match_phrase" => [
                   "session_name" => $sessionId
               ]
            ]
        ];

        $response = $service->searchQuery('beein_surveillance_faces_3', $body);

        $collection = $response['result'];

        if (empty($collection)) {
            return $this->successResponse(200, 'Nothing found');
        }

        // $sources = array_column($collection, '_source');
        // $cameraIds = array_column($sources, 'camera_id');

        $cameras = $this->cameraService
            ->findAll()
            ->groupBy('camera_id');

        $collection = $this->surveillanceService->filterElasticShowBySessionCollection($collection, $cameras);

        return SurveillanceFacesResource::collection($collection)->response();
    }


    /**
     * The attributes that are mass assignable.
     *
     *
     * @param $items
     * @param int $perPage
     * @param null $page
     * @param array $options
     * @param $total
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function paginate($items, int $perPage = 15, $page = null, array $options = [], $total): LengthAwarePaginator
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?? 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items, $total, $perPage, $page, $options);
    }

    /**
     * The attributes that are mass assignable.
     *
     *
     * @param $items
     * @param int $perPage
     * @param null $page
     * @param array $options
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function paginateAll($items, int $perPage = 5, $page = null, array $options = []): LengthAwarePaginator
    {
        $page = $page ?: (Paginator::resolveCurrentPage() ?? 1);
        $items = $items instanceof Collection ? $items : Collection::make($items);
        return new LengthAwarePaginator($items->forPage($page, $perPage), $items->count(), $perPage, $page, $options);
    }
}
