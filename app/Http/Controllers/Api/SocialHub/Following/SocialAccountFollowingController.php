<?php

namespace App\Http\Controllers\Api\SocialHub\Following;

use App\Http\Controllers\Controller;
use App\Http\Requests\SocialHubRequests\FollowingAccountRequest;
use App\Services\SocialHub\Following\SocialAccountFollowingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;

class SocialAccountFollowingController extends Controller
{

    private SocialAccountFollowingService $accountFollowingService;

    public function __construct(SocialAccountFollowingService $accountFollowingService){
        $this->accountFollowingService = $accountFollowingService;
    }

    public function index(Request $request)
    {
        return $this->accountFollowingService->list($request);
    }


    public function store(FollowingAccountRequest $request): JsonResponse
    {
        return $this->accountFollowingService->store($request->validated());
    }

    public function users_posts(Request $request): AnonymousResourceCollection
    {
        $per_page = $request->per_page ?? 8;

        $text = $request->text ?? "";
        return $this->accountFollowingService->users_posts($per_page, $text);
    }


    public function users_comments(Request $request): AnonymousResourceCollection
    {
        $per_page = $request->per_page ?? 8;

        $text = $request->text ?? "";
        return $this->accountFollowingService->users_comments($per_page, $text);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    public function destroy(int $id)
    {
        $this->accountFollowingService->delete($id);
    }

    public function deleteSelected(Request $request)
    {
        $this->accountFollowingService->deleteSelected($request->ids);
    }
}
