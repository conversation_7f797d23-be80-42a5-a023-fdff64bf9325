<?php

namespace App\Http\Controllers\Api\SocialHub\Following;

use App\Http\Controllers\Controller;
use App\Http\Requests\SocialHubRequests\FollowingWordRequest;
use App\Services\SocialHub\Following\SocialPageFollowingService;
use App\Services\SocialHub\Following\SocialWordFollowingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SocialWordFollowingController extends Controller
{

    private SocialWordFollowingService $wordFollowingService;

    public function __construct(SocialWordFollowingService $wordFollowingService){
        $this->wordFollowingService = $wordFollowingService;
    }


    public function index(Request $request): JsonResponse
    {
        return $this->wordFollowingService->list($request);
    }

    public function store(FollowingWordRequest $request): JsonResponse
    {
        return $this->wordFollowingService->store($request->validated());
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }


    public function destroy($id)
    {
        $this->wordFollowingService->delete($id);
    }

    public function deleteSelected(Request $request)
    {
        $this->wordFollowingService->deleteSelected($request->ids);
    }

    public function dataByFollowing(Request $request, $type="comment")
    {
        return $this->wordFollowingService->dataByFollowingWords($request->per_page ?? 12, $type);
    }
}
