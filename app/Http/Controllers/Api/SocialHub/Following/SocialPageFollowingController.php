<?php

namespace App\Http\Controllers\Api\SocialHub\Following;

use App\Http\Controllers\Controller;
use App\Http\Requests\SocialHubRequests\FollowingAccountRequest;
use App\Services\SocialHub\Following\SocialPageFollowingService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SocialPageFollowingController extends Controller
{

    private SocialPageFollowingService $pageFollowingService;

    public function __construct(SocialPageFollowingService $pageFollowingService){
        $this->pageFollowingService = $pageFollowingService;
    }

    public function index(Request $request)
    {
        return $this->pageFollowingService->list($request);
    }

    public function store(FollowingAccountRequest $request): JsonResponse
    {
        return $this->pageFollowingService->store($request->validated());
    }

    public function page_posts(Request $request)
    {
        $per_page = $request->per_page ?? 8;

        $text = $request->text ?? "";

        return $this->pageFollowingService->page_posts($per_page, $text);
    }

    public function page_comments(Request $request)
    {
        $per_page = $request->per_page ?? 8;

        $text = $request->text ?? "";

        return $this->pageFollowingService->page_comments($per_page, $text);
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    public function destroy($id)
    {
        $this->pageFollowingService->delete($id);
    }

    public function deleteSelected(Request $request)
    {
        $this->pageFollowingService->deleteSelected($request->ids);
    }
}
