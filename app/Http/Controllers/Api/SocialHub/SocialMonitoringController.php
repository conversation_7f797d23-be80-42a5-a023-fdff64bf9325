<?php

namespace App\Http\Controllers\Api\SocialHub;

use App\Http\Controllers\Controller;
use App\Http\Resources\Social\SocialPageResource;
use App\Services\SocialHub\SocialMonitoringService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;

class SocialMonitoringController extends Controller
{

    private SocialMonitoringService $socialMonitoringService;

    public function __construct(SocialMonitoringService $socialMonitoringService)
    {
        $this->socialMonitoringService = $socialMonitoringService;
    }

    public function info($pageId): SocialPageResource
    {
        return $this->socialMonitoringService->getPageInfo($pageId);
    }
    public function getPagePosts($pageId, Request $request): LengthAwarePaginator
    {
        return $this->socialMonitoringService->getPagePosts($pageId, $request);
    }

    public function getPageActiveUsers($pageId, Request $request)
    {
        return $this->socialMonitoringService->getPageActiveUsers($pageId, $request);
    }

    public function wordCloud($pageId): string
    {
        return config('servers.s3_bucket_server').'/all-pics/wordcloud_' . $pageId . '.jpeg';

    }
}
