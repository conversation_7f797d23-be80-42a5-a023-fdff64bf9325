<?php

namespace App\Http\Controllers\Api\SocialHub;

use App\Http\Controllers\Controller;
use App\Services\SocialHub\SocialSearchService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SocialSearchController extends Controller
{

    private SocialSearchService $searchService;

    public function __construct(SocialSearchService $searchService){
        $this->searchService = $searchService;
    }

    public function searchByProfile(Request $request)
    {

        return $this->searchService->searchByProfile($request);
    }

    public function searchByPage(Request $request)
    {
        return $this->searchService->searchByPage($request);
    }

    public function searchByPost(Request $request)
    {

        return $this->searchService->searchByPost($request);
    }

    public function searchByComment(Request $request)
    {
        return $this->searchService->searchByComment($request);
    }

    public function searchByOcr(){
        return "ocr uzre axtaris";
    }

    public function searchByPhoto(Request $request)
    {

        $request->validate([
            'platform_id' => 'nullable|numeric|min:1|max:10',
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|dimensions:max_width=5000,max_height=5000|max:2048',
            'real_person' => 'nullable|boolean'
        ]);

        return $this->searchService->searchByPhoto($request);
    }

    public function searchByPhotoImage(Request $request)
    {

        $request->validate([
            'platform_id' => 'nullable|numeric|min:1|max:10',
            'photo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|dimensions:max_width=5000,max_height=5000|max:2048',
            'real_person' => 'nullable|boolean'
        ]);

        return $this->searchService->searchByPhoto($request, 'image');
    }

    public function searchByPhotoText(Request $request): AnonymousResourceCollection
    {

        return $this->searchService->searchByPhotoText($request);
    }

    public function searchByCarNumber(Request $request)
    {

        return $this->searchService->getImageByCarNumber($request);
    }
}
