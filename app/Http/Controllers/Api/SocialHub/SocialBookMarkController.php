<?php

namespace App\Http\Controllers\Api\SocialHub;

use App\Http\Controllers\Controller;
use App\Models\Social\Account;
use App\Models\Social\Comment;
use App\Models\Social\Post;
use App\Models\Social\SocialPage;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SocialBookMarkController extends Controller
{

    public function index()
    {
        $user = Auth::user();
        $bookmarks = $user->bookmarks()->with('bookmarkable')->get();

        return response()->json(['bookmarks' => $bookmarks]);
    }



    public function store(Request $request)
    {
        $request->validate([
            'bookmarkable_type' => 'required|string',
            'bookmarkable_id' => 'required|integer',
        ]);

        $user = Auth::user();
        $bookmarkableType = $request->input('bookmarkable_type');
        $bookmarkableId = $request->input('bookmarkable_id');

        $model = $this->getModelClass($bookmarkableType);

        if ($model == null) {
            return response()->json(['error' => 'Bookmarkable not found.'], 400);
        }

        $bookmark = $user->bookmarks()->create([
            'bookmarkable_id' => $bookmarkableId,
            'bookmarkable_type' => $model,
        ]);

        return response()->json(['message' => 'Bookmark added.', 'bookmark' => $bookmark], 201);
    }

    public function destroy($id)
    {
        $user = Auth::user();
        $bookmark = $user->bookmarks()->findOrFail($id);

        $bookmark->delete();

        return response()->json(['message' => 'Bookmark deleted']);
    }

    private function getModelClass(string $type): ?string
    {
        $map = [
            'post' => Post::class,
            'account' => Account::class,
            'comment' => Comment::class,
            'page' => SocialPage::class,
        ];

        return $map[$type] ?? null;
    }
}
