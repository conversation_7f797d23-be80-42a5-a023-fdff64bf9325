<?php

namespace App\Http\Controllers\Api\SocialHub;

use App\Http\Controllers\Controller;
use App\Services\SocialHub\SocialUserService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class SocialUserController extends Controller
{

    private SocialUserService $socialUserService;

    public function __construct(SocialUserService $socialUserService)
    {
        $this->socialUserService = $socialUserService;
    }

    public function getUserInfo($accountId)
    {

        return $this->socialUserService->userInfo($accountId);
    }
    public function getUserRealPerson($accountId)
    {

        return $this->socialUserService->getUserRealPersonWithCache($accountId);
    }

    public function userPosts($accountId, Request $request): AnonymousResourceCollection
    {
        $by_limit = $request->by_limit ?? 1;

        return $this->socialUserService->userPosts($accountId, $by_limit, $request);
    }

    public function userComments($accountId, Request $request)
    {
        $by_limit = $request->by_limit ?? 1;
        $limit = $request->limit ?? 7;

        return $this->socialUserService->userComments($accountId, $by_limit, $limit);
    }

    public function userCommentsByPage($accountId, Request $request)
    {
        $by_limit = $request->by_limit ?? 1;
        $per_page = $request->per_page ?? 7;

        return $this->socialUserService->userCommentsByPage($accountId, $by_limit, $per_page);
    }

    public function userCommentsByProfile($accountId, Request $request)
    {
        $by_limit = $request->by_limit ?? 1;
        $per_page = $request->per_page ?? 7;

        return $this->socialUserService->userCommentsByProfile($accountId, $by_limit, $per_page);
    }

    public function userAllComments($accountId, Request $request)
    {
        $per_page = $request->per_page ?? 7;

        return $this->socialUserService->userAllComments($accountId, $per_page, $request->text ?? "");
    }

    public function userAllCommentsWithoutPost($accountId, Request $request)
    {
        $per_page = $request->per_page ?? 7;

        return $this->socialUserService->userAllCommentsWithoutPost($accountId, $per_page, $request->text ?? "");
    }

    public function userPhotos($accountId, Request $request)
    {
        return $this->socialUserService->getUserSharedPhotos($accountId, $request);
    }

    public function userFriends($accountId, Request $request)
    {
        return $this->socialUserService->userFacebookFriends($accountId, $request);
    }

    public function statisticBySectionAndDate($accountId, Request $request)
    {
        return $this->socialUserService->statisticBySectionAndDate($accountId, $request);
    }

    public function bio($accountId)
    {
        return $this->socialUserService->getBio($accountId);
    }
}
