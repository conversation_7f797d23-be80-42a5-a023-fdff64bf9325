<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MonitoringRoute;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MonitoringRouteController extends Controller
{

    public function index(): JsonResponse
    {
        $rows = MonitoringRoute::query()->get();

        return response()->json($rows);
    }


    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'url' => 'required|url',
            'method' => 'required|in:GET,POST,PUT,DELETE',
            'payload' => 'nullable|json',
            'headers' => 'nullable|json',
            'status' => 'boolean',
        ]);

        $route = MonitoringRoute::query()->create($validated);

        return response()->json(['message' => 'Route created successfully!', 'data' => $route], 201);

    }

    public function show($id): JsonResponse
    {
        $row = MonitoringRoute::query()->findOrFail($id);

        return response()->json($row);
    }


    public function update(Request $request, $id): JsonResponse
    {
        $validated = $request->validate([
            'url' => 'sometimes|url',
            'method' => 'sometimes|in:GET,POST,PUT,DELETE',
            'payload' => 'nullable|json',
            'headers' => 'nullable|json',
            'status' => 'boolean',
        ]);

        $route = MonitoringRoute::query()->findOrFail($id);
        $route->update($validated);

        return response()->json(['message' => 'Route updated successfully!', 'data' => $route]);

    }

    public function destroy($id): JsonResponse
    {
        $route = MonitoringRoute::query()->findOrFail($id);
        $route->delete();

        return response()->json(['message' => 'Route deleted successfully!']);
    }
}
