<?php

namespace App\Http\Controllers\Api;

use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;

class VendorController extends Controller
{

    public function __construct()
    {
//        $this->middleware('permission:vendors', ['only' => ['index']]);
//        $this->middleware('permission:vendors:read', ['only' => ['show']]);
//        $this->middleware('permission:vendors:create', ['only' => ['store']]);
//        $this->middleware('permission:vendors:update', ['only' => ['update']]);
//        $this->middleware('permission:vendors:delete', ['only' => ['destroy']]);

    }

    public function index(Request $request): JsonResponse
    {
        $vendor = Vendor::query()->orderBy('id', 'DESC')
            ->paginate($request->get('per_page') ?? 10);

        return response()->json($vendor);
    }

    public function store(Request $request): JsonResponse
    {
        /** @var Vendor $vendor */
        $request->validate([
            'name' => 'required|string|unique:vendors,name',
            'status' => 'required',
        ]);

        $vendor = Vendor::query()->create([
            'name' => $request->post('name'),
            'status' => (int)$request->post('status'),
        ]);

        return response()->json($vendor);
    }

    public function show(int $id): JsonResponse
    {
        $vendor = Vendor::query()->findOrFail($id);

        return response()->json($vendor);
    }

    public function update(Request $request, int $id): JsonResponse
    {
        /** @var Vendor $vendor */
        $request->validate([
            'name' => 'required|string|unique:vendors,name,'.$id,
            'status' => 'required',
        ]);

        $vendor = Vendor::query()->where('id',$id)->update([
            'name' => $request->post('name'),
            'status' => (int)$request->post('status'),
        ]);

        return response()->json($vendor);
    }

    public function destroy(int $id): JsonResponse
    {
        $vendor = Vendor::query()->findOrFail($id);

        $vendor->delete();

        return response()->json([], Response::HTTP_NO_CONTENT);
    }
}
