<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserWork;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

class UserWorkController extends Controller
{

    public function list(): JsonResponse
    {
        $works = UserWork::query()
            ->where('user_id', auth()->id())
            ->orderByDesc('created_at')
            ->get();

        return response()->json(['data' => $works]);
    }

    public function create(Request $request): JsonResponse
    {
        $request->validate([
            'work_name' => 'required|string|max:255|min:6',
            'work_description' => 'nullable|string|min:3',
        ]);

        try {
            $userId = Auth::id();

            UserWork::query()->where(['user_id' => $userId])->update(['selected' => 0]);

            $workName = strip_tags($request->work_name);
            $workDescription =  strip_tags($request->work_description);

            UserWork::query()->create([
                'user_id' => $userId,
                'work_name' => $workName,
                'work_description' => $workDescription,
                'selected' => 1,
                'created_at' => now()
            ]);

            $this->updateWorkCache($userId, $workName, $workDescription);

//            $cacheKey = 'user_work_' . $userId;

//            Cache::put($cacheKey, [
//                'work_name' => $workName,
//                'work_description' => $workDescription
//            ], now()->addDay());

            return response()->json(['status' => true]);

        }catch (\Exception $exception)
        {
            return response()->json(['status' => $exception->getCode(), 'message' => $exception->getMessage()], 500);
        }
    }

    public function changeWork($workId): JsonResponse
    {
        try {
            $userId = Auth::id();

            UserWork::query()->where(['user_id' => $userId])->update(['selected' => 0]);

            $currentWork = UserWork::query()->where(['user_id' => $userId])->find($workId);

            $currentWork->selected = 1;
            $currentWork->save();

            $this->updateWorkCache($userId, $currentWork->work_name, $currentWork->work_description);

            return response()->json(['status' => true]);
        }catch (\Exception $exception)
        {
            return response()->json(['status' => $exception->getCode(), 'message' => $exception->getMessage()], 500);
        }
    }

    public function reset()
    {
        $userId = Auth::id();

        $cacheKey = 'user_work_' . $userId;

        UserWork::query()->where('user_id', Auth::id())->delete();

        Cache::forget($cacheKey);
    }

    private function updateWorkCache($userId, $workName, $workDescription)
    {
        $cacheKey = 'user_work_' . $userId;

        $updatedData = [
            'work_name' => $workName,
            'work_description' => $workDescription
        ];

        $now = now()->addDay();
//        if (\auth('api')->id()==124){
//            $now = now();
//        }
        Cache::put($cacheKey, $updatedData, $now);
    }
}
