<?php

namespace App\Http\Controllers\Api\CameraMonitoring;

use App\Http\Controllers\Controller;
use App\Services\CameraMonitoring\CameraPointerService;
use Illuminate\Http\Request;

class CameraPointerController extends Controller
{

    private CameraPointerService $cameraPointerService;

    public function __construct(CameraPointerService $cameraPointerService){
        $this->cameraPointerService = $cameraPointerService;
    }

    public function list(){
        return $this->cameraPointerService->getList();
    }
}
