<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\UserRoleUpdateRequest;
use App\Models\User;
use App\Traits\ApiResponsible;
use Illuminate\Http\Request;
use App\Services\UserService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use App\Http\Requests\UserRequest;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use App\Http\Requests\UserUpdateRequest;
use App\Http\Resources\PermissionResource;
use App\Http\Requests\UpdatePasswordRequest;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Validator;
use OpenApi\Annotations as OA;
use Spatie\Permission\PermissionRegistrar;
use Symfony\Component\HttpFoundation\Response;

class UserController extends Controller
{
    use ApiResponsible;

    /**
     * @param \App\Services\UserService $userService
     */
    public function __construct(
        protected UserService $userService
    )
    {
        $this->middleware('permission:users', ['only' => ['index']]);
        $this->middleware('permission:users:read', ['only' => ['show']]);
        $this->middleware('permission:users:create', ['only' => ['store']]);
        $this->middleware('permission:users:update', ['only' => ['update']]);
        $this->middleware('permission:users:delete', ['only' => ['destroy']]);
        $this->middleware('permission:users:status', ['only' => ['updateStatus']]);
        $this->middleware('permission:users:password-reset', ['only' => ['updatePassword']]);
        $this->middleware('permission:users:role-assign', ['only' => ['assignRoles']]);
    }

    /**
     * @OA\Get  (
     *      path="/users",
     *      operationId="get-users-list",
     *      tags={"İstifadəçilər"},
     *      summary="İstifadəçi siyahısını əldə olunması servisi",
     *      description="İstifadəçi siyahısını əldə olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="per_page",
     *          description="Səhifə başına düşən item sayı",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="page",
     *          description="Səhifə",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="search",
     *          description="Axtarılan kəlimə",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="type",
     *          description="Axtarış tipi",
     *          in="query",
     *          @OA\Schema(
     *              type="string",
     *              example="name"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        return $this->userService
            ->paginate($request->all(), $request->input('per_page') ?? 10);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @OA\Post   (
     *      path="/users",
     *      operationId="create-users",
     *      tags={"İstifadəçilər"},
     *      summary="Yeni İstifadəçi əlavə olunması servisi",
     *      description="Yeni İstifadəçi əlavə olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="name",
     *          description="İstifadəçi adı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="surname",
     *          description="İstifadəçi soyadı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="entity_name",
     *          description="Qurum adı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="phone_number",
     *          description="Telefon nömrəsi",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="email",
     *          description="email",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="password",
     *          description="Şifrə",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter(
     *          name="roles[]",
     *          description="array of roles numbers",
     *          in="query",
     *          @OA\Schema(
     *              type="array",
     *              @OA\Items(type="enum", enum={1,2,3,4,5,6,7,8,9}),
     *              example={1,2}
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="status",
     *          description="Status",
     *          in="query",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     * @param UserRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(UserRequest $request): JsonResponse
    {


        $data = $request->only('name', 'surname', 'email', 'password', 'entity_name', 'phone_number');



        $data['status'] = $request->has('status') ? 1 : 0;

        if (isset($data['password'])) {
            $data['password'] = bcrypt($data['password']);
        }

        $user = $this->userService->save($data);

        if ($request->has('roles')) {

            $validateRoles = Validator::make($request->all(), [
                'roles' => 'required',
                'roles.*' => 'required'
            ]);

            if ($validateRoles->fails()) {
                return $this->errorResponse(
                    Response::HTTP_UNPROCESSABLE_ENTITY,
                    $validateRoles->errors()
                );
            }

            $this->userService->assignRoles($user, $request->only('roles'));
        }

        return UserResource::make($user->fresh())->response();
    }

    /**
     * Display the specified resource.
     *
     * @OA\Get    (
     *      path="/users/{user_id}",
     *      operationId="show-users",
     *      tags={"İstifadəçilər"},
     *      summary="İstifadəçi məlumatları",
     *      description="İstifadəçi məlumatları",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="user_id",
     *          description="User ID",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        return $this->userService->show($id);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @OA\Put   (
     *      path="/users/{user_id}",
     *      operationId="update-users",
     *      tags={"İstifadəçilər"},
     *      summary="İstifadəçi üzərində dəyişiklik olunması servisi",
     *      description="İstifadəçi üzərində dəyişiklik olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="user_id",
     *          description="User ID",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="name",
     *          description="İstifadəçi adı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="surname",
     *          description="İstifadəçi soyadı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="entity_name",
     *          description="Qurum adı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="phone_number",
     *          description="Telefon nömrəsi",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="email",
     *          description="email",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="password",
     *          description="Şifrə",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter(
     *          name="roles[]",
     *          description="array of roles numbers",
     *          in="query",
     *          @OA\Schema(
     *              type="array",
     *              @OA\Items(type="enum", enum={1,2,3,4,5,6,7,8,9}),
     *              example={1,2}
     *          )
     *      ),
     *     @OA\Parameter (
     *          name="status",
     *          description="Status",
     *          in="query",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     * @param UserUpdateRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(UserUpdateRequest $request, int $id): JsonResponse
    {
        /**
         * @var User $user
         */
        $user = $this->userService->find($id);

        $validated = $request->validated();

        $data = $request->only('name', 'surname', 'email', 'entity_name', 'phone_number');

        $data['status'] = $request->has('status') ? 1 : 0;

        if ($user->is_system === 1) {
            $data['status'] = 1;
        }

        /**
         * @var $user User
         */
        $user = $this->userService->update(
            $data,
            $user->id
        );

        if (isset($validated['password'])) {
            $user->password = bcrypt($validated['password']);
            $user->save();
        }

        if ($request->has('roles')) {

            $validateRoles = Validator::make($request->all(), [
                'roles' => 'required',
                'roles.*' => 'required'
            ]);

            if ($validateRoles->fails()) {
                return $this->errorResponse(
                    Response::HTTP_UNPROCESSABLE_ENTITY,
                    $validateRoles->errors()
                );
            }

            $this->userService->assignRoles($user, $request->only('roles'));
        }

        return UserResource::make($user->fresh())->response();
    }

    public function assignRoles(UserRoleUpdateRequest $request, int $id): JsonResponse
    {
        /**
         * @var User $user
         */
        $user = $this->userService->find($id);

        $this->userService->assignRoles($user, $request->only('roles'));

        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        return UserResource::make($user->fresh())->response();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @OA\Patch   (
     *      path="/users/{user_id}/update-password",
     *      operationId="update-password-users",
     *      tags={"İstifadəçilər"},
     *      summary="İstifadəçinin şifərəsinin yenilənməsi servisi",
     *      description="İstifadəçinin şifərəsinin yenilənməsi servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="user_id",
     *          description="User ID",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="password",
     *          description="Şifrə",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="password_confirmation",
     *          description="Təkrar Şifrə",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param UpdatePasswordRequest $request
     * @param $id
     * @return UserResource
     */
    public function updatePassword(UpdatePasswordRequest $request, $id): UserResource
    {
        $validated = $request->validated();
        /**
         * @var $user User
         */
        $user = $this->userService->find($id);
        $user->password = bcrypt($validated['password']);
        $user->save();
        return UserResource::make($user->fresh());
    }

    /**
     * Remove the specified resource from storage.
     *
     * @OA\Delete    (
     *      path="/users/{user_id}",
     *      operationId="delete-users",
     *      tags={"İstifadəçilər"},
     *      summary="İstifadəçinin silinməsi servisi",
     *      description="İstifadəçinin silinməsi servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="user_id",
     *          description="User ID",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        /**
         * @var User $user
         */
        $user = $this->userService->find($id);

        if ($user->is_system === 1) {
            return $this->errorResponse(
                Response::HTTP_FORBIDDEN,
                "Bu istifadəçi sistem istifadəçisidir. Silmək və statusu yeniləmək mümkün deyil"
            );
        }

        DB::table('model_has_roles')
            ->where('model_id', $id)
            ->delete();

        return $this->userService->delete($id);
    }

    /**
     * List of User Permissions
     *
     * @OA\Get    (
     *      path="/users/{user_id}/permissions",
     *      operationId="permissions-users",
     *      tags={"İstifadəçilər"},
     *      summary="İstifadəçinin icazələrinin əldə olunması servisi",
     *      description="İstifadəçinin icazələrinin əldə olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="user_id",
     *          description="User ID",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     * @param int $id
     * @return AnonymousResourceCollection
     */
    public function permissions(int $id): AnonymousResourceCollection
    {
        /**
         * @var $user User
         */
        $user = $this->userService->find($id);

        return PermissionResource::collection($user->permissions());
    }

    /**
     * Store a newly created resource in storage.
     *
     * @OA\Patch   (
     *      path="/users/{user_id}/update-status",
     *      operationId="update-status-users",
     *      tags={"İstifadəçilər"},
     *      summary="İstifadəçinin statusunun yenilənməsi servisi",
     *      description="İstifadəçinin statusunun yenilənməsi servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="user_id",
     *          description="User ID",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="status",
     *          description="Status",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param UpdatePasswordRequest $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, int $id): JsonResponse
    {
        /**
         * @var User $user
         */
        $user = $this->userService->find($id);

        if ($user->is_system === 1) {
            return $this->errorResponse(
                Response::HTTP_FORBIDDEN,
                "Bu istifadəçi sistem istifadəçisidir. Silmək və statusu yeniləmək mümkün deyil"
            );
        }

        $data['status'] = $request->has('status') ? $request->input('status') : 0;

        /**
         * @var $user User
         */
        $user = $this->userService->update(
            $data,
            $id
        );

        return UserResource::make($user->fresh())->response();
    }

    private string $validApiKey = 'LCJJc3NZXIiLCJVc2VybmFtImV4cCI6MTJVc2VybmFtImiOiJ';

    public function getUserListForAuditSystem(Request $request)
    {
        $apiKey = $request->header('x-api-key');

        if ($apiKey !== $this->validApiKey) {
            return response()->json(['error' => 'Invalid API key'], 401);
        }

        return DB::table('users')
            ->select('id', 'name', 'surname', 'email', 'entity_name', 'status', 'is_system')
            ->get();
    }
}
