<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SearchCustomsRequest;
use App\Services\Customs\LocalCustomsService;
use App\Services\PersonService;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class LocalCustomsController extends Controller
{
    use ApiResponsible;

    /**
     * @param \App\Services\PersonService $personService
     * @param \App\Services\Customs\LocalCustomsService $localCustomsService
     */
    public function __construct(
        protected PersonService $personService,
        protected LocalCustomsService $localCustomsService,
    )
    {
        /*TODO: yerliler ucun ayri permission yazilmalidir! */

        $this->middleware('permission:customs:view', ['only' => ['search', 'searchByPin']]);
    }

    /**
     * @OA\Post  (
     *      path="/customs/search/local",
     *      operationId="get-customs-search-local",
     *      tags={"Bənzər şəxslər"},
     *      summary="Bənzər şəxslərin axtarılması servisi",
     *      description="Bənzər şəxslərin axtarılması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="photo",
     *          description="Şəkil",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="max_count",
     *          description="Maksimim say",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="similarity",
     *          description="Oxşarlıq",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param \App\Http\Requests\SearchCustomsRequest $request
     * @return JsonResponse
     */
    public function search(SearchCustomsRequest $request): JsonResponse
    {
        $params = $request->all();
        $perPage = $request->input('per_page', 10);

        if (!isset($params['photo'])) {
            return $this->localCustomsService
                ->paginateByParams($params, $perPage);
        }

        return $this->localCustomsService->search($params, $perPage);
    }


    public function searchByPin(string $pin, Request $request): JsonResponse
    {
        if(!checkIfSecretKeyExists($pin)){
            return $this->successResponse(401,'Not Permission');
        }
        $params = ['pin' => strtoupper($pin)];

        return $this->localCustomsService
            ->paginateByParams($params, $request->input('per_page', 10));
    }
}
