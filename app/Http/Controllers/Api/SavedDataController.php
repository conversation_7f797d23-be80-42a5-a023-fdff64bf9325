<?php

namespace App\Http\Controllers\Api;

use Exception;
use App\Models\Person;
use App\Models\ForeignCitizen;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use App\Services\SavedDataService;
use App\Http\Controllers\Controller;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Requests\SavedData\SavedDataRequest;
use App\Http\Requests\SavedData\ToggleSavedDataRequest;

class SavedDataController extends Controller
{
    use ApiResponsible;

    /**
     * @param \App\Services\SavedDataService $savedDataService
     */
    public function __construct(
        public SavedDataService $savedDataService
    ) {
    }

    /**
     * @param \App\Http\Requests\SavedData\SavedDataRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(SavedDataRequest $request): JsonResponse
    {
        /**
         * @var \App\Models\User $user
         */
        $user = auth('api')->user();

        $data = $request->validated();

        $data['user_id'] = $user->id;

        return $this->savedDataService
            ->paginate($data, $request->input('per_page', 10));
    }

    /**
     * @param \App\Http\Requests\SavedData\ToggleSavedDataRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleData(ToggleSavedDataRequest $request): JsonResponse
    {
        /**
         * @var \App\Models\User $user
         */
        $user = auth('api')->user();

        $validated = $request->validated();

        $validated['user_id'] = $user->id;

        $validated['data_id'] = $validated['type'] === 'person'
            ? (int) $validated['data_id']
            : (string) $validated['data_id'];

        $haveSavedData = $this->savedDataService->findOneByCondition([
            'type' => $validated['type'],
            'user_id' => $validated['user_id'],
            'data_id' => $validated['data_id']
        ]);

        if ($haveSavedData) {
            try {

                $haveSavedData->delete();

                return $this->successResponse(
                    Response::HTTP_OK,
                    'Successfully removed from saved list'
                );
            } catch (Exception $exception) {
                return $this->errorResponse(
                    $exception->getCode(),
                    $exception->getMessage()
                );
            }
        }

        try {

            $this->savedDataService->save($validated);

            return $this->successResponse(
                Response::HTTP_OK,
                'Successfully added to saved list.'
            );
        } catch (Exception $exception) {
            return $this->errorResponse(
                $exception->getCode(),
                $exception->getMessage()
            );
        }
    }


    /**
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showUser(int $id): JsonResponse
    {

        if ($person = Person::find($id)) {
            return $this->successResponse(
                Response::HTTP_OK,
                '',
                $person->toArray()
            );
        }

        return $this->errorResponse(
            Response::HTTP_NOT_FOUND,
            'User not found'
        );
    }

    /**
     * @param string $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function showCustom(string $id): JsonResponse
    {

        $custom = ForeignCitizen::where(['_id' => $id])->first();

        if ($custom) {
            return $this->successResponse(
                Response::HTTP_OK,
                '',
                $custom->toArray()
            );
        }

        return $this->errorResponse(
            Response::HTTP_NOT_FOUND,
            'Custom data not found'
        );
    }
}
