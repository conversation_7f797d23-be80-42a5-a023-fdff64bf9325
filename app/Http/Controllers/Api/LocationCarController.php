<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Traits\ApiResponsible;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\JsonResponse;

class LocationCarController extends Controller
{
    use ApiResponsible;

    public const URL = '/api/v1/find-location';

    public function getVehicleEntered(Request $request): JsonResponse
    {
        $params['carNumber'] = str_replace([' ', '-'], '', strtoupper($request->carNumber));

        $getVehicleEntered = Http::withHeaders([
            'Authorization' => 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ1c2VyX3NzcyIsImlkIjoxOSwiZXhwIjoxOTExODgyNDY2LCJpYXQiOjE2NTI2ODI0NjZ9.G8D4wbBLrrB4gldT2QPfEoTUmhuoxurcDEj61izAojEs-7XcrKfWr7ykfs3c-RuB-LUlo4RmnOKmVM9kLriehg',
            'System-Id' => '9ae78b036a90487cbb5b04f1',
            'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
        ])
            ->get(config('servers.vehicle_second_host') . '/api/vehicle-number-recognition?vehicleNumber=' . $params['carNumber'] . '&dateFrom=' . $request->from . '&dateTo=' . $request->to)
            ->json();

        $dataWithImages = [];
        // print_r($getVehicleEntered);die;
        if (!empty($getVehicleEntered)) {
            if(!empty($getVehicleEntered['status']) && $getVehicleEntered['status']==404){
                return $this->successResponse(200, 'Məlumat tapılmadı', $dataWithImages);
            }
            $dataWithImages = $this->preparingShortedData($getVehicleEntered);
        }
        return $this->successResponse(200, 'response ok', $dataWithImages);
    }

    private function preparingShortedData($getVehicleEntered): array
    {
        $dataWithImages = [];
        foreach ($getVehicleEntered as $getVehicle) {
            $cameraPointer = getCameraPointer($getVehicle['cameraName']);
            $dataWithImages[] = [
                "insertDate" => date("Y-m-d H:i:s", strtotime($getVehicle['insertDate'])),
                'latitude' => (float)$cameraPointer->latitude,
                'longitude' => (float)$cameraPointer->longitude,
                'coordinates_latitude' => (float)$cameraPointer->coordinates_latitude,
                'coordinates_longitude' => (float)$cameraPointer->coordinates_longitude,
                'camera_name' => $getVehicle['cameraName'] ?? "",
                'camera_pointer_name' => $cameraPointer->name ?? ""
            ];
        }
        return $dataWithImages;
    }
}
