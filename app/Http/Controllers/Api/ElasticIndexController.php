<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Oracle\Azeri;
use App\Models\Oracle\Ecnebi;
use App\Models\Person;
use App\Models\PersonData;
use App\Services\ElasticSearch\ESIndexService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use App\Services\ElasticSearch\ESQuery;
use App\Services\ElasticSearch\ESService;
use App\Services\PersonService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\Client\RequestException as LaravelRequestException;
use Exception;

class ElasticIndexController extends Controller
{

    protected
        $index_names = [
            'people' => "people_2",
            'beein_contact' => "beein_contact_2",
            'azerisiq' => "azerisiq_2",
            'azeriqaz' => "azeriqaz_2",
            'azersu' => "azersu_2",
            'avtovagzal' => "avtovagzal_2",
            'sm_accounts' => "sm_accounts_2",
            'sm_posts' => "sm_posts_2",
            'sm_comments' => "sm_comments_2",
            'sm_descriptions' => "sm_descriptions_2",
            'bina_az' => 'bina_az_2',
            'tap_az' => 'tap_az_2',
            'turbo_az' => 'turbo_az_2',
            'food_orders' => 'food_orders_2',
            'voens' => 'voens_2',
            '189_data' => '189_data_2',
            '189_customers' => '189_customers_2',
            '189_drivers' => '189_drivers_2',
            'uklon_customers' => 'uklon_customers_2',
            'uklon_drivers' => 'uklon_drivers_2',
        ],
        $es_query,
        $es_service,
        $person_service,
        $es_index_service;

    public function __construct(PersonService $person_service,  ESIndexService  $es_index_service)
    {
        $this->es_query = new ESQuery('hosts_2');
        $this->es_service = new ESService();
        $this->person_service = $person_service;
        $this->es_index_service = $es_index_service;
    }

    // 189

    public function taksi_189_data()
    {
        $data = DB::table('189_data')
            ->select(['189_data.*', '189_drivers.name as driver_name'])
            ->leftJoin('189_drivers', '189_drivers.id', '=', '189_data.driverID')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('elastic_index_status')
                    ->where('elastic_index_status.type', '=', '189_data')
                    ->whereColumn('elastic_index_status.fk_id', '=', '189_data.id')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('189_data.id', 'ASC')
            ->take(5000)
            ->get();
            
        //    print_r($data);die;


        if (count($data)>0) {
            // DB::beginTransaction();
            $this->es_index_service->insertIndexStatus($data, '189_data');
            try {
                $params = [];
                foreach ($data as $item) {

                    $search_keys = [
                        'driver_name'        => Str::slug($item->driver_name ?? ''),
                        'cli'       => Str::slug($item->cli ?? ''),
                        'dial'        => Str::slug($item->dial ?? ''),
                        'carModel'     => Str::slug($item->carModel ?? ''),
                        'carColor' => Str::slug($item->carColor ?? ''),
                        'carPlate'         => Str::slug($item->carPlate ?? ''),
                        'adFirst'  => Str::slug($item->adFirst ?? ''),
                        'firstName'       => Str::slug($item->firstName ?? ''),
                        'adLast'        => Str::slug($item->adLast ?? ''),
                        'lastName'     => Str::slug($item->lastName ?? ''),
                    ];


                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['189_data'],
                            '_id'    => $item->id,
                        ]
                    ];



                    $params['body'][] = [
                        'data'        => $item,
                        'search_keys' =>  $search_keys,
                        'order_by'    => (int)$item->order_id,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),

                    ];
                }

                $esResponse = $this->es_query->bulk($params);

                $this->es_index_service->updateEsIndexStatus($esResponse, '189_data');

                // DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                // DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty 189_data';
        }
    }
    public function taksi_189_customers()
    {
        $data = DB::table('189_customers')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('elastic_index_status')
                    ->where('elastic_index_status.type', '=', '189_customers')
                    ->whereColumn('elastic_index_status.fk_id', '=', '189_customers.id')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('189_customers.id', 'ASC')
            ->take(5000)
            ->get();


         if (count($data)>0) {

            // DB::beginTransaction();
            $this->es_index_service->insertIndexStatus($data, '189_customers');

            try {

                $params = [];
                foreach ($data as $item) {

                    $saved_address = json_decode($item->saved_address);

                    $text_saved_address = '';
                    if (!empty($saved_address)) {
                        foreach ($saved_address as $address) {
                            $text_saved_address = $text_saved_address . ' ' . Str::slug($address->address);
                        }
                    }
                    $item->saved_address = $saved_address;
                    $search_keys = [
                        'name'        => Str::slug($item->name),
                        'surname'        => Str::slug($item->surname),
                        'phone'        => Str::slug($item->phone),
                        'email'        => Str::slug($item->email),
                        'home_address'        => Str::slug($item->home_address),
                        'ip_address'        => Str::slug($item->ip_address),
                        'saved_address'        => Str::slug($text_saved_address),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['189_customers'],
                            '_id'    => $item->id,
                        ]
                    ];

                    $params['body'][] = [
                        'data'        => $item,
                        'search_keys' =>  $search_keys,
                        'order_by'    => (int)$item->id,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }
                $esResponse = $this->es_query->bulk($params);

                $this->es_index_service->updateEsIndexStatus($esResponse, '189_customers');

                // DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                // DB::rollBack();
                return $e->getMessage();
            }
        } else {
            return 'empty 189_customers';
        }
    }
    public function taksi_189_drivers()
    {
        $data = DB::table('189_drivers')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('elastic_index_status')
                    ->where('elastic_index_status.type', '=', '189_drivers')
                    ->whereColumn('elastic_index_status.fk_id', '=', '189_drivers.id')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('189_drivers.id', 'ASC')
            ->take(5000)
            ->get();


         if (count($data)>0) {

            // DB::beginTransaction();
            $this->es_index_service->insertIndexStatus($data, '189_drivers');

            try {

                $params = [];
                foreach ($data as $item) {


                    $search_keys = [
                        'name'        => Str::slug($item->name),
                        'surname'        => Str::slug($item->surname),
                        'phone'        => Str::slug($item->phone),
                        'pin'        => Str::slug($item->pin),
                        'voen'        => Str::slug($item->voen),
                        'license_plate'        => Str::slug($item->license_plate),
                        'id_serial'        => Str::slug($item->id_serial),
                        'ip'        => Str::slug($item->ip),
                        'technical_passport'        => Str::slug($item->technical_passport),
                        'car_model'        => Str::slug($item->car_model),
                        'car_brand'        => Str::slug($item->car_brand),
                        'manufacture_year' => Str::slug($item->manufacture_year),
                        'color'        => Str::slug($item->color),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['189_drivers'],
                            '_id'    => $item->id,
                        ]
                    ];

                    $params['body'][] = [
                        'data'        => $item,
                        'search_keys' =>  $search_keys,
                        'order_by'    => (int)$item->id,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }
                $esResponse = $this->es_query->bulk($params);

                $this->es_index_service->updateEsIndexStatus($esResponse, '189_drivers');

                // DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                // DB::rollBack();
                return $e->getMessage();
            }
        } else {
            return 'empty 189_drivers';
        }
    }

    public function uklon_customers()
    {
        $data = DB::table('uklon_customers')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('elastic_index_status')
                    ->where('elastic_index_status.type', '=', 'uklon_customers')
                    ->whereColumn('elastic_index_status.fk_id', '=', 'uklon_customers.id')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('uklon_customers.id', 'ASC')
            ->take(100)
            ->get();


        if (!empty($data)) {

            // DB::beginTransaction();
            $this->es_index_service->insertIndexStatus($data, 'uklon_customers');

            try {

                $params = [];
                foreach ($data as $item) {

                    $search_keys = [
                        'first_name'        => Str::slug($item->first_name),
                        'last_name'        => Str::slug($item->last_name),
                        'email'        => Str::slug($item->email),
                        'phone_number'        => Str::slug($item->phone_number),
                        'home_address'        => Str::slug($item->home_address),
                        'work_address'        => Str::slug($item->work_address),
                        'saved_addresses'        => Str::slug($item->saved_addresses),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['uklon_customers'],
                            '_id'    => $item->id,
                        ]
                    ];

                    $params['body'][] = [
                        'data'        => $item,
                        'search_keys' =>  $search_keys,
                        'order_by'    => (int)$item->id,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }
                $esResponse = $this->es_query->bulk($params);

                $this->es_index_service->updateEsIndexStatus($esResponse, 'uklon_customers');

                // DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                // DB::rollBack();
                return $e->getMessage();
            }
        } else {
            return 'empty uklon_customers';
        }
    }

    public function uklon_drivers()
    {
        $data = DB::table('uklon_drivers')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('elastic_index_status')
                    ->where('elastic_index_status.type', '=', 'uklon_drivers')
                    ->whereColumn('elastic_index_status.fk_id', '=', 'uklon_drivers.id')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('uklon_drivers.id', 'ASC')
            ->take(100)
            ->get();


        if (!empty($data)) {

            // DB::beginTransaction();
            $this->es_index_service->insertIndexStatus($data, 'uklon_drivers');

            try {

                $params = [];
                foreach ($data as $item) {

                    $search_keys = [
                        'first_name'        => Str::slug($item->first_name),
                        'last_name'        => Str::slug($item->last_name),
                        'email'        => Str::slug($item->email),
                        'phone'        => Str::slug($item->phone),
                        'id_number'        => Str::slug($item->id_number),
                        'tax_identity_number'        => Str::slug($item->tax_identity_number),
                        'driver_license'        => Str::slug($item->driver_license),
                        'license_plate'        => Str::slug($item->license_plate),
                        'vehicle_model'        => Str::slug($item->vehicle_model),
                        'body_color'        => Str::slug($item->body_color),
                        'vehicle_made_year'        => Str::slug($item->vehicle_made_year),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['uklon_drivers'],
                            '_id'    => $item->id,
                        ]
                    ];

                    $params['body'][] = [
                        'data'        => $item,
                        'search_keys' =>  $search_keys,
                        'order_by'    => (int)$item->id,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }
                $esResponse = $this->es_query->bulk($params);

                $this->es_index_service->updateEsIndexStatus($esResponse, 'uklon_drivers');

                // DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                // DB::rollBack();
                return $e->getMessage();
            }
        } else {
            return 'empty uklon_drivers';
        }
    }

    // People
    public function peopleIndex($pin = null)
    {
        die;
        $persons_query = DB::table('people');

        if (!empty($pin)) {
            $persons_query->where('pin', $pin);
        } else {
            $persons_query
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                        ->from('elastic_index_status')
                        ->whereRaw('elastic_index_status.fk_id = people.id::VARCHAR')
                        ->where('elastic_index_status.type', 'people');
                    // ->where('elastic_index_status.status', '!=', 0);
                })
                ->orderBy('id', 'ASC')
                ->take(500);
        }

        $data = $persons_query->get();

        if (!empty($data)) {
            DB::beginTransaction();
            $this->es_index_service->insertIndexStatus($data, 'people');

            try {
                $params = [];
                foreach ($data as $person) {
                    $phones = DB::table('phones')->select('phone')->where('pin', $person->pin)->distinct()->get();
                    $phones_search_keys = '';
                    foreach ($phones as $phone) {
                        $phone = $phone->phone;
                        $phones_search_keys .= ' 0' . $phone . ' 994' . $phone . ' +994' . $phone;
                    }


                    $driver_license = PersonData::where('pin', $person->pin)->where('type', 'driver_license')->first();
                    $driver_license_dl_code = $driver_license['data']['data']['dlCode'] ?? '';



                    $vehicles = PersonData::where('pin', $person->pin)->where('type', 'vehicles')->first();
                    $vehicles_search_keys = '';
                    if (isset($vehicles['data']['data']['result']['vehicleInfoForSocial'])) {
                        foreach ($vehicles['data']['data']['result']['vehicleInfoForSocial'] as $car) {
                            $vehicles_search_keys .= ' ' .
                                (isset($car['vehicleNumber']) ?  Str::slug($car['vehicleNumber']) : '') . '-' .
                                (isset($car['vehicleModel']) ? Str::slug($car['vehicleModel']) : '');
                        }
                    }

                    $foreign_passport = PersonData::where('pin', $person->pin)->where('type', 'foreign_passport')->first();
                    $foreign_passport_number = $foreign_passport['data']['data']['response']['response']['Result']['PassportNumber'] ?? '';



                    $studi_keys = '';
                    $job_keys = '';
                    $tqdk_keys = $this->es_service->tqdk_keys($person->pin);


                    try {
                        $docType = trim($person->doc_serial_number) !== '' ? 'old' : 'new';
                        $image_path = 'imgs_' . $docType . '/' . $person->pin . '.png';
                    } catch (Exception $e) {
                        $image_path = '';
                    }

                    $age = Carbon::parse($person->birthdate)->age;

                    // $l = DB::table('label_pins')->where('label_pins.pin', $person->pin)
                    //     ->join('labels', 'labels.id', 'label_pins.fk_id_label')->first();

                    // $labels = DB::table('label_pins')->where('label_pins.pin', $person->pin)
                    //     ->join('labels', 'labels.id', 'label_pins.fk_id_label')->pluck('labels.name')->toArray();
                    // Prepare search keys
                    $search_keys = [
                        'pin' => Str::slug($person->pin),
                        'doc_serial_number' => Str::slug($person->doc_number),
                        'name' => Str::slug($person->name),
                        'surname' => Str::slug($person->surname),
                        'father_name' => Str::slug($person->father_name),
                        'age' => (int)$age,
                        'eye_color' => Str::slug($this->es_service->eye_colors[$person->eye_color]),
                        'height' => (int)$person->height,
                        'blood_group' => $this->es_service->blood_groups[$person->blood_group] . '-' . Str::slug($person->blood_group),
                        'birth_date' => (new \DateTime($person->birthdate))->format('Y-m-d') ?? null,
                        'sex' => Str::slug($person->sex),
                        'marital_status' => Str::slug($person->marital_status),
                        'country' => Str::slug($person->country),
                        'city' => Str::slug($person->city),
                        'address' => str_replace('-', '_', Str::slug($person->address)),
                        'foreign_passport_number' => Str::slug($foreign_passport_number),
                        'driver_license' => Str::slug($driver_license_dl_code),
                        'vehicles' => Str::slug($vehicles_search_keys),
                        'phones' => Str::slug($phones_search_keys),
                        'jobs' => Str::slug($job_keys),
                        'studies' => Str::slug($studi_keys),
                        'tqdk' => Str::slug($tqdk_keys),
                        'danger_level'   => (int) ($l->degree ?? 0),
                        // 'labels' => Str::slug(implode(' ',  $labels))
                    ];

                    // Prepare Elasticsearch index parameters
                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['people'],
                            '_id' => $person->id,
                        ]
                    ];
                    $params['body'][] = [
                        'danger_level'   => (int) ($l->degree ?? 0),
                        'pin'            => (string) $person->pin,
                        'doc_number'     => (string) $person->doc_number,
                        'name'           => (string) $person->name,
                        'surname'        => (string) $person->surname,
                        'father_name'    => (string) $person->father_name,
                        'image'          => (string) $image_path,
                        'eye_color'      => (string) $this->es_service->eye_colors[$person->eye_color],
                        'height'         => (int) $person->height,
                        'blood_group'    => (string) $person->blood_group,
                        'birth_date' => (new \DateTime($person->birthdate))->format('Y-m-d') ?? null,
                        'sex'            => (string) $person->sex,
                        'marital_status' => (string) $person->marital_status,
                        'country'        => (string) $person->country,
                        'city'           => (string) $person->city,
                        'address'        => (string) $person->address,
                        'foreign_passport_number' => (string) $foreign_passport_number,
                        'search_keys'    => $search_keys,
                        'order_by'       => (int) ($l->degree ?? 0),
                        'created_at' => now()->format('Y-m-d H:i:s'),
                        'updated_at' => null,

                    ];
                }
                $esResponse = $this->es_query->bulk($params);

                // $this->es_index_service->updateEsIndexStatus($esResponse, 'people');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty people';
        }
    }
    // End People

    public function beeinContact()
    {
        $data = DB::table('beein_contact_2')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('elastic_index_status')
                    ->whereRaw('elastic_index_status.fk_id = beein_contact_2.id::VARCHAR')
                    ->where('elastic_index_status.type', 'beein_contact')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('beein_contact_2.id', 'ASC')
            ->take(100)
            ->get();


        if (!empty($data)) {

            // DB::beginTransaction();
            $this->es_index_service->insertIndexStatus($data, 'beein_contact');

            try {

                $params = [];
                foreach ($data as $item) {

                    $phone = $item->phone;

                    $phone = substr($phone, -9);
                    $phones_str = '994' . $phone . '-0' . $phone;
                    $search_keys = Str::slug($phones_str . '-' . $item->pin . '-' .  $item->name . '-' . $item->email . '-' . $item->source);

                    $search_keys = [
                        'phone'       => Str::slug($phones_str),
                        'name'        => Str::slug($item->name),
                        'surname'     => Str::slug($item->surname),
                        'father_name' => Str::slug($item->father_name),
                        'pin'         => Str::slug($item->pin),
                        'doc_number'  => Str::slug($item->doc_number),
                        'email'       => Str::slug($item->email),
                        'birth_date'  => (new \DateTime($item->birthday))->format('Y-m-d') ?? null,
                        'city'        => Str::slug($item->city),
                        'address'     => Str::slug($item->address),
                        'note'        => Str::slug($item->note),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['beein_contact'],
                            '_id'    => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'phone'       => (string)$item->phone ?? '',
                        'name'        => (string)$item->name ?? '',
                        'surname'     => (string)$item->surname ?? '',
                        'father_name' => (string)$item->father_name ?? '',
                        'pin'         => (string)$item->pin ?? '',
                        'doc_number'  => (string)$item->doc_number ?? '',
                        'email'       => (string)$item->email ?? '',
                        'birth_date'  => (new \DateTime($item->birthday))->format('Y-m-d') ?? null,
                        'city'        => (string)$item->city ?? '',
                        'address'     => (string)$item->address ?? '',
                        'note'        => (string)$item->note ?? '',
                        'source'      => (string)$item->source ?? '',
                        'search_keys' =>  $search_keys,
                        'order_by'    => (int)$item->weight,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),

                    ];
                }

                $esResponse = $this->es_query->bulk($params);

                $this->es_index_service->updateEsIndexStatus($esResponse, 'beein_contact');

                // DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                // DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty beein contact';
        }
    }

    public function azerisiqBaki()
    {
        // die;
        $data = DB::table('azerisiq_baki')
            ->select('*', "subid as id")
            // ->whereNotExists(function ($query) {
            //     $query->select(DB::raw(1))
            //         ->from('elastic_index_status')
            //         ->whereRaw('elastic_index_status.fk_id = azerisiq_baki.subid')
            //         ->where('elastic_index_status.type', 'azerisiq_baki')
            //         ->where('elastic_index_status.status', '!=', 0);
            // })
            ->orderBy('subid', 'DESC')
            ->take(5000)
            ->get();


        if (!empty($data)) {
            DB::beginTransaction();
            try {

                // $this->es_index_service->insertIndexStatus($data, 'azerisiq_baki');
                $params = [];
                foreach ($data as $item) {

                    // print_r($item);die;
                    $phone = preg_replace('/\D/', '', $item->contact);
                    $phone = substr($phone, -9);
                    $phone_str = '994' . $phone . '-0' . $phone;



                    $search_keys = [
                        'subid'       => Str::slug($item->subid),
                        'type'        => 'baki',
                        'phone'       => Str::slug($phone_str),
                        'name'        => Str::slug($item->name),
                        'surname'     => Str::slug($item->surname),
                        'father_name' => Str::slug($item->middlename),
                        'rayon'       => Str::slug($item->rayon),
                        'matrix'      => Str::slug($item->matrix),
                        'street'      => Str::slug($item->street),
                        'full_adress' => Str::slug($item->full_adress),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['azerisiq'],
                            '_id' => $item->subid,
                        ]
                    ];
                    $params['body'][] = [
                        "subid"       => (int) $item->subid,
                        'type'        => (string) 'baki',
                        "name"        => (string) $item->name,
                        "surname"     => (string) $item->surname,
                        "father_name" => (string) $item->middlename,
                        "contact"     => (string) $item->contact,
                        "rayon"       => (string) $item->rayon,
                        "matrix"      => (string) $item->matrix,
                        "street"      => (string) $item->street,
                        "full_adress" => (string) $item->full_adress,
                        "status"      => (string) $item->status,
                        'search_keys' => $search_keys,
                        'order_by'    => (int) $item->subid,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                // $this->es_index_service->updateEsIndexStatus($esResponse, 'azerisiq_baki');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty azerisiqBaki';
        }
    }


    public function azparking()
    {
        die;
        $data = DB::connection('mongodb')->table('azparking_users')->orderBy('id', 'DESC')->take(10)->get();
        return $data;
    }

    public function azerisiqRegion()
    {
        // die;

        $data = DB::table('azerisiq_region')
            ->select('*', "subid as id")
            // ->whereNotExists(function ($query) {
            //     $query->select(DB::raw(1))
            //         ->from('elastic_index_status')
            //         ->whereRaw('elastic_index_status.fk_id = azerisiq_region.subid')
            //         ->where('elastic_index_status.type', 'azerisiq_region')
            //         ->where('elastic_index_status.status', '!=', 0);
            // })
            ->orderBy('subid', 'DESC')
            ->take(5000)
            ->get();


        if (!empty($data)) {
            DB::beginTransaction();
            try {

                // $this->es_index_service->insertIndexStatus($data, 'azerisiq_region');
                $params = [];
                foreach ($data as $item) {

                    $phone = Str::slug($item->contact);
                    $phone = substr($phone, -9);
                    $phone_str = '994' . $phone . '-0' . $phone;


                    $search_keys = [
                        'subid'       => Str::slug($item->subid),
                        'type'        => 'region',
                        'phone'       => Str::slug($phone_str),
                        'name'        => Str::slug($item->name),
                        'surname'     => Str::slug($item->surname),
                        'father_name' => Str::slug($item->middlename),
                        'rayon'       => Str::slug($item->rayon),
                        'matrix'      => Str::slug($item->matrix),
                        'street'      => Str::slug($item->street),
                        'full_adress' => Str::slug($item->full_adress),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['azerisiq'],
                            '_id' => $item->subid,
                        ]
                    ];
                    $params['body'][] = [
                        "subid"       => (int) $item->subid,
                        'type'        => (string) 'region',
                        "name"        => (string) $item->name,
                        "surname"     => (string) $item->surname,
                        "father_name" => (string) $item->middlename,
                        "contact"     => (string) $item->contact,
                        "rayon"       => (string) $item->rayon,
                        "matrix"      => (string) $item->matrix,
                        "street"      => (string) $item->street,
                        "full_adress" => (string) $item->full_adress,
                        "status"      => (string) $item->status,
                        'search_keys' => $search_keys,
                        'order_by'    => (int) $item->subid,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                // $this->es_index_service->updateEsIndexStatus($esResponse, 'azerisiq_region');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty azerisiq_region';
        }
    }

    public function azeriqaz()
    {
        // die;
        $data = DB::table('azeriqaz_ehali')
            ->select('*')
            // ->whereNotExists(function ($query) {
            //     $query->select(DB::raw(1))
            //         ->from('elastic_index_status')
            //         ->whereRaw('elastic_index_status.fk_id = azeriqaz_ehali.id::VARCHAR')
            //         ->where('elastic_index_status.type', 'azeriqaz_ehali')
            //         ->where('elastic_index_status.status', '!=', 0);
            // })
            ->orderBy('subid', 'ASC')
            ->take(5000)
            ->get();


        // print_r($data);die;
        if (!empty($data)) {
            DB::beginTransaction();
            try {
                // $this->es_index_service->insertIndexStatus($data, 'azeriqaz_ehali');


                $params = [];
                foreach ($data as $item) {



                    $phone = Str::slug($item->contact);
                    $phone = substr($phone, -9);
                    $phone_str = '';
                    if (strlen($phone) >= 9) {
                        $phone_str = '994' . $phone . '-0' . $phone;
                    } elseif (strlen($phone) == 7) {
                        $phone = '12' . $phone;
                        $phone_str = '994' . $phone . '-0' . $phone;
                    }


                    $search_keys = [
                        'subid'       => Str::slug($item->subid),
                        'phone'       => Str::slug($phone_str),
                        'fin'        => Str::slug($item->fin),
                        'name'        => Str::slug($item->name),
                        'surname'     => Str::slug($item->surname),
                        'father_name' => Str::slug($item->middlename),
                        'rayon'       => Str::slug($item->rayon),
                        'kuce'      => Str::slug($item->kuce),
                        'unvan'      => Str::slug($item->unvan),
                        'imtiyaz' => Str::slug($item->imtiyaz),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['azeriqaz'],
                            '_id' => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        "subid"             => (int) $item->subid,
                        "fin"               => (string) $item->fin,
                        "name"              => (string) $item->name,
                        "surname"           => (string) $item->surname,
                        "father_name"       => (string) $item->middlename,
                        "contact"           => (string) $item->contact,
                        "unvan"             => (string) $item->unvan,
                        "rayon"             => (string) $item->rayon,
                        "kuce"              => (string) $item->kuce,
                        "house"             => (string) $item->house,
                        "menzil"            => (string) $item->menzil,
                        "m3_12ay"           => (string) $item->m3_12ay,
                        "son_oden"          => (string) $item->son_oden,
                        "son_oden_tar"      => (string) $item->son_oden_tar,
                        "son_oden_tip"      => (string) $item->son_oden_tip,
                        "sayqac_nom"        => (string) $item->sayqac_nom,
                        "marka"             => (string) $item->marka,
                        "say_qur_tarixi"    => (string) $item->say_qur_tarixi,
                        "family_population" => (string) $item->family_population,
                        "pspt_ser"          => (string) $item->pspt_ser,
                        "pspt_no"           => (string) $item->pspt_no,
                        "regdate"           => (string) $item->regdate,
                        "men_tip"           => (string) $item->men_tip,
                        "imtiyaz"           => (string) $item->imtiyaz,
                        "teyin_noqte"       => (string) $item->teyin_noqte,
                        'search_keys'       => $search_keys,
                        'order_by'          => (int) $item->subid,
                        'created_at' => now()->format('Y-m-d\TH:i:s'),
                        'updated_at' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                // $this->es_index_service->updateEsIndexStatus($esResponse, 'azeriqaz_ehali');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty azeriqaz_ehali';
        }
    }

    public function azersu()
    {
        // die;
        $data = DB::table('azersu_abonent')
            ->select('*', "abonent_kodu as id")
            // ->whereNotExists(function ($query) {
            //     $query->select(DB::raw(1))
            //         ->from('elastic_index_status')
            //         ->whereRaw('elastic_index_status.fk_id = azersu_abonent.abonent_kodu')
            //         ->where('elastic_index_status.type', 'azersu_abonent')
            //         ->where('elastic_index_status.status', '!=', 0);
            // })
            ->orderBy('abonent_kodu', 'ASC')
            ->take(5000)
            ->get();

        // die;

        if (!empty($data)) {
            DB::beginTransaction();
            try {
                // $this->es_index_service->insertIndexStatus($data, 'azersu_abonent');


                $params = [];
                foreach ($data as $item) {

            
                    $search_keys = [
                        'abonent_kodu'       => Str::slug($item->abonent_kodu),
                        'muqavile_no'        => Str::slug($item->muqavile_no),
                        'ad_soyad'        => Str::slug($item->ad_soyad),
                        'ata_adi' => Str::slug($item->ata_adi),
                        'unvan'       => Str::slug($item->unvan),
                        'voen_no'      => Str::slug($item->voen_no),
                    ];
                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['azersu'],
                            '_id' => $item->abonent_kodu,
                        ]
                    ];
                    $params['body'][] = [
                        "abonent_kodu" => (string) $item->abonent_kodu,
                        "muqavile_no"  => (string) $item->muqavile_no,
                        "ad_soyad" => (string) $item->ad_soyad,
                        "ata_adi"  => (string) $item->ata_adi,
                        "unvan"        => (string) $item->unvan,
                        "voen_no"      => (string) $item->voen_no,
                        'search_keys'  => $search_keys,
                        'order_by'     => $item->abonent_kodu,
                        'created_at' => now()->format('Y-m-d\TH:i:s'),
                        'updated_at' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                // $this->es_index_service->updateEsIndexStatus($esResponse, 'azersu_abonent');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty azersu_abonent';
        }
    }
    public function azerpoct()
    {
        die;
        $data = DB::table('azerisiq_baki')
            ->take(200)
            ->get();

        return $data;
    }
    public function avtovagzal()
    {
        $data = DB::table('avtovagzal_tickets')
            ->select(
                'avtovagzal_passengers.user_id',
                'avtovagzal_passengers.name as passenger_name',
                'avtovagzal_passengers.phone as passenger_phone',
                'avtovagzal_passengers.email as passenger_email',
                'avtovagzal_passengers.gender as passenger_gender',
                'avtovagzal_passengers.active as passenger_active',


                'avtovagzal_tickets.id',
                'avtovagzal_tickets.trip_name as tickets_trip_name',
                'avtovagzal_tickets.trip_day as tickets_trip_day',
                'avtovagzal_tickets.time_start as tickets_time_start',
                'avtovagzal_tickets.number as tickets_number',
                'avtovagzal_tickets.duration as tickets_duration',
                'avtovagzal_tickets.dqn as tickets_dqn',
                'avtovagzal_tickets.sub_trip as tickets_sub_trip',
                'avtovagzal_tickets.name as tickets_name',
                'avtovagzal_tickets.phone as tickets_phone',
                'avtovagzal_tickets.place as tickets_place',
                'avtovagzal_tickets.seat as tickets_seat',
                'avtovagzal_tickets.price as tickets_price',
                'avtovagzal_tickets.ticket_type as tickets_ticket_type',
                'avtovagzal_tickets.payment_status as tickets_payment_status',
                'avtovagzal_tickets.sale_date as tickets_sale_date',
                'avtovagzal_tickets.bank_id as tickets_bank_id',
                'avtovagzal_tickets.foreign_citizen as tickets_foreign_citizen',
                'avtovagzal_tickets.idcard as tickets_idcard',
                'avtovagzal_tickets.fin as tickets_fin',
                'avtovagzal_tickets.foreign_doc as tickets_foreign_doc',
                'avtovagzal_tickets.foreign_country as tickets_foreign_country',

            )
            ->leftJoin('avtovagzal_passengers', 'avtovagzal_passengers.user_id', '=', 'avtovagzal_tickets.user_id')
            // ->whereNotExists(function ($query) {
            //     $query->select(DB::raw(1))
            //         ->from('elastic_index_status')
            //         ->whereRaw('elastic_index_status.fk_id = avtovagzal_tickets.id::VARCHAR')
            //         ->where('elastic_index_status.type', 'avtovagzal_tickets')
            //         ->where('elastic_index_status.status', '!=', 0);
            // })
            ->orderBy('id', 'ASC')
            ->take(10)
            ->get();

        // print_r($data);die;

        if (!empty($data)) {

            DB::beginTransaction();
            $this->es_index_service->insertIndexStatus($data, 'avtovagzal_tickets');


            try {
                $params = [];
                foreach ($data as $item) {

                    $phone_passenger = preg_replace('/\D/', '', $item->passenger_phone);
                    $phone_passenger = substr($phone_passenger, -9);

                    $phone_passenger_str = '';
                    if (strlen($phone_passenger) >= 9) {
                        $phone_passenger_str = $phone_passenger_str . ' 994' . $phone_passenger . '-0' . $phone_passenger;
                    }

                    $phone_ticket = preg_replace('/\D/', '', $item->tickets_phone);
                    $phone_ticket = substr($phone_ticket, -9);

                    $phone_ticket_str = '';
                    if (strlen($phone_ticket) >= 9) {
                        $phone_ticket_str = $phone_ticket_str . '994' . $phone_ticket . '-0' . $phone_ticket;
                    }

                    $search_keys = [
                        'passenger_phone'       => Str::slug($phone_passenger_str),
                        'passenger_name' => Str::slug($item->passenger_name),
                        'passenger_email'       => Str::slug($item->passenger_email),

                        'ticket_phone'       => Str::slug($phone_ticket_str),
                        'tickets_pin'         => Str::slug($item->tickets_fin),
                        'tickets_name'            => Str::slug($item->tickets_name),
                        'tickets_foreign_doc'      => Str::slug($item->tickets_foreign_doc),
                        'tickets_foreign_country'      => Str::slug($item->tickets_foreign_country),
                        'tickets_dqn'      => Str::slug($item->tickets_dqn),
                        'tickets_trip_name'      => Str::slug($item->tickets_trip_name),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['avtovagzal'],
                            '_id' => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'id' => $item->id ?? '',
                        'user_id' => $item->user_id ?? '',
                        'passenger_name' => $item->passenger_name ?? '',
                        'passenger_phone' => $item->passenger_phone ?? '',
                        'passenger_email' => $item->passenger_email ?? '',
                        'passenger_gender' => $item->passenger_gender ?? '',
                        'passenger_active' => $item->passenger_active ?? '',
                        'tickets_name' => $item->tickets_name ?? '',
                        'tickets_pin' => strlen($item->tickets_fin) == 7 ? strtoupper($item->tickets_fin)  : null,
                        'tickets_doc_number' => $item->tickets_idcard ?? '',
                        'tickets_phone' => $item->tickets_phone ?? '',
                        'tickets_foreign_doc' => $item->tickets_foreign_doc ?? '',
                        'tickets_foreign_country' => $item->tickets_foreign_country ?? '',
                        'tickets_foreign_citizen' => $item->tickets_foreign_citizen ?? '',
                        'tickets_trip_name' => $item->tickets_trip_name ?? '',
                        'tickets_trip_day' => $item->tickets_trip_day ?? '',
                        'tickets_sale_date' => $item->tickets_sale_date ?? '',
                        'tickets_time_start' => $item->tickets_time_start ?? '',
                        'tickets_number' => $item->tickets_number ?? '',
                        'tickets_duration' => $item->tickets_duration ?? '',
                        'tickets_dqn' => $item->tickets_dqn ?? '',
                        'tickets_sub_trip' => $item->tickets_sub_trip ?? '',
                        'tickets_place' => $item->tickets_place ?? '',
                        'tickets_seat' => $item->tickets_seat ?? '',
                        'tickets_price' => $item->tickets_price ?? '',
                        'tickets_ticket_type' => $item->tickets_ticket_type ?? '',
                        'tickets_payment_status' => $item->tickets_payment_status ?? '',
                        'search_keys' => $search_keys,
                        'order_by'    => Carbon::parse($item->tickets_sale_date)->format('Y-m-d\TH:i:s'),
                        'created_at'  => now()->format('Y-m-d H:i:s'),
                        'updated_at'  => now()->format('Y-m-d H:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                $this->es_index_service->updateEsIndexStatus($esResponse, 'avtovagzal_tickets');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty avtovagzal';
        }
    }
    public function ady()
    {
        die;
        $data = DB::table('azerisiq_baki')
            ->take(200)
            ->get();

        return $data;
    }


    //Social Media

    public function socialMediaAccountsIndex()
    {
        die;
        $accounts = $this->es_index_service->getAccounts();
        // print_r($accounts);
        // die;
        if (count($accounts) > 0) {
            DB::beginTransaction();
            try {
                $this->es_index_service->insertIndexStatus($accounts, 'accounts', 'social_hub');

                $params = [];
                foreach ($accounts as $item) {

                    $search_keys = [
                        'platform_name'       => Str::slug($item->platform_name),
                        'username'        => Str::slug($item->username),
                        'name'        => Str::slug($item->name),
                        'surname' => Str::slug($item->surname),
                        'nationality'       => Str::slug($item->nationality)
                    ];

                    $search_keys = [
                        'platform_name'       => Str::slug($item->platform_name),
                        'username'       => Str::slug($item->username),
                        'name'       => Str::slug($item->name),
                        'surname'       => Str::slug($item->surname),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['sm_accounts'],
                            '_id' => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'id' => (int) $item->id,
                        'platform_name' => (string) $item->platform_name,
                        'pin' => (string) $item->pin,
                        'username' => (string) $item->username,
                        'name' => (string) $item->name,
                        'surname' => (string) $item->surname,
                        'photo_path' => (string) $item->photo_path,
                        'nationality' => (string) $item->nationality,
                        'link' => (string) $item->link,
                        'insert_time' => (string) $item->insert_time,
                        'search_keys' => $search_keys,
                        'order_by'   => Carbon::parse($item->insert_time)->format('Y-m-d\TH:i:s'),
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                $this->es_index_service->updateEsIndexStatus($esResponse, 'accounts', 'social_hub');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty accounts';
        }
    }

    public function socialMediaPostsIndex()
    {
        die;
        $posts = $this->es_index_service->getPosts();

        // print_r($posts);die;
        if (count($posts) > 0) {
            DB::beginTransaction();
            try {
                $this->es_index_service->insertIndexStatus($posts, 'posts', 'social_hub');

                $params = [];
                foreach ($posts as $item) {

                    $search_keys = [
                        'platform_name'       => Str::slug($item->platform_name),
                        'post_context'        => Str::slug($item->post_context)
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['sm_posts'],
                            '_id' => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'platform_name' => (string) $item->platform_name,

                        'account_id' => (int) $item->account_id,
                        'account_pin' => (string) $item->account_pin,
                        'account_name' => (string) $item->account_name,
                        'account_surname' => (string) $item->account_surname,
                        'account_username' => (string) $item->account_username,
                        'account_link' => (string) $item->account_link,
                        'account_photo_path' => (string) $item->account_photo_path,

                        'post_id' => (int) $item->id,
                        'post_page_id' => (int) $item->post_page_id,
                        'post_source_type' => (string) $item->post_source_type,
                        'post_comment_count' => (int) $item->post_comment_count,
                        'post_publish_time' => (string) $item->post_publish_time,
                        'post_context' => (string) $item->post_context,
                        'post_link' => (string) $item->post_link,
                        'post_photo_path' => (string) $item->post_photo_path,

                        'search_keys' => $search_keys,
                        'order_by'   => Carbon::parse($item->post_publish_time)->format('Y-m-d\TH:i:s'),
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }


                $esResponse = $this->es_query->bulk($params);
                $this->es_index_service->updateEsIndexStatus($esResponse, 'posts', 'social_hub');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty posts';
        }
    }

    public function socialMediaCommentsIndex()
    {
        die;
        $comments = $this->es_index_service->getComments();
        // die;

        if (count($comments) > 0) {
            DB::beginTransaction();
            try {
                $this->es_index_service->insertIndexStatus($comments, 'comments', 'social_hub');

                $params = [];
                foreach ($comments as $item) {
                    $search_keys = [
                        'platform_name'       => Str::slug($item->platform_name),
                        'comment_context'        => Str::slug($item->comment_context)
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['sm_comments'],
                            '_id' => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'platform_name' => (string) $item->platform_name,

                        'account_id' => (int) $item->account_id,
                        'account_pin' => (string) $item->account_pin,
                        'account_name' => (string) $item->account_name,
                        'account_surname' => (string) $item->account_surname,
                        'account_username' => (string) $item->account_username,
                        'account_link' => (string) $item->account_link,
                        'account_photo_path' => (string) $item->account_photo_path,

                        'post_id' => (int) $item->id,
                        'post_page_id' => (int) $item->post_page_id,
                        'post_source_type' => (string) $item->post_source_type,
                        'post_comment_count' => (int) $item->post_comment_count,
                        'post_publish_time' => (string) $item->post_publish_time,
                        'post_context' => (string) $item->post_context,
                        'post_link' => (string) $item->post_link,
                        'post_photo_path' => (string) $item->post_photo_path,

                        'comment_id' => (int) $item->id ?? '',
                        'comment_context' => (string) $item->comment_context,
                        'comment_publish_time' => (string) $item->comment_publish_time,
                        'comment_link' => (string) $item->comment_link,

                        'search_keys' =>  $search_keys,
                        'order_by'   => Carbon::parse($item->comment_publish_time)->format('Y-m-d\TH:i:s'),
                        'created_at' => now()->format('Y-m-d H:i:s'),
                        'updated_at' => now()->format('Y-m-d H:i:s'),
                    ];
                }
                $esResponse = $this->es_query->bulk($params);
                $this->es_index_service->updateEsIndexStatus($esResponse, 'comments', 'social_hub');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty comments';
        }
    }

    public function socialMediaDescriptionsIndex()
    {
        die;
        $descriptions = $this->es_index_service->getDescriptions();
        // die;
        if (count($descriptions) > 0) {
            DB::beginTransaction();

            try {
                $this->es_index_service->insertIndexStatus($descriptions, 'descriptions', 'social_hub');

                $params = [];
                foreach ($descriptions as $item) {

                    $search_keys = [
                        'platform_name' => Str::slug($item->platform_name),
                        'desc_aze'     => Str::slug($item->desc_aze),
                        'desc_eng'    => Str::slug($item->desc_eng),
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['sm_descriptions'],
                            '_id' => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'pin' => (string) $item->pin,
                        'platform_name' => (string) $item->platform_name,

                        'desc_id' => (int) $item->id,
                        'desc_aze' => (string) $item->desc_aze,
                        'desc_eng' => (string) $item->desc_eng,
                        'desc_sort_order' => (int) $item->desc_sort_order,
                        'desc_photo_path' => (string) $item->desc_photo_path,

                        'account_id' => (int) $item->account_id,
                        'account_pin' => (string) $item->account_pin,
                        'account_name' => (string) $item->account_name,
                        'account_surname' => (string) $item->account_surname,
                        'account_username' => (string) $item->account_username,
                        'account_link' => (string) $item->account_link,
                        'account_photo_path' => (string) $item->account_photo_path,

                        'search_keys' => $search_keys,
                        'order_by'   => now()->format('Y-m-d H:i:s'),
                        'index_date' => now()->format('Y-m-d H:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                $this->es_index_service->updateEsIndexStatus($esResponse, 'descriptions', 'social_hub');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty descriptions';
        }
    }

    //End Social Media

    // Border Crossing
    public function borderCrossingAzeriIndex()
    {
        // die;
        // DB::beginTransaction();

        // try {
  

            $border_crossing = Azeri::select('id', 'crossing_date', 'birth_date', 'direction', 'menteqe', 'vetendashligi', 'given_name', 'surname', 'patronymic', 'pin', 'document_number')
             
                ->orderBy('id', 'ASC')
                ->take(100)
                ->get();

            $direction = [
                'Cixish' => 'Çıxış',
                'Girish' => 'Giriş'
            ];

            $insertData = [];
            foreach ($border_crossing as $item) {
                $insertData[] = [
                    'fk_id' => $item->id,
                    'type' => 'border_crossing_azeri',
                    'status' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            }

            // DB::table('elastic_index_status')->insert($insertData);

            $params = [];
            foreach ($border_crossing as $item) {
                $search_keys = [
                    'pin' => Str::slug($item->pin),
                    'document_number' => Str::slug($item->document_number),
                    'given_name' => Str::slug($item->given_name),
                   'surname' => Str::slug($item->surname),
                   'patronymic' => Str::slug($item->patronymic),
                   'vetendashligi' => Str::slug($item->vetendashligi),
                   'birth_date' => Str::slug($item->birth_date),
                   'direction' => Str::slug($direction[$item->direction]),
                   'menteqe' => Str::slug($item->menteqe),
                   'crossing_date' => Str::slug($item->crossing_date),
                ];

                $params['body'][] = [
                    'index' => [
                        '_index' => 'border_crossing_azeri_2',
                        '_id' => $item->id,
                    ]
                ];
                $params['body'][] = [
                    "pin"           => $item->pin,
                    "doc_number"    => $item->document_number,
                    "name"          => $item->given_name,
                    "surname"       => $item->surname,
                    "father_name"   => $item->patronymic,
                    "crossing_date" => $item->crossing_date,
                    "birthdate"     => $item->birth_date,
                    "direction"     => $direction[$item->direction],
                    "point"         => $item->menteqe,
                    "citizenship"   => $item->vetendashligi,
                    "image"         => $item->id . '.jpg',
                    'search_keys'   => $search_keys,
                    'created_at'    => now(),
                    'updated_at'    => now(),
                ];
            }

            $this->es_query->bulk($params);

            // DB::table('elastic_index_status')
            //     ->where('type', 'border_crossing_azeri')
            //     ->whereIn('fk_id', $border_crossing->pluck('id'))
            //     ->update(['status' => 2, 'updated_at' => now()]);

            // DB::commit();

            return 'ok';
        // } catch (\Exception $e) {
        //     DB::rollback();

        //     DB::table('elastic_index_status')
        //         ->where('type', 'border_crossing_azeri')
        //         ->whereIn('fk_id', $border_crossing->pluck('id'))
        //         ->update([
        //             'status' => 0,
        //             'error_message' => $e->getMessage(),
        //             'updated_at' => now()
        //         ]);

        //     return 'Error: ' . $e->getMessage();
        // }
    }

    public function borderCrossingEcnebiIndex()
    {
        // die;
        // DB::beginTransaction();

        // try {
        //     $not_exist_ids = DB::table('elastic_index_status')
        //         ->where('type', 'border_crossing_ecnebi')
        //         ->where('status', '!=', 0)
        //         ->pluck('fk_id')
        //         ->toArray();

            $border_crossing = Ecnebi::select('*')
                // ->whereNotIn('id', $not_exist_ids)
                ->orderBy('id', 'ASC')
                ->take(100)
                ->get();

            $direction = [
                'Cixish' => 'Çıxış',
                'Girish' => 'Giriş'
            ];

            $insertData = [];
            foreach ($border_crossing as $item) {
                $insertData[] = [
                    'fk_id' => $item->id,
                    'type' => 'border_crossing_ecnebi',
                    'status' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            }

            // DB::table('elastic_index_status')->insert($insertData);

            $params = [];
            foreach ($border_crossing as $item) {

                $search_keys = [
                    'document_number' => Str::slug($item->document_number),
                    'given_name' => Str::slug($item->given_name),
                   'surname' => Str::slug($item->surname),
                   'patronymic' => Str::slug($item->patronymic),
                   'vetendashligi' => Str::slug($item->vetendashligi),
                   'birth_date' => Str::slug($item->birth_date),
                   'direction' => Str::slug($direction[$item->direction]),
                   'menteqe' => Str::slug($item->menteqe),
                   'crossing_date' => Str::slug($item->crossing_date),
                ];
                $params['body'][] = [
                    'index' => [
                        '_index' => 'border_crossing_ecnebi_2',
                        '_id' => $item->id,
                    ]
                ];
                $params['body'][] = [
                    "pin"           => $item->pin,
                    "doc_number"    => $item->document_number,
                    "name"          => $item->given_name,
                    "surname"       => $item->surname,
                    "crossing_date" => $item->crossing_date,
                    "birthdate"     => $item->birth_date,
                    "direction"     => $direction[$item->direction],
                    "point"         => $item->menteqe,
                    "citizenship"   => $item->vetendashligi,
                    "image"         => $item->id . '.jpg',
                    'search_keys'   => $search_keys,
                    'created_at'    => now(),
                ];
            }

            $this->es_query->bulk($params);

        //     DB::table('elastic_index_status')
        //         ->where('type', 'border_crossing_ecnebi')
        //         ->whereIn('fk_id', $border_crossing->pluck('id'))
        //         ->update(['status' => 2, 'updated_at' => now()]);

        //     DB::commit();

            return 'ok';
        // } catch (\Exception $e) {
        //     DB::rollback();

        //     DB::table('elastic_index_status')
        //         ->where('type', 'border_crossing_ecnebi')
        //         ->whereIn('fk_id', $border_crossing->pluck('id'))
        //         ->update([
        //             'status' => 0,
        //             'error_message' => $e->getMessage(),
        //             'updated_at' => now()
        //         ]);

        //     return 'Error: ' . $e->getMessage();
        // }
    }

    // End Border Crossing
    public function binaAz()
    {
        die;
        $data = DB::table('binaaz_data')
            ->select('*')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw('1'))
                    ->from('elastic_index_status')
                    ->whereRaw('elastic_index_status.fk_id = binaaz_data.id')
                    ->where('elastic_index_status.type', '=', 'binaaz_data')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('binaaz_data.id', 'ASC')
            ->take(value: 5000)
            ->get();

        if (count($data) > 0) {
            DB::beginTransaction();

            try {
                $this->es_index_service->insertIndexStatus($data, 'binaaz_data');

                $params = [];
                foreach ($data as $item) {
                    $phones = explode(',', $item->mobile_number);
                    $phones_str = '';
                    foreach ($phones as $p) {
                        $p = Str::slug($p);
                        $p = substr($p, -9);
                        $phones_str .= ' 994' . $p . ' 0' . $p;
                    }
                    $item->elan_no =  preg_replace('/\D/', '', $item->elan_no);
                    isset($p) ? $item->mobile_number = '0' . $p : '';
                    $item->create_date    = Carbon::parse($item->create_date)->format('Y-m-d H:i:s') ?? '';
                    $item->last_update_date    = Carbon::parse($item->last_update_date)->format('Y-m-d H:i:s') ?? '';
                    $item->expire_date    = Carbon::parse($item->expire_date)->format('Y-m-d H:i:s') ?? '';

                    $item->images = json_decode($item->images);

                    $search_keys = [
                        'elan_no'        => Str::slug($item->elan_no),
                        'phone'       => Str::slug($phones_str),
                        'seller_name'        => Str::slug($item->seller_name),
                        'seller_email'        => Str::slug($item->seller_email),
                        'address'        => Str::slug($item->address),
                        'elan_desc'        => Str::slug($item->elan_desc),
                        'region'        => Str::slug($item->region),
                        'ip'        => Str::slug($item->ip),
                        'create_date'    => Carbon::parse($item->create_date)->format('Y-m-d\TH:i:s') ?? '',
                        'last_update_date'    => Carbon::parse($item->last_update_date)->format('Y-m-d\TH:i:s') ?? '',
                        'expire_date'    => Carbon::parse($item->expire_date)->format('Y-m-d\TH:i:s') ?? '',
                    ];



                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['bina_az'],
                            '_id' => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'data' => $item,
                        'search_keys' =>  $search_keys,
                        'order_by'    => Carbon::parse($item->last_update_date)->format('Y-m-d\TH:i:s'),
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                // print_r($params);die;

                $esResponse = $this->es_query->bulk($params);
                $this->es_index_service->updateEsIndexStatus($esResponse, 'binaaz_data');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty binaaz_data';
        }
    }

    public function tapAz()
    {
        die;
        $data = DB::table('tapaz_data')
            ->select('*')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw('1'))
                    ->from('elastic_index_status')
                    ->whereRaw('elastic_index_status.fk_id = tapaz_data.id')
                    ->where('elastic_index_status.type', '=', 'tapaz_data')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('tapaz_data.id', 'ASC')
            ->take(5000)
            ->get();

        if (count($data) > 0) {
            DB::beginTransaction();

            try {

                $this->es_index_service->insertIndexStatus($data, 'tapaz_data');

                $params = [];
                foreach ($data as $item) {

                    $phones = explode(',', $item->mobile_number);
                    $phones_str = '';
                    foreach ($phones as $p) {
                        if (strlen($p) >= 9) {
                            $p = Str::slug($p);
                            $p = substr($p, -9);
                            $phones_str .= ' 994' . $p . ' 0' . $p;
                        }
                    }

                    $item->elan_attributes = (array) json_decode($item->elan_attributes);
                    $item->image_url = json_decode($item->image_url);

                    $item->elan_no =  preg_replace('/\D/', '', $item->elan_no);
                    isset($p) ? $item->mobile_number = '0' . $p : '';
                    $item->created_date    = Carbon::parse(convertToEnglishDate($item->created_date))->format('Y-m-d H:i:s') ?? '';
                    $item->last_update_date    = Carbon::parse($item->last_update_date)->format('Y-m-d H:i:s') ?? '';
                    $item->expire_date    = Carbon::parse($item->expire_date)->format('Y-m-d H:i:s') ?? '';

                    $attributes = '';
                    foreach ($item->elan_attributes as $attr) {
                        $attributes = $attributes . ' ' . $attr;
                    }


                    $search_keys = [
                        'phone'        => Str::slug($phones_str),
                        'seller_name'  => Str::slug($item->seller_name),
                        'seller_email' => Str::slug($item->seller_email),
                        'elan_header'       => Str::slug($item->elan_header),
                        'elan_desc'       => Str::slug($item->elan_desc),
                        'region'       => Str::slug($item->region),
                        'ip'           => Str::slug($item->ip),
                        'category' => Str::slug($item->category),
                        'attributes' => Str::slug($attributes),
                        'created_date'    => Carbon::parse(convertToEnglishDate($item->created_date))->format('Y-m-d\TH:i:s') ?? '',
                        'last_update_date'    => Carbon::parse($item->last_update_date)->format('Y-m-d\TH:i:s') ?? '',
                        'expire_date'    => Carbon::parse($item->expire_date)->format('Y-m-d\TH:i:s') ?? '',

                    ];


                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['tap_az'],
                            '_id'    => Str::slug($item->id),
                        ]
                    ];
                    $params['body'][] = [
                        "data" =>  $item,
                        'search_keys' => $search_keys,
                        'order_by'    => Carbon::parse($item->created_date)->format('Y-m-d\TH:i:s'),
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                $this->es_index_service->updateEsIndexStatus($esResponse, 'tapaz_data');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty tapaz_data';
        }
    }

    public function turboAz()
    {
        die;
        // print_r('ok');die;
        $data = DB::table('turboaz_data')
            ->select('*')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw('1'))
                    ->from('elastic_index_status')
                    ->whereRaw("turboaz_data.id = elastic_index_status.fk_id")
                    ->where('elastic_index_status.type', '=', 'turboaz_data')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('turboaz_data.id', 'ASC')
            ->take(5000)
            ->get();

        // print_r($data);die;
        if (!empty($data)) {
            DB::beginTransaction();

            try {
                $this->es_index_service->insertIndexStatus($data, 'turboaz_data');

                $params = [];
                foreach ($data as $item) {
                    $phones = explode(',', $item->seller_phone_number);
                    $phones_str = '';
                    foreach ($phones as $p) {
                        if (strlen($p) >= 9) {
                            $p = Str::slug($p);
                            $p = str_replace('-', '', $p);
                            $p = substr($p, -9);
                            $phones_str .= ' 994' . $p . ' 0' . $p;
                        }
                    }

                    $item->images = json_decode($item->images);


                    $item->elan_no =  preg_replace('/\D/', '', $item->elan_no);
                    isset($p) ? $item->mobile_number = '0' . $p : '';
                    $item->create_date    = Carbon::parse(convertToEnglishDate($item->create_date))->format('Y-m-d H:i:s') ?? '';
                    $item->last_update_date    = Carbon::parse($item->last_update_date)->format('Y-m-d H:i:s') ?? '';
                    $item->expire_date    = Carbon::parse($item->expire_date)->format('Y-m-d H:i:s') ?? '';




                    $search_keys = [
                        'elan_no'        => Str::slug($item->elan_no),
                        'phone'        => Str::slug($phones_str),
                        'seller_ip'      => Str::slug($item->seller_ip),
                        'seller_name'  => Str::slug($item->seller_name),
                        'seller_email' => Str::slug($item->seller_email),
                        'region' => Str::slug($item->region),
                        'marka' => Str::slug($item->marka),
                        'ban_type' => Str::slug($item->ban_type),
                        'colour' => Str::slug($item->colour),
                        'description' => Str::slug($item->description),
                        'create_date'    => Carbon::parse(convertToEnglishDate($item->create_date))->format('Y-m-d\TH:i:s') ?? '',
                        'last_update_date'    => Carbon::parse(convertToEnglishDate($item->last_update_date))->format('Y-m-d\TH:i:s') ?? '',
                        'expire_date'    => Carbon::parse(convertToEnglishDate($item->expire_date))->format('Y-m-d\TH:i:s') ?? '',
                    ];


                    $params['body'][] = [
                        'index' => [
                            '_index' => $this->index_names['turbo_az'],
                            '_id'    => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'data' => $item,
                        'search_keys' => $search_keys,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                $this->es_index_service->updateEsIndexStatus($esResponse, 'turboaz_data');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty turboaz_data';
        }
    }

    public function foodOrders()
    {
        $data = DB::table('food_orders')
            // ->whereNotExists(function ($query) {
            //     $query->select(DB::raw('1'))
            //         ->from('elastic_index_status')
            //         ->whereRaw('CAST(food_orders.id AS VARCHAR) = elastic_index_status.fk_id')
            //         ->where('elastic_index_status.type', '=', 'food_orders')
            //         ->where('elastic_index_status.status', '!=', 0);
            // })
            ->orderBy('food_orders.id', 'ASC')
            ->take(1000)
            ->get();

        if (!empty($data)) {
            DB::beginTransaction();

            try {
                $this->es_index_service->insertIndexStatus($data, 'food_orders');

                $params = [];
                foreach ($data as $item) {
                    $item->data = json_decode($item->data);

                    $phone = substr($item->phone, -9);
                    $phones_str = '994' . $phone . ' 0' . $phone;


              
                    $search_keys = [
                        'phone'        => Str::slug($phones_str),
                        'name'     => Str::slug($item->name),
                        'email'    => Str::slug($item->email),
                        'address'  => Str::slug($item->address),
                        // 'birthday' => $item->data->birthday,
                    ];

                    $params['body'][] = [
                        'index' => [
                            '_index' => 'food_orders_2',
                            '_id'    => $item->id,
                        ]
                    ];
                    $params['body'][] = [
                        'data' => $item,
                        'search_keys' => $search_keys,
                        'index_date' => now()->format('Y-m-d\TH:i:s'),
                    ];
                }

                $esResponse = $this->es_query->bulk($params);
                // $this->es_index_service->updateEsIndexStatus($esResponse, 'binaaz_data');

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty foodOrders';
        }
    }

    public function voens()
    {
        // die;
        // DB::beginTransaction();

        // try {
            // Fetch data from voens table
            $data = DB::table('voens')
                // ->whereNotExists(function ($query) {
                //     $query->select(DB::raw('1'))
                //         ->from('elastic_index_status')
                //         ->whereRaw('CAST(voens.id AS VARCHAR) = elastic_index_status.fk_id')
                //         ->where('elastic_index_status.type', '=', 'voens')
                //         ->where('elastic_index_status.status', '!=', 0);
                // })
                ->orderBy('voens.id', 'ASC')
                ->take(200)
                ->get();

            // Insert index status
            // $this->es_index_service->insertIndexStatus($data, 'voens');

            $params = [];
            foreach ($data as $item) {

                $search_keys = [
                    "code" => Str::slug($item->code),
                    "name" => Str::slug($item->name),
                ];


                $params['body'][] = [
                    'index' => [
                        '_index' => $this->index_names['voens'],
                        '_id'    => $item->id,
                    ]
                ];
                $params['body'][] = [
                    'channel' => $item->code ?? '',
                    'name' => $item->name ?? '',
                    'search_keys' => $search_keys,
                    'index_date' => now()->format('Y-m-d\TH:i:s'),
                ];
            }

            // Bulk insert data
            $this->es_index_service->bulkInsert($data, $params, 'voens');

        //     DB::commit();

            return 'ok';
        // } catch (RequestException | LaravelRequestException | Exception $e) {
        //     DB::rollback();
        //     return 'Error: ' . $e->getMessage();
        // }
    }

    public function mobContact()
    {
        die;
        $data = DB::table('mobcontact_4m')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw('1'))
                    ->from('elastic_index_status')
                    ->whereRaw('CAST(mobcontact_4m.id AS VARCHAR) = elastic_index_status.fk_id')
                    ->where('elastic_index_status.type', '=', 'mobcontact_4m')
                    ->where('elastic_index_status.status', '!=', 0);
            })
            ->orderBy('mobcontact_4m.id', 'ASC')
            ->take(100)
            ->get();

        // print_r($data);die;

        if (!empty($data)) {
            DB::beginTransaction();
            try {

                $this->es_index_service->insertIndexStatus($data, 'mobcontact_4m');

                $data_beein_contact = [];
                foreach ($data as $item) {
                    $bc_data = [
                        'source'     => 'mobcontact',
                        'phone'      => $item->number,
                        'name'       => $item->name,
                        'email'      => null,
                        'pin'        => null,
                        'birthday'   => null,
                        'city'       => null,
                        'address'    => null,
                        'note'       => null,
                    ];

                    $data_beein_contact[] = $this->es_index_service->dataBeeinContact($bc_data);
                    $exp = explode(';', $item->tags);
                    $tags = array_filter($exp, function ($item) {
                        return trim($item) !== '';
                    });

                    // print_r($tags);die;

                    foreach ($tags as $t) {
                        $bc_data = [
                            'source'     => 'mobcontact',
                            'phone'      => $item->number,
                            'name'       => $t,
                            'email'      => null,
                            'pin'        => null,
                            'birthday'   => null,
                            'city'       => null,
                            'address'    => null,
                            'note'       => null,
                        ];
                        $data_beein_contact[] = $this->es_index_service->dataBeeinContact($bc_data);
                    }
                }

                // print_r($data_beein_contact);die;
                $this->es_index_service->bulkInsertBeeinContact($data_beein_contact);


                DB::table('elastic_index_status')
                    ->where('type', 'mobcontact_4m')
                    ->whereIn('fk_id', $data->pluck('id'))
                    ->update(['status' => 2, 'updated_at' => now()]);

                DB::commit();
                return 'ok';
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();


                return $e->getMessage();
            }
        } else {

            return 'empty mbcontact';
        }
    }

    public function statisticInfo()
    {
        die;

        // $data = DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->get();
        // return $data;
        // $data = DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'posts')->where('status',0)->delete();
        // $data = DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'posts')->where('fk_id','96423')->get();


        // $data = DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'posts')->where('status', 2)->take(100)->orderBy('fk_id', 'DESC')->get();
        // $data =  DB::table('elastic_index_status')->where('type','azerisiq_baki')->take(1000)->orderBy('id','DESC')->get();

        // print_r($data);die;

        return [
            // 'people' => [
            //     'db' => DB::table('people')->count(),
            //     'db_status_0' => DB::table('people')->where('elastic_index', 0)->count(),
            //     'db_status_1' => DB::table('people')->where('elastic_index', 1)->count(),
            //     'db_status_2' => DB::table('people')->where('elastic_index', 2)->count(),
            //     'daily_count' =>  DB::table('people')->where('elastic_index', 2)->whereBetween('updated_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->count(),
            //     'elastic' => $this->es_query->count('people')['count'],
            // ],
            // 'beein_contact' => [
            //     'db' => DB::table('beein_contact')->count(),
            //     'db_c' => DB::table('beein_contact')->distinct('phone')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'beein_contact')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'beein_contact')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'beein_contact')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->where('type', 'beein_contact')->where('status', 2)->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->count(),
            //     'elastic' => $this->es_query->count('beein_contact')['count'],
            // ],

            // 'azerisiq_baki' => [
            //     'db' => DB::table('azerisiq_baki')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'azerisiq_baki')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'azerisiq_baki')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'azerisiq_baki')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->where('type', 'azerisiq_baki')->where('status', 2)->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->count(),
            //     'elastic' => $this->es_query->count('azerisiq_2')['count'],
            // ],

            // 'azerisiq_region' => [
            //     'db' => DB::table('azerisiq_region')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'azerisiq_region')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'azerisiq_region')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'azerisiq_region')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->where('type', 'azerisiq_region')->where('status', 2)->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->count(),
            //     'elastic' => $this->es_query->count('azerisiq_2')['count'],
            // ],


            'sm_accounts' => [
                'db' => DB::connection('pgsql_social')->table('social_hub.accounts')->where('checked', 1)->where('platform_id', 1)->count(),
                'db_status_0' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'accounts')->where('status', 0)->count(),
                'db_status_1' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'accounts')->where('status', 1)->count(),
                'db_status_2' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'accounts')->where('status', 2)->count(),
                'daily_count' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'accounts')->where('status', 2)->count(),
                'elastic' => $this->es_query->count('sm_accounts_2')['count'],
            ],
            'sm_posts' => [
                'db' => DB::connection('pgsql_social')->table('social_hub.posts')->count(),
                'db_status_0' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'posts')->where('status', 0)->count(),
                'db_status_1' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'posts')->where('status', 1)->count(),
                'db_status_2' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'posts')->where('status', 2)->count(),
                'daily_count' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'posts')->where('status', 2)->count(),
                'elastic' => $this->es_query->count('sm_posts_2')['count'],
            ],
            'sm_comments' => [
                'db' => DB::connection('pgsql_social')->table('social_hub.comments')->count(),
                'db_status_0' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'comments')->where('status', 0)->count(),
                'db_status_1' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'comments')->where('status', 1)->count(),
                'db_status_2' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'comments')->where('status', 2)->count(),
                'daily_count' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'comments')->where('status', 2)->count(),
                'elastic' => $this->es_query->count('sm_comments_2')['count'],
            ],
            'sm_descriptions' => [
                'db' => DB::connection('pgsql_social')->table('social_hub.description')->distinct('social_hub.description.id')->whereNotNull('media_id')->count(),
                'db_status_0' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'descriptions')->where('status', 0)->count(),
                'db_status_1' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'descriptions')->where('status', 1)->count(),
                'db_status_2' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->where('type', 'descriptions')->where('status', 2)->count(),
                'daily_count' => DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'descriptions')->where('status', 2)->count(),
                'elastic' => $this->es_query->count('sm_descriptions_2')['count'],
            ],
            // 'food_orders' => [
            //     'db' => DB::table('food_orders')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'food_orders')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'food_orders')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'food_orders')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'food_orders')->where('status', 2)->count(),
            //     'elastic' => $this->es_query->count('food_orders')['count'],
            // ],
            'bina_az' => [
                'db' => DB::table('binaaz_data')->count(),
                'db_status_0' => DB::table('elastic_index_status')->where('type', 'binaaz_data')->where('status', 0)->count(),
                'db_status_1' => DB::table('elastic_index_status')->where('type', 'binaaz_data')->where('status', 1)->count(),
                'db_status_2' => DB::table('elastic_index_status')->where('type', 'binaaz_data')->where('status', 2)->count(),
                'daily_count' => DB::table('elastic_index_status')->where('type', 'binaaz_data')->where('status', 2)->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->count(),
                'elastic' => $this->es_query->count('bina_az_2')['count'],
            ],
            'tap_az' => [
                'db' => DB::table('tapaz_data')->count(),
                'db_status_0' => DB::table('elastic_index_status')->where('type', 'tapaz_data')->where('status', 0)->count(),
                'db_status_1' => DB::table('elastic_index_status')->where('type', 'tapaz_data')->where('status', 1)->count(),
                'db_status_2' => DB::table('elastic_index_status')->where('type', 'tapaz_data')->where('status', 2)->count(),
                'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'tapaz_data')->where('status', 2)->count(),
                'elastic' => $this->es_query->count('tap_az_2')['count'],
            ],
            'turbo_az' => [
                'db' => DB::table('turboaz_data')->count(),
                'db_status_0' => DB::table('elastic_index_status')->where('type', 'turboaz_data')->where('status', 0)->count(),
                'db_status_1' => DB::table('elastic_index_status')->where('type', 'turboaz_data')->where('status', 1)->count(),
                'db_status_2' => DB::table('elastic_index_status')->where('type', 'turboaz_data')->where('status', 2)->count(),
                'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'turboaz_data')->where('status', 2)->count(),
                'elastic' => $this->es_query->count('turbo_az_2')['count'],
            ],
            // 'voens' => [
            //     'db' => DB::table('voens')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'voens')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'voens')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'voens')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'voens')->where('status', 2)->count(),
            //     'elastic' => $this->es_query->count('voens')['count'],
            // ],



            // 'azeriqaz' => [
            //     'db' => DB::table('azeriqaz_ehali')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'azeriqaz_ehali')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'azeriqaz_ehali')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'azeriqaz_ehali')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'azeriqaz_ehali')->where('status', 2)->count(),
            //     'elastic' => $this->es_query->count('azeriqaz')['count'],
            // ],
            // 'azersu' => [
            //     'db' => DB::table('azersu_abonent')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'azersu_abonent')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'azersu_abonent')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'azersu_abonent')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'azersu_abonent')->where('status', 2)->count(),
            //     'elastic' => $this->es_query->count('azersu')['count'],
            // ],

            // 'avtovagzal' => [
            //     'db' => DB::table('avtovagzal_tickets')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'avtovagzal_tickets')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'avtovagzal_tickets')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'avtovagzal_tickets')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'avtovagzal_tickets')->where('status', 2)->count(),
            //     'elastic' => $this->es_query->count('avtovagzal')['count'],
            // ],

            // 'mobcontact_to_beein_contact' => [
            //     'mobcontact' => DB::table('mobcontact_4m')->count(),
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'mobcontact_4m')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'mobcontact_4m')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'mobcontact_4m')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'mobcontact_4m')->where('status', 2)->count(),
            //     'beein_contact' => DB::table('beein_contact')->where('source', 'mobcontact')->count(),
            // ],
            // 'missing_people' => [
            //     'missing_people' => DB::table('missing_people')->count(),
            // ],
            // 'bc_azeri' => [
            //     // 'db' => Azeri::count() ?? 0,
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'border_crossing_azeri')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'border_crossing_azeri')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'border_crossing_azeri')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'border_crossing_azeri')->where('status', 2)->count(),
            //     'elastic' => $this->es_query->count('border_crossing_azeri')['count'],
            // ],
            // 'bc_ecnebi' => [
            //     // 'db' => Ecnebi::count() ?? 0,
            //     'db_status_0' => DB::table('elastic_index_status')->where('type', 'border_crossing_ecnebi')->where('status', 0)->count(),
            //     'db_status_1' => DB::table('elastic_index_status')->where('type', 'border_crossing_ecnebi')->where('status', 1)->count(),
            //     'db_status_2' => DB::table('elastic_index_status')->where('type', 'border_crossing_ecnebi')->where('status', 2)->count(),
            //     'daily_count' => DB::table('elastic_index_status')->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])->where('type', 'border_crossing_ecnebi')->where('status', 2)->count(),
            //     'elastic' => $this->es_query->count('border_crossing_ecnebi')['count'],
            // ],

        ];
    }
}
