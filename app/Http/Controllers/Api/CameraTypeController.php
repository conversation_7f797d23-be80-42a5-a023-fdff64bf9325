<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\CameraTypeRequest;
use App\Http\Requests\CameraTypeSearchRequest;
use App\Http\Resources\CameraTypeResource;
use App\Services\CameraTypeService;
use App\Traits\ApiResponsible;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CameraTypeController extends Controller
{
    use ApiResponsible;

    public function __construct(
        protected CameraTypeService $cameraTypeService
    ) {

        $this->middleware('permission:cameras-types:read', ['only' => ['index', 'search', 'show', 'list']]);
        $this->middleware('permission:cameras-types:create', ['only' => ['store']]);
        $this->middleware('permission:cameras-types:update', ['only' => ['update']]);
        $this->middleware('permission:cameras-types:delete', ['only' => ['destroy']]);
    }

    /**
     * @OA\Get  (
     *      path="/cameraTypes/search/list",
     *      operationId="get-camera-types-search",
     *      tags={"Kamera tipləri"},
     *      summary="Kamera tiplərinin axtarılması servisi",
     *      description="Kamera tiplərinin axtarılması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="q",
     *          description="Axtarılacaq kəlimə",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param \App\Http\Requests\CameraTypeSearchRequest $request
     * @return JsonResponse
     */
    public function search(CameraTypeSearchRequest $request): JsonResponse
    {
        $q = $request->input('q');

        $cameraTypes = $this->cameraTypeService->getAllByCondition($q);

        return $this->successResponse(
            Response::HTTP_OK,
            null,
            $cameraTypes
        );
    }

    /**
     * @OA\Get  (
     *      path="/cameraTypes",
     *      operationId="get-camera-types-list",
     *      tags={"Kamera tipləri"},
     *      summary="Kamera tiplərinin əldə olunması servisi",
     *      description="Kamera tiplərinin əldə olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="per_page",
     *          description="Səhifə başına düşən item sayı",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="page",
     *          description="Səhifə",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        return $this->cameraTypeService->paginate(request()->input('per_page') ?? 10);
    }

    /**
     * @OA\Get   (
     *      path="/cameraTypes/{camera_type_id}",
     *      operationId="show-camera-type",
     *      tags={"Kamera tipləri"},
     *      summary="Kamera tipi məlumatları",
     *      description="Kamera tipi məlumatları",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="camera_type_id",
     *          description="Camera type İD",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(int $id): JsonResponse
    {

        return $this->cameraTypeService->show($id);
    }

    /**
     * @OA\Post   (
     *      path="/cameraTypes",
     *      operationId="create-camera-type",
     *      tags={"Kamera tipləri"},
     *      summary="Kamera tipinin əlavə olunması servisi",
     *      description="Kamera tipinin əlavə olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="name",
     *          description="Kamera tipinin adı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="active",
     *          description="Kameranın statusu",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param CameraTypeRequest $request
     * @return Model
     */
    public function store(CameraTypeRequest $request): Model
    {
        return $this->cameraTypeService->save($request->validated());
    }

    /**
     * @OA\Put   (
     *      path="/cameraTypes/{camera_type_id}",
     *      operationId="update-camera-type",
     *      tags={"Kamera tipləri"},
     *      summary="Kamera tipinə düzəliş olunması servisi",
     *      description="Kamera tipinə düzəliş olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="camera_type_id",
     *          description="Camera type İD",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="name",
     *          description="Kamera tipinin adı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="active",
     *          description="Kameranın statusu",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param CameraTypeRequest $request
     * @param int $id
     * @return Model
     */
    public function update(CameraTypeRequest $request, int $id): Model
    {
        return $this->cameraTypeService->update($request->validated(), $id);
    }

    /**
     * @OA\Delete   (
     *      path="/cameraTypes/{camera_type_id}",
     *      operationId="delete-camera-type",
     *      tags={"Kamera tipləri"},
     *      summary="Kamera tipinin silinməsi",
     *      description="Kamera tipinin silinməsi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="camera_type_id",
     *          description="Camera type İD",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        return $this->cameraTypeService->findAndDelete($id);
    }

    public function list(): JsonResponse
    {
        return $this->cameraTypeService->getLists();
    }

    public function getByName(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
            ]);
            $objects = $this->cameraTypeService->getByName($validated['name']);
            if ($objects->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No camera types found with the given name.',
                    'data' => []
                ]);
            }
            return $objects;
        }
        catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving objects.',
                'error' => config('app.debug') ? $e->getMessage() : 'Server error'
            ], 500);
        }
    }
}
