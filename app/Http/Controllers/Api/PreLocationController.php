<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\LocationRequests\FindCarRequest;
use App\Http\Requests\LocationRequests\FindIntervalSimilarityRequest;
use App\Http\Requests\LocationRequests\FindLocationRequest;
use App\Http\Requests\LocationRequests\FindMeetingPlacesRequest;
use App\Http\Requests\LocationRequests\FindPhoneSimilarityRequest;
use App\Http\Requests\LocationRequests\NativeAndStrangersRequest;
use App\Http\Requests\LocationRequests\SearchByAreaRequest;
use App\Services\PreLocationService;
use App\Traits\ApiResponsible;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class PreLocationController extends Controller
{
    use ApiResponsible;
    private PreLocationService $preLocationService;

    public function __construct(PreLocationService $preLocationService){
        $this->preLocationService = $preLocationService;
    }
    public function findLocation(FindLocationRequest $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        $phoneNumber = $request->get('phone_number');
        $startTime = $request->input('start_time');
        $endTime = $request->input('end_time');
        $radius = (float)$request->input('radius') / 1000 ?? 0.1;
        $polygon = $this->preLocationService->buildPolygon($request->input('polygon'));
        $correlationID = (string) Str::uuid();
        $logData = [
            "description" => $phoneNumber . " telefon nömrəsinə görə " . $startTime . " - " . $endTime . " tarixləri arasında kəsişmə axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        try {
            $requestID = $this->preLocationService->findLocation($phoneNumber, $startTime, $endTime, $radius, $polygon, $correlationID,$search_head_name);
            return response()->json($requestID);
        } catch (\Exception|GuzzleException $e) {
            return Response::json(['error' => 'Location servis ilə əlaqə saxlamaq mümkün olmadı', 'message' => $e->getMessage()], 404);
        }
    }
    public function findCar(FindCarRequest $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        $car_number = strtoupper($request->input('car_number'));
        $start = $request->input('start');
        $end = $request->input('end');
        $logData = [
            "description" => $car_number . " maşın nömrəsinə görə " . $start . " - " . $end . " tarixləri arasında oxşarlıq axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $response_count = (int)$request->input('response_count');
        $small_radius = (float)$request->input('small_radius') / 1000;
        $big_radius = (float)$request->input('big_radius') / 1000;
        $delta_time = (int)$request->input('delta_time');
        $polygon = $this->preLocationService->buildPolygon($request->input('polygon'));
        $correlationID = (string) Str::uuid();
        try {
            $requestID = $this->preLocationService->findCar(
                $car_number, $start, $end, $response_count, $small_radius, $big_radius, $delta_time,$correlationID,$polygon,$search_head_name
            );
            return response()->json($requestID);
        } catch (\Exception $e) {
            Log::error('findCar error : ' . $e->getMessage());
            return response()->json(['error' => 'Xəta baş verdi.', 'msg' => $e->getMessage()], 404);
        }
    }
    public function findMeetingPlaces(FindMeetingPlacesRequest $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        $number1 = (int)$request->input('number1');
        $number2 = array_map('intval', (array)$request->input('number2'));
        $start = $request->input('start');
        $end = $request->input('end');
        $radius = (float)$request->input('radius') / 1000;
        $delta_time = (int)$request->input('delta_time');
        $break_time = (int)$request->input('break_time', 800);
        $polygon = $this->preLocationService->buildPolygon($request->input('polygon'));
        $correlationID = (string) Str::uuid();
        $logData = [
            "description" => $number1 . " və " . implode(',', $number2) . " telefon nömrələrinə görə " . $start . " - " . $end . " tarixləri arasında görüş üzrə axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        try {
            $requestID = $this->preLocationService->findMeetingPlaces(
                $number1, $number2, $start, $end, $radius, $delta_time, $break_time,$correlationID,$polygon,$search_head_name
            );
            return response()->json($requestID);
        } catch (\Exception $e) {
            Log::error('findMeetingPlaces error : ' . $e->getMessage());
            return response()->json([
                'error' => 'Xəta baş verdi.',
                'message' => $e->getMessage(),
                'line' => $e->getLine() . $e->getFile(),
            ], 404);
        }
    }
    public function findIntervalSimilarity(FindIntervalSimilarityRequest $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        if ((!$request->has('points') && !$request->has('polygons'))) {
            return response()->json([
                'message' => 'Ya point ya da polygon daxil edilməlidir'
            ], 422);
        }
        $response_count = (int)$request->input('response_count');
        $min_match = (int)$request->input('min_match');
        $points = $request->input('points');
        $polygons = $request->input('polygons');
        $convertedPolygons = [];
        $correlationID = (string) Str::uuid();
        $logData = [
            "description" => " səfər ehtimalı axtarışı etdi .",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        if ($polygons) {
            foreach ($polygons as $polygon) {
                $coordinates = [];
                foreach ($polygon['polygon'] as $point) {
                    $coordinates[] = [(float)$point["lng"], (float)$point["lat"]];
                }
                $convertedPolygons[] = [
                    'start' => $polygon['start'],
                    'end' => $polygon['end'],
                    'pg_geom' => $coordinates,
                    'radius' => (float)$polygon['radius'] / 1000
                ];
            }
        }
        if ($points) {
            foreach ($points as &$point) {
                $point['lat'] = (float)$point['lat'];
                $point['lon'] = (float)$point['lon'];
                $point['radius'] = (float)$point['radius'] / 1000;
            }
            unset($point);
        }
        try {
            $requestID = $this->preLocationService->findIntervalSimilarity($response_count, $min_match, $correlationID,$points, $convertedPolygons,$search_head_name);
            return response()->json($requestID);
        } catch (\Exception $e) {
            Log::error('findIntervalSimilarity error : ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 404);
        }
    }
    public function getSocialCallLocationWithOthersNew(FindPhoneSimilarityRequest $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        $pin = $request->input('pin');

        $messages = [
            'pin.required' => 'Telefon nömrəsi mütləqdir və boş ola bilməz.',
            'pin.regex' => 'Telefon nömrəsi 994XXXXXXXXX formatında olmalıdır.'
        ];

        $request->validate([
            'pin' => 'required|regex:/^(994)[1-9][0-9]{8}$/'
        ], $messages
        );
        ini_set('memory_limit', -1);
        $given = $pin;
        $start_date = $request->input('from', '2024-05-15 09:00:00');
        $end_date = $request->input('to', '2024-05-15 23:00:00');
        $response_count = (int)$request->input('top', 20);
        $small_radius = (float)$request->input('min_radius') / 1000;
        $big_radius = (float)$request->input('max_radius') / 1000;
        if ($small_radius <= 0 || $big_radius <= 0) {
            return response()->json([
                'message' => 'Radius dəyərləri mənfi və ya sıfır ola bilməz.'
            ], 422);
        }
        $delta_time = (int)$request->input('delta_time', 100);
        $break_time = (int)$request->input('break_time', 86400);
        $polygon = $this->preLocationService->buildPolygon($request->input('polygon'));
        $correlationID = (string) Str::uuid();
        $logData = [
            "description" => $pin . " telefon nömrəsinə görə " . $start_date . " - " . $end_date . " tarixləri arasında oxşarlıq axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        try {
            $requestID = $this->preLocationService->findSimilarity((int)$given, $start_date, $end_date, $break_time, $response_count, $small_radius, $big_radius, $delta_time,$correlationID,$polygon,$search_head_name);
            return response()->json($requestID);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while processing your request.',
                'msg' => $e->getMessage() . ' ' . $e->getFile() . ' ' . $e->getLine()
            ], 404);
        }
    }
    public function searchByArea(SearchByAreaRequest $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        $startTime = $request->input('start_time');
        $endTime = $request->input('end_time');
        $polygon = $this->preLocationService->buildPolygon($request->input('polygon'));
        $correlationID = (string) Str::uuid();
        $logData = [
            "description" => $startTime . " - " . $endTime . " tarixləri arasında ərazi üzrə axtarış etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $requestID = $this->preLocationService->searchByArea($startTime,$endTime,$correlationID,$polygon,$search_head_name);
        return response()->json($requestID);
    }
    public function getSocialCallLocationInfo(Request $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        $request->validate([
            'pin' => 'required|regex:/^(994)[1-9][0-9]{8}$/',
            'search_head_name' => 'required|string'
        ], [
            'pin.required' => 'Telefon nömrəsi tələb olunur.',
            'pin.regex' => 'Telefon nömrəsi formatı səhvdir.',
            'search_head_name.required' => 'Axtarışın adı mütləqdir.',
            'search_head_name.string' => 'Axtarışın adı mətn olmalıdır.'
        ]);
        $perPage = $request->input('per_page', 12);
        $page = $request->input('page', 1);
        $pin = array_map('intval', (array)$request->input('pin'));
        $fromDate = $request->input('from');
        $toDate = $request->input('to');
        $logData = [
            "description" => implode(',', $pin) . " telefon nömrəsinə görə " . $fromDate . " - " . $toDate . " tarixləri arasında trayektoriya axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $polygon = $this->preLocationService->buildPolygon($request->input('polygon'));
        $correlationID = (string) Str::uuid();
        $requestID = $this->preLocationService->findPhoneNumberTrajectory($pin,$perPage,$fromDate,$toDate,$correlationID,$polygon,$search_head_name);
        return response()->json($requestID);
    }
    public function nativeAndStrangers(NativeAndStrangersRequest $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        if (($request->has('point') && $request->has('polygon')) || (!$request->has('point') && !$request->has('polygon'))) {
            return response()->json([
                'message' => 'Ya point ya da polygon daxil edilməlidir, ikisi də birlikdə və ya heç biri daxil edilməməlidir.'
            ], 422);
        }
        $polygon = $this->preLocationService->buildPolygon($request->input('polygon'));
        $correlationID = (string) Str::uuid();
        $radius = (float)$request->input('radius') / 1000;
        $type = strtoupper($request->input('type'));
        $frequency_count = (int)$request->input('frequency_count');
        $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $request->input('start_time'));
        $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $request->input('end_time'));
        $start = $request->input('start_time');
        $end = $request->input('end_time');
        $daysDifference = $startTime->diffInDays($endTime);
        if ($frequency_count > $daysDifference) {
            return response()->json([
                'errors' => [
                    'frequency_count' => ['Tezlik sayı başlanğıc vaxt ilə son vaxt arasındakı gün sayından az olmalıdır.'],
                ]
            ], 422);
        };
        $logData = [
            "description" => $startTime . " - " . $endTime . " tarixləri arasında özgələr və yerlilər üzrə axtarış etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $requestID = $this->preLocationService->findNativeAndStrangers($start,$end,$type,$frequency_count,$radius,$correlationID,$polygon,$search_head_name);
        return response()->json($requestID);
    }
    public function getVehicleEntered(Request $request): JsonResponse
    {
        $search_head_name = $request->input('search_head_name');
        $carNumber = $request->input('carNumber');
        $start = $request->input('from');
        $end = $request->input('to');
        $request->validate([
            'search_head_name' => 'required|string'
        ], [
            'search_head_name.required' => 'Axtarışın adı mütləqdir.',
            'search_head_name.string' => 'Axtarışın adı mətn olmalıdır.'
        ]);
        $polygon = $this->preLocationService->buildPolygon($request->input('polygon'));
        $correlationID = (string) Str::uuid();
        $logData = [
            "description" => $request->input('carNumber') . " maşın nömrəsinə görə " . $request->input('from') . " - " . $request->input('to') . " tarixləri arasında  trayektoriya axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $requestID = $this->preLocationService->getVehicleEntered($carNumber,$start,$end,$correlationID,$polygon,$search_head_name);
        return response()->json($requestID);
    }
    public function checkStatus(Request $request): JsonResponse
    {
        $searchID = $request->input('searchID');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 1000);
        $response = $this->preLocationService->checkStatus($searchID, $page, $perPage);
        return response()->json($response);
    }

    public function getLocations(Request $request): JsonResponse
    {
        $type = $request->input('type');
        $value = $request->input('value');
        $lat = $request->input('lat');
        $lon = $request->input('lon');
        $columns = $request->input('columns');
        $parquet = $request->input('file');
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 1000);
        $locations = $this->preLocationService->getLocations($parquet, $lat, $lon, $columns, $page, $perPage);
        return response()->json($locations);
    }
    public function filterResults(Request $request): JsonResponse{
        $filterModel = $request->except(['columns','file','lat','lon','page','per_page']);
        $parquet = $request->input('file');
        $columns = $request->input('columns');
        $lat = $request->input('lat');
        $lon = $request->input('lon');
        $page = (int)$request->input('page', 1);
        $perPage = (int)$request->input('per_page', 1000);
        $modifiedFilterModel = [];
        foreach ($filterModel as $key => $value) {
            $modifiedFilterModel["response.$key"] = $value;
        }
        $locations = $this->preLocationService->filterResults($parquet,$modifiedFilterModel,$columns,$lat,$lon,$page,$perPage);
        return response()->json($locations);
    }
}
