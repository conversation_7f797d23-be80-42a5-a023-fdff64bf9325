<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\PermissionUpdateRequest;
use App\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\PermissionRequest;
use App\Http\Resources\PermissionResource;
use Spatie\Permission\PermissionRegistrar;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class PermissionController extends Controller
{

    public function __construct()
    {
        $this->middleware('permission:permissions', ['only' => ['index']]);
        $this->middleware('permission:permissions:read', ['only' => ['show']]);
        $this->middleware('permission:permissions:create', ['only' => ['store']]);
        $this->middleware('permission:permissions:update', ['only' => ['update']]);
        $this->middleware('permission:permissions:delete', ['only' => ['destroy']]);
    }


    public function index(Request $request): AnonymousResourceCollection
    {
        /** @var $permissions Permission */
        $permissions = Permission::query()->when($request->filled('search'), function ($query) use ($request) {
            $query->search('name', $request->get('search'));
            $query->orSearch('translation_name', $request->get('search'));
        })->orderBy('id', 'DESC')->paginate($request->get('per_page') ?? 10);

        return PermissionResource::collection($permissions);
    }

    public function store(PermissionRequest $request): PermissionResource
    {
        $validated = $request->validated();

        $permission = Permission::create($validated);

        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        return new PermissionResource($permission);
    }

    public function show(int $id): PermissionResource
    {
        return new PermissionResource(Permission::findOrFail($id));
    }

    public function update(PermissionUpdateRequest $request, int $id): PermissionResource
    {
        $validated = $request->validated();

        $permission = Permission::findOrFail($id);

        $permission->update($validated);

        $permission->fresh();

        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        return new PermissionResource($permission);
    }

    public function destroy(int $id): JsonResponse
    {
        $permission = Permission::findOrFail($id);
        $permission->delete();
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        return response()->json([], Response::HTTP_NO_CONTENT);
    }
}
