<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\UserMobileDeviceRequest;
use App\Models\UserMobileDevice;
use Illuminate\Http\Request;

class UserMobileDeviceController extends Controller
{

    public function createDevice(UserMobileDeviceRequest $request)
    {
        UserMobileDevice::query()->updateOrCreate([
                'user_id' => $request->user_id,
                'token' => strip_tags($request->token),
            ] + [
                'device_type' => $request->device_type ?? "android",
            ]
        );

        return true;
    }
}
