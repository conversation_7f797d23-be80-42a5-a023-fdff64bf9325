<?php

namespace App\Http\Controllers\Api;

use App\DTO\MovementRouteDTO;
use App\Exports\Export;
use App\Http\Controllers\Controller;
use App\Http\Requests\LocationRequests\FindCarRequest;
use App\Http\Requests\LocationRequests\FindIntervalSimilarityRequest;
use App\Http\Requests\LocationRequests\FindLocationRequest;
use App\Http\Requests\LocationRequests\FindMeetingPlacesRequest;
use App\Http\Requests\LocationRequests\FindPhoneSimilarityRequest;
use App\Http\Requests\LocationRequests\NativeAndStrangersRequest;
use App\Http\Requests\LocationRequests\SearchByAreaRequest;
use App\Jobs\SaveAdvanceSearchLogsJob;
use App\Models\User;
use App\Services\LocationService;
use App\Services\UserInstance;
use App\Traits\ApiResponsible;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;


class LocationController extends Controller
{
    use ApiResponsible;

    private LocationService $locationService;

    public function __construct(LocationService $locationService)
    {
//        $this->middleware('permission:polygons')->except([
//            'getVehicleEntered',
//            'getAzParking',
//        ]);

        $this->middleware('permission:profil-search:cdr-location-history', ['only' => ['getSocialCallLocationInfo']]);

        $this->middleware('permission:profil-search', ['only' => ['getVehicleEntered']]);

        $this->middleware('permission:car-trajectory', ['only' => ['getVehicleEntered','getAzParking']]);

        $this->locationService = $locationService;
    }

    // Telefon nomresine gore kesishme
    /**
     * @deprecated
     */
    public function findLocation(FindLocationRequest $request): JsonResponse
    {
        $phoneNumber = $request->get('phone_number');
        $startTime = $request->input('start_time');
        $endTime = $request->input('end_time');
        $radius = (float)$request->input('radius') / 1000;
        $polygon = $request->input('polygon');
//        request()->merge([
//            'work_name' => $request->input('work_name', $request->input('search_head_name'))
//        ]);
        $logData = [
            "description" => $phoneNumber . " telefon nömrəsinə görə " . $startTime . " - " . $endTime . " tarixləri arasında kəsişmə axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        try {
            $response = $this->locationService->findLocation($phoneNumber, $startTime, $endTime, $radius);
            $decodedJSON = json_decode($response->getBody(), true);
            if (empty($decodedJSON['data'])) {
                $response = [
                    'data' => [
                        'data' => [
                            'data' => $decodedJSON['data']
                        ]
                    ]
                ];
                return response()->json($response);
            }
            $coordinates = $decodedJSON['data'];
            if ($polygon) {
                foreach ($polygon as $point) {
                    $convertedPolygon[] = [(float)$point['lat'], (float)$point['lng']];
                }
                $points = [];
                foreach ($coordinates as $coordinateGroup) {
                    foreach ($coordinateGroup['intersections'] as $intersection) {
                        $points[] = [
                            $intersection['lat'],
                            $intersection['lon']
                        ];
                    }
                }
                $results = (new \App\Services\PointInPolygon)->arePointsInsidePolygon($points, $convertedPolygon);
                $filteredCoordinates = [];
                $resultIndex = 0;
                foreach ($coordinates as $coordinateGroup) {
                    $filteredIntersections = [];
                    foreach ($coordinateGroup['intersections'] as $intersection) {
                        if ($results[$resultIndex]) {
                            $filteredIntersections[] = $intersection;
                        }
                        $resultIndex++;
                    }
                    if (!empty($filteredIntersections)) {
                        $coordinateGroup['intersections'] = $filteredIntersections;
                        $filteredCoordinates[] = $coordinateGroup;
                    }
                }
                $filteredCoordinates = array_map(function ($item) use ($request) {
                    $item['radius'] = (int)$request->input('radius');
                    return $item;
                }, $filteredCoordinates);
                $result = [
                    'data' => $filteredCoordinates
                ];
            } else {
                $coordinates = array_map(function ($item) use ($request) {
                    $item['radius'] = (int)$request->input('radius');
                    return $item;
                }, $coordinates);
                $result = [
                    'data' => $coordinates
                ];
            }
            if ((!$request->get('page') || $request->get('page') == 1) && $request->get('search_head_name') !== null && $request->get('search_head_name') && !$request->filled('from_Job')) {
                $queue = [
                    'name' => $request->get('search_head_name') ?? 'manually',
                    'search_params' => $request->all(),
                    'pins' => [],
                    'count' => count($coordinates),
                    'user_id' => auth('api')->user()->id,
                    'search_type' => 'find_location_by_phone_number',
                    'search_tab' => [[
                        'value' => 'find_location_by_phone_number',
                        'name' => 'tab',
                        'label' => 'Kəsişməni axtar (Telefon nömrəsi ilə)',
                    ]],
                    'search_text' => 'find_location_by_phone_number',
                ];
//                Log::emergency('SaveFindLocationSearchLogsAfterJob: ' . json_encode($queue));
                SaveAdvanceSearchLogsJob::dispatch($queue)->onQueue('find_location_by_phone_number')->onConnection('database')->delay(now()->addSeconds(20));
            }
            return response()->json($result);
        } catch (\Exception|GuzzleException $e) {
            return Response::json(['error' => 'Location servis ilə əlaqə saxlamaq mümkün olmadı', 'm' => $e->getMessage()], 404);
        }
    }

    /**
     * @deprecated
     */
    public function findCar(FindCarRequest $request): JsonResponse
    {
        $car_number = $request->input('car_number');
        $start = $request->input('start');
        $end = $request->input('end');
//        request()->merge([
//            'work_name' => $request->input('work_name', $request->input('search_head_name'))
//        ]);
        $logData = [
            "description" => $car_number . " maşın nömrəsinə görə " . $start . " - " . $end . " tarixləri arasında oxşarlıq axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $response_count = (int)$request->input('response_count');
        $small_radius = (float)$request->input('small_radius') / 1000;
        $big_radius = (float)$request->input('big_radius') / 1000;
        $delta_time = (int)$request->input('delta_time');
        $match_percentage = (float)$request->input('match_percentage', 1.0);
        $perPage = (int)$request->input('per_page', 10);
        $page = (int)$request->input('page', 1);
        $offset = ($page - 1) * $perPage;
        $polygon = $request->input('polygon');
        try {
            $data = $this->locationService->findCar(
                $car_number, $start, $end, $response_count, $small_radius, $big_radius, $delta_time, $match_percentage, $perPage, $offset
            );
            if (array_key_exists('code', $data) && $data['code'] == 404) {
                return response()->json($data);
            }
            if (array_key_exists('message', $data)) {
                return response()->json($data);
            }
            if (empty($data['dataMap'])) {
                $response = [
                    'data' => [
                        'data' => $data
                    ]
                ];
                return response()->json($response);
            }
            if ($polygon) {
                $polygon = array_map(function ($point) {
                    return [(float)$point["lat"], (float)$point["lng"]];
                }, $polygon);
                $points = [];
                foreach ($data['dataMap'] as $coordinateGroup) {
                    foreach ($coordinateGroup['points'] as $intersection) {
                        $points[] = [
                            $intersection['lat'],
                            $intersection['lon']
                        ];
                    }
                }
                $results = (new \App\Services\PointInPolygon)->arePointsInsidePolygon($points, $polygon);
                $filteredDataMap = array_filter($data['dataMap'], function($mapItem) use ($results) {
                    foreach ($mapItem['points'] as $index => $point) {
                        if (!$results[$index]) {
                            return false;
                        }
                    }
                    return true;
                });
                $filteredDataResult = [];
                $pointMap = [];
                foreach ($filteredDataMap as $dataMapItem) {
                    foreach ($dataMapItem['points'] as $dataMapPoint) {
                        $key = $dataMapPoint['lat'] . ',' . $dataMapPoint['lon'];
                        $pointMap[$key] = true;
                    }
                }

                foreach ($data['dataResult'] as $resultItem) {
                    $key = $resultItem['lat'] . ',' . $resultItem['lon'];
                    if (isset($pointMap[$key])) {
                        $filteredDataResult[] = $resultItem;
                    }
                }

                $data['dataMap'] = $filteredDataMap;
                $data['dataResult'] = $filteredDataResult;
            }
            usort($data['dataMap'], function ($a, $b) {
                return count($b['points']) - count($a['points']);
            });
            $totalCount = count($data['dataResult']);
            $lastPage = (int)ceil($totalCount / $perPage);
            $url = url('/api/v1/find-car');
            $response = [
                "status" => "Success",
                "data" => [
                    "current_page" => $page,
                    "data" => $data,
                    "first_page_url" => $url . "?page=1",
                    "from" => $offset + 1,
                    "last_page" => $lastPage,
                    "last_page_url" => $url . "?page=" . $lastPage,
                    "links" => generatePaginationLinks($page, $lastPage, $url),
                    "next_page_url" => $page < $lastPage ? $url . "?page=" . ($page + 1) : null,
                    "prev_page_url" => $page > 1 ? $url . "?page=" . ($page - 1) : null,
                    "per_page" => $perPage,
                    "to" => min($totalCount, ($page - 1) * $perPage + $perPage),
                    "total" => $totalCount,
                ],
                "code" => 200,
                "message" => "response ok",
            ];
            if (($request->get('search_head_name') !== null) && ($request->get('search_head_name')) && (!$request->filled('from_Job')) && $totalCount > 0) {
                $queue = [
                    'name' => $request->get('search_head_name') ?? 'manually',
                    'search_params' => $request->all(),
                    'pins' => [],
                    'count' => count($data['dataMap']),
                    'user_id' => auth('api')->user()->id,
                    'search_type' => 'find_car_similarity',
                    'search_tab' => [[
                        'value' => 'find_car_similarity',
                        'name' => 'tab',
                        'label' => 'Avtomobildə ehtimal olunan şəxslərin telefon nömrələri (Avtomobil nömrəsinə görə)',
                    ]],
                    'search_text' => 'find_car_similarity',
                ];
                SaveAdvanceSearchLogsJob::dispatch($queue)->onQueue('find_car_similarity')->onConnection('database')->delay(now()->addSeconds(20));
            }
            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('findCar error : ' . $e->getMessage());
            return response()->json(['error' => 'Xəta baş verdi.', 'msg' => $e->getMessage()], 404);
        }
    }

    /**
     * @deprecated
     */
    public function findMeetingPlaces(FindMeetingPlacesRequest $request): JsonResponse
    {
        $number1 = (int)$request->input('number1');
        $number2 = array_map('intval', (array)$request->input('number2'));
        $polygon = $request->input('polygon', []);
        $start = $request->input('start');
        $end = $request->input('end');
//        request()->merge([
//            'work_name' => $request->input('work_name', $request->input('search_head_name'))
//        ]);
        $logData = [
            "description" => $number1 . " və " . implode(',', $number2) . " telefon nömrələrinə görə " . $start . " - " . $end . " tarixləri arasında görüş üzrə axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $radius = (float)$request->input('radius') / 1000;
        $delta_time = (int)$request->input('delta_time');
        $break_time = (int)$request->input('break_time', 800);
        try {
            $data = $this->locationService->findMeetingPlaces(
                $number1, $number2, $start, $end, $radius, $delta_time, $break_time
            );
            if (empty($data['dataMap'])) {
                return response()->json($data);
            }

            $my_data = [];
            $phoneNumber = [];
            foreach ($data['dataMap'] as $item) {
                $phoneNumber[] = substr((string)$item['phone'], 3);
            }
            $query1 = DB::table('people')
                ->join('pin_phone', 'pin_phone.pin', '=', 'people.pin')
                ->select(
                    'pin_phone.pin',
                    'pin_phone.index',
                    DB::raw('CAST(pin_phone.monitoring AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('pin_phone.monitoring', $phoneNumber);

            $query2 = DB::table('people')
                ->join('phones', 'phones.pin', '=', 'people.pin')
                ->select(
                    'phones.pin',
                    'phones.id',
                    DB::raw('CAST(phones.phone AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('phones.phone', $phoneNumber);

            $combinedQuery = $query2->unionAll($query1);
            $combinedQuery->orderByRaw('1')
                ->chunk(100, function ($pins) use (&$my_data) {
                    foreach ($pins as $pin) {
                        if (!isset($my_data[$pin->contact])) {
                            $my_data[$pin->contact] = $pin;
                        }
                    }
                });
            foreach ($data['dataMap'] as &$item) {
                $phoneNumber = substr((string)$item['phone'], 3);
                $pin = $my_data[$phoneNumber] ?? [];
                if ($pin) {
                    $item['first_name'] = $pin->name ?? null;
                    $item['surname'] = $pin->surname ?? null;
                    $item['father_name'] = $pin->father_name ?? null;
                } else {
                    $item['first_name'] = null;
                    $item['surname'] = null;
                    $item['father_name'] = null;
                }
            }
            unset($item);
            foreach ($data['dataResult'] as &$item) {
                $phoneNumber = substr((string)$item['phone'], 3);
                $pin = $my_data[$phoneNumber] ?? [];
                if ($pin) {
                    $item['first_name'] = $pin->name ?? null;
                    $item['surname'] = $pin->surname ?? null;
                    $item['father_name'] = $pin->father_name ?? null;
                } else {
                    $item['first_name'] = null;
                    $item['surname'] = null;
                    $item['father_name'] = null;
                }
            }
            unset($item);
            if (!empty($polygon)) {
                $points = array_map(function ($item) {
                    return [(float)$item['lat'], (float)$item['lon']];
                }, $data['dataMap']);
                $polygon = array_map(function ($point) {
                    return [(float)$point["lat"], (float)$point["lng"]];
                }, $polygon);
                $results = (new \App\Services\PointInPolygon)->arePointsInsidePolygon($points, $polygon);
                $updatedDataMap = [];
                $updatedDataResult = [];
                foreach ($results as $index => $isInside) {
                    if ($isInside) {
                        if (isset($data['dataMap'][$index])) {
                            $updatedDataMap[] = $data['dataMap'][$index];
                            if (isset($data['dataResult'][$index])) $updatedDataResult[] = $data['dataResult'][$index];
                        }
                    }
                }
                $data['dataMap'] = $updatedDataMap;
                $data['dataResult'] = $updatedDataResult;
            }
            if ((!$request->get('page') || $request->get('page') == 1) && $request->get('search_head_name') !== null && $request->get('search_head_name') && !$request->filled('from_Job')) {
                $queue = [
                    'name' => $request->get('search_head_name') ?? 'manually',
                    'search_params' => $request->all(),
                    'pins' => [],
                    'count' => count($data['dataMap']),
                    'user_id' => auth('api')->user()->id,
                    'search_type' => 'find_meeting_places',
                    'search_tab' => [[
                        'value' => 'find_meeting_places',
                        'name' => 'tab',
                        'label' => 'İki şəxsin görüş ehtimalı (Telefon nömrələrinə görə)',
                    ]],
                    'search_text' => 'find_meeting_places',
                ];
//                Log::emergency('SaveFindMeetingPlacesLogsAfterJob: ' . json_encode($queue));
                SaveAdvanceSearchLogsJob::dispatch($queue)->onQueue('find_meeting_places')->onConnection('database')->delay(now()->addSeconds(20));
            }
            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('findMeetingPlaces error : ' . $e->getMessage());
            return response()->json([
                'error' => 'Xəta baş verdi.',
                'message' => $e->getMessage(),
                'line' => $e->getLine() . $e->getFile(),
            ], 404);
        }
    }

    /**
     * @deprecated
     */
    public function findIntervalSimilarity(FindIntervalSimilarityRequest $request): JsonResponse
    {
        $lang = $request->input('lang');
        if ((!$request->has('points') && !$request->has('polygons'))) {
            if ($lang == 'az')
                return response()->json([
                    'message' => 'Ya point ya da polygon daxil edilməlidir'
                ], 422);
            else return response()->json([
                'message' => 'Either point or polygon must be provided.'
            ], 422);
        }
        $response_count = (int)$request->input('response_count');
        $min_match = (int)$request->input('min_match');
        $points = $request->input('points');
        $polygons = $request->input('polygons');
        $convertedPolygons = [];
//        request()->merge([
//            'work_name' => $request->input('work_name', $request->input('search_head_name'))
//        ]);
        $logData = [
            "description" => " səfər ehtimalı axtarışı etdi .",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        if ($polygons) {
            foreach ($polygons as $polygon) {
                $coordinates = [];
                foreach ($polygon['polygon'] as $point) {
                    $coordinates[] = [(float)$point["lng"], (float)$point["lat"]];
                }
                $convertedPolygons[] = [
                    'start' => $polygon['start'],
                    'end' => $polygon['end'],
                    'pg_geom' => $coordinates,
                    'radius' => (float)$polygon['radius'] / 1000
                ];
            }
        }
        if ($points) {
            foreach ($points as &$point) {
                $point['lat'] = (float)$point['lat'];
                $point['lon'] = (float)$point['lon'];
                $point['radius'] = (float)$point['radius'] / 1000;
            }
            unset($point);
        }
        try {
            $data = $this->locationService->findIntervalSimilarity($response_count, $min_match, $points, $convertedPolygons);
            if (empty($data['dataMap'])) {
                if ($lang == 'az')
                    return response()->json(['message' => 'Bu koordinatlara uyğun məlumat tapılmadı.'], 404);
                else return response()->json(['message' => 'No data found for these coordinates.'], 404);
            }
            $response = [
                'data' => [
                    'data' => $data
                ]
            ];
            if ((!$request->get('page') || $request->get('page') == 1) && $request->get('search_head_name') !== null && $request->get('search_head_name') && !$request->filled('from_Job')) {
                $queue = [
                    'name' => $request->get('search_head_name') ?? 'manually',
                    'search_params' => $request->all(),
                    'pins' => [],
                    'count' => count($data),
                    'user_id' => auth('api')->user()->id,
                    'search_type' => 'find_interval_similarity',
                    'search_tab' => [[
                        'value' => 'find_interval_similarity',
                        'name' => 'tab',
                        'label' => 'İki şəxsin səfər ehtimalı',
                    ]],
                    'search_text' => 'find_interval_similarity',
                ];
//                Log::emergency('SaveFindIntervalSimilarityLogsAfterJob: ' . json_encode($queue));
                SaveAdvanceSearchLogsJob::dispatch($queue)->onQueue('find_interval_similarity')->onConnection('database')->delay(now()->addSeconds(20));
            }
            return response()->json($response);
        } catch (\Exception $e) {
            Log::error('findIntervalSimilarity error : ' . $e->getMessage());
            return response()->json(['error' => $e->getMessage()], 404);
        }
    }

    /**
     * @deprecated
     */
    public function getSocialCallLocationWithOthersNew(FindPhoneSimilarityRequest $request, string $pin = null): JsonResponse
    {
        if ($pin) {
            $request = $request->merge(['pin' => $pin]);
        }
        $pin = $request->input('pin');
        $lang = $request->input('lang');
        if ($lang == 'en') {
            $messages = [
                'pin.required' => 'Phone number is required and cannot be empty.',
                'pin.regex' => 'Phone number must be in the format 994XXXXXXXXX.'
            ];
        } else {
            $messages = [
                'pin.required' => 'Telefon nömrəsi mütləqdir və boş ola bilməz.',
                'pin.regex' => 'Telefon nömrəsi 994XXXXXXXXX formatında olmalıdır.'
            ];
        }
        $request->validate([
            'pin' => 'required|regex:/^(994)[1-9][0-9]{8}$/'
        ], $messages
        );
        ini_set('memory_limit', -1);
        $given = $pin;
        $start_date = $request->input('from', '2024-05-15 09:00:00');
        $end_date = $request->input('to', '2024-05-15 23:00:00');
        $response_count = (int)$request->input('top', 20);
        $small_radius = (float)$request->input('min_radius') / 1000;
        $big_radius = (float)$request->input('max_radius') / 1000;
        if ($small_radius <= 0 || $big_radius <= 0) {
            if ($lang == 'az') return response()->json([
                'message' => 'Radius dəyərləri mənfi və ya sıfır ola bilməz.'
            ], 422);
            else return response()->json(['message' => 'Radius values cannot be negative or zero.']);
        }
        $delta_time = (int)$request->input('delta_time', 100);
        $break_time = (int)$request->input('$break_time', 86400);
        $match_percentage = (float)$request->input('match_percentage', 1.0);
        $perPage = (int)$request->input('per_page', 10);
        $page = (int)$request->input('page', 1);
        $offset = ($page - 1) * $perPage;
//        request()->merge([
//            'work_name' => $request->input('work_name', $request->input('search_head_name'))
//        ]);
        $logData = [
            "description" => $pin . " telefon nömrəsinə görə " . $start_date . " - " . $end_date . " tarixləri arasında oxşarlıq axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $polygon = $request->input('polygon');
        $convertedPolygon = [];
        try {
            $dataMap = $this->locationService->findSimilarity((int)$given, $start_date, $end_date, $match_percentage, $break_time, $response_count, $small_radius, $big_radius, $delta_time);
            if (!$dataMap['data']) {
                $response = [
                    'data' => [
                        'data' => $dataMap['data']
                    ]
                ];
                return response()->json($response);
            }
            if ($polygon) {
                foreach ($polygon as $point) {
                    $convertedPolygon[] = [(float)$point['lat'], (float)$point['lng']];
                }
                $points = [];
                foreach ($dataMap['data'] as $coordinateGroup) {
                    foreach ($coordinateGroup['points'] as $intersection) {
                        $points[] = [
                            $intersection['lat'],
                            $intersection['lon']
                        ];
                    }
                }
                $results = (new \App\Services\PointInPolygon)->arePointsInsidePolygon($points, $convertedPolygon);
                $filteredDataMap = [];
                $resultIndex = 0;
                foreach ($dataMap['data'] as &$mapItem) {
                    $filteredPoints = [];
                    foreach ($mapItem['points'] as &$point) {
                        if ($results[$resultIndex]) {
                            $filteredPoints[] = $point;
                        }
                        $resultIndex++;
                    }
                    if (!empty($filteredPoints)) {
                        $mapItem['points'] = $filteredPoints;
                        $filteredDataMap[] = $mapItem;
                    }
                }
                if (empty($filteredDataMap)) {
                    $response = [
                        'data' => [
                            'data' => $filteredDataMap
                        ]
                    ];
                    return response()->json($response);
                }
                $dataMap['data'] = $filteredDataMap;
            }
            $my_data = [];
            $phoneNumber = [];
            foreach ($dataMap['data'] as $itemPhone) {
                $phoneNumber[] = substr((string)$itemPhone['phone'], 3);
            }
            $query1 = DB::table('people')
                ->join('pin_phone', 'pin_phone.pin', '=', 'people.pin')
                ->select(
                    'pin_phone.pin',
                    'pin_phone.index',
                    DB::raw('CAST(pin_phone.monitoring AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('pin_phone.monitoring', $phoneNumber);

            $query2 = DB::table('people')
                ->join('phones', 'phones.pin', '=', 'people.pin')
                ->select(
                    'phones.pin',
                    'phones.id',
                    DB::raw('CAST(phones.phone AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('phones.phone', $phoneNumber);

            $combinedQuery = $query2->unionAll($query1);
            $combinedQuery->orderByRaw('1')
                ->chunk(100, function ($pins) use (&$my_data) {
                    foreach ($pins as $pin) {
                        if (!isset($my_data[$pin->contact])) {
                            $my_data[$pin->contact] = $pin;
                        }
                    }
                });
            foreach ($dataMap['data'] as &$item) {
                $phoneNumber = substr((string)$item['phone'], 3);
                $pin = $my_data[$phoneNumber] ?? [];
                if ($pin) {
                    $item['first_name'] = $pin->name ?? null;
                    $item['surname'] = $pin->surname ?? null;
                    $item['father_name'] = $pin->father_name ?? null;
                } else {
                    $item['first_name'] = null;
                    $item['surname'] = null;
                    $item['father_name'] = null;
                }
            }
            unset($item);
            $points = [];
            foreach ($dataMap['data'] as $user) {
                foreach ($user['points'] as $point) {
                    $key = "{$point['lat']},{$point['lon']}";
                    if (!isset($uniquePoints[$key])) {
                        $uniquePoints[$key] = true;
                        $points[] = "({$point['lon']}, {$point['lat']})";
                    }
                }
            }
            $points = implode(',', $points);
            $sql = "SELECT lon, lat, max(ctd.name) as name, max(ctd.description) as description,
                    max(ctd.radius) as radius, max(ctd.beam) as beam, max(ctd.hash) as antenna_hash,
                    max(ctd.lac) as lac, max(ctd.cell_id) as cell_id
                    FROM radius_events.cell_towers_dictionary ctd
                    WHERE (ctd.lon, ctd.lat) in ($points)
                    GROUP BY ctd.lon, ctd.lat";

            $results = DB::connection('clickhouse_radius')->select($sql);
            $dataResult = [];
            foreach ($dataMap['data'] as &$user) {
                foreach ($user['points'] as &$point) {
                    foreach ($results as $result) {
                        if ($point['lat'] == $result['lat'] && $point['lon'] == $result['lon']) {
                            $point['name'] = $result['name'];
                            $point['description'] = $result['description'];
                            $point['radius'] = $result['radius'];
                            $point['beam'] = $result['beam'];
                            $point['antenna_hash'] = $result['antenna_hash'];
                            $point['lac'] = $result['lac'];
                            $point['cell_id'] = $result['cell_id'];
                            break;
                        }
                    }
                }
                $dataResult[] = $user;
            }
            unset($mapItem);
            unset($point);
            $dataResultWithNames = [];
            unset($user);
            foreach ($dataMap['data'] as $user) {
                foreach ($user['points'] as $point) {
                    $dataResultWithNames[] = [
                        'lon' => $point['lon'] ?? null,
                        'lat' => $point['lat'] ?? null,
                        'description' => $point['description'] ?? '',
                        'lac' => $point['lac'] ?? null,
                        'cell_id' => $point['cell_id'] ?? null,
                        'beam' => $point['beam'] ?? null,
                        'name' => $point['name'] ?? '',
                        'radius' => $point['radius'] ?? null,
                        'phone' => $user['phone'] ?? null,
                        'first_name' => $user['first_name'] ?? null,
                        'surname' => $user['surname'] ?? null,
                        'father_name' => $user['father_name'] ?? null,
                        'start' => $point['start'] ?? null,
                        'end' => $point['end'] ?? null
                    ];
                }
            }
            usort($dataMap['data'], function ($a, $b) {
                return count($b['points']) - count($a['points']);
            });
            $allData = ["dataMap" => $dataMap['data'], "dataResult" => $dataResultWithNames];
            $totalCount = count($dataResult);
            $lastPage = (int)ceil($totalCount / $perPage);
            $url = url('/api/v1/person/' . $given . '/social-call-location-with-others');
            $response = [
                "status" => "Success",
                "data" => [
                    "current_page" => $page,
                    "data" => $allData,
                    "first_page_url" => $url . "?page=1",
                    "from" => $offset + 1,
                    "last_page" => $lastPage,
                    "last_page_url" => $url . "?page=" . $lastPage,
                    "links" => generatePaginationLinks($page, $lastPage, $url),
                    "next_page_url" => $page < $lastPage ? $url . "?page=" . ($page + 1) : null,
                    "prev_page_url" => $page > 1 ? $url . "?page=" . ($page - 1) : null,
                    "per_page" => $perPage,
                    "to" => min($totalCount, ($page - 1) * $perPage + $perPage),
                    "total" => $totalCount,
                ],
                "code" => 200,
                "message" => "response ok",
            ];
            if ((!$request->get('page') || $request->get('page') == 1) && $request->get('search_head_name') !== null && $request->get('search_head_name') && !$request->filled('from_Job') && $totalCount > 0) {
                $queue = [
                    'name' => $request->get('search_head_name') ?? 'manually',
                    'search_params' => $request->all(),
                    'pins' => [],
                    'count' => count($dataMap),
                    'user_id' => auth('api')->user()->id,
                    'search_type' => 'similarity_by_number',
                    'search_tab' => [[
                        'value' => 'similarity_by_number',
                        'name' => 'tab',
                        'label' => 'Oxşar trayektoriyalar (Telefon nömrəsi ilə)',
                    ]],
                    'search_text' => 'similarity_by_number',
                ];
//                Log::emergency('SaveSimilarityByNumber: ' . json_encode($queue));
                SaveAdvanceSearchLogsJob::dispatch($queue)->onQueue('similarity_by_number')->onConnection('database')->delay(now()->addSeconds(20));
            }
            return response()->json($response);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while processing your request.',
                'msg' => $e->getMessage() . ' ' . $e->getFile() . ' ' . $e->getLine()
            ], 404);
        }
    }

    /**
     * @deprecated
     */
    public function searchByArea(SearchByAreaRequest $request): JsonResponse
    {
        $perPage = $request->input('per_page', 12);
        $page = $request->input('page', 1);
        $radius = $request->input('radius');
        $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $request->input('start_time'));
        $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $request->input('end_time'));
        $offset = ($page - 1) * $perPage;
        $polygon = $request->input('polygon');
        $coordinates = $this->parsePolygon($polygon);
//        request()->merge([
//            'work_name' => $request->input('work_name', $request->input('search_head_name'))
//        ]);
        $logData = [
            "description" => $startTime . " - " . $endTime . " tarixləri arasında ərazi üzrə axtarış etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $polygonQuery = "
            WITH cte_0
    AS (SELECT '$coordinates' pgn_aswkt_str,
               readWKTPolygon(pgn_aswkt_str)                                                                                                                                                                              pgn_geom)
   , cte_1 AS (SELECT pgn_geom,
                      pgn_aswkt_str,
                      arrayJoin(flatten(pgn_geom))     corner_point,
                      tupleElement(corner_point, 1, 0) _lon_corner_point,
                      tupleElement(corner_point, 2, 0) _lat_corner_point
               FROM cte_0)
, cte_antennas as (SELECT distinct ctd.hash,
                            ctd.lon lon_antenna,
                            ctd.lat lat_antenna
            FROM cte_1
                     CROSS JOIN radius_events.cell_towers_dictionary ctd
            WHERE h3PointDistM(_lat_corner_point, _lon_corner_point,
                               ctd.lat, ctd.lon
                      ) <= $radius
               OR pointInPolygon((ctd.lon, ctd.lat), cte_1.pgn_geom))

SELECT log.calling_station_id calling_station_id
FROM radius_events.dist_radius_log log
GLOBAL JOIN cte_antennas ON cte_antennas.hash=log.hash
WHERE log.event_timestamp BETWEEN '$startTime' AND '$endTime'
GROUP BY log.calling_station_id
LIMIT $perPage OFFSET $offset;";
        $results = DB::connection('clickhouse_radius')->select($polygonQuery);
        $countSql = /** @lang sql */
            "WITH cte_0
    AS (SELECT '$coordinates' pgn_aswkt_str,
               readWKTPolygon(pgn_aswkt_str)                                                                                                                                                                              pgn_geom)
   , cte_1 AS (SELECT pgn_geom,
                      pgn_aswkt_str,
                      arrayJoin(flatten(pgn_geom))     corner_point,
                      tupleElement(corner_point, 1, 0) _lon_corner_point,
                      tupleElement(corner_point, 2, 0) _lat_corner_point
               FROM cte_0)
, cte_antennas as (SELECT distinct ctd.hash,
                            ctd.lon lon_antenna,
                            ctd.lat lat_antenna
            FROM cte_1
                     CROSS JOIN radius_events.cell_towers_dictionary ctd
            WHERE h3PointDistM(_lat_corner_point, _lon_corner_point,
                               ctd.lat, ctd.lon
                      ) <= $radius
               OR pointInPolygon((ctd.lon, ctd.lat), cte_1.pgn_geom))
, cte_2 as (
SELECT log.calling_station_id calling_station_id
FROM radius_events.dist_radius_log log
GLOBAL JOIN cte_antennas ON cte_antennas.hash=log.hash
WHERE log.event_timestamp BETWEEN '$startTime' AND '$endTime'
GROUP BY log.calling_station_id)
select count(*) as count
from cte_2;";
        $totalCount = DB::connection('clickhouse_radius')->select($countSql)[0]['count'] ?? 0;
        $my_data = [];
        $phoneNumber = [];
        foreach ($results as $item) {
            $phoneNumber[] = substr((string)$item['calling_station_id'], 3);
        }
        $query1 = DB::table('people')
            ->join('pin_phone', 'pin_phone.pin', '=', 'people.pin')
            ->select(
                'pin_phone.pin',
                'pin_phone.index',
                DB::raw('CAST(pin_phone.monitoring AS TEXT) AS contact'),
                'people.name',
                'people.surname',
                'people.father_name'
            )
            ->whereIn('pin_phone.monitoring', $phoneNumber);

        $query2 = DB::table('people')
            ->join('phones', 'phones.pin', '=', 'people.pin')
            ->select(
                'phones.pin',
                'phones.id',
                DB::raw('CAST(phones.phone AS TEXT) AS contact'),
                'people.name',
                'people.surname',
                'people.father_name'
            )
            ->whereIn('phones.phone', $phoneNumber);

        $combinedQuery = $query2->unionAll($query1);

        $combinedQuery->orderByRaw('1')
            ->chunk(100, function ($pins) use (&$my_data) {
                foreach ($pins as $pin) {
                    if (!isset($my_data[$pin->contact])) {
                        $my_data[$pin->contact] = $pin;
                    }
                }
            });
        foreach ($results as &$item) {
            $phoneNumber = substr((string)$item['calling_station_id'], 3);
            $paramPin = $my_data[$phoneNumber] ?? [];
            if ($paramPin) {
                $item['first_name'] = $my_data[$phoneNumber]->name ?? '';
                $item['surname'] = $my_data[$phoneNumber]->surname ?? '';
                $item['father_name'] = $my_data[$phoneNumber]->father_name ?? '';
            } else {
                $item['first_name'] = '';
                $item['surname'] = '';
                $item['father_name'] = '';
            }
        }
        $lastPage = ceil($totalCount / $perPage);
        $baseUrl = url("/api/v1/search-by-area/");
        $response = [
            "status" => "Success",
            "data" => [
                "current_page" => $page,
                "data" => $results,
                "first_page_url" => $baseUrl . "?page=1",
                "from" => (int)$offset + 1,
                "last_page" => $lastPage,
                "last_page_url" => $baseUrl . "?page=" . $lastPage,
                "next_page_url" => $page < $lastPage ? $baseUrl . "?page=" . ($page + 1) : null,
                "prev_page_url" => $page > 1 ? $baseUrl . "?page=" . ($page - 1) : null,
                "per_page" => (int)$perPage,
                "to" => (int)$offset + count($results),
                "total" => (int)$totalCount,
            ],
            "code" => 200,
            "message" => "response ok"
        ];
        return response()->json($response);
    }


    public function getSocialCallLocationInfo(Request $request, string $pin = null): JsonResponse
    {
        if ($pin) {
            $request = $request->merge(['pin' => $pin]);
        }
        $lang = $request->input('lang');
        if ($lang == 'en') {
            $messages = [
                'pin.required' => 'Phone number is required and cannot be empty.',
                'pin.regex' => 'Phone number must be in the format 994XXXXXXXXX.'
            ];
        } else {
            $messages = [
                'pin.required' => 'Telefon nömrəsi mütləqdir və boş ola bilməz.',
                'pin.regex' => 'Telefon nömrəsi 994XXXXXXXXX formatında olmalıdır.'
            ];
        }
        $request->validate([
            'pin' => 'required|regex:/^(994)[1-9][0-9]{8}$/'
        ], $messages
        );
        $perPage = $request->input('per_page', 12);
        $page = $request->input('page', 1);
        $pin = $request->input('pin');
        $phoneRegex = '/^994[1-9][0-9]{8}$/';
        $isPhoneNumber = preg_match($phoneRegex, $pin);
        if (!$isPhoneNumber) {
            if ($lang == 'az') return response()->json([
                'error' => 'Telefon nömrəsinin formatı uyğun deyil. Format : 994XXXXXXXXX kimi olmalıdır'
            ], 422);
            else return response()->json(['error' => 'The phone number format is incorrect. The format should be 994XXXXXXXXX.']);
        };
        $fromDate = $request->input('from', today()->startOfDay());
        if ($request->input('debug') || in_array(auth('api')->user()?->id, [126, 139])) {
            $fromDate = $request->input('from', '2023-05-15 09:00:00');
        }
        $toDate = $request->input('to', today()->endOfDay());
        $fromDateWithoutTime = Carbon::parse($fromDate)->format('Y-m-d');
        $toDateWithoutTime = Carbon::parse($toDate)->format('Y-m-d');
//        request()->merge([
//            'work_name' => $request->input('work_name') ?? $request->input('search_head_name') ?? 'location dashboard'
//        ]);

        $logData = [
            "description" => $pin . " telefon nömrəsinə görə " . $fromDate . " - " . $toDate . " tarixləri arasında trayektoriya axtarışı etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $offset = ($page - 1) * $perPage;
        $polygon = $request->input('polygon');
        $polygonCondition = '';
        if (!empty($polygon)) {
            $polygonPoints = implode(', ', array_map(function ($index) use ($polygon) {
                $lat = $polygon[$index]['lat'];
                $lng = $polygon[$index]['lng'];
                return "($lng, $lat)";
            }, array_keys($polygon)));
            $polygonCondition = "AND pointInPolygon((c.lon, c.lat),[$polygonPoints])";
        }
        $sql = /** @lang sql */
            "with cte_1 as (SELECT
                r.event_timestamp,
                r.calling_station_id,
                c.lat,
                c.lon,
                c.operator as operator,
                c.lac as lac,
                c.cell_id as cell_id,
                c.radius,
                c.beam,
                c.description,
                c.name,
                max(r.framed_ip_address) as framed_ip_address,
                max(r.`3gpp_rat_type`) as `3gpp_rat_type`,
                max(r.`3gpp_imsi`) as `3gpp_imsi`,
                max(r.`3gpp_imeisv`) as `3gpp_imeisv`
            FROM
                radius_events.dist_radius_log r
                JOIN radius_events.cell_towers_dictionary c ON c.hash = r.hash
            WHERE
                r.event_timestamp BETWEEN :fromTime AND :toTime
                AND r.calling_station_id = :pin
                AND ((c.date_begin <= :from
                AND (c.date_end IS NULL OR c.date_end = '1970-01-01'))
                OR (c.date_begin >= :to
                AND (c.date_end <= :to
                OR c.date_end IS NULL OR c.date_end = '1970-01-01')))
                $polygonCondition
            GROUP BY r.event_timestamp, r.calling_station_id, c.lat, c.lon, c.operator, c.lac, c.cell_id, c.radius, c.beam, c.description, c.name)
            SELECT * FROM cte_1
            ORDER BY cte_1.event_timestamp ASC
            LIMIT :limit OFFSET :offset;";

        $results = DB::connection('clickhouse_radius')->select($sql, ['from' => $fromDateWithoutTime, 'to' => $toDateWithoutTime, 'fromTime' => $fromDate, 'toTime' => $toDate, 'pin' => $pin, 'limit' => (int)$perPage, 'offset' => (int)$offset]);
//        foreach ($results as $index => &$item) {
//            $item['row'] = $index + 1;
//        }
        //elave eleme bug olur bashka cure handle ele pls
        // if (empty($results)) return response()->json(['data'=>['data'=>[]]]);
        $countSql = /** @lang sql */
            "SELECT COUNT(DISTINCT concat(r.event_timestamp, '_', r.calling_station_id, '_', c.lat, '_', c.lon)) as total
                 FROM radius_events.dist_radius_log r
                 JOIN radius_events.cell_towers_dictionary c ON c.hash = r.hash
                 WHERE r.event_timestamp BETWEEN :fromTime AND :toTime
                   AND r.calling_station_id = :pin
                   AND ((c.date_begin <= :from AND (c.date_end IS NULL OR c.date_end = '1970-01-01'))
                        OR (c.date_begin >= :to AND (c.date_end <= :to OR c.date_end IS NULL OR c.date_end = '1970-01-01')))
                        $polygonCondition";
        $totalCount = DB::connection('clickhouse_radius')->select($countSql, ['from' => $fromDateWithoutTime, 'to' => $toDateWithoutTime, 'fromTime' => $fromDate, 'toTime' => $toDate, 'pin' => $pin])[0]['total'] ?? 0;
        $my_data = [];
        $phoneNumber = [];
        foreach ($results as $item) {
            $phoneNumber[] = substr((string)$item['calling_station_id'], 3);
        }
        $query1 = DB::table('people')
            ->join('pin_phone', 'pin_phone.pin', '=', 'people.pin')
            ->select(
                'pin_phone.pin',
                'pin_phone.index',
                DB::raw('CAST(pin_phone.monitoring AS TEXT) AS contact'),
                'people.name',
                'people.surname',
                'people.father_name'
            )
            ->whereIn('pin_phone.monitoring', $phoneNumber);

        $query2 = DB::table('people')
            ->join('phones', 'phones.pin', '=', 'people.pin')
            ->select(
                'phones.pin',
                'phones.id',
                DB::raw('CAST(phones.phone AS TEXT) AS contact'),
                'people.name',
                'people.surname',
                'people.father_name'
            )
            ->whereIn('phones.phone', $phoneNumber);

        $combinedQuery = $query2->unionAll($query1);

        $combinedQuery->orderByRaw('1')
            ->chunk(100, function ($pins) use (&$my_data) {
                foreach ($pins as $pin) {
                    if (!isset($my_data[$pin->contact])) {
                        $my_data[$pin->contact] = $pin;
                    }
                }
            });
        foreach ($results as &$item) {
            $phoneNumber = substr((string)$item['calling_station_id'], 3);
            $paramPin = $my_data[$phoneNumber] ?? [];
            if ($paramPin) {
                $item['first_name'] = $my_data[$phoneNumber]->name ?? null;
                $item['surname'] = $my_data[$phoneNumber]->surname ?? null;
                $item['father_name'] = $my_data[$phoneNumber]->father_name ?? null;
            } else {
                $item['first_name'] = null;
                $item['surname'] = null;
                $item['father_name'] = null;
            }
        }
        $lastPage = (int)ceil($totalCount / $perPage);
        $baseUrl = url("/api/v1/person/" . $pin . "/social-call-location");
        $response = [
            "status" => "Success",
            "data" => [
                "current_page" => (int)$page,
                "data" => $results,
                "first_page_url" => $baseUrl . "?page=1",
                "from" => (int)$offset + 1,
                "last_page" => $lastPage,
                "last_page_url" => $baseUrl . "?page=" . $lastPage,
                "next_page_url" => $page < $lastPage ? $baseUrl . "?page=" . ($page + 1) : null,
                "prev_page_url" => $page > 1 ? $baseUrl . "?page=" . ($page - 1) : null,
                "per_page" => (int)$perPage,
                "to" => (int)$offset + count($results),
                "total" => (int)$totalCount,
            ],
            "code" => 200,
            "message" => "response ok"
        ];
        if ((!$request->get('page') || $request->get('page') == 1) && ($request->get('search_head_name') !== null) && ($request->get('search_head_name')) && (!$request->filled('from_Job')) && $totalCount > 0) {
            $queue = [
                'name' => $request->get('search_head_name') ?? 'manually',
                'search_params' => $request->all(),
                'pins' => [],
                'count' => count($results),
                'user_id' => auth('api')->user()->id,
                'search_type' => 'get_social_call_location',
                'search_tab' => [[
                    'value' => 'get_social_call_location',
                    'name' => 'tab',
                    'label' => 'Trayektoriyalar (Telefon nömrəsi ilə)',
                ]],
                'search_text' => 'get_social_call_location',
            ];
            SaveAdvanceSearchLogsJob::dispatch($queue)->onQueue('get_social_call_location')->onConnection('database')->delay(now()->addSeconds(20));
        }

        return response()->json($response);
    }

    public function getSocialCallLocationInfoExport(Request $request, string $pin = null)
    {
        $response = $this->getSocialCallLocationInfo($request, $pin);

        $responseArray = json_decode(json_encode($response), true);
        $responseArray = $responseArray['original'];

        if (isset($responseArray['errors'])) {
            return response()->json([
                'status' => 'error',
                'message' => $responseArray['errors']
            ], 422);
        }

        $data = isset($responseArray['data']) ? $responseArray['data']['data'] : [];

        $exportType = strtoupper($request->post('export_type', 'XLSX'));

        $fileExtensions = [
            'XLSX' => 'xlsx',
            'HTML' => 'html',
            'DOMPDF' => 'pdf'
        ];

        $exportFormats = [
            'XLSX' => \Maatwebsite\Excel\Excel::XLSX,
            'HTML' => \Maatwebsite\Excel\Excel::HTML,
            'DOMPDF' => \Maatwebsite\Excel\Excel::DOMPDF
        ];

        if (!isset($fileExtensions[$exportType]) || !isset($exportFormats[$exportType])) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid export type specified'
            ], 422);
        }

        if (empty($data)) {
            return response()->json([
                'status' => 'error',
                'message' => 'No data found for Export'
            ], 422);
        }

        $headTags = array_keys($data[0]);

        $data = collect($data);

        return (new Export($headTags, $data))
            ->download(
                time() . '.' . $fileExtensions[$exportType],
                $exportFormats[$exportType]
            );
    }

    /**
     * @deprecated
     */
    public function nativeAndStrangers(NativeAndStrangersRequest $request): JsonResponse
    {
        if (($request->has('point') && $request->has('polygon')) || (!$request->has('point') && !$request->has('polygon'))) {
            return response()->json([
                'message' => 'Ya point ya da polygon daxil edilməlidir, ikisi də birlikdə və ya heç biri daxil edilməməlidir.'
            ], 422);
        }
        $polygon = $request->input('polygon');
        $pointLat = $request->input('point.lat');
        $pointLon = $request->input('point.lng');
        $radius = $request->input('radius');
        $type = strtoupper($request->input('type'));
        $frequency_count = (int)$request->input('frequency_count');
        $page = 1;
        $perPage = (int)$request->input('topK', 10);
        $offset = ($page - 1) * $perPage;
        $startTime = Carbon::createFromFormat('Y-m-d H:i:s', $request->input('start_time'));
        $endTime = Carbon::createFromFormat('Y-m-d H:i:s', $request->input('end_time'));
        $daysDifference = $startTime->diffInDays($endTime);
        if ($frequency_count > $daysDifference) {
            return response()->json([
                'errors' => [
                    'frequency_count' => ['Tezlik sayı başlanğıc vaxt ilə son vaxt arasındakı gün sayından az olmalıdır.'],
                ]
            ], 422);
        };
//        request()->merge([
//            'work_name' => $request->input('work_name', $request->input('search_head_name'))
//        ]);
        $logData = [
            "description" => $startTime . " - " . $endTime . " tarixləri arasında özgələr və yerlilər üzrə axtarış etdi.",
            "type" => "polygon"
        ];
        sendRequestLogToAudit($logData, "audit");
        $timestamp = now()->format('YmdHisv');
        $drop_query = "DROP TABLE IF EXISTS radius_events.natives_strangers_result_$timestamp;";
        DB::connection('clickhouse_radius')->statement($drop_query);
        $locationType = $request->has('point') ? 'point' : 'polygon';
        $createQuery = "CREATE TABLE radius_events.natives_strangers_result_$timestamp ENGINE = Memory AS WITH ";
        $polygonQuery = '';
        $pointQuery = '';
        if ($locationType == 'polygon') {
            $coordinates = $this->parsePolygon($polygon);
            $polygonQuery = "
            cte_0
        AS (SELECT '$coordinates' pgn_aswkt_str,
                   readWKTPolygon(pgn_aswkt_str)                                                                                                                                                                         pgn_geom)
   , cte_1 AS (SELECT pgn_geom,
                      pgn_aswkt_str,
                      arrayJoin(flatten(pgn_geom))     corner_point,
                      tupleElement(corner_point, 1, 0) _lon_corner_point,
                      tupleElement(corner_point, 2, 0) _lat_corner_point
               FROM cte_0)
   , cte_target_antennas AS (SELECT distinct ctd.hash hash_antenna
                                             , ctd.lon  lon_antenna
                                             , ctd.lat  lat_antenna
                                             , cte_1.pgn_geom
                               FROM cte_1
                                        CROSS JOIN radius_events.cell_towers_dictionary ctd
                               WHERE h3PointDistM(_lat_corner_point, _lon_corner_point,
                                                  ctd.lat, ctd.lon
                                         ) <= $radius
                                  OR pointInPolygon((ctd.lon, ctd.lat), cte_1.pgn_geom))";

        } else {
            $pointQuery = "cte_given_point AS (SELECT readWKTPoint('POINT ($pointLon $pointLat)') wkt_point
                              , tupleElement(wkt_point, 1)                                  lon
                              , tupleElement(wkt_point, 2)                                  lat)
   , cte_target_antennas_1 AS (SELECT ctd.hash hash_antenna
                                    , ctd.lon  lon_antenna
                                    , ctd.lat  lat_antenna
                               FROM radius_events.cell_towers_dictionary ctd
                                        CROSS JOIN cte_given_point given_point
                               WHERE h3PointDistM(given_point.lat, given_point.lon,
                                                  ctd.lat, ctd.lon
                                         ) <= $radius);";
        }
        $createQuery .= $locationType === 'polygon' ? $polygonQuery : $pointQuery;
        if ($type === 'NATIVE') {
            $type = 'LOCAL';
            $havingClause = "HAVING (visiter_type = 'LOCAL' AND COUNT(*) >= 4))";
            $orderClause = "ORDER BY visit_count DESC";
        } elseif ($type === 'STRANGER') {
            $havingClause = "HAVING (visiter_type = 'STRANGER' AND COUNT(*) <= 1))";
            $orderClause = "ORDER BY visit_count ASC";
        } else {
            return response()->json([
                'errors' => [
                    'type' => ['Belə bir tip yoxdur.'],
                ]
            ], 422);
        }
        $query = "
        , cte_signal_agg__day_data as (SELECT log.calling_station_id,
                                         toDate(log.event_timestamp)      day_event_ts,
                                         count(*)                         signal_freq_daily,
                                         '$type'                          visiter_type,
                                         groupUniqArray(cta.hash_antenna) groupped_hash_antenna
                                  FROM radius_events.dist_radius_log log
                                           GLOBAL
                                           JOIN cte_target_antennas cta on log.hash = cta.hash_antenna
                                  WHERE log.event_timestamp BETWEEN '$startTime'
                                            AND '$endTime'
                                  GROUP BY log.calling_station_id, toDate(log.event_timestamp))
   , cte_agg_data AS (SELECT calling_station_id     as         _calling_station_id,
                             count(*)               AS         visit_count,
                             visiter_type,
                             sum(signal_freq_daily) AS         signal_over_interval,
                             groupArray(groupped_hash_antenna) groupped_hash_antenna_x2,
                             arrayMap(x -> (toString(x.1), toInt64OrDefault(x.2)),
                                 arraySort(x -> x.1,
                                     groupArray((day_event_ts, signal_freq_daily))
                                     )
                                 ) detailed_info
                      FROM cte_signal_agg__day_data
                      GROUP BY calling_station_id, visiter_type
                      $havingClause
SELECT DISTINCT ad._calling_station_id _calling_station_id,
                visit_count,
                flatten(groupped_hash_antenna_x2) antenna_list,
                signal_over_interval,
                detailed_info
FROM cte_agg_data ad
$orderClause;";
        $createQuery .= $query;
        $insertResult = DB::connection('clickhouse_radius')->statement($createQuery);
        if ($insertResult) {
            $selectQuery = "
            SELECT _calling_station_id as calling_station_id
             , visit_count
             , signal_over_interval
             , detailed_info
            FROM radius_events.natives_strangers_result_$timestamp
            ORDER BY visit_count DESC, signal_over_interval DESC
            LIMIT $perPage OFFSET $offset;";
            $selectResult = DB::connection('clickhouse_radius')->select($selectQuery);
            $drop_table_sql = "DROP TABLE IF EXISTS radius_events.natives_strangers_result_$timestamp";
            DB::connection('clickhouse_radius')->statement($drop_table_sql);
            $my_data = [];
            $phoneNumber = [];
            foreach ($selectResult as $item) {
                $phoneNumber[] = substr((string)$item['calling_station_id'], 3);
            }
            $query1 = DB::table('people')
                ->join('pin_phone', 'pin_phone.pin', '=', 'people.pin')
                ->select(
                    'pin_phone.pin',
                    'pin_phone.index',
                    DB::raw('CAST(pin_phone.monitoring AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('pin_phone.monitoring', $phoneNumber);

            $query2 = DB::table('people')
                ->join('phones', 'phones.pin', '=', 'people.pin')
                ->select(
                    'phones.pin',
                    'phones.id',
                    DB::raw('CAST(phones.phone AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('phones.phone', $phoneNumber);

            $combinedQuery = $query2->unionAll($query1);
            $combinedQuery->orderByRaw('1')
                ->chunk(100, function ($pins) use (&$my_data) {
                    foreach ($pins as $pin) {
                        if (!isset($my_data[$pin->contact])) {
                            $my_data[$pin->contact] = $pin;
                        }
                    }
                });
            foreach ($selectResult as &$item) {
                $phoneNumber = substr((string)$item['calling_station_id'], 3);
                $paramPin = $my_data[$phoneNumber] ?? null;
                if ($paramPin) {
                    $item['first_name'] = $paramPin->name ?? null;
                    $item['surname'] = $paramPin->surname ?? null;
                    $item['father_name'] = $paramPin->father_name ?? null;
                } else {
                    $item['first_name'] = null;
                    $item['surname'] = null;
                    $item['father_name'] = null;
                }
            }
            unset($item);
            $lastPage = 1;
            $url = url('/api/v1/native-and-strangers');
            $response = [
                "status" => "Success",
                "data" => [
                    "current_page" => $page,
                    "data" => $selectResult,
                    "first_page_url" => $url . "?page=1",
                    "from" => $offset + 1,
                    "last_page" => $lastPage,
                    "last_page_url" => $url . "?page=" . $lastPage,
                    "links" => generatePaginationLinks($page, $lastPage, $url),
                    "next_page_url" => $page < $lastPage ? $url . "?page=" . ($page + 1) : null,
                    "prev_page_url" => $page > 1 ? $url . "?page=" . ($page - 1) : null,
                    "per_page" => $perPage,
                    "to" => min($perPage, ($page - 1) * $perPage + $perPage),
                    "total" => $perPage,
                ],
                "code" => 200,
                "message" => "response ok",
            ];
            if ((!$request->get('page') || $request->get('page') == 1) && $request->get('search_head_name') !== null && $request->get('search_head_name') && !$request->filled('from_Job') && $perPage > 0) {
                $queue = [
                    'name' => $request->get('search_head_name') ?? 'manually',
                    'search_params' => $request->all(),
                    'pins' => [],
                    'count' => count($selectResult),
                    'user_id' => auth('api')->user()->id,
                    'search_type' => 'native_and_strangers',
                    'search_tab' => [[
                        'value' => 'native_and_strangers',
                        'name' => 'tab',
                        'label' => 'Özgələr və yerlilər',
                    ]],
                    'search_text' => 'native_and_strangers',
                ];
//                Log::emergency('SaveNativesAndStrangers: ' . json_encode($queue));
                SaveAdvanceSearchLogsJob::dispatch($queue)->onQueue('native_and_strangers')->onConnection('database')->delay(now()->addSeconds(20));
            }
            return response()->json($response);
        }
        $drop_table_sql = "DROP TABLE IF EXISTS radius_events.natives_strangers_result_$timestamp;";
        DB::connection('clickhouse_radius')->statement($drop_table_sql);
        return response()->json([
            'message' => 'Sorğu uğursuz oldu.'
        ], 500);
    }

    /**
     * @deprecated
     */
    protected function parsePolygon($coordinates): string
    {
        $polygonString = 'POLYGON((';
        foreach ($coordinates as $coordinate) {
            $polygonString .= $coordinate['lng'] . ' ' . $coordinate['lat'] . ',';
        }
        $polygonString = rtrim($polygonString, ',');
        $polygonString .= '))';
        return $polygonString;
    }

    public function getVehicleEntered(Request $request): JsonResponse
    {
//        request()->merge([
//            'work_name' => $request->input('work_name', $request->input('search_head_name', 'location dashboard'))
//        ]);


//        if (!$request->header('Dev-Key')) {
//            $logData = [
//                "description" => $request->input('carNumber') . " maşın nömrəsinə görə " . $request->input('from') . " - " . $request->input('to') . " tarixləri arasında  trayektoriya axtarışı etdi.",
//                "type" => "polygon"
//            ];
//            sendRequestLogToAudit($logData, "audit");
//        }


        $getVehicleEntered = $this->locationService->getVehicleEntered($request->all());

        $dataWithImages = [];
        $cameraPointers = [];

        if (!empty($getVehicleEntered)) {
            if (isset($getVehicleEntered['status']) && $getVehicleEntered['status'] == 404) {
                return $this->successResponse(404, 'response no', []);
            }
            if ($request->has('short_data')) {
                $dataWithImages = $this->preparingShortedData($getVehicleEntered);
            } else {
                foreach ($getVehicleEntered as $getVehicle) {
                    $cameraPointer = getCameraPointer($getVehicle['cameraName']);
                    $cameraPointers[] = [
                        'cameraPointer' => $cameraPointer,
                        'getVehicle' => $getVehicle
                    ];
                }

            }


            if (!$request->has('short_data')) {

                if ($request->has('has_paginate')){

                    $cameraPointers = collect($cameraPointers)->paginate($request->get('per_page', 10));;

                    $dataWithImages[] =  $cameraPointers->through(function ($item) {
                        $item['id'] = $item['getVehicle']['id'];
                        $item['vehicleNumber'] = $item['getVehicle']['vehicleNumber'];
                        $item['cameraNumber'] = $item['getVehicle']['cameraNumber'];
                        $item['cameraName'] = $item['getVehicle']['cameraName'];
                        $item['insertDate'] = $item['getVehicle']['insertDate'];
                        $item['image'] = $this->locationService->getVehicleLocationImage($item['getVehicle']['id'], $item['getVehicle']['vehicleNumber']);
                        $item['icon'] = config('app.url') . '/icon.png';
                        return $item;
                    });

                }else{

                    foreach ($cameraPointers as $cameraPointerData) {
                        $dataWithImages[] = [
                            "id" => $cameraPointerData['getVehicle']['id'],
                            "vehicleNumber" => $cameraPointerData['getVehicle']['vehicleNumber'],
                            "cameraNumber" => $cameraPointerData['getVehicle']['cameraNumber'],
                            "cameraName" => $cameraPointerData['getVehicle']['cameraName'],
                            "insertDate" => $cameraPointerData['getVehicle']['insertDate'],
                            "image" => $this->locationService->getVehicleLocationImage($cameraPointerData['getVehicle']['id'], $cameraPointerData['getVehicle']['vehicleNumber']),
                            'cameraPointer' => $cameraPointerData['cameraPointer'],
                            'icon' => config('app.url') . '/icon.png',
                        ];
                    }

                }
            }


        }
        return $this->successResponse(200, 'response ok', $dataWithImages);
    }

    private function preparingShortedData($getVehicleEntered): array
    {
        $dataWithImages = [];
        foreach ($getVehicleEntered as $getVehicle) {
            $cameraPointer = getCameraPointer($getVehicle['cameraName']);
            $dataWithImages[] = [
                "insertDate" => date("Y-m-d H:i:s", strtotime($getVehicle['insertDate'])),
                'latitude' => (float)$cameraPointer->latitude,
                'longitude' => (float)$cameraPointer->longitude,
                'coordinates_latitude' => (float)$cameraPointer->coordinates_latitude,
                'coordinates_longitude' => (float)$cameraPointer->coordinates_longitude,
            ];
        }
        return $dataWithImages;
    }

    public function getAzParking(Request $request): JsonResponse
    {
        /** @var User $userInstance */
//        $logData = [
//            "description" => $request->input('carNumber') . " maşın nömrəsinə görə  Azparking axtarışı etdi.",
//            "type" => "polygon"
//        ];
//        sendRequestLogToAudit($logData, "audit");

        if (config('app.env') == 'production' && config('app.url') != 'http://api.beein.loc') {
            $getAzParking = $this->locationService->getAzParking($request->all());
            $getAzParking = json_decode($getAzParking, true);
        } else {
            $mockAzParking = public_path('mocks/azparking.json');
            $getAzParking = json_decode(file_get_contents($mockAzParking), true);
        }

        return $this->successResponse(200, 'response ok', $getAzParking);
    }

    /**
     * @throws GuzzleException
     */
    public function lastLocationsByPhone(Request $request): JsonResponse
    {
        $start = now()->subMonths(6)->format('Y-m-d');
        $end = now()->format('Y-m-d');
        $mobil_number = substr($request->input('input'), -9);
        $mobil_number = '994' . $mobil_number;

        $res = Cache::remember($mobil_number . $start . $end, 3600, function () use ($mobil_number, $start, $end) {
            return $this->locationService->lastLocationsByPhone((int)$mobil_number, $start, $end);
        });

//        $res = $this->locationService->lastLocationsByPhone((int)$mobil_number, $start, $end);
        usort($res['data'], function ($a, $b) {
            return $b['_count'] <=> $a['_count'];
        });
        foreach ($res['data'] as $index => &$item) {
            $item['order'] = $index + 1;
        }
        return response()->json($res);
    }

    public function movementRoute(Request $request): JsonResponse
    {
        $getTravel = $this->getVehicleEntered($request);
        $getTravelArray = $getTravel->getData(true);
        //TODO: will fix
        if (config('app.env') == 'production' && false) {
            $getAzParking = $this->locationService->getAzParking($request->all());
            $getAzParking = json_decode($getAzParking, true);
        } else {
            $mockAzParking = public_path('mocks/azparking.json');
            $getAzParking = json_decode(file_get_contents($mockAzParking), true);
//            $mockTravel = public_path('mocks/travel.json');
//            $getTravelArray = json_decode(file_get_contents($mockTravel), true);
        }
        $dtoData = MovementRouteDTO::fromArray([
            'azparking-data' => $getAzParking,
            'din-api-result' => $getTravelArray
        ])->toArray();
        $responseData = [
            'dto-data' => $dtoData,
            'din-api-result' => $getTravelArray
        ];
        return $this->successResponse(200, 'response ok', $responseData);
    }
}
