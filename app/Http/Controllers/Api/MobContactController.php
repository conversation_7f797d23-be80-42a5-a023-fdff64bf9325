<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MobContact;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class MobContactController extends Controller
{

    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'nullable',
            'input' => 'nullable',
            'tags' => 'nullable'
        ]);

        $mobContact = MobContact::query()
            ->when($request->filled('name'), function ($query) use ($request) {
                $query->search('name', $request->get('name'));
            })->when($request->filled('input'), function ($query) use ($request) {
                $query->search('number', $request->get('input'));
            })->when($request->filled('tags'), function ($query) use ($request) {
                $query->search('tags', $request->get('tags'));
            })->paginate($request->per_page ?? 10);
        return response()->json($mobContact);

    }

}
