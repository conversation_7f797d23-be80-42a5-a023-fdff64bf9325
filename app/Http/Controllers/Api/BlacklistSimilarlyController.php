<?php

namespace App\Http\Controllers\Api;

use App\Models\Blacklist;
use App\Models\Camera;
use App\Services\AIService;
use App\Traits\ApiResponsible;
use App\Models\Object\Object_;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Services\BlacklistSimilarlyService;
use App\Http\Requests\SearchBlacklistSimilarlyRequest;

class BlacklistSimilarlyController extends Controller
{
    use ApiResponsible;

    /**
     * BlacklistController construct
     *
     * @param \App\Services\AIService $AIService
     * @param BlacklistSimilarlyService $blacklistSimilarlyService
     */
    public function __construct(
        public AIService $AIService,
        public BlacklistSimilarlyService $blacklistSimilarlyService
    ) {
    }

    /**
     * Get Blacklist By Params
     *
     * @param \App\Http\Requests\SearchBlacklistSimilarlyRequest $request
     * @return JsonResponse
     */
    public function index(SearchBlacklistSimilarlyRequest $request): JsonResponse
    {

        $objects = Object_::query()->when($request->input('object_types'), static function ($query, $objectTypes) {
            $query->whereIn('object_type_id', string_to_int_from_array($objectTypes));
        })->when($request->input('objects'), static function($query, $objects) {
            $query->whereIn('id', string_to_int_from_array($objects));
        })->get();


        $objectIds = $objects->pluck('id')->toArray();

        $cameras = Camera::query()->when($request->input('cameras'), static function($query, $cameraIds) {
            $query->whereIn('camera_id', string_to_int_from_array($cameraIds));
        })->when($objectIds, static function ($query, $objectIds) {
            $query->whereIn('object_id', string_to_int_from_array($objectIds));
        })->get();


        $cameraIds = $cameras->pluck('camera_id')->toArray();

        $data = $request->validated();

        $data['blacklist_ids'] = Blacklist::query()->where('status', true)->get()->pluck('id')->toArray();
        if (count($cameraIds) > 0) {
            $data['camera_ids'] = $cameraIds;
        }


        return $this->blacklistSimilarlyService
            ->paginate($data, $request->input('per_page', 10));
    }
}
