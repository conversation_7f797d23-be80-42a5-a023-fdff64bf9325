<?php

namespace App\Http\Controllers\Api;

use App\Traits\ApiResponsible;
use App\Services\ObjectService;
use Illuminate\Http\JsonResponse;
use App\Http\Requests\ObjectRequest;
use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\Model;
use App\Http\Requests\ObjectSearchRequest;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpFoundation\Response;

class ObjectController extends Controller
{
    use ApiResponsible;

    /**
     * @param ObjectService $objectService
     */
    public function __construct(
        protected ObjectService $objectService
    ) {

        $this->middleware('permission:objects:read', ['only' => ['index', 'search', 'show', 'list']]);
        $this->middleware('permission:objects:create', ['only' => ['store']]);
        $this->middleware('permission:objects:update', ['only' => ['update']]);
        $this->middleware('permission:objects:delete', ['only' => ['destroy']]);
    }

    /**
     * @OA\Get  (
     *      path="/objects/search/list",
     *      operationId="get-objects-search",
     *      tags={"Obyektlər"},
     *      summary="Obyektlərin axtarılması servisi",
     *      description="Obyektlərin axtarılması servisi",
     *      security={{ "bearerAuth": {} }},
     *     @OA\Parameter(
     *          name="object_type_ids[]",
     *          description="array of object type id",
     *          in="query",
     *          @OA\Schema(
     *              type="array",
     *              @OA\Items(type="enum", enum={1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21}),
     *              example={1,2}
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="q",
     *          description="Axtarılacaq kəlimə",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param \App\Http\Requests\ObjectSearchRequest $request
     * @return JsonResponse
     */
    public function search(ObjectSearchRequest $request): JsonResponse
    {
        $q = $request->input('q');

        $objects = $this->objectService->getAllByCondition($q, $request->all());
        return $this->successResponse(
            Response::HTTP_OK,
            null,
            $objects
        );
    }

    /**
     * Retrieve objects by name
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getByName(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
            ]);
            $objects = $this->objectService->getByName($validated['name']);
            if ($objects->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No objects found with the given name.',
                    'data' => []
                ]);
            }
            return $objects;
        }
        catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving objects.',
                'error' => config('app.debug') ? $e->getMessage() : 'Server error'
            ], 500);
        }
    }

    /**
     * @OA\Get  (
     *      path="/objects",
     *      operationId="get-objects-list",
     *      tags={"Obyektlər"},
     *      summary="Obyekt siyahısını əldə olunması servisi",
     *      description="Obyekt siyahısını əldə olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="per_page",
     *          description="Səhifə başına düşən item sayı",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="page",
     *          description="Səhifə",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @return JsonResponse
     */
    public function index(): JsonResponse
    {
        return $this->objectService->paginate(request()->input('per_page') ?? 10);
    }

    public function show($id): JsonResponse
    {

        return $this->objectService->show($id);
    }

    /**
     * @OA\Post   (
     *      path="/objects",
     *      operationId="create-object",
     *      tags={"Obyektlər"},
     *      summary="Yeni Obyekt əlavə olunması servisi",
     *      description="Yeni Obyekt əlavə olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="object_type_id",
     *          description="Obyektin tipi id nömrəsi",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="name",
     *          description="Obyektin adı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="address",
     *          description="Obyektin yerləşdiyi ünvan",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="active",
     *          description="Obyektin statusu",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="lat",
     *          description="Obyektin xəritə üzərindəki latitude nöqtəsi",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="lng",
     *          description="Obyektin xəritə üzərindəki longitude nöqtəsi",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param ObjectRequest $request
     * @return Model
     */
    public function store(ObjectRequest $request): Model
    {
        return $this->objectService->save($request->validated());
    }

    /**
     * @OA\Put   (
     *      path="/objects/{object_id}",
     *      operationId="update-object",
     *      tags={"Obyektlər"},
     *      summary="Obyekt üzərində düzəliş olunması servisi",
     *      description="Obyekt üzərində düzəliş olunması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="object_id",
     *          description="Object ID",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="object_type_id",
     *          description="Obyektin tipi id nömrəsi",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="name",
     *          description="Obyektin adı",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="address",
     *          description="Obyektin yerləşdiyi ünvan",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="active",
     *          description="Obyektin statusu",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="lat",
     *          description="Obyektin xəritə üzərindəki latitude nöqtəsi",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="lng",
     *          description="Obyektin xəritə üzərindəki longitude nöqtəsi",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param ObjectRequest $request
     * @param int $id
     * @return JsonResponse|void
     */
    public function update(ObjectRequest $request, int $id)
    {

        if ($this->objectService->update($request->validated(), $id)) {
            return response()->json([
                'status' => true
            ]);
        }

    }

    /**
     * @OA\Delete   (
     *      path="/objects/{object_id}",
     *      operationId="delete-object",
     *      tags={"Obyektlər"},
     *      summary="Obyektin silinməsi servisi",
     *      description="Obyektin silinməsi servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter(
     *          name="object_id",
     *          description="Object ID",
     *          example=1,
     *          required=true,
     *          in="path",
     *          @OA\Schema(
     *              type="integer"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        return $this->objectService->findAndDelete($id);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(): JsonResponse
    {
        return $this->objectService->getLists();
    }
}
