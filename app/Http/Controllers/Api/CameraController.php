<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\CameraArchiveRequest;
use Illuminate\Http\Request;
use App\Traits\ApiResponsible;
use App\Services\CameraService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\CameraRequest;
use Illuminate\Database\Eloquent\Model;
use OpenApi\Annotations as OA;

class CameraController extends Controller
{
    use ApiResponsible;

    /**
     * @param CameraService $cameraService
     */
    public function __construct(
        protected CameraService $cameraService,
    ) {
        $this->middleware('permission:video-archive',  ['only' => ['getVideoArchiveCameras']]);
        $this->middleware('permission:live-stream',    ['only' => ['getLiveStreamCameras']]);
        $this->middleware('permission:cameras:read',   ['only' => ['index', 'show', 'list']]);
        $this->middleware('permission:cameras:create', ['only' => ['store']]);
        $this->middleware('permission:cameras:update', ['only' => ['update']]);
        $this->middleware('permission:cameras:delete', ['only' => ['destroy']]);
    }
    public function index(Request $request): JsonResponse
    {
        $logData = [
            "description" => "Kameralar siyahısını açdı",
            "type" => "camera"
        ];
        sendRequestLogToAudit($logData, "audit");
        return $this->cameraService->paginate($request->all(), $request->input('per_page', 10));
    }
    public function getLiveStreamCameras(Request $request): JsonResponse
    {
        $logData = [
            "description" => "Canlı yayım kameralar siyahısını açdı",
            "type" => "camera"
        ];
        sendRequestLogToAudit($logData, "audit");
        return $this->cameraService->paginate($request->all(), $request->input('per_page', 10));
    }
    public function getVideoArchiveCameras(Request $request): JsonResponse
    {
        $logData = [
            "description" => "Video arxiv kameralar siyahısını açdı",
            "type" => "camera"
        ];
        sendRequestLogToAudit($logData, "audit");
        return $this->cameraService->paginate($request->all(), $request->input('per_page', 10));
    }

    public function store(CameraRequest $request): Model
    {
        $data = $request->validated();

        $data['camera_id'] = $this->cameraService->generateCameraId();

        return $this->cameraService->save($data);
    }

    public function show(int $cameraId): JsonResponse
    {
        return $this->cameraService->show($cameraId);
    }

    public function update(CameraRequest $request, int $id): Model
    {
        return $this->cameraService->update($request->validated(), $id);
    }

    public function destroy(int $id): JsonResponse
    {
        return $this->cameraService->findAndDelete($id);
    }

    public function list(): JsonResponse
    {
        $logData = [
            "description" => "Kameralar siyahısını açdı",
            "type" => "camera"
        ];
        sendRequestLogToAudit($logData, "audit");
        return $this->cameraService->getLists();
    }

    public function getCameraList(Request $request): JsonResponse
    {
        $logData = [
            "description" => "Kameralar siyahısını açdı",
            "type" => "camera"
        ];
        sendRequestLogToAudit($logData, "audit");
        return $this->cameraService->filerAndPaginate($request->all(), request('per_page', 10));
    }

    public function category(int $cameraId): JsonResponse
    {
        return response()->json(['data' => $this->cameraService->getCategories($cameraId)]);
    }

    public function archive(CameraArchiveRequest $request): JsonResponse
    {
        return $this->cameraService->getArchiveByCamera($request);
    }

    public function getByName(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
            ]);
            $objects = $this->cameraService->getByName($validated['name']);
            if ($objects->isEmpty()) {
                return response()->json([
                    'success' => true,
                    'message' => 'No cameras found with the given name.',
                    'data' => []
                ]);
            }
            return $objects;
        }
        catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving objects.',
                'error' => config('app.debug') ? $e->getMessage() : 'Server error'
            ], 500);
        }
    }
}
