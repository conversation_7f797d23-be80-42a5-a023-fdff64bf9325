<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;        // <<-- Düzgün Facade import
use App\Exports\ArrayExport;

class ExportController extends Controller
{
    /**
     * Universal Excel export (internal JWT-authenticated route çağırır).
     */
    public function universalExport(Request $request)
    {
        $request->validate([
            'path'   => 'required|string',
            'method' => 'required|string',
        ]);
    
        try {
            // 1) Authorization header hazırlığı
            $headers = [];
            if ($token = $request->bearerToken()) {
                $headers['Authorization'] = 'Bearer ' . $token;
            }
    
            // 2) URL-decode path
            $decodedPath = urldecode($request->input('path'));
            $method      = strtoupper($request->input('method'));
    
            // 3) parse URL və təmiz path
            $url     = parse_url($decodedPath);
            $rawPath = $url['path'] ?? $decodedPath;
            $path    = preg_replace('#/+#', '/', $rawPath);
    
            // 4) query string → array
            parse_str($url['query'] ?? '', $query);
    
            // 5) JSON sahələri üçün təhlükəsiz “ikiqat” decode
            foreach (['filters', 'filterModel', 'sortModel'] as $field) {
                if (! isset($query[$field])) {
                    continue;
                }
                $value = $query[$field];
    
                // Yalnız string olduğu müddətcə decode etməyə çalış
                while (is_string($value)) {
                    $decoded = json_decode($value, true);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $value = $decoded;
                    } else {
                        // Daha decode etmək mümkün deyilsə, çıx dövrdən
                        break;
                    }
                }
    
                // Nəticə yalnız array olarsa saxla; yoxsa boş array ver
                $query[$field] = is_array($value) ? $value : [];
            }
    
            // 6) Daxili request yarat
            $internalRequest = Request::create(
                $path,
                $method,
                $method === 'GET' ? $query : [],    // GET üçün query parameters
                [], [],                              // cookies & files
                array_merge($request->server->all(), [
                    'HTTP_AUTHORIZATION' => $headers['Authorization'] ?? '',
                ]),
                $method === 'GET' ? null : json_encode($query)  // POST/PUT üçün JSON body
            );
            if ($method !== 'GET') {
                $internalRequest->headers->set('Content-Type', 'application/json');
            }
            $internalRequest->setUserResolver(fn() => $request->user('api'));
    
            // 7) Sorğunu icra et
            $response = app()->handle($internalRequest);
    
            if ($response->getStatusCode() !== 200) {
                return response()->json([
                    'error'   => "Internal request status: {$response->getStatusCode()}",
                    'message' => $response->getContent(),
                ], $response->getStatusCode());
            }
    
            // 8) Cevabı Excel-ə çevir
            $payload = json_decode($response->getContent(), true);
            $data    = $payload['data'] ?? $payload;
            return Excel::download(new ArrayExport($data), 'export.xlsx');
    
        } catch (\Throwable $e) {
            return response()->json([
                'error' => 'Exception: ' . $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ], 500);
        }
    }
    /**
     * Mövcud exportPolygonData metodun da işlək qalır.
     */
    public function exportPolygonData(Request $request)
    {
        $selectedData = $request->selected;
        $title        = $request->title ?? 'Polygon';

        if (empty($selectedData)) {
            return response()->json([
                'status'  => 'error',
                'message' => 'No data found for Export'
            ], 422);
        }

        $fileExtensions = [
            'XLSX'  => 'xlsx',
            'HTML'  => 'html',
            'DOMPDF'=> 'pdf',
        ];

        $exportFormats = [
            'XLSX'  => Excel::XLSX,
            'HTML'  => Excel::HTML,
            'DOMPDF'=> Excel::DOMPDF,
        ];

        $headings = array_keys($selectedData[0]);

        return (new \App\Exports\Export($headings))
            ->setQuery(fn() => $selectedData)
            ->download(
                "{$title}-".time().'.'.$fileExtensions['XLSX'],
                $exportFormats['XLSX']
            );
    }
}
