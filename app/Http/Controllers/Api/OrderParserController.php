<?php

namespace App\Http\Controllers\Api;
ini_set('max_execution_time', -1);
ini_set('memory_limit', '1024M');

use App\Http\Controllers\Controller;
use App\Models\FoodOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class OrderParserController extends Controller
{

    public function __invoke()
    {


        $chunked_files = glob(public_path('order/chunk/*.csv'));

        foreach ($chunked_files as $key => $file){

            echo('Count ' . $key . ' of ' . count($chunked_files)) ." <br>";

        }
        die;

//        $channel_name = 'PM2';
//
//        $inputFile = public_path('order/' . $channel_name . '.csv'); // Replace with your CSV file's path
//        $outputDirectory = public_path('order/chunk2'); // Replace with the directory where split files should be saved
//        $chunkSize = 500; // The maximum number of lines in each split file
//
//        splitCsvFileWithHeaderAndData($inputFile, $outputDirectory, $chunkSize);
//        die('1');


       // $channel_name = 'PJA2';
        $channel_name = 'chunk/chunk19';
        $channel = 'PJA';
        $orders = csvToArray(public_path('order/' . $channel_name . '.csv'), ';');
        $orders = collect($orders)->map(function ($item) {
            return collect($item)->mapWithKeys(function ($value, $key) {
                return [Str::slug($key) => $value];
            });
        })->toArray();

        echo count($orders);

        DB::beginTransaction();
        try {
            foreach ($orders as $order) {
                if (isset($order['last-visit']) && $order['last-visit'] != '') {
                    if (str_contains($order['last-visit'], '/')) {
                        $order['order-date'] = Carbon::createFromFormat('d/m/Y H:i', $order['last-visit'])->format('Y-m-d H:i:s');
                    } else {
                        if (str_contains($order['last-visit'], ' ')) {
                            $order['order-date'] =  Carbon::createFromFormat('d.m.Y H:i', $order['last-visit'])->format('Y-m-d H:i:s');
                        } else {
                            $order['order-date'] =  Carbon::createFromFormat('d.m.Y', $order['last-visit'])->format('Y-m-d H:i:s');
                        }
                    }
                }

                $orderFood = FoodOrder::query()->where('channel', $channel);
                if (isset($order['last-visit']) && $order['last-visit'] != '') {
                    $orderFood->where('order_date', $order['order-date']);
                }
                $orderFood = $orderFood->where('phone', $order['phone'])->exists();
                if (!$orderFood) {
                    $phones = explode(',', $order['phone']);
                    foreach ($phones as $phone) {
                        $orderx = [
                            'channel' => $channel,
                            'name' => $order['name'] ?? null .' '. $order['surname'] ?? null,
                            'email' => $order['email'] ?? null,
                            'address' => $order['address'] ?? null,
                            'phone' => trim($phone) ?? null,
                            'data' => json_encode($order),
                        ];


                        if (isset($order['last-visit']) && $order['last-visit'] != '') {
                            if (str_contains($order['last-visit'], '/')) {
                                $order['order-date'] = Carbon::createFromFormat('d/m/Y H:i', $order['last-visit'])->format('Y-m-d H:i:s');
                            } else {
                                if (str_contains($order['last-visit'], ' ')) {
                                    $order['order_date'] =  Carbon::createFromFormat('d.m.Y H:i', $order['last-visit'])->format('Y-m-d H:i:s');
                                } else {
                                    $order['order_date'] =  Carbon::createFromFormat('d.m.Y', $order['last-visit'])->format('Y-m-d H:i:s');
                                }
                            }
                        }
                        FoodOrder::create($orderx);
                    }
                }
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            dd($exception);
        }

        echo 'done';

    }

}
