<?php

namespace App\Http\Controllers\Api\Company;

use App\Http\Controllers\Controller;
use App\Http\Resources\Company\DetailInfoResource;
use App\Http\Resources\Company\TenderInfoResource;
use App\Http\Resources\Company\VoenInfoResource;
use App\Models\VoenAwarded;
use App\Models\VoenInfo;
use App\Services\EHDIS\VoenListService;
use Illuminate\Http\Request;

class CompanyController extends Controller
{

    public function detailInfo($voen)
    {
        try {
            $company = VoenListService::run($voen);
            return new DetailInfoResource($company);
    
        } catch (\Exception $e) {
            return response()->json(['message' => 'EHDIS-də sorğu xəta verdi'], 500);
        }
    }

    public function voenInfo($voen)
    {
        try {
            $company = VoenInfo::where('voen', $voen)->first();
    
            if (!$company) {
                return response()->json(['message' => 'Məlumat tapılmadı'], 404);
            }
    
            return new VoenInfoResource($company);
    
        } catch (\Exception $e) {
            return response()->json(['message' => 'Xəta baş verdi'], 500);
        }
    }

    public function tenderInfo($voen)
    {
        try {
            $companies = VoenAwarded::where('VOEN', $voen)->get();
    
            if ($companies->isEmpty()) {
                return response()->json(['message' => 'Tender məlumatı tapılmadı'], 404);
            }
    
            return TenderInfoResource::collection($companies);
    
        } catch (\Exception $e) {
            return response()->json(['message' => 'Sorğu zamanı xəta baş verdi'], 500);
        }
    }

    public function getCompaniesByRegistrationAddress($voen)
    {
        try {
            $companies = VoenInfo::join('voen_info as v2', 'voen_info.address', '=', 'v2.address')
    ->where('voen_info.voen', $voen) 
    ->where('v2.voen', '!=', $voen) 
    ->select('v2.*')
    ->get();

    
            if ($companies->isEmpty()) {
                return response()->json(['message' => 'Məlumatı tapılmadı'], 404);
            }
    
            return response()->json($companies);
    
        } catch (\Exception $e) {
            return response()->json(['message' => 'Sorğu zamanı xəta baş verdi'], 500);
        }
    }

    public function getCompaniesByDirector($voen)
    {
        try {
            $companies = VoenInfo::join('voen_info as v2', 'voen_info.director', '=', 'v2.director')
            ->where('voen_info.voen', $voen) 
            ->where('v2.voen', '!=', $voen) 
            ->select('v2.*')
            ->get();
    
            if ($companies->isEmpty()) {
                return response()->json(['message' => 'Məlumatı tapılmadı'], 404);
            }
    
            return response()->json($companies);
    
        } catch (\Exception $e) {
            return response()->json(['message' => 'Sorğu zamanı xəta baş verdi'], 500);
        }
    }
}
