<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\GrayListRequest;
use App\Http\Requests\GrayListUpdateRequest;
use App\Http\Resources\GrayListResource;
use App\Models\GrayList;
use App\Models\Person;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Response;
use App\Repository\PersonRepository;
use App\Services\PersonService;
use Illuminate\Support\Facades\DB;

class GrayListController extends Controller
{
    protected GrayList $model;

    public function __construct(GrayList $model)
    {
        $this->model = $model;

        $this->middleware('permission:graylist:read', ['only' => ['index', 'show']]);
        $this->middleware('permission:graylist:create', ['only' => ['store']]);
        $this->middleware('permission:graylist:update', ['only' => ['update', 'updateStatus']]);
        $this->middleware('permission:graylist:delete', ['only' => ['destroy']]);
    }

    /**
     * Display a listing of the resource.
     *
     * @return AnonymousResourceCollection
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $data = $this->model->paginate($request->per_page ?? 24);
        return GrayListResource::collection($data);
    }


    /**
     * Store a newly created resource in storage.
     *
     * @param GrayListRequest $request
     * @return JsonResponse
     */
    public function store(GrayListRequest $request): JsonResponse
    {

        $person = $this->model->where('pin', strtoupper($request->pin))->exists();
        if ($person) {
            return response()->json([
                'status' => 422,
                'message' => 'Fin siyahıda mövcuddur',
                'data' => [],
            ], 422);
        }
        $person_info = $this->personInfo($request->pin);
        if (!$person_info) {
            return response()->json([
                'status' => 422,
                'message' => 'Bu fində şəxs tapılmadı',
                'data' => [],
            ],422);
        }

        try {
            DB::beginTransaction();
            $new = new $this->model;
            $new->pin = strtoupper($request->pin);

            $new->name = $person_info['name'] ?? '';
            $new->surname = $person_info['surname'] ?? '';
            $new->father_name = $person_info['father_name'] ?? '';
            $new->birthdate = $person_info['birthdate'] ?? '';
            $new->image = $person_info['image'] ?? '';
            $new->note = $request->note;
            $new->save();
            DB::commit();

        }catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'status' => 422,
                'message' => 'Fin siyahıda mövcuddur',
                'data' => '',
            ], 422);
        }

        return response()->json([
            'status' => 201,
            'message' => 'Created data',
            'data' => new GrayListResource($new)
        ], 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return JsonResponse
     */
    public function show($id)
    {
        $new = $this->model->where('id', $id)->first();

        // $service = new PersonDataService(new PersonDataRepository(new PersonData()));
        // $person = $service->getPersonInfo($new->pin);

        // return $person;

        if ($new) {
            return response()->json([
                'status' => 200,
                'message' => 'Success',
                'data' => new GrayListResource($new)
            ], 200);
        } else {
            return response()->json([
                'status' => 404,
                'message' => 'Not Found',
                'data' => []
            ], 404);
        }
    }


    /**
     * Update the specified resource in storage.
     *
     * @param GrayListUpdateRequest $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(GrayListUpdateRequest $request, $id)
    {

        $new = $this->model->where('id', $id)->first();
        if ($new) {
            $person_info = $this->personInfo($request->pin);
            if (!$person_info) {
                return response()->json([
                    'status' => 404,
                    'message' => 'Bu fində şəxs tapılmadı',
                    'data' => '',
                ], 404);
            }
            $new->pin = strtoupper($request->pin);

            $new->name = $person_info['name'] ?? '';
            $new->surname = $person_info['surname'] ?? '';
            $new->father_name = $person_info['father_name'] ?? '';
            $new->birthdate = $person_info['birthdate'] ?? '';
            $new->image = $person_info['image'] ?? '';
            $new->note = $request->note;
            $new->save();

            return response()->json([
                'status' => 200,
                'message' => 'Updated data',
                'data' => new GrayListResource($new)
            ], 200);
        } else {
            return response()->json([
                'status' => 404,
                'message' => 'Məlumat Tapılmadı',
                'data' => []
            ], 404);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return JsonResponse
     */
    public function destroy($id)
    {
        $new = $this->model->where('id', $id)->first();
        if ($new) {
            $new->delete();
            return response()->json([
                'status' => 200,
                'message' => 'Deleted data',
                'data' => []
            ], 200);
        } else {
            return response()->json([
                'status' => 404,
                'message' => 'Not Found',
                'data' => []
            ], 404);
        }
    }

    public function updateStatus(Request $request, int $id)
    {
        if ($graylist = $this->model->find($id)) {

            $graylist->status = $request->status;
            $graylist->save();

            return response()->json([
                'status' => 200,
                'message' => 'Update status',
                'data' => new GrayListResource($graylist)
            ], 200);
        }

        return $this->errorResponse(
            Response::HTTP_NOT_FOUND,
            '404 not found'
        );
    }

    private function personInfo($pin): array
    {
        try {
            $person_service = new PersonService(new PersonRepository(new Person()));
            return $person_service->getGallery($pin);
        }catch (\Exception $e) {
            return [];
        }
    }
}
