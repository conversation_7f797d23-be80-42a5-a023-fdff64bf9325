<?php

namespace App\Http\Controllers\Api;

use App\Models\Blacklist;
use App\Services\AIService;
use App\Services\Neo4J\Neo4JUpdateBlackListService;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use App\Services\BlacklistService;
use App\Http\Controllers\Controller;
use Illuminate\Database\Eloquent\Model;
use App\Http\Requests\BlacklistRequest;
use App\Services\BlacklistSimilarlyService;
use App\Http\Requests\UpdateBlacklistRequest;
use App\Http\Requests\SearchBlacklistRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Resources\BlackList\ShowBlacklistResource;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class BlacklistController extends Controller
{
    use ApiResponsible;

    /**
     * BlacklistController construct
     *
     * @param AIService $AIService
     * @param BlacklistService $blacklistService
     * @param BlacklistSimilarlyService $blacklistSimilarlyService
     * @param Neo4JUpdateBlackListService $neo4JUpdateBlackListService
     */
    public function __construct(
        public AIService                   $AIService,
        public BlacklistService            $blacklistService,
        public BlacklistSimilarlyService   $blacklistSimilarlyService,
        public Neo4JUpdateBlackListService $neo4JUpdateBlackListService
    )
    {
    }

    public function index(SearchBlacklistRequest $request): JsonResponse
    {
        $data = $request->validated();

        return $this->blacklistService
            ->paginate($data, $request->input('per_page', 10));
    }

    public function store(BlacklistRequest $request): Model|JsonResponse
    {
        $data = $request->validated();

        $base64TXT = $data['photo'];

        if (!Storage::disk('public')->exists('blacklists')) {
            Storage::makeDirectory('blacklists');
        }

        // Generate file name
        $filePath = filePathGenerator(
            'blacklists/',
            'blk_',
            '.png'
        );

        // Save Image to Local
        base64ImageSave($filePath, $base64TXT);

        // Save Local file to S3
        localFileSaveS3($filePath);

        $data['photo'] = implode("/", [
            config('servers.s3_beein'),
            env('AWS_BUCKET'),
            $filePath
        ]);

        /**
         * @var Blacklist $blacklist
         */
        $blacklist = $this->blacklistService->save($data);

        $response = $this->AIService->create([
            'blacklist_id' => $blacklist->id,
            'name' => $blacklist->name,
            'surname' => $blacklist->surname,
            'photo' => $base64TXT
        ]);

//        return $this->successResponse(
//            200,
//            "Created",
//            $blacklist->toArray()
//        );

        if ($response['status']) {
            return $this->successResponse(
                $response['code'],
                $response['message'] ?? "",
                $blacklist->toArray()
            );
        }

        $this->blacklistService->findAndDelete($blacklist->id);

        return $this->errorResponse($response['code'], $response['message']);
    }

    public function show(int $id): JsonResponse
    {
        /**
         * @var $blacklist Blacklist
         */
        $blacklist = $this->blacklistService
            ->find($id);

        $this->blacklistSimilarlyService
            ->appendCameraNameToCollection($blacklist->similarities);

        if (!$blacklist) {
            return $this->errorResponse(Response::HTTP_NOT_FOUND, '');
        }

        return ShowBlacklistResource::make($blacklist)->response();
    }

    public function update(UpdateBlacklistRequest $request, int $id): JsonResponse
    {
        /**
         * @var $blacklist Blacklist
         */
        if ($blacklist = $this->blacklistService->find($id)) {

            $response = $this->AIService->remove($id);

            if ($response['status']) {

                $data = $request->validated();

                $base64TXT = $data['photo'];

                // Generate file name
                $filePath = filePathGenerator(
                    'blacklists/',
                    'blk_',
                    '.png'
                );

                // Save Image to Local
                base64ImageSave($filePath, $base64TXT);

                // Save Local file to S3
                localFileSaveS3($filePath);

                $data['photo'] = implode("/", [
                    config('servers.s3_beein'),
                    env('AWS_BUCKET'),
                    $filePath
                ]);

                if ($blacklist->update($data)) {

                    $response = $this->AIService->create([
                        'blacklist_id' => $blacklist->id,
                        'name' => $blacklist->name,
                        'surname' => $blacklist->surname,
                        'photo' => $base64TXT
                    ]);

                    if ($response) {

                        if ($blacklist->pin) {
                            // $this->neo4JUpdateBlackListService->execute($blacklist->pin, true);
                        }

                        return $this->successResponse(
                            200,
                            "Updated",
                            $blacklist->toArray()
                        );
                    }

                    return $this->errorResponse(
                        404,
                        "error"
                    );
                }

                return $this->errorResponse(
                    Response::HTTP_BAD_REQUEST,
                    'Bad Request'
                );
            }

            return $this->errorResponse(
                $response['code'],
                $response['message']
            );
        }

        return $this->errorResponse(
            Response::HTTP_NOT_FOUND,
            '404 not found'
        );
    }

    public function destroy(int $id): JsonResponse
    {
        $response = $this->AIService->remove($id);

        if ($response['status']) {

            $person = $this->blacklistService
                ->find($id);

            if ($person->pin) {
                //$this->neo4JUpdateBlackListService->execute($person->pin, false);
            }

            $this->blacklistService->findAndDelete($id);

            Http::delete(config('servers.gpu_api_single') . 'blacklist/delete', [
                'blacklist_id' => $id
            ]);

            return $this->successResponse(
                200,
                $response['message'] ?? 'Deleted'
            );
        }

        return $this->errorResponse($response['code'], $response['message'] ?? 'Deleted');
    }

    public function updateStatus(Request $request, int $id): JsonResponse
    {
        /**
         * @var $blacklist Blacklist
         */
        if ($blacklist = $this->blacklistService->find($id)) {

            $blacklist->status = $request->status;
            $blacklist->save();

            return $this->successResponse(
                '201',
                'Status yeniləndi',
                $blacklist->toArray()
            );
        }

        return $this->errorResponse(
            Response::HTTP_NOT_FOUND,
            '404 not found'
        );
    }


}
