<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Person;
use App\Services\ApiStatusService;
use App\Services\EhdisStatusService;
use App\Services\ServerStatusService;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ServerStatusController extends Controller
{
    use ApiResponsible;

    public function __construct(protected ServerStatusService $serverStatusService,
                                protected ApiStatusService $apiStatusService,
                                protected EhdisStatusService $ehdisStatusService,
    )
    {

        $this->middleware('permission:server-status:servers', ['only' => ['servers', 'ehdisStatus']]);
        $this->middleware('permission:server-status:api-list', ['only' => ['apiStatus']]);
    }

    public function servers(): JsonResponse
    {
        return response()->json([
            'status' => '200',
            'msg' => 'success',
            'data' => $this->serverStatusService->getALLStatuses()
        ]);
    }

    public function apiStatus(): JsonResponse
    {
        return response()->json([
            'status' => '200',
            'msg' => 'success',
            'data' => $this->apiStatusService->getALLStatuses()
        ]);
    }


    public function ehdisStatus(): JsonResponse
    {
        return response()->json([
            'status' => '200',
            'msg' => 'success',
            'data' => $this->ehdisStatusService->getALLStatuses()
        ]);
    }

}
