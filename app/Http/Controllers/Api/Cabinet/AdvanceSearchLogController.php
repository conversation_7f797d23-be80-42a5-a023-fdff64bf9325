<?php

namespace App\Http\Controllers\Api\Cabinet;

use App\Exports\Export;
use App\Http\Controllers\Controller;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Models\User;
use App\Services\AdvanceSearchSaverService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class AdvanceSearchLogController extends Controller
{

    protected AdvanceSearchSaverService $filterService;

    public function __construct(AdvanceSearchSaverService $filterService)
    {
        $this->filterService = $filterService;
    }

    public function index(): JsonResponse
    {
        $before = BeforeAdvancedSearchLog::query()
            ->with(['user:id,name,surname,email'])
            //->has('advancedSearchLogs')
            ->where('status', '1')
            ->when(request('search_text'), function ($query) {
                $query->where('search_text', 'like', '%' . request('search_text') . '%');
            })
            ->where('user_id', auth('api')->id())
            ->orderBy('created_at', 'DESC')
            ->paginate(request('per_page', 10));

        $beforeX = $before->through(function ($item) {
            $item->user->makeHidden(['permissions', 'roles', 'gray_list', 'tester_list', 'profile_view', 'root_user']);
            $item->makeHidden('pins');
            return $item;
        });

        return response()->json([
            'status' => 'Success',
            'data' => $beforeX,
            'code' => 200,
            'message' => 'response ok'
        ]);

    }

    public function show(Request $request, $parent_id = null)
    {


        $request->validate([
            'merge_parent_id' => 'nullable|array|min:1|max:4',
            'merge_parent_id.*' => 'integer',
        ], [
            'merge_parent_id.*.integer' => 'Kəsişməli id-lər integer olmalıdır',
            'merge_parent_id.min' => 'Kəsişməli id-lər minimum 1 ədəd olmalıdır',
            'merge_parent_id.max' => 'Kəsişməli id-lər maksimum 4 ədəd olmalıdır',
        ]);

        $filterModel = $request->all();

        $merged_pins = [];
        if ($request->filled('merge_parent_id') && ($request->post('type') == 'person')) {
            $merge_parent_id = $request->post('merge_parent_id', []);
            foreach ($merge_parent_id as $key => $value) {
                $merge_parent_id[$key] = (int)$value;
                if ($parent_id == $value) {
                    unset($merge_parent_id[$key]);
                }
            }


            $model = AdvancedSearchLog::query();
            $merge_query = $model->select('response.pin')
                ->where('user_id', auth('api')->id())
                ->whereIn('parent_id', $merge_parent_id)
                ->where('type', (string)$request->post('type', 'person'));
            $merged_pins = $merge_query->get()->pluck('response.pin')->toArray();
            $merged_pins = array_unique(array_diff_assoc($merged_pins, array_unique($merged_pins)));

        }


        $model = AdvancedSearchLog::query();
        $query = $model->select('*')
            ->where('user_id', auth('api')->id())
            ->where('type', (string)$request->get('type', 'person'));

        if ($request->filled('merge_parent_id') && ($request->post('type') == 'person')) {
            $query->whereIn('response.pin', $merged_pins);
        } else {
            $query->where('parent_id', (int)$parent_id);
        }


        if ($request->filled('simple')) {
            $this->filterService->applySimpleFilters($query, $filterModel);
        } else {
            $this->filterService->applyFilters($query, $filterModel);
        }

        if ($request->filled('merge_parent_id')) {
            $data = collect($query->get());
            $data = $data->unique('response.pin');
            $data = $data->paginate($request->get('per_page', 10));
        } else {
            $data = $query->paginate($request->get('per_page', 10));
        }

        $search_tab = BeforeAdvancedSearchLog::query()->where('id', $parent_id)->first()?->search_tab ?? "";
        return response()->json([
            'status' => 'Success',
            'search_tab' => $search_tab,
            'data' => $data,
            'code' => 200,
            'message' => 'response ok'
        ]);

    }


    public function excelExport(Request $request, int $parent_id = null): Response|BinaryFileResponse|JsonResponse
    {


        $request->validate([
            'merge_parent_id' => 'nullable|array|min:1|max:4',
            'merge_parent_id.*' => 'integer',
        ], [
            'merge_parent_id.*.integer' => 'Kəsişməli id-lər integer olmalıdır',
            'merge_parent_id.min' => 'Kəsişməli id-lər minimum 1 ədəd olmalıdır',
            'merge_parent_id.max' => 'Kəsişməli id-lər maksimum 4 ədəd olmalıdır',
        ]);


        $filterModel = $request->all();


        $merged_pins = [];
        if ($request->filled('merge_parent_id') && ($request->post('type') == 'person')) {
            $merge_parent_id = $request->post('merge_parent_id', []);
            foreach ($merge_parent_id as $key => $value) {
                $merge_parent_id[$key] = (int)$value;
                if ($parent_id == $value) {
                    unset($merge_parent_id[$key]);
                }
            }
            $merge_query = AdvancedSearchLog::query()->select('response.pin')
                ->where('user_id', auth('api')->id())
                ->whereIn('parent_id', $merge_parent_id)
                ->where('type', (string)$request->post('type', 'person'));
            $merged_pins = $merge_query->get()->pluck('response.pin')->toArray();
        }

        $selected_id = [];
        if ($request->filled('selected_id')) {
            $selected_id = $request->post('selected_id', []);
            foreach ($selected_id as $key => $value) {
                $selected_id[$key] = (string)$value;
            }
        }


        $query = AdvancedSearchLog::query()->select('*')
          ->where('user_id', auth('api')->id())
          ->where('type', (string)$request->get('type', 'person'));

        if (!empty($selected_id)) {
            $query->whereIn('_id', $selected_id);
        }

        if ($request->filled('merge_parent_id') && ($request->post('type') == 'person')) {
            $query->whereIn('response.pin', $merged_pins);
        } else {
            $query->where('parent_id', (int)$parent_id);
        }

        unset($filterModel['type']);

        if ($request->filled('simple')) {
            $this->filterService->applySimpleFilters($query, $filterModel);
        } else {
            $this->filterService->applyFilters($query, $filterModel);
        }

        $query->orderBy('created_at', 'DESC');

        $data = $query->get();

        $persons = collect($data)->map(function ($item) {
            return $item;
        });


        if (!in_array($request->post('export_type', 'XLSX'), ['XLSX', 'HTML', 'DOMPDF'])) {
            return response()->json(['message' => 'Please , use correct type.'], 422);
        }

        $type = [
            'XLSX' => 'xlsx',
            'HTML' => 'html',
            'DOMPDF' => 'pdf'
        ];

        $type_type = [
            'XLSX' => Excel::XLSX,
            'HTML' => Excel::HTML,
            'DOMPDF' => Excel::MPDF
        ];

        foreach ($persons as $key => $person) {
            $persons[$key] = $person['response'];
        }

        if ($persons instanceof Collection) {
            $persons = $persons->toArray();

            if (count($persons) == 0) {
                return response()->json(['message' => 'No data found'], 422);
            }

            $head_tags = array_keys($persons[0]);
        } else {
            $head_tags = $persons->first()->getAttributes();

            $head_tags = array_keys($head_tags);
            $head_tags = collect($head_tags)->map(function ($item) {
                return ucfirst(str_ireplace('_', ' ', $item));
            })->toArray();
        }


        return (new Export($head_tags))
            ->setQuery(function () use ($persons) {
                return $persons;
            })
            ->download(
                time() . '.' . $type[$request->post('export_type', 'XLSX')],
                $type_type[$request->post('export_type', 'XLSX')]
            );
    }


}
