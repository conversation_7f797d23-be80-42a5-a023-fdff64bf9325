<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\SearchCustomsRequest;
use App\Http\Resources\ForeignCitizenshipCountriesResource;
use App\Http\Resources\PrecinctListResource;
use App\Models\Oracle\Ecnebi;
use App\Services\Customs\ForeignCustomsService;
use App\Services\PersonService;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;

class ForeignCustomsController extends Controller
{
    use ApiResponsible;


    public function __construct(
        protected PersonService         $personService,
        protected ForeignCustomsService $foreignCustomsService,
    )
    {

        /*TODO: asagidaki bolmelere permissionlar elave olunmalidir! */

        $this->middleware('permission:customs:view', ['only' => ['search', 'getPerson',
            'getPersonFromOracle', 'listForeignPrecinct', 'listCountries']]);
    }

    /**
     * @OA\Post  (
     *      path="/customs/search",
     *      operationId="get-customs-search",
     *      tags={"Bənzər şəxslər"},
     *      summary="Bənzər şəxslərin axtarılması servisi",
     *      description="Bənzər şəxslərin axtarılması servisi",
     *      security={{ "bearerAuth": {} }},
     *      @OA\Parameter (
     *          name="photo",
     *          description="Şəkil",
     *          in="query",
     *          @OA\Schema(
     *              type="string"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="max_count",
     *          description="Maksimim say",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Parameter (
     *          name="similarity",
     *          description="Oxşarlıq",
     *          in="query",
     *          @OA\Schema(
     *              type="numeric"
     *          )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *      ),
     *      @OA\Response(
     *          response=301,
     *          description="Moved Permanently",
     *      ),
     *      @OA\Response(
     *          response=400,
     *          description="Bad Request",
     *      ),
     *      @OA\Response(
     *          response=401,
     *          description="Unauthorized",
     *      ),
     *      @OA\Response(
     *          response=403,
     *          description="Forbidden",
     *      ),
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *      ),
     *      @OA\Response(
     *          response=405,
     *          description="Method Not Allowed",
     *      ),
     *      @OA\Response(
     *          response=409,
     *          description="Conflict",
     *      ),
     *      @OA\Response(
     *          response=422,
     *          description="Unprocessable entity",
     *      ),
     *      @OA\Response(
     *          response=500,
     *          description="Internal Server Error",
     *      ),
     *      @OA\Response(
     *          response=503,
     *          description="Service Unavailable",
     *      )
     * )
     *
     * @param SearchCustomsRequest $request
     * @return JsonResponse
     */
    public function search(SearchCustomsRequest $request): JsonResponse
    {

        $params = $request->all();

        $perPage = $request->input('per_page', 10);

        if (!isset($params['photo'])) {

            return $this->foreignCustomsService->paginateByParams($params, $perPage);
        }

        return $this->foreignCustomsService->search($params, $perPage);
    }

    /**
     * @param $rowId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPerson($rowId): JsonResponse
    {
        return $this->foreignCustomsService->findByRowId($rowId);
    }

    /**
     * @param $rowId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getPersonFromOracle($rowId): JsonResponse
    {
        return $this->foreignCustomsService->findByRowIdOracle($rowId);
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function listForeignPrecinct(): JsonResponse
    {
        $rows = Ecnebi::query()->select('MENTEQE as label')->distinct('label')->get();

        return PrecinctListResource::collection($rows)->response();
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function listCountries(): JsonResponse
    {
        $rows = Ecnebi::query()->select('VETENDASHLIGI as label')->distinct('label')->get();

        return ForeignCitizenshipCountriesResource::collection($rows)->response();
    }
}
