<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\Person\PersonResource;
use App\Jobs\UpdatePinDataJob;
use App\Models\ForTestingPin;
use App\Models\MyJob;
use App\Models\Person;
use App\Models\ProfileSearchHistory;
use App\Models\ScannedFaces;
use App\Models\User;
use App\Services\EHDIS\DoctorListService;
use App\Services\EHDIS\JobListService;
use App\Services\EHDIS\PersonPhoneService;
use App\Services\EHDIS\VoenListService;
use App\Services\PersonDataService;
use App\Services\UserInstance;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\Pool;
use Illuminate\Http\Request;
use App\Traits\ApiResponsible;
use App\Services\PersonService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\FacebookProfileResource;
use App\Http\Resources\SimilarFaceResource;
use App\Services\EHDIS\DoctorListASAService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\Response;
use App\Services\EHDIS\PersonIDCardInfoService;
use App\Http\Resources\Person\PersonDoctorResource;
use App\Models\FacebookPhotoPin;
use App\Models\Phone;

class PersonController extends Controller
{
    use ApiResponsible;

    public function __construct(
        protected PersonService     $personService,
        protected PersonDataService $personDataService,
    )
    {

        /*
         *
            profil-search:family-members',
            profil-search:foreign-country',
            profil-search:duty',
            profil-search:fines',
            profil-search:protocols',
            profil-search:driving-license',
            profil-search:mobile-phones',
            profil-search:vehicle-list',
            profil-search:education-info',
            profil-search:custom',
         *
         */

        $this->middleware('permission:profil-search', ['only' => ['index', 'show']]);
        $this->middleware('permission:profil-search:family-members', ['only' => ['getPersonRelations']]);
        $this->middleware('permission:profil-search:foreign-country', ['only' => ['getForeignPassportInfo']]);
        $this->middleware('permission:profil-search:duty', ['only' => ['getMilitaryInfo']]);
        $this->middleware('permission:profil-search:fines', ['only' => ['getFines']]);
        $this->middleware('permission:profil-search:protocols', ['only' => ['getProtocols']]);
        $this->middleware('permission:profil-search:driving-license', ['only' => ['getDriverLicenseInfo']]);
        $this->middleware('permission:profil-search:mobile-phones', ['only' => ['getPersonPhone']]);
        $this->middleware('permission:profil-search:vehicle-list', ['only' => ['getVehicleInfo']]);
        $this->middleware('permission:profil-search:education-info', ['only' => ['getTQDKInfo', 'getStudentInfo']]);
        // $this->middleware('permission:profil-search:custom', ['only' => ['getCrossingBorderInfo']]);
        // $this->middleware('permission:profil-search:doctor-list', ['only' => ['getDoctorList']]);
        $this->middleware('permission:profil-search:job', ['only' => ['getPersonJobByPin']]);
        $this->middleware('permission:profil-search:cdr-call-history', ['only' => ['getSocialCall']]);
        //$this->middleware('permission:profil-search:cdr-location-history', ['only' => ['getSocialCallLocation']]);
        //$this->middleware('permission:profil-search:social_media', ['only' => ['getTelegramProfileByPin','getFacebookProfile']]);

    }

    public function index(Request $request): Response
    {
        return $this->personService
            ->paginate($request->all(), $request->input('per_page') ?? 10);
    }

    public function show(string $pin): JsonResponse
    {
        /**
         * @var $person Person
         */
        $person = $this->personService->getOrSyncPerson($pin);

        if (!$person) {
            return $this->errorResponse(404, 'Iamas invalid Data');
        }

        return $this->personService->show($person);
    }

    public function similar(Request $request, string $fin): JsonResponse
    {
        $scannedFaces = ScannedFaces::query()
            ->when($request->input('camera_id'), function ($query) use ($request) {
                if ($request->input('camera_id') !== null || $request->input('camera_id') !== '') {
                    $query->where('photos.camera_id', (int)$request->input('camera_id'));
                }
            })
            ->orderBy('created_at', 'desc')
            ->where('fins.' . strtoupper($fin), 'exists', true)
            ->paginate($request->input('per_page') ?? 10);

        return SimilarFaceResource::collection($scannedFaces)->response();
    }

    /**
     * @param string $pin
     * @return array
     */
    public function getPersonInfo(string $pin): array
    {
        return $this->personDataService->getPersonInfo($pin);
    }

    /**
     * @param string $pin
     * @return JsonResponse
     */
    public function getPersonRelations(string $pin): JsonResponse
    {
        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }
        return $this->personDataService->getPersonRelations($pin);
    }

    /**
     * @param string $pin
     * @return JsonResponse
     */
    public function getPersonPhone(string $pin)
    {

        //  return PersonPhoneService::run($pin);


        //return $this->personDataService->getPersonPhones($pin);

        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }
        $phones = Phone::query()->select('phone', 'pin')->where('pin', $pin)
            ->groupBy(['phone', 'pin'])
            ->get();

        function getOperator($phone): string
        {
            $substr = substr($phone, 0, 2);
            if ($substr == "50" || $substr == "51" || $substr == "10") {
                return "Azercell";
            } else if ($substr == "55" || $substr == "99") {
                return "Bakcell";
            } else if ($substr == "70" || $substr == "77") {
                return "Azerfon";
            } else {
                return "Baktelekom";
            }
        }

        $data = $phones->map(function ($item) {
            $used_phone = DB::table('pin_phone')->where('pin', $item->pin)->first();
            $item->used_phone = false;
            if ($used_phone && $used_phone->vaccine_reg != null) {
                $phone_vaccine_reg = Str::startsWith($used_phone->vaccine_reg, '0') ? substr($used_phone->vaccine_reg, 1) : $used_phone->vaccine_reg;
                $item->used_phone = ((int)$phone_vaccine_reg == (int)$item->phone);
            }

            if ($used_phone) {
                $person = DB::table('people')->where('pin', $item->pin)->first();
                $item->name = $person->name;
                $item->surname = $person->surname;
                $item->father_name = $person->father_name;
                $item->ad_iamas = $item->name . ' ' . $item->surname . ' ' . $item->father_name;
            }


            return [
                'Phone' => $item->phone,
                "City" => "",
                "IMSI" => "",
                "Pasport" => "",
                "Pin" => $item->pin,
                "Adres_Iamas" => "",
                "Ad_Iamas" => $item->ad_iamas ?? "",
                "Adres_Operator" => "",
                "Ad_operator" => "",
                "DataAktivacii" => "",
                "ContactPhone" => "",
                "Typeid" => 3,
                "TypeName" => getOperator($item->phone),
                "used_phone" => $item->used_phone,
                "vaccine_reg" => 0
            ];
        });

        return response()->json(["data" => $data]);
    }

    /**
     * @param string $pin
     * @return JsonResponse
     */
    public function getPersonUsePhone(string $pin): JsonResponse
    {
        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }
        return $this->personDataService->getPersonUsePhones($pin);
    }

    /**
     * @param string $pin
     * @return JsonResponse
     */
    public function getFines(string $pin): JsonResponse
    {
        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }
        return $this->personDataService->getPersonFines($pin);
    }

    /**
     * @param string $pin
     * @return array
     */
    public function getProtocols(string $pin): array
    {
        if (!checkIfSecretKeyExists($pin)) {
            return [];
        }
        return $this->personDataService->getPersonProtocols($pin);
    }


    public function getDriverLicenseInfo(string $pin)
    {
//        if(!checkIfSecretKeyExists($pin)){
//            return [];
//        }
        return $this->personDataService->getPersonDriverLicense($pin);
    }


    public function getCrossingBorderInfo(string $passportNumber, Request $request)
    {

        return $this->personDataService->getPersonCrossingBorder($passportNumber, $request->page ?? 1, $request->per_page ?? 20);
    }

    /**
     * @param string $pin
     * @return array
     */
    public function getMilitaryInfo(string $pin): array
    {
        if (!checkIfSecretKeyExists($pin)) {
            return [];
        }
        return $this->personDataService->getPersonMilitary($pin);
    }

    /**
     * @param string $pin
     * @return
     */
    public function getDoctorList(string $pin)
    {
        if (!checkIfSecretKeyExists($pin)) {
            return [];
        }
        return DoctorListService::run($pin);
    }

    /**
     * @param string $pin
     * @return JsonResponse
     */
    public function getForeignPassportInfo(string $pin): JsonResponse
    {
        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }
        return $this->personDataService->getPersonForeignPassport($pin);
    }

    /**
     * @param string $pin
     * @return array
     */
    public function getVehicleInfo(string $pin)
    {
        if (!checkIfSecretKeyExists($pin)) {
            return [];
        }
        /**
         * @var $person Person
         */
        // $person = $this->personService->getOrSyncPerson($pin, 30, false);


        //TODO: must be refactor it

        return [
            'status' => 200,
            'message' => 'OK',
            'data' => []
        ];


        // return $this->personDataService->getPersonVehiclesNewService($pin);
//        return $this->personDataService->getPersonVehicles($pin, $person->name, $person->surname, $person->father_name);
    }

    /**
     * @param string $pin
     * @return JsonResponse
     */
    public function getTQDKInfo(string $pin)
    {
        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }
        // return $this->personDataService->getPersonStudies($pin);

        if ($pin == '5WD0A30') {
            $response = public_path('mocks/TQDK_5WD0A30.json');
        } else {
            $response = public_path('mocks/TQDK_64AXG9V.json');
        }

        // if(auth('api')->id()==124){

        //TODO: must be refactor it

        $response = public_path('mocks/tqdk.json');
        $response = file_get_contents($response);
        $response = json_decode($response, true);

        return response()->json($response);

        $responseX['result'] = $response;

        return $this->successResponse(200, 'response ok', $response);


        $newResponse = $response['data'];

        $newResponse['result'] = json_encode($newResponse['result']);

        $response['data'] = json_encode($newResponse['result']);

        return $this->successResponse(200, 'response ok', $response);
        //}


        return $this->successResponse(200, 'response ok', $response);
    }

    /**
     * @param string $pin
     * @return array|mixed
     */
    public function getStudentInfo(string $pin)
    {
        if (!checkIfSecretKeyExists($pin)) {
            return [];
        }
        return $this->personDataService->getPersonStudentInfo($pin);
    }

    /**
     * @param string $pin
     * @return array
     */
    public function getSocialCall(string $pin)
    {
        if (!checkIfSecretKeyExists($pin)) {
            return [];
        }
        return $this->personDataService->getSocialCallInfo($pin);
    }


    /**
     * @return array
     */
    public function getSocialDip()
    {
//        if(!checkIfSecretKeyExists($pin)){
//            return [];
//        }
        return $this->personDataService->getSocialDip();
    }

    /**
     * @param string $pin
     * @return array
     * @throws \Exception
     */
    public function getSocialDipLocation(): array
    {
//        if(!checkIfSecretKeyExists($pin)){
//            return [];
//        }
        return $this->personDataService->getSocialDipLocation();
    }


    /**
     * @param string $pin
     * @return array
     * @throws \Exception
     */
    public function getSocialDipLocationWithPin(): array
    {
//        if(!checkIfSecretKeyExists($pin)){
//            return [];
//        }
        return $this->personDataService->getSocialDipLocationWithPin();
    }

    /**
     * @return array
     */

    public function getSocialDipApplications(): array
    {
        return $this->personDataService->getSocialDipApplications();
    }

    /**
     * @param string $pin
     * @param Request $request
     * @return array
     */
    public function getGsmCall(string $pin, Request $request)
    {
        if (!checkIfSecretKeyExists($pin)) {
            return [];
        }

        return $this->personDataService->getGsmCall($request->number);
    }

    /**
     * @param string $pin
     */
//    public function getSocialCallLocation(Request $request,string $pin): JsonResponse
//    {
//        $request = $request->merge(['pin'=>$pin]);
//        return $this->personDataService->getSocialCallLocationInfo($request);
//    }


    /**
     * @param string $pin
     */
//    public function getSocialCallLocationWithOthers(string $pin): JsonResponse
//    {
//        return $this->personDataService->getSocialCallLocationWithOthers($pin);
//    }

//    public function getSocialCallLocationWithOthersNew(Request $request,string $pin): JsonResponse
//    {
//        $request = $request->merge(['pin'=>$pin]);
//        return $this->personDataService->getSocialCallLocationWithOthersNew($request);
//    }
    /**
     * @param string $pin
     * @return array
     */
    public function getPersonIDCardInfo(string $pin): array
    {
        return PersonIDCardInfoService::run($pin);
    }

    /**
     * @param string $pin
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function updateViaIamas(string $pin): ?Model
    {
        return $this->personService->getOrSyncPerson($pin, 0);
    }

    /**
     * @param string $pin
     * @return JsonResponse
     */
    public function getGallery(string $pin): JsonResponse
    {
        //        $this->personService->getOrSyncPerson($pin);

        if ($pin) {
            ProfileSearchHistory::query()->create([
                'pin' => strtoupper($pin),
                'user_id' => auth('api')->id()
            ]);
        }

        // try {
        //     //TODO: Hamid Hamidli bunu fix et controlleri controllerden cagirma
        //   app(ElasticIndexController::class)->peopleIndex($pin);
        // }catch (\Exception $exception){
        // }


        $gallery = $this->personService->getGallery($pin);
        $personName = $gallery['name'] . " " . $gallery['surname'] . " " . $gallery['father_name'] ?? " (" . $gallery['pin'] . ")";
        $logData = [
            "description" => $personName . " profilinə daxil oldu",
        ];

        sendRequestLogToAudit($logData, "audit");

        createSecurityKeyForProfile($userInstance->id ?? 0, $pin);

        return PersonResource::make($gallery)->response();
    }

    public function updatePin(string $pin): JsonResponse
    {
        try {
            $token = request()->bearerToken();
            $results = [];
            $timingResults = [];
            $mapping = [
                'military' => 'military',
                'foreign_passport' => 'foreign-passport',
                'relations' => 'relations',
                'studies' => 'tqdk',
                'crossing_border' => 'border-crossing',
                'gallery' => 'gallery',
                'phones' => 'phone'
            ];

            foreach (array_keys($mapping) as $cacheKey) {
                Cache::put("{$cacheKey}:{$pin}", "pin-{$cacheKey}");
            }

            $responses = Http::pool(function (Pool $pool) use ($token, $pin, $mapping, &$timingResults) {
                $requests = [];
                foreach ($mapping as $cacheKey => $requestKey) {
                    $startTime = microtime(true);
                    $timingResults[$cacheKey] = $startTime;

                    if ($requestKey === 'gallery') {
                        $requests[$cacheKey] = $pool->as($cacheKey)->withToken($token)
                            ->get(url("api/v1/person/{$pin}/{$requestKey}"), [
                                'work_name' => request()->input('work_name', 'default_work_name')
                            ]);
                    } else if ($requestKey === 'border-crossing') {
                        $passportNumber = request()->input('passportNumber');
                        $requests[$cacheKey] = $pool->as($cacheKey)->withToken($token)
                            ->get(url("api/v1/person/{$passportNumber}/{$requestKey}"));
                    } else {
                        $requests[$cacheKey] = $pool->as($cacheKey)->withToken($token)
                            ->get(url("api/v1/person/{$pin}/{$requestKey}"));
                    }
                }
                return $requests;
            });

            foreach ($responses as $cacheKey => $response) {
                $fullCacheKey = "{$cacheKey}:{$pin}";
                try {
                    $data = $response->json();
                    $endTime = microtime(true);
                    $duration = $endTime - $timingResults[$cacheKey];
                    $timingResults[$cacheKey] = $duration;

                    Cache::put($fullCacheKey, json_encode($data));
                    $results[$fullCacheKey] = [
                        'data' => $data,
                        'duration' => $duration
                    ];
                } catch (\Throwable $e) {
                    return response()->json(['error' => "Error processing response for " . $cacheKey . " : " . $e->getMessage()]);
                }
            }

            return response()->json([
                'results' => $results,
                'timings' => $timingResults
            ]);
        } catch (\Throwable $exception) {
            return response()->json(['error' => $exception->getMessage()]);
        }
    }


    public function lists($type = 'all'): int
    {

        if ($type == 'ready') {
            return Person::query()->where('doc_number', '!=', '00')->count();
        }

        return Person::query()->count();
    }

    /**
     * @param string $pin
     * @return JsonResponse
     */
    public function getFacebookProfile(string $pin): JsonResponse
    {

        $data = FacebookPhotoPin::where('fin', $pin)
            ->join('facebook.photos', 'facebook.photos.id', '=', 'facebook.fins.photo_id')
            ->join('facebook.accounts', 'facebook.accounts.id', '=', 'facebook.photos.account_id')
            ->where('facebook.fins.distance', '>=', '0.6')
            ->orderBy('distance', 'DESC')->get()->toArray();
        $data['storage'] = ['s3' => config('endpoint.find-related-fins-s3')];
        $data['path'] = ['url' => 'https://facebook.com/'];
        return response()->json(['status' => 200, 'msg' => 'success', 'data' => $data]);


        //return response()->json(['status' => 200, 'msg' => 'success', 'data' => FacebookProfileResource::collection($data)], 200);
    }

    public function getFacebookProfileByPin(string $pin): JsonResponse
    {
        try {
//            $pins = Http::timeout(10)->get(config('endpoint.find-related-fins-from-facebook'), [
//                'fin' => $pin
//            ]);
//            $data = json_decode($pins->body(), true);
            $data['storage'] = ['s3' => config('endpoint.find-related-fins-s3')];
            $data['path'] = ['url' => 'https://facebook.com/'];
//            return response()->json(['status' => 200, 'msg' => 'success', 'data' => $data]);
            $data = FacebookPhotoPin::where('fin', $pin)
                ->join('facebook.photos', 'facebook.photos.id', '=', 'facebook.fins.photo_id')
                ->join('facebook.accounts', 'facebook.accounts.id', '=', 'facebook.photos.account_id')
                ->where('facebook.fins.distance', '>=', '0.7')
                ->orderBy('distance', 'DESC')->get()->toArray();
            $data['storage'] = ['s3' => config('endpoint.find-related-fins-s3')];
            $data['path'] = ['url' => 'https://facebook.com/'];
            return response()->json(['status' => 200, 'msg' => 'success', 'data' => $data]);
        } catch (\Exception $exception) {
            return response()->json(['status' => 500, 'msg' => 'ai serves error', 'data' => $exception->getMessage()]);
        }
    }

    public function getTelegramProfileByPin(string $pin): JsonResponse
    {
        try {

            // select * from facebook.tg_pins tp where distance>0.7 and photo_id in
            // (select id from facebook.tg_photos where account_id in
            // (select id from facebook.tg_accounts where fullname is not null and username is not null and phone_number is not null));

            //convert to laravel DB query


            $data = DB::table('facebook.telegram_pins as tp')
                ->selectRaw('tp.*, tphoto.*, taccount.*')
                ->where('tp.pin', $pin)
                ->join('facebook.telegram_photos as tphoto', 'tphoto.id', '=', 'tp.photo_id')
                ->join('facebook.tg_accounts as taccount', 'taccount.id', '=', 'tphoto.account_id')
                // ->where('taccount.fullname', '!=', null)
                //->where('taccount.username', '!=', null)
                //->where('taccount.phone_number', '!=', null)
                ->where('tp.distance', '>=', '0.65')
                ->orderBy('tp.distance', 'DESC')
                ->get()->toArray();


            $data['storage'] = ['s3' => config('servers.frontend') . '/telegram-pics/'];

            $data['path'] = ['url' => 'https://t.me/'];

            return response()->json(['status' => 200, 'msg' => 'success', 'data' => $data]);
        } catch (\Exception $exception) {
            return response()->json(['status' => 500, 'msg' => 'ai serves error', 'data' => $exception->getMessage()]);
        }
    }

    public function getPersonJobByPin(string $pin)
    {
        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }


        $data = MyJob::query()
            ->selectRaw(
                'WORKPLACE as Name,
             WORKPLACE as NameNew,
            POSITION_MANUAL as PositionManual,
            POSITION as Position,
            EMPLOYER_NAME as Employer_Name,
            CON_STATUS  as ConStatus,
            EMPLOYER_TPN as Tpn,
            CON_BEGIN_DATE as JobStartDate,
            CON_END_DATE as ContractEndDate,
            WORKPLACE_TYPE as WorkPlaceType
            EMPLOYER_LEG_ADDRESS as Employer_Leg_Address,
            WORKPLACE as WorkPlace
            ',

            )
            ->where('PIN', $pin)
            ->orderBy('CON_REG_DATE', 'DESC')
            ->get()->toArray();

        //  }
        /*
                return \response()->json(['status' => 200, 'msg' => 'success', 'data' => [
                    'total' => 0,
                    'message' => 'success',
                ]]);
        */
//        $job = Http::timeout(10)->acceptJson()->get(config('servers.work_host').'/api/v1/get_work_info?pin=' . $pin . '&token=04d94913699cd591a4d7d94a0bbaeb61');
//
//        $job = json_decode($job->body(), true);
//
//        if (isset($job['data'])) {
//            $job = collect($job['data'])->map(function ($item) {
//                $voen = VoenListService::run($item['Tpn']);
//                if (isset($voen['data']['LegalPayerEnt']['fullName'])) {
//                    $item['Name'] = $voen['data']['LegalPayerEnt']['fullName'];
//                }
//                $poz = $item['Position'];
//                $item['Position'] = $item['PositionManual'];
//                $item['PositionManual'] = $poz;
//                return $item;
//            })->toArray();
//        }


        $job['data'] = $data;
        if (isset($job['data'])) {
            $job = collect($job['data'])->map(function ($item) {


                // TODO: uncomment when fix ehdis
                // $voen = VoenListService::run($item['tpn']);
                // if (isset($voen['data']['LegalPayerEnt']['fullName'])) {
                //     $item['Name'] = $voen['data']['LegalPayerEnt']['fullName'];
                // } else {
                //     $item['Name'] = $item['name'];
                // }


                $item['JobStartDate'] = Carbon::parse($item['jobstartdate'])->format('Y-m-d');
                $poz = $item['position'];
                $item['Position'] = $item['positionmanual'];
                $item['PositionManual'] = $poz;
                $item['ConStatus'] = $item['constatus'];
                $item['Tpn'] = $item['tpn'];
                $item['WorkPlaceType'] = $item['workplacetype'];
                $item['EMPLOYER_NAME'] = $item['employer_name'] ?? $item['Employer_Name'];
                $item['Name'] = $item['EMPLOYER_NAME'];
                $item['endDate'] = $item['contractenddate'];
                $item['ContractEndDate'] = is_null($item['contractenddate']) ? "naməlum" : Carbon::parse($item['contractenddate'])->format('Y-m-d');
                return $item;

            })->toArray();
        }

        if (isset($job)) {
            return $this->successResponse(200, 'response ok', $job);
        }

        return $this->successResponse(200, 'response ok', $job);

    }


    public function getTesterAccessToProfile(string $pin): JsonResponse
    {
        if (!checkIfSecretKeyExists($pin)) {
            return response()->json(['access' => false]);
        }
        //TODO: for testing
        /**
         * @var $user User
         */
        $user = auth('api')->user();
        $user->setRelation('permissions', $user->getAllPermissions());
        $is_tester = $user->checkPermissionTo('permission:users:tester-assign', 'api');
        // $profile_view =$user->checkPermissionTo('permission:profile-view', 'api');
        if ($is_tester) {
            $accessed_pin = ForTestingPin::query()->where('pin', $pin)->exists();
            if ($accessed_pin) {
                return response()->json(['access' => true]);
            } else {
                return response()->json(['access' => false]);
            }
        }
        return response()->json(['access' => true]);

    }

    public function getCovidByPin(Request $request, string $pin): JsonResponse
    {
        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }
        $page = $request->get('page');
        $perPage = $request->get('per_page');
        $getPerson = $this->personService->getCovidInfo($pin, $page, $perPage);
        return $this->successResponse(200, 'response ok', $getPerson);
    }

    //VaccineRegistration
    public function getVaccineRegistrationByPin(Request $request, string $pin): JsonResponse
    {
        if (!checkIfSecretKeyExists($pin)) {
            return $this->successResponse(401, 'Not Permission');
        }
        $page = (int)$request->get('page', 1);
        $perPage = (int)$request->get('per_page', 10);
        $getPerson = $this->personService->getVaccineRegistration($pin, $page, $perPage);
        return response()->json(['status' => 200, 'message' => 'success', 'data' => $getPerson]);
    }
}
