<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\SocialNetworkService;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SocialNetworkController extends Controller
{
    use ApiResponsible;

    public function __construct(protected SocialNetworkService $socialNetworkService)
    {

    }

    public function findComments(Request $request): JsonResponse
    {
        $text = $request->get('text');
        $page = $request->get('page', 1);
        $per_page = $request->get('per_page', 10);

        $data = $this->socialNetworkService->searchByComment(strip_tags($text));

        return response()->json($data);
    }

    public function getCommentsByUsername(Request $request): JsonResponse
    {
        $username = $request->get('username');
        $page = $request->get('page', 1);
        $per_page = $request->get('per_page', 10);

        $data = $this->socialNetworkService->getCommentsByUsername(strip_tags(strtoupper($username)));

        return response()->json($data);
    }

    public function findByAccount($pin): JsonResponse
    {
        $data = $this->socialNetworkService->accountByPin(strip_tags($pin));

        return response()->json($data);
    }
}
