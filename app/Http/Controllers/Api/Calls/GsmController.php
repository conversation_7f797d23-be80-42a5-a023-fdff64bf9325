<?php

namespace App\Http\Controllers\Api\Calls;

use App\Http\Controllers\Controller;
use App\Http\Resources\CdrCallResource;
use App\Services\Calls\GsmService;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Symfony\Component\HttpFoundation\StreamedResponse;

class GsmController extends Controller
{

    public function __construct(protected GsmService $gsmService){

    }

    public function getAllGsmCall(Request $request)
    {
        return $this->gsmService->getAllGsmCall($request->per_page ?? 10);
    }

    public function getGsmCall(Request $request): LengthAwarePaginator
    {
        $request->validate([
            'number' => 'required|min:5'
        ]);

        return $this->gsmService->getGsmCall($request->number, $request->per_page ?? 10);
    }

    public function playTalkGsm($traceId): JsonResponse|StreamedResponse
    {
        return $this->gsmService->playTalkGsm($traceId);
    }

    public function getAllCdrCall(Request $request): CdrCallResource
    {
        return $this->gsmService->getAllCdrCall($request->per_page ?? 10);
    }


}
