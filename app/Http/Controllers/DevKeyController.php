<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Api\LocationCarController;
use App\Http\Controllers\Api\LocationController;
use App\Services\ExternalService;
use App\Services\LocationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DevKeyController extends Controller
{
    public function getVehicleEntered(Request $request): JsonResponse
    {
        $externalService = new ExternalService();
        $externalController = new ExternalController($externalService);
        return $externalController->getVehicleEntered($request);
    }

    public function getAzParking(Request $request): JsonResponse{
        $locationService = new LocationService();
        $locationController = new LocationController($locationService);
        return $locationController->getAzParking($request);
    }

    public function getCarVehicleEntered(Request $request): JsonResponse
    {
        $locationController = new LocationCarController();
        return $locationController->getVehicleEntered($request);
    }

}
