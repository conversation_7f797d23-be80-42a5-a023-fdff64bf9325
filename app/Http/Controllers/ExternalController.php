<?php

namespace App\Http\Controllers;

use App\Jobs\SaveAdvanceSearchLogsJob;
use App\Services\ExternalService;
use App\Services\LocationService;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ExternalController extends Controller
{
    use ApiResponsible;

    private ExternalService $externalService;

    public function __construct(ExternalService $externalService)
    {
//        $this->middleware('permission:polygons')->except([
//            'getVehicleEntered',
//            'getAzParking',
//        ]);
//
//        $this->middleware('permission:profil-search:cdr-location-history', ['only' => ['getSocialCallLocationInfo']]);
//
//        $this->middleware('permission:profil-search', ['only' => ['getVehicleEntered']]);
//
//        $this->middleware('permission:car-trajectory', ['only' => ['getVehicleEntered','getAzParking']]);

        $this->externalService = $externalService;
    }

    public function getVehicleEntered(Request $request): JsonResponse
    {
        $getVehicleEntered = $this->externalService->getVehicleEntered($request->all());
        $dataWithImages = [];
        $points = [];
        $cameraPointers = [];

        if (!empty($getVehicleEntered)) {
            if (isset($getVehicleEntered['status']) && $getVehicleEntered['status'] == 404) {
                return $this->successResponse(404, 'response no', ['message' => 'Belə bir maşın nömrəsi tapılmadı']);
            }
            if ($request->has('short_data')) {
                $dataWithImages = $this->preparingShortedData($getVehicleEntered);
            } else {
                foreach ($getVehicleEntered as $getVehicle) {
                    $cameraPointer = getCameraPointer($getVehicle['cameraName']);
                    $lat = $cameraPointer->coordinates_latitude;
                    $lon = $cameraPointer->coordinates_longitude;
                    $points[] = [$lat, $lon];
                    $cameraPointers[] = [
                        'cameraPointer' => $cameraPointer,
                        'getVehicle' => $getVehicle
                    ];
                }
                if ($request->has('polygon')) {
                    $polygon = [];
                    foreach ($request->get('polygon') as $point) {
                        $polygon[] = [(float)$point['lat'], (float)$point['lng']];
                    }
                    $results = (new \App\Services\PointInPolygon)->arePointsInsidePolygon($points, $polygon);
                    foreach ($results as $index => $isInside) {
                        if ($isInside) {
                            $cameraPointerData = $cameraPointers[$index];
                            $dataWithImages[] = [
                                "id" => $cameraPointerData['getVehicle']['id'],
                                "vehicleNumber" => $cameraPointerData['getVehicle']['vehicleNumber'],
                                "cameraNumber" => $cameraPointerData['getVehicle']['cameraNumber'],
                                "cameraName" => $cameraPointerData['getVehicle']['cameraName'],
                                "insertDate" => $cameraPointerData['getVehicle']['insertDate'],
                                "image" => $this->externalService->getVehicleLocationImage($cameraPointerData['getVehicle']['id'], $cameraPointerData['getVehicle']['vehicleNumber']),
                                'cameraPointer' => $cameraPointerData['cameraPointer'],
                                'icon' => config('app.url') . '/icon.png',
                            ];
                        }
                    }
                } else {
                    foreach ($cameraPointers as $cameraPointerData) {
                        $dataWithImages[] = [
                            "id" => $cameraPointerData['getVehicle']['id'],
                            "vehicleNumber" => $cameraPointerData['getVehicle']['vehicleNumber'],
                            "cameraNumber" => $cameraPointerData['getVehicle']['cameraNumber'],
                            "cameraName" => $cameraPointerData['getVehicle']['cameraName'],
                            "insertDate" => $cameraPointerData['getVehicle']['insertDate'],
                            "image" => $this->externalService->getVehicleLocationImage($cameraPointerData['getVehicle']['id'], $cameraPointerData['getVehicle']['vehicleNumber']),
                            'cameraPointer' => $cameraPointerData['cameraPointer'],
                            'icon' => config('app.url') . '/icon.png',
                        ];
                    }
                }
            }
        }
        if ((!$request->get('page') || $request->get('page') == 1) && $request->get('search_head_name') !== null && $request->get('search_head_name') && !$request->filled('from_Job')) {
            $queue = [
                'name' => $request->get('search_head_name') ?? 'manually',
                'search_params' => $request->all(),
                'pins' => [],
                'count' => count($dataWithImages),
                'user_id' => auth('api')->user()->id,
                'search_type' => 'find_travel',
                'search_tab' => [[
                    'value' => 'find_travel',
                    'name' => 'tab',
                    'label' => 'Avtomobil nömrəsinə görə trayektoriya',
                ]],
                'search_text' => 'find_travel',
            ];
//            Log::emergency('SaveFindTravelJobsAfterLog: ' . json_encode($queue));
            SaveAdvanceSearchLogsJob::dispatchSync($queue);
        }
        return $this->successResponse(200, 'response ok', $dataWithImages);
    }

    private function preparingShortedData($getVehicleEntered): array
    {
        $dataWithImages = [];
        foreach ($getVehicleEntered as $getVehicle) {
            $cameraPointer = getCameraPointer($getVehicle['cameraName']);
            $dataWithImages[] = [
                "insertDate" => date("Y-m-d H:i:s", strtotime($getVehicle['insertDate'])),
                'latitude' => (float)$cameraPointer->latitude,
                'longitude' => (float)$cameraPointer->longitude,
                'coordinates_latitude' => (float)$cameraPointer->coordinates_latitude,
                'coordinates_longitude' => (float)$cameraPointer->coordinates_longitude,
            ];
        }
        return $dataWithImages;
    }

}
