<?php

namespace App\Repository\SocialHubRepository;

use App\Models\Social\Following;
use App\Repository\AbstractRepository;

class SocialHubFollowingRepository extends AbstractRepository
{
    /**
     * BlacklistRepository constructor
     *
     * @param Following $model
     */
    public function __construct(Following $model)
    {
        parent::__construct($model);
    }


    public function existsByTypeIdAndFollowingType(int $accountId, string $followingType): bool
    {
        return $this
            ->model
            ->where('account_id', $accountId)
            ->where('following_type', $followingType)
            ->exists();
    }

    public function existsByWordAndFollowingType(string $word, string $followingType): bool
    {
        return $this
            ->model
            ->where('following_params', $word)
            ->where('following_type', $followingType)
            ->exists();
    }



}
