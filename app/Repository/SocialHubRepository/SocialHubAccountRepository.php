<?php

namespace App\Repository\SocialHubRepository;

use App\Models\Social\Account;
use App\Models\Social\Following;
use App\Repository\AbstractRepository;

class SocialHubAccountRepository extends AbstractRepository
{
    /**
     * BlacklistRepository constructor
     *
     * @param Following $model
     */
    public function __construct(Account $model)
    {
        parent::__construct($model);
    }

}
