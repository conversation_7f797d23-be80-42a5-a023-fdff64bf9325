<?php

namespace App\Repository;

use App\Models\Person;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class PersonRepository extends AbstractRepository
{
    /**
     * @param \App\Models\Person $model
     */
    public function __construct(Person $model)
    {
        parent::__construct($model);
    }

    /**
     * @param array $params
     * @param array $pins
     * @return mixed
     */
    public function getByParams(array $params, array $pins = []): mixed
    {
        return $this->buildQuery()
            ->select($this->selectedColumnsForList())
            ->when(count($pins) > 0, function ($q) use ($pins) {
                $q->pins($pins);
            })
            ->when(isset($params['pin']), function ($q) use ($params) {
                $q->pin($params['pin'], $params['new_search'] ?? false);
            })
            ->when(isset($params['name']), function ($q) use ($params) {
                $q->name($params['name'], $params['new_search'] ?? false);
            })
            ->when(isset($params['surname']), function ($q) use ($params) {
                $q->surname($params['surname'], $params['new_search'] ?? false);
            })
            ->when(isset($params['father_name']), function ($q) use ($params) {
                $q->fatherName($params['father_name'], $params['new_search'] ?? false);
            })
            ->when(isset($params['doc_num']), function ($q) use ($params) {
                $q->docNum($params['doc_num'], $params['new_search'] ?? false);
            })
            ->when(isset($params['birthdate']), function ($q) use ($params) {
                $q->birthdate($params['birthdate']);
            })
            ->when(isset($params['gender']), function ($q) use ($params) {
                $q->gender($params['gender']);
            })
            ->when(isset($params['search_type']), function ($q) use ($params) {
                if ((int)$params['search_type'] === 1) {
                    $q->where('doc_serial_number', '');
                } elseif ((int)$params['search_type'] === 2) {
                    $q->where('doc_serial_number', '<>', '');
                }
            })
            ->orderBy('id');
    }

    public function moreFieldSearch($searchTerms, $pins = [], $per_page)
    {
        $age = 0;
        $rangeAges = [];
        $currentYear = date('Y');
        $detectedAge = false;

        foreach ($searchTerms as $key => $searchTerm)
        {
            if($this->isHumanAge($searchTerm))
            {
                $age = $currentYear - $searchTerm;
                unset($searchTerms[$key]);
                $detectedAge = true;
            }
            if(!$detectedAge)
            {
                $rangeAges = $this->isWithinAgeRange($searchTerm);
                if(count($rangeAges) > 0)
                {
                    unset($searchTerms[$key]);
                }
            }
        }


        $joinTerms = implode(" ",$searchTerms);
        $joinTermsForLike = '%' . str_replace(' ', '%', $joinTerms) . '%';

        $query = Person::query()
            ->select(
                '*',
                DB::raw("ts_rank(to_tsvector('english', name || ' ' || surname || ' ' || father_name), plainto_tsquery('english', '$joinTerms')) AS rank"),
            )
//            ->select(
//                '*',
//                DB::raw("CASE
//                    WHEN CONCAT(name, ' ', surname, ' ', father_name) LIKE ? THEN 1
//                    ELSE 0
//                 END AS rank", [$joinTerms])
//            )
            ->orderByDesc('rank');

//        $query->whereRaw("to_tsvector('english', name || ' ' || surname || ' ' || father_name) @@ plainto_tsquery('english', '$joinTerms')");
        $query->whereRaw("to_tsvector('english', name || ' ' || surname || ' ' || father_name) @@ plainto_tsquery('english', ?)", [mb_strtolower($joinTerms)]);
//        $query->whereRaw("CONCAT(name, ' ', surname, ' ', father_name) LIKE ?", [$joinTermsForLike]);


        $query->when($age > 0, function ($q) use ($age) {
            $q->whereRaw("SUBSTRING(birthdate FROM 1 FOR 4)::INTEGER = ?", [$age]);
        });


        $query->when(count($rangeAges) > 0, function ($q) use ($rangeAges) {
            $q->whereRaw("SUBSTRING(birthdate FROM 1 FOR 4)::INTEGER BETWEEN ? AND ?", [$rangeAges['max'], $rangeAges['min']]);
        });


        $query->when(count($pins) > 0, function ($q) use ($pins) {
            $q->pins($pins);
        });

        return $query->orderBy('id')->paginate($per_page);

    }

    /**
     * @param array $pins
     * @return mixed
     */
    public function getListsByPins(array $pins, $newSearch = false): mixed
    {
        return $this->buildQuery()
            ->pins($pins, $newSearch)
            ->select($this->selectedColumnsForList())
            ->get();
    }

    /**
     * @return string[]
     */
    public function selectedColumnsForList(): array
    {
        return [
            'id',
            'name',
            'surname',
            'father_name',
            'birthdate',
            'pin',
            'image',
            'doc_serial_number',
            'doc_number'
        ];
    }

    public function isHumanAge($age): bool
    {
        return is_numeric($age) && $age >= 0 && $age < 130;
    }

    public function isWithinAgeRange($input): array
    {
        $ages = [];

        $pattern = '/(\d+)\s*-\s*(\d+)/';
        preg_match_all($pattern, $input, $matches);
        $currentYear = date('Y');

        if (!empty($matches[0])) {
            foreach ($matches[0] as $match) {
                $range = explode('-', $match);
                if (count($range) === 2) {
                    if(!$this->isHumanAge($range[0]) || !$this->isHumanAge($range[1]))
                    {
                        return [];
                    }
                    $minAge = intval($range[0]);
                    $maxAge = intval($range[1]);

                    if ($minAge >= 0 && $maxAge <= 130 && $minAge < $maxAge) {
                        $ages = ['min' => $currentYear - $minAge, 'max' => $currentYear - $maxAge];
                    }
                }
            }
        }

        return !empty($ages) ? $ages : [];
    }

    public function detailedSearch(array $params){
        $query = $this->model;

        $query->when(isset($params['pin']), function($q) use ($params){
            return $q->where('pin', $params['pin']);
        });

        $query->when(isset($params['name']), function($q) use ($params){
            return $q->where('name', $params['name']);
        });

        $query->when(isset($params['surname']), function($q) use ($params){
            return $q->where('surname', $params['surname']);
        });

        $person = $query->get();

        return $person;

    }
}
