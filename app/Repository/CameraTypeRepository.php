<?php

namespace App\Repository;

use App\Models\CameraType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class CameraTypeRepository extends AbstractRepository
{
    /**
     * @param \App\Models\CameraType $model
     */
    public function __construct(CameraType $model)
    {
        parent::__construct($model);
    }

    /**
     * @param string $q
     * @return \Illuminate\Database\Eloquent\Collection|array
     */
    public function searchAllByKeyword(string $q): Collection|array
    {
        return $this->buildQuery()
            ->where('name', 'like', '%'.$q.'%')
            ->get();
    }

    /**
     * @param int $limit
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function paginate(int $limit = 15): LengthAwarePaginator
    {
        return $this->buildQuery()
            ->where('active', true)
            ->orderBy('id', 'desc')
            ->paginate($limit);
    }
    public function getByName(string $name): Collection|array
    {
        return $this->buildQuery()->where('name', 'like', '%' . $name . '%')
            ->orderBy('created_at', 'desc')
            ->get();
    }

}
