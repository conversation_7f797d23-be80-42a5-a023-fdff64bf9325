<?php

namespace App\Repository;

use App\Models\SavedData;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class SavedDataRepository extends AbstractRepository {

    /**
     * BlacklistRepository constructor
     *
     * @param \App\Models\SavedData $model
     */
    public function __construct(SavedData $model)
    {
        parent::__construct($model);
    }

    /**
     * @param array $params
     * @return mixed
     */
    public function getByParams(array $params): mixed
    {
        return $this->buildQuery()
            ->select($this->selectedColumnsForList())
            ->when(isset($params['user_id']), function ($q) use ($params) {
                $q->userId($params['user_id']);
            })
            ->when(isset($params['type']), function ($q) use ($params) {
                $q->type($params['type']);
            })
            ->when(($params['type'] === SavedData::CUSTOM), function ($q) use ($params) {
                $q->customFilter($params);
            })
            ->when(($params['type'] === SavedData::PERSON), function ($q) use ($params) {
                $q->personFilter($params);
            });
    }

    /**
     * @return string[]
     */
    public function selectedColumnsForList(): array
    {
        return [
            '_id',
            'user_id',
            'data_id',
            'type',
            'data'
        ];
    }

    /**
     * @param int $limit
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function paginate(int $limit = 15): LengthAwarePaginator
    {
        return $this->getByParams($this->params['params'])
            ->orderBy('id', 'desc')
            ->paginate($limit);
    }
}
