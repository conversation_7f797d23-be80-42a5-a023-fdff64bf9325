<?php

namespace App\Repository;

use App\Models\Blacklist;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class BlacklistRepository extends AbstractRepository {

    /**
     * BlacklistRepository constructor
     *
     * @param \App\Models\Blacklist $model
     */
    public function __construct(Blacklist $model)
    {
        parent::__construct($model);
    }

    /**
     * @param array $params
     * @return mixed
     */
    public function getByParams(array $params): mixed
    {
        return $this->buildQuery()
            ->select($this->selectedColumnsForList())
            ->when(isset($params['pin']), function ($q) use ($params) {
                 $q->pin($params['pin']);
            })
            ->when(isset($params['name']), function ($q) use ($params) {
                 $q->name($params['name']);
            })
            ->when(isset($params['surname']), function ($q) use ($params) {
                 $q->surname($params['surname']);
            })
            ->when(isset($params['father_name']), function ($q) use ($params) {
                 $q->fatherName($params['father_name']);
            })
            ->when(isset($params['gender']), function ($q) use ($params) {
                $q->gender($params['gender']);
            })
            ->when(isset($params['birthdate']), function ($q) use ($params) {
                 $q->birthdate($params['birthdate']);
            })
            ->when(isset($params['from_date'], $params['to_date']), function ($q) use ($params) {
                $q->betweenDate($params['from_date'], $params['to_date']);
            });
    }

    /**
     * @return string[]
     */
    public function selectedColumnsForList(): array
    {
        return [
            'id',
            'name',
            'surname',
            'father_name',
            'birthdate',
            'pin',
            'note',
            'photo',
            'status'
        ];
    }

    /**
     * @param int $limit
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function paginate(int $limit = 15): LengthAwarePaginator
    {
        return $this->getByParams($this->params['params'])
            ->orderBy('id', 'desc')
            ->paginate($limit);
    }
}
