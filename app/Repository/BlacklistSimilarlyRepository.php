<?php

namespace App\Repository;

use App\Models\BlacklistSimilarly;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

class BlacklistSimilarlyRepository extends AbstractRepository {

    /**
     * BlacklistRepository constructor
     *
     * @param \App\Models\BlacklistSimilarly $model
     */
    public function __construct(BlacklistSimilarly $model)
    {
        parent::__construct($model);
    }

    /**
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getByBlacklistId(int $id): Builder
    {
        return $this->buildQuery()->where(['blacklist_id' => $id])->orderBy('blacklist_id');
    }

    /**
     * @param array $params
     * @return mixed
     */
    public function getByParams(array $params): mixed
    {
        return $this->buildQuery()
            ->select($this->selectedColumnsForList())
//            ->where('status', true)
            ->whereIn('blacklist_id', $params['blacklist_ids'])
            ->when(isset($params['pin']), function ($q) use ($params) {
                $q->pin($params['pin']);
            })
            ->when(isset($params['name']), function ($q) use ($params) {
                $q->name($params['name']);
            })
            ->when(isset($params['surname']), function ($q) use ($params) {
                $q->surname($params['surname']);
            })
            ->when(isset($params['father_name']), function ($q) use ($params) {
                $q->fatherName($params['father_name']);
            })
            ->when(isset($params['birthdate']), function ($q) use ($params) {
                $q->birthdate($params['birthdate']);
            })
            ->when(isset($params['gender']), function ($q) use ($params) {
                $q->gender($params['gender']);
            })
            ->when(isset($params['alignment_error']), function ($q) use ($params) {
                $q->alignmentError($params['alignment_error']);
            })
            ->when(isset($params['from_date'], $params['to_date']), function ($q) use ($params) {
                $q->betweenDate($params['from_date'], $params['to_date']);
            })
            ->when(isset($params['camera_ids']), function ($q) use ($params) {
                $q->cameras($params['camera_ids']);
            });
    }

    /**
     * @return string[]
     */
    public function selectedColumnsForList(): array
    {
        return [
            'id',
            'name',
            'surname',
            'father_name',
            'birthdate',
            'pin',
            'photo',
            'similarity_photo',
            'face_coordinates',
            'distance',
            'timestamp',
            'blacklist_id',
            'camera_id',
            'camera_ip',
        ];
    }

    /**
     * @param int $limit
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function paginate(int $limit = 15): LengthAwarePaginator
    {
        return $this->getByParams($this->params['params'])
            ->orderBy('created_at', 'desc')
            ->paginate($limit);
    }
}
