<?php

namespace App\Repository;

use App\Models\Oracle\Ecnebi;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class ForeignCustomsRepository
{
    /**
     * @param $params
     * @param $perPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getPeopleFromOracleServer($params, $perPage): LengthAwarePaginator
    {
        return Ecnebi::query()
            ->when(isset($params['ids']), function ($q) use ($params) {
                $q->whereByIds($params['ids']);
            })
            ->when(isset($params['name']), function ($q) use ($params) {
                $q->whereByName($params['name']);
            })
            ->when(isset($params['surname']), function ($q) use ($params) {
                $q->whereBySurName($params['surname']);
            })
            ->when(isset($params['passport_id']), function ($q) use ($params) {
                $q->whereByPassportId($params['passport_id']);
            })
            ->when(isset($params['direction']), function ($q) use ($params) {
                $q->whereByDirection($params['direction']);
            })
            ->when(isset($params['citizenship']), function ($q) use ($params) {
                $q->whereByCitizenship($params['citizenship']);
            })
            ->when(isset($params['precinct']), function ($q) use ($params) {
                $q->whereByPrecinct($params['precinct']);
            })
            ->when(isset($params['birthdate']), function ($q) use ($params) {
                $q->whereByBirthDate($params['birthdate']);
            })
            ->select(
                'ID',
                'CROSSING_DATE',
                'BIRTH_DATE',
                'DIRECTION',
                'MENTEQE',
                'VETENDASHLIGI',
                'GIVEN_NAME',
                'SURNAME',
                'DOCUMENT_NUMBER'
            )
            ->orderBy('id', 'desc')
            ->paginate($perPage);
    }

    /**
     * @param $rowId
     * @return \Illuminate\Database\Eloquent\Collection|array
     */
    public function getDocumentNumberFromOracleServer($rowId): Collection|array
    {
        return DB::connection('oracle')
            ->select(DB::raw("select ID,
                CROSSING_DATE,
                BIRTH_DATE,
                DIRECTION,
                MENTEQE,
                VETENDASHLIGI,
                GIVEN_NAME,
                SURNAME,
                DOCUMENT_NUMBER, STATUS from ECNEBI2020 where ID = ".$rowId));
    }
    /**
     * @param $params
     * @param $perPage
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function getPeopleFromRawQueryWithPaginate($params, $perPage): LengthAwarePaginator
    {
        $query = DB::connection('oracle')
            ->table("ECNEBI2020");

        $query = $query->select([
            "ID",
            "CROSSING_DATE",
            "BIRTH_DATE",
            "DIRECTION",
            "MENTEQE",
            "VETENDASHLIGI",
            "GIVEN_NAME",
            "SURNAME",
            "DOCUMENT_NUMBER"
        ]);

        if (isset($params['name'])) {
            $query->where('GIVEN_NAME', 'like', '%'.$params['name'].'%');
        }

        if (isset($params['surname'])) {
            $query->where('SURNAME', 'like', '%'.$params['surname'].'%');
        }

        if (isset($params['passport_id'])) {
            $query->where('DOCUMENT_NUMBER', $params['passport_id']);
        }

        if (isset($params['direction'])) {

            $direction = 'Cixish';

            if ((int) $params['direction'] === 1) {
                $direction = 'Cixish';
            }

            $query->where('DIRECTION', $direction);
        }


        if (isset($params['vetendashligi'])) {
            $query->where('VETENDASHLIGI', $params['citizenship']);
        }

        if (isset($params['menteqe'])) {
            $query->where('MENTEQE', $params['precinct']);
        }

        if (isset($params['birthdate'])) {
            $query->where('BIRTH_DATE', $params['birthdate']);
        }


        return $query->paginate($perPage);
    }

    /**
     * @param $params
     * @param $perPage
     * @param $page
     * @return array
     */
    public function getPeopleFromRawQuery($params, $perPage, $page): array
    {
        $where = " WHERE STATUS in(0, 1, 2)";

        if (isset($params['name'])) {
            $where .= " AND GIVEN_NAME like '%".$params['name']."%'";
        }

        if (isset($params['surname'])) {
            $where .= " AND SURNAME like '%".$params['surname']."%'";
        }

        if (isset($params['passport_id'])) {
            $where .= " AND DOCUMENT_NUMBER='".$params['passport_id']."'";
        }

        if (isset($params['direction'])) {

            $direction = 'Cixish';

            if ((int) $params['direction'] === 1) {
                $direction = 'Girish';
            }

            $where .= " AND DIRECTION='".$direction."'";
        }

        if (isset($params['citizenship'])) {
            $where .= " AND VETENDASHLIGI='".$params['citizenship']."'";
        }

        if (isset($params['precinct'])) {
            $where .= " AND MENTEQE='".$params['precinct']."'";
        }

        if (isset($params['birthdate'])) {
            $where .= " AND BIRTH_DATE='".$params['birthdate']."'";
        }

        $offset = ((int) $page * (int) $perPage) - (int) $perPage;

        $sql = "select ID,CROSSING_DATE,BIRTH_DATE,DIRECTION,MENTEQE,VETENDASHLIGI,GIVEN_NAME,SURNAME,DOCUMENT_NUMBER from ECNEBI2020 ".$where." ORDER BY ID DESC OFFSET ".$offset." ROWS FETCH NEXT ".$perPage." ROWS ONLY";

        return DB::connection('oracle')->select(DB::raw($sql));
    }

    /**
     * @param $params
     * @return array
     */
    public function getPeopleCount($params): array
    {
        $where = " WHERE STATUS in(0, 1, 2)";

        if (isset($params['name'])) {
            $where .= " AND GIVEN_NAME like '%".$params['name']."%'";
        }

        if (isset($params['surname'])) {
            $where .= " AND SURNAME like '%".$params['surname']."%'";
        }

        if (isset($params['passport_id'])) {
            $where .= " AND DOCUMENT_NUMBER='".$params['passport_id']."'";
        }

        if (isset($params['direction'])) {

            $direction = 'Cixish';

            if ((int) $params['direction'] === 1) {
                $direction = 'Girish';
            }

            $where .= " AND DIRECTION='".$direction."'";
        }

        if (isset($params['citizenship'])) {
            $where .= " AND VETENDASHLIGI='".$params['citizenship']."'";
        }

        if (isset($params['precinct'])) {
            $where .= " AND MENTEQE='".$params['precinct']."'";
        }

        if (isset($params['birthdate'])) {
            $where .= " AND BIRTH_DATE='".$params['birthdate']."'";
        }

        $sql = "select COUNT(*) as count from ECNEBI2020 ".$where;

        return DB::connection('oracle')->select(DB::raw($sql));
    }
}
