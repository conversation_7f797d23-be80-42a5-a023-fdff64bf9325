<?php

namespace App\Repository;


use App\Models\Camera;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class CameraRepository extends AbstractRepository
{
    /**
     * @param \App\Models\Camera $model
     */
    public function __construct(Camera $model)
    {
        parent::__construct($model);
    }

    /**
     * @param int $limit
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function paginate(int $limit = 15): LengthAwarePaginator
    {
        return $this->buildQuery()
            ->when(!empty($this->params['camera_ids']), function ($query) {
                $query->whereIn('camera_id', string_to_int_from_array($this->params['camera_ids']));
            })
            ->when(!empty($this->params['nested_object_ids']), function ($query) {
                $query->whereIn('object_id', string_to_int_from_array($this->params['nested_object_ids']));
            })
            ->when(!empty($this->params['camera_type_id']), function ($query) {
                $query->whereIn('camera_type_id', string_to_int_from_array($this->params['camera_type_id']));
            })
            ->when($this->params['camera_name'] != "", function ($query) {
                $query->where('name', 'like',  '%'.$this->params['camera_name'].'%');
            })
            ->orderBy('id', 'desc')
            ->paginate($limit);
    }

    /**
     * @param array $params
     * @param int $limit
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function filterAndPaginate(array $params, int $limit = 10): LengthAwarePaginator
    {
        return  Camera::objectIds($params['object_ids'] ?? [])
            ->objectId($params['object_id'] ?? null)
            ->cameraIds($params['camera_ids'] ?? [])
            ->isActive($params['is_active'] ?? true)
            ->objectTypeIds($params['object_type_ids'] ?? [])
            ->objectTypeId($params['object_type_id'] ?? null)
            ->cameraTypeIds($params['camera_type_ids'] ?? [])
            ->orderBy('id', 'desc')
            ->paginate($limit);
    }

    /**
     * @param int $objectId
     * @return bool
     */
    public function hasCameraByObjectId(int $objectId): bool
    {
        return $this->buildQuery()
            ->where('object_id', $objectId)
            ->exists();
    }

    /**
     * @param int $cameraTypeId
     * @return bool
     */
    public function hasCameraByTypeId(int $cameraTypeId): bool
    {
        return $this->buildQuery()
            ->where('camera_type_id', $cameraTypeId)
            ->exists();
    }

    /**
     * @param array $cameraIds
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function findByCameraIds(array $cameraIds): Collection
    {
        return $this->buildQuery()
            ->with('object:id,name')
            ->whereIn('camera_id', $cameraIds)
            ->select('id', 'name', 'object_id', 'camera_type_id', 'camera_id', 'ip_address')
            ->get();
    }

    public function getByName(string $name): Collection|array
    {
        return $this->buildQuery()->where('name', 'like', '%' . $name . '%')
            ->orderBy('created_at', 'desc')
            ->get();
    }
}
