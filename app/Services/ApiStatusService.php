<?php

namespace App\Services;

use App\Models\Log;
use App\Models\Oracle\Azeri;
use App\Models\PersonData;
use Exception;
use Carbon\Carbon;
use App\Models\Person;
use Illuminate\Http\JsonResponse;
use App\Repository\PersonRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use App\Http\Resources\Person\PersonResource;
use App\Http\Resources\Person\PersonListResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use JetBrains\PhpStorm\ArrayShape;

class ApiStatusService
{


    public array $response = [
        'name' => 'UNKNOWN',
        'status' => 200,
        'message' => 'Connected',
        'type' => 'API',
        'host' => '*.*.*.*'
    ];

    public array $missingParams = [
        'carNumber' => '90DV829',
        'pin' => '24Q6J8A'
    ];

    public function getALLStatuses(): array
    {
        return [
            $this->carInfo(),
            $this->warrantVehicle(),
        ];
    }


    //masinin sahibi haqqinda melumat
    public function carInfo(): array
    {
        $this->response['name'] = 'Car Info Api';
        $this->response['host'] = '*.*.11.3:8185';
        try {
            $this->response['status'] = Http::get(config('servers.vehicle_host')."/v1/eIntegration/out/StateSecurityServiceWS/getVehicleForSSS/" . $this->missingParams['carNumber'] . "/123123")->status();
            $this->checkStatusForTelegramBot($this->response);
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }

    //masinin etibarnameleri haqqinda melumat
    public function warrantVehicle(): array
    {
        $this->response['name'] = 'Car Warrant Api';
        $this->response['host'] = '*.*.11.3:8185';
        try {
            $this->response['status'] = Http::get(config('servers.vehicle_host')."/v1/eIntegration/out/StateSecurityServiceWS/getProxyListByVehRegNumberForSSS/" . $this->missingParams['carNumber'] . "/123123")->status();
            $this->checkStatusForTelegramBot($this->response);
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }

    public function checkStatusForTelegramBot($response): void
    {
        if($response['status'] != 200)
        {
            sendServerStatusToTelegram($response);
        }
    }

}
