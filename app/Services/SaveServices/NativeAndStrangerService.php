<?php

namespace App\Services\SaveServices;

use App\Http\Controllers\Api\LocationController;
use App\Http\Requests\LocationRequests\NativeAndStrangersRequest;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Services\LocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class NativeAndStrangerService
{
    public function start(): void
    {
        $get_data = BeforeAdvancedSearchLog::query()
            ->where('search_type', 'native_and_strangers')
            ->where('status', '0')->get();

        foreach ($get_data as $key => $value) {
            $this->jobX($value);
        }
    }

    private function jobX($get_data = []): void
    {
        BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
            'retry' => 1,
            'status' => 3
        ]);
        try {
            $tab_value = $get_data->search_tab;
            usort($tab_value, function ($a, $b) {
                return $b['value'] <=> $a['value'];
            });
            foreach ($tab_value as $key_tab => $value_tab) {
                $page = 1;
                do {
                    $request = [
                        "per_page" => 100,
                        "page" => $page,
                        "from_Job" => "SaveNativesAndStrangers",
                    ];
                    $request_prompt = array_merge($get_data->search_params, $request);
                    $locationService = new LocationService();
                    $es = new LocationController($locationService);
                    $request = new NativeAndStrangersRequest();
                    $requestX = $request->merge($request_prompt);
                    $response = $es->nativeAndStrangers($requestX);
                    $response = collect($response)->toArray();
                    $tabs = $response['tabs'] ?? [[
                        'value' => 'native_and_strangers',
                        'name' => 'tab',
                        'label' => 'Özgələr və yerlilər',
                    ]];
                    $data = $response['original'];
                    $response_data = $data['data']['data'];
                    $response_data_X = [];
                    BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                        'name' => $get_data->name
                    ]);
                    foreach ($response_data as $key => $value) {
                        $response_data_X['parent_id'] = $get_data->id;
                        $response_data_X['user_id'] = $get_data->user_id;
                        $response_data_X['hash'] = $get_data->hash;
                        $response_data_X['request'] = collect($requestX)->toArray();
                        $response_data_X['response'] = collect($value)->toArray();
                        $response_data_X['type'] = $value_tab['value'];
                        $response_data_X['tab'] = json_encode($tabs, JSON_UNESCAPED_UNICODE);
                        $response_data_X['created_at'] = now();
                        AdvancedSearchLog::create($response_data_X);
                    }
                    if ($page == 1) {
                        BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                            'status' => 1,
                            'is_completed' => 0
                        ]);
                    }
                    $page++;
                } while ($data['data']['last_page']>= $page);
                if ($key_tab == count($tab_value) - 1) {
                    BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                        'status' => 1,
                        'is_completed' => 1,
                        'retry' => $get_data->retry + 1
                    ]);
                }
            }
        } catch (\Exception $e) {
            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'name' => $e->getFile() . $e->getLine() . $e->getMessage(),
            ]);
        }
    }
}
