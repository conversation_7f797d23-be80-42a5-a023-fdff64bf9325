<?php

namespace App\Services\SaveServices;

use App\Http\Controllers\Api\LocationController;
use App\Http\Requests\LocationRequests\FindCarRequest;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Services\LocationService;
use Illuminate\Support\Facades\Log;

class CarSimilarityService
{
    public function start(): void
    {
        $get_data = BeforeAdvancedSearchLog::query()
            ->where('search_type', 'find_car_similarity')
            ->where('status', '0')->get();
        foreach ($get_data as $key => $value) {
            $this->jobX($value);
        }
    }

    private function jobX($get_data): void
    {
        BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
            'retry' => 1,
            'status' => 3
        ]);
        try {
            $tab_value = $get_data->search_tab;
            usort($tab_value, function ($a, $b) {
                return $b['value'] <=> $a['value'];
            });
            foreach ($tab_value as $key_tab => $value_tab) {
                $page = 1;
                do {
                    $request = [
                        "per_page" => 100,
                        "page" => $page,
                        "from_Job" => "SaveFindCarSimilarityLogsAfterJob",
                    ];
                    $request_prompt = array_merge($get_data->search_params, $request);
                    $locationService = new LocationService(
                        config('services.loc_api_key'),
                        config('services.loc_base_url')
                    );
                    $es = new LocationController($locationService);
                    $request_X = new FindCarRequest($request_prompt);
                    $response = $es->findCar($request_X);
                    Log::info('find_location_by_phone_number response : ', ['data' => json_encode($response)]);
                    $response = collect($response)->toArray();
                    $tabs = $response['tabs'] ?? [[
                        'value' => 'find_car_similarity',
                        'name' => 'tab',
                        'label' => 'Maşında ehtimal olunan şəxslərin telefon nömrələri (Maşın nömrəsinə görə)',
                    ]];
                    $data = $response['original'];
                    $data = json_decode(json_encode($data), true);
                    $response_data = $data['data'];
                    $original_data = $response_data['data'];
                    Log::debug('find_car_similarity response : ', ['data' => json_encode($original_data)]);
                    $response_data_X = [];
                    foreach ($original_data['dataResult'] as $key => $value) {
                        $response_data_X['parent_id'] = $get_data->id;
                        $response_data_X['user_id'] = $get_data->user_id;
                        $response_data_X['hash'] = $get_data->hash;
                        $response_data_X['request'] = collect($request_X)->toArray();
                        $response_data_X['response'] = collect($value)->toArray();
                        $response_data_X['type'] = $value_tab['value'];
                        $response_data_X['tab'] = json_encode($tabs, JSON_UNESCAPED_UNICODE);
                        $response_data_X['created_at'] = now();
                        AdvancedSearchLog::create($response_data_X);
                    }
                    $page++;
                } while ($response_data['last_page'] >= $page);
                if ($key_tab == count($tab_value) - 1) {
                    BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                        'status' => 1,
                        'retry' => $get_data->retry + 1,
                    ]);
                }
            }
        }catch (\Exception $exception){
            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'name' => $exception->getMessage() . $exception->getFile() . $exception->getLine(),
            ]);
        }
    }
}
