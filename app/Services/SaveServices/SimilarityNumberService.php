<?php

namespace App\Services\SaveServices;

use App\Http\Controllers\Api\LocationController;
use App\Http\Requests\LocationRequests\FindPhoneSimilarityRequest;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Services\LocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SimilarityNumberService
{
    public function start(): void
    {
        $get_data = BeforeAdvancedSearchLog::query()
            ->where('search_type', 'similarity_by_number')
            ->where('status', '0')->get();
        foreach ($get_data as $key => $value) {
            $this->jobX($value);
        }
    }

    private function jobX($get_data = []): void
    {
        BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
            'retry' => 1,
            'status' => 3
        ]);
        try {
            $tab_value = $get_data->search_tab;
            usort($tab_value, function ($a, $b) {
                return $b['value'] <=> $a['value'];
            });
            foreach ($tab_value as $key_tab => $value_tab) {
                $page = 1;
                do {
                    $request = [
                        "per_page" => 100,
                        "page" => $page,
                        "from_Job" => "SaveSimilarityByNumber",
                        "pin" => $get_data->search_params['pin']
                    ];
                    $request_prompt = array_merge($get_data->search_params, $request);
                    $locationService = new LocationService(
                        config('services.loc_api_key'),
                        config('services.loc_base_url')
                    );
                    $es = new LocationController($locationService);
                    $request = new FindPhoneSimilarityRequest();
                    $requestX = $request->merge($request_prompt);
                    $response = $es->getSocialCallLocationWithOthersNew($requestX);
                    Log::debug('similarity_by_number response : ', ['data' => json_encode($response)]);
                    $response = collect($response)->toArray();
                    $tabs = $response['tabs'] ?? [[
                        'value' => 'similarity_by_number',
                        'name' => 'tab',
                        'label' => 'Oxşar trayektoriyalar (Telefon nömrəsi ilə)',
                    ]];
                    $data = $response['original'];
                    $response_data = $data['data']['data']['dataMap'];
                    $response_data_X = [];
                    foreach ($response_data as $key => $value) {
                        $response_data_X['parent_id'] = $get_data->id;
                        $response_data_X['user_id'] = $get_data->user_id;
                        $response_data_X['hash'] = $get_data->hash;
                        $response_data_X['request'] = collect($requestX)->toArray();
                        $response_data_X['response'] = collect($value)->toArray();
                        $response_data_X['type'] = $value_tab['value'];
                        $response_data_X['tab'] = json_encode($tabs, JSON_UNESCAPED_UNICODE);
                        $response_data_X['created_at'] = now();
                        AdvancedSearchLog::create($response_data_X);
                    }
                    $page++;
                } while ($data['data']['last_page'] >= $page);

                if ($key_tab == count($tab_value) - 1) {
                    BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                        'status' => 1,
                        'retry' => $get_data->retry + 1
                    ]);
                }
            }
        }
        catch (\Exception $e) {
            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'name' => $e->getMessage(),
            ]);
        }
    }
}
