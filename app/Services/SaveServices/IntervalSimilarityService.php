<?php

namespace App\Services\SaveServices;

use App\Http\Controllers\Api\LocationController;
use App\Http\Requests\LocationRequests\FindIntervalSimilarityRequest;
use App\Models\AdvancedSearchLog;
use App\Models\BeforeAdvancedSearchLog;
use App\Services\LocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class IntervalSimilarityService
{
    public function start(): void
    {
        $get_data = BeforeAdvancedSearchLog::query()
            ->where('search_type', 'find_interval_similarity')
            ->where('status', '0')->get();
        foreach ($get_data as $key => $value) {
            $this->jobX($value);
        }
    }

    private function jobX($get_data): void
    {
        $get_data->update([
            'retry' => 1,
            'status' => 3,
        ]);
        try {
            $tab_value = $get_data->search_tab;

            usort($tab_value, function ($a, $b) {
                return $b['value'] <=> $a['value'];
            });

            foreach ($tab_value as $key_tab => $value_tab) {

                $request = [
                    "per_page" => 100,
                    "page" => 1,
                    "from_Job" => "SaveFindIntervalSimilarityLogsAfterJob",
                ];
                $request_prompt = array_merge($get_data->search_params, $request);
                $locationService = new LocationService();
                $es = new LocationController($locationService);
                $request = new FindIntervalSimilarityRequest();
                $request_X = $request->merge($request_prompt);
                $response = $es->findIntervalSimilarity($request_X);
                $response = collect($response)->toArray();
                $tabs = $response['tabs'] ?? [[
                    'value' => 'find_interval_similarity',
                    'name' => 'tab',
                    'label' => 'İki şəxsin səfər ehtimalı',
                ]];
                $data = $response['original'];
                $response_data = $data['data']['data']['dataResult'];
                foreach ($response_data as $value) {
                    $response_data_X = [
                        'parent_id' => $get_data->id,
                        'user_id' => $get_data->user_id,
                        'hash' => $get_data->hash,
                        'request' => $get_data->search_params,
                        'response' => $value,
                        'type' => $value_tab['value'],
                        'tab' => $tabs,
                        'created_at' => now(),
                    ];
                    AdvancedSearchLog::create($response_data_X);
                }
                BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                    'status' => 1,
                    'retry' => $get_data->retry + 1,
                ]);

            }
        } catch (\Exception $exception) {
            BeforeAdvancedSearchLog::query()->where('id', $get_data->id)->update([
                'name' => $exception->getMessage() . $exception->getFile() . $exception->getLine(),
            ]);
        }
    }
}
