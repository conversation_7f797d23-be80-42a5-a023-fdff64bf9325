<?php

namespace App\Services;

use App\Http\Resources\RelationTwoPersonResource;
use App\Models\Voen;
use App\Services\EHDIS\VoenListService;
use App\Services\Neo4J\Neo4JAdapterInterface;
use http\Env\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use JetBrains\PhpStorm\ArrayShape;

use function Aws\map;

class RelationNebulaServiceNew
{


    public function __construct(
        protected PersonService $personService,
    )
    {
    }
    private function translateRole($role): string
    {
        $translations = [
            'family' => 'Ailə',
            'workmate' => 'İş yoldaşı',
            'classmate' => 'Sinif yoldaşı',
            'neighbor' => 'Qonşu',
            'university' => 'Qrup yoldaşı',
        ];

        return $translations[$role] ?? $role;
    }

    #[ArrayShape(['status' => "bool", 'type' => "string", 'people' => "array"])]
    public function getRelationBetweenTwoPersonNew($request)
    {

        try {

            $per_page = $request['per_page'] ?? 30;
            $from = '1CFJ6DL';
            $to = '6JPYGAZ';

//            $client = new NGraphClient('10.14.71.71', 9669);

            $client = new NGraphClient(config('servers.ngraph'), config('servers.ngraph_port'));

            $client->authenticate(config('servers.ngraph_username'), config('servers.ngraph_password'));

            $query = 'USE beein_x;
                MATCH p = allShortestPaths( (a)-[e*..5]-(b) )
                WHERE id(a) == "'.$from.'" and id(b)  == "'.$to.'"
                WITH count(p) as total_count
                MATCH p = allShortestPaths( (a)-[e*..5]-(b) )
                WHERE id(a) == "'.$from.'" and id(b)  == "'.$to.'"
                WITH total_count, p, nodes(p) AS pathNodes, relationships(p) AS pathEdges
                RETURN total_count, p,
                REDUCE(w = 0.0, idxE IN pathEdges | w +
                idxE.classmate*20 + idxE.family*40 + idxE.neighbor*10 + idxE.university*10  + idxE.workmate*30 +
                + (29.89899 + 0.10101 *  min([idxE.photo, 100.00])[1])) AS pathWeight,
                hash(pathNodes) as pathNodesHash
                ORDER BY pathWeight DESC, pathNodesHash ASC
                SKIP 0 LIMIT ' . $per_page . ';';

            $apiData = $client->executeJson($query);

            $response = json_decode($apiData, true);


            echo '<pre>';
            print_r($response);

            die;


            $mainPins = [strtoupper($request['from']), strtoupper($request['to'])];

            if ($response['status'] && !empty($response['data'])) {

                $allPins = [];
                $workName = 'UNKNOWN';
                $workNames = [];

                foreach ($response['data'] as $query) {
                    foreach ($query['query']['nodes'] as $node) {
                        $allPins[] = $node['id'];
                    }
                }

                $params['pins'] = array_values(array_flip(array_flip($allPins)));

                $people = $this->personService->paginateListsByParams($params, 1000, 0);

                $all_people = [];
                foreach ($people->original as $person) {
                    $all_people[$person->pin] = $person;
                }

                foreach ($response['data'] as $key => $query) {
                    foreach ($query['query']['nodes'] as $key2 => $node) {
                        $response['data'][$key]['query']['nodes'][$key2]['properties'] = isset($all_people[$node['id']]) ? RelationTwoPersonResource::make($all_people[$node['id']]) : [];

                        if (in_array($node['id'], $mainPins)) {
                            $response['data'][$key]['query']['nodes'][$key2]['isMain'] = true;
                        }

                        if (isset($response['data'][$key]['query']['relationships'][$key2]['type']) &&
                            $response['data'][$key]['query']['relationships'][$key2]['type'] == 'Workmate') {

                            $response['data'][$key]['query']['relationships'][$key2]['properties']['workName'] = $workNames[$response['data'][$key]['query']['relationships'][$key2]['properties']['code']] ?? $workName;
                        }
                    }
                }
            }

            return $response;


        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }


    public function generateTypeString($types): string
    {
        $typeForQuery = '';
        //(e.classmate>0 OR e.neighbor>0)
        if (isset($types) and !empty($types))
        {
            $typeForQuery = ' AND ';
            foreach ($types as $key => $value)
            {
                $value = strtolower($value);
                if($key == 0)
                {
                    $typeForQuery .= '(e.'.$value.'>0';
                }else{
                    $typeForQuery .= ' OR e.'.$value.'>0';
                }
            }
            $typeForQuery .= ')';
        }
        return $typeForQuery;

    }
}

