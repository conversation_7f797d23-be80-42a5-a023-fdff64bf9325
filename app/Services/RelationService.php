<?php

namespace App\Services;

use App\Http\Resources\RelationTwoPersonResource;
use App\Models\Role;
use App\Models\Voen;
use App\Services\EHDIS\VoenListService;
use App\Services\Neo4J\Neo4JAdapterInterface;
use http\Env\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use JetBrains\PhpStorm\ArrayShape;
use function Aws\map;

class RelationService
{
    public const BASIC_SEARCH = 'basic';
    public const SHORTEST_SEARCH = 'shortest';
    public const ALL_SHORTEST_SEARCH = 'all_shortest';

    public const RELATION_STATIC_TOKEN = 'cfH2CIJolTNj2J3fk8MF614zmw7MFauNrSXEjb0oh8RIHyZGFgPpKaioPSzMX7DB';

    private string $milvus_host;

    public function __construct(
        protected PersonService $personService,
    )
    {
        $this->milvus_host = config('servers.nebula');
    }

    /**
     * @param array $params
     * @return array
     */
    #[ArrayShape(['status' => "bool", 'data' => "array|mixed"])]
    public function run(array $params)
    {

        $data = $this->getPersonSubGraph($params);

//        $searchType = $params['search_type'] ?? self::BASIC_SEARCH;
//
//        if ($searchType === self::BASIC_SEARCH) {
//            $data = $this->search($params, new Neo4JBasicSearchAdapter(new Neo4JBasicSearch));
//        } elseif ($searchType === self::SHORTEST_SEARCH) {
//            $data = $this->search($params, new Neo4JShortestSearchAdapter(new Neo4JShortestJSearch));
//        } else {
//            $data = $this->search($params, new Neo4JAllShortestSearchAdapter(new Neo4JAllShortestJSearch));
//        }

        return [
            'status' => !empty($data),
            'data' => $data['data']
        ];
    }

    /**
     * @param array $params
     * @param \App\Services\Neo4J\Neo4JAdapterInterface $neo4JAdapter
     * @return mixed
     */
    private function search(array $params, Neo4JAdapterInterface $neo4JAdapter): mixed
    {
        $personParams = [
            'filter_type' => $params['filter_type'] ?? 'pin',
            'value' => $params['value'] ?? null,
            'second_filter_type' => $params['second_filter_type'] ?? 'pin',
            'second_value' => $params['second_value'] ?? null,
        ];

        return $neo4JAdapter->processQuery($params, $personParams);
    }


    #[ArrayShape(['status' => "bool", 'type' => "mixed|string", 'people' => "array|\Illuminate\Http\JsonResponse"])]
    public function relationBetweenTwoPerson($request)
    {
        $response = Http::withHeaders([
            'x-api-key' => self::RELATION_STATIC_TOKEN
        ])
            ->post($this->milvus_host.'/find_shortest_path', [
                'from_node' => strtoupper($request['from']),
                'to_node' => strtoupper($request['to']),
            ])->json();

        $mainPins = [strtoupper($request['from']), strtoupper($request['to'])];

        if ($response['status'] && !empty($response['data'])) {

            $allPins = [];
            $findVoen = false;
            $workName = 'UNKNOWN';
            $workNames = [];
            $voens = [];
            foreach ($response['data'] as $query) {
                foreach ($query['query']['relationships'] as $relationship) {
                    if (isset($relationship['properties']['type']) && $relationship['properties']['type'] == 'worker' && !in_array($relationship['properties']['code'], $voens)) {
                        try {
                            $getVoenData = VoenListService::run($relationship['properties']['code']);

                            if ($getVoenData['data']['faultCode'] != 0) {
                                $workNames[$relationship['properties']['code']] = $getVoenData['data']['LegalPayerEnt']['fullName'];
                            }
                        } catch (\Exception $exception) {
                        }
                    }
                }
                foreach ($query['query']['nodes'] as $node) {
                    $allPins[] = $node['id'];
                }
            }

            $params['pins'] = array_values(array_flip(array_flip($allPins)));

            $people = $this->personService->paginateListsByParams($params, 1000, 0);

            $all_people = [];
            foreach ($people->original as $person) {
                $all_people[$person->pin] = $person;
            }

            foreach ($response['data'] as $key => $query) {
                foreach ($query['query']['nodes'] as $key2 => $node) {
                    $response['data'][$key]['query']['nodes'][$key2]['properties'] = isset($all_people[$node['id']]) ? RelationTwoPersonResource::make($all_people[$node['id']]) : [];

                    if (in_array($node['id'], $mainPins)) {
                        $response['data'][$key]['query']['nodes'][$key2]['isMain'] = true;
                    }

                    if (isset($response['data'][$key]['query']['relationships'][$key2]['type']) &&
                        $response['data'][$key]['query']['relationships'][$key2]['type'] == 'Workmate') {

                        $response['data'][$key]['query']['relationships'][$key2]['properties']['workName'] = $workNames[$response['data'][$key]['query']['relationships'][$key2]['properties']['code']] ?? $workName;
                    }
                }
            }
        }

        return $response;
    }

    public function relationBetweenTwoPersonPagination($request): JsonResponse
    {

        $currentPage = $request['page_number'] ?? 0;

        $params = [
            'from_node' => strtoupper($request['from']),
            'to_node' => strtoupper($request['to']),
            "page_size" => 1,
            "page_number" => $currentPage - 1
        ];

        if (isset($request['types']) && !empty($request['types'])) {
            $params['rel_types'] = $request['types'];
        }

        try {
            $response = Http::withHeaders([
                'x-api-key' => self::RELATION_STATIC_TOKEN
            ])
                ->post($this->milvus_host.'/v2/find-shortest-path', $params)->json();


            $mainPins = [strtoupper($request['from']), strtoupper($request['to'])];

            if ($response['result']['status'] && !empty($response['result']['data'])) {

                $allPins = [];
                $findVoen = false;
                $workName = 'UNKNOWN';
                $workNames = [];
                $voens = [];
                foreach ($response['result']['data'] as $query) {
                    foreach ($query['query']['relationships'] as $relationship) {
                        if (isset($relationship['properties']['type']) && $relationship['properties']['type'] == 'worker' && !in_array($relationship['properties']['code'], $voens)) {
                            try {
                                $getVoenData = VoenListService::run($relationship['properties']['code']);

                                if ($getVoenData['data']['faultCode'] != 0) {
                                    $workNames[$relationship['properties']['code']] = $getVoenData['data']['LegalPayerEnt']['fullName'];
                                }
                            } catch (\Exception $exception) {
                            }
                        }
                    }
                    foreach ($query['query']['nodes'] as $node) {
                        $allPins[] = $node['id'];
                    }
                }

                $params['pins'] = array_values(array_flip(array_flip($allPins)));

                $people = $this->personService->paginateListsByParams($params, 1000, 0);

                $all_people = [];
                foreach ($people->original as $person) {
                    $all_people[$person->pin] = $person;
                }

                foreach ($response['result']['data'] as $key => $query) {
                    foreach ($query['query']['nodes'] as $key2 => $node) {
                        $response['result']['data'][$key]['query']['nodes'][$key2]['properties'] = isset($all_people[$node['id']]) ? RelationTwoPersonResource::make($all_people[$node['id']]) : [];

                        if (in_array($node['id'], $mainPins)) {
                            $response['result']['data'][$key]['query']['nodes'][$key2]['isMain'] = true;
                        }

                        if (isset($response['result']['data'][$key]['query']['relationships'][$key2]['type']) &&
                            $response['result']['data'][$key]['query']['relationships'][$key2]['type'] == 'Workmate') {

                            $response['result']['data'][$key]['query']['relationships'][$key2]['properties']['workName'] = $workNames[$response['result']['data'][$key]['query']['relationships'][$key2]['properties']['code']] ?? $workName;
                        }
                    }
                }
            }


            $perPage = count($response['result']['data']); // data count per page
            if ($perPage == 0) {
                return response()->json('Data Not Found', 404);
            }
            $collection = new Collection($response['result']['data']); // Convert the data to a collection


            $paginatedData = new LengthAwarePaginator(
//            $collection->forPage($currentPage, $perPage),
                $collection,
                $response['total_count'] ?? 10,
                $perPage,
                $currentPage,
                ['path' => url('/relation/between-two-person-pagination')]
            );

            return response()->json($paginatedData)->setEncodingOptions(JSON_NUMERIC_CHECK);
        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    public function relationBetweenTwoPersonTabular($request)
    {

        $currentPage = $request['page_number'] ?? 1;
        $per_page = $request['per_page'] ?? 3;

        $params = [
            'from_node' => strtoupper($request['from']),
            'to_node' => strtoupper($request['to']),
            "page_size" => $per_page,
            "page_number" => $currentPage - 1
        ];

        if (isset($request['types']) && !empty($request['types'])) {
            $params['rel_types'] = $request['types'];
        }

        try {
            $response = Http::withHeaders([
                'x-api-key' => self::RELATION_STATIC_TOKEN
            ])
                ->post($this->milvus_host.'/v3/find-shortest-path/tabular', $params)->json();

//            $response = $this->getPersonSubGraphNew($request);

            $mainPins = [strtoupper($request['from']), strtoupper($request['from'])];

            $logData = [
                "description" => $request['from']." və ".$request['from']." finləri arasında əlaqəni axtardı.",
                "type" => "relation"
            ];
            sendRequestLogToAudit($logData, "audit");

            if (\request('debug')) {
                return $response;
            }

            if ($response['result']['status'] && !empty($response['result']['data'])) {

                $allPins = [];
                $findVoen = false;
                $workName = 'UNKNOWN';
                $workNames = [];
                $voens = [];

                foreach ($response['result']['data'] as $data) {
                    foreach ($data['path_queries'] as $path_queries) {
                        foreach ($path_queries['query']['relationships'] as $relationship) {
                            if (isset($relationship['properties']['type']) && $relationship['properties']['type'] == 'worker' && !in_array($relationship['properties']['code'], $voens)) {
                                try {

                                    $getVoenFromDb = Voen::query()->where('code', $relationship['properties']['code'])->first();
                                    if (!empty($getVoenFromDb)) {
                                        $workNames[$relationship['properties']['code']] = $getVoenFromDb->name;
                                        $voens[] = $relationship['properties']['code'];
                                        continue;
                                    }

                                    $getVoenData = VoenListService::run($relationship['properties']['code']);

                                    $voens[] = $relationship['properties']['code'];
                                    if ($getVoenData['data']['faultCode'] != 0) {
                                        $workNames[$relationship['properties']['code']] = $getVoenData['data']['LegalPayerEnt']['fullName'];
                                        Voen::query()->create([
                                            'code' => $relationship['properties']['code'],
                                            'name' => $getVoenData['data']['LegalPayerEnt']['fullName'],
                                            'created_at' => now()
                                        ]);
                                    }
                                } catch (\Exception $exception) {
                                }
                            }
                        }
                    }
                    foreach ($data['path_pins'] as $path_pin) {
                        $allPins[] = $path_pin;
                    }
                }


                $params['pins'] = array_values(array_flip(array_flip($allPins)));

                $people = $this->personService->paginateListsByParams($params, 1000, 0);

                $all_people = [];
                foreach ($people->original as $person) {
                    $all_people[$person->pin] = $person;
                }

                foreach ($response['result']['data'] as $key => $query) {
                    foreach ($query['path_pins'] as $path_pin) {
                        $response['result']['data'][$key]['people'][] = isset($all_people[$path_pin]) ? RelationTwoPersonResource::make($all_people[$path_pin]) : [];
                    }
                    foreach ($query['path_queries'] as $order => $path_queries) {
                        foreach ($path_queries['query']['nodes'] as $key2 => $node) {
                            $response['result']['data'][$key]['path_queries'][$order]['query']['nodes'][$key2]['properties'] = isset($all_people[$node['id']]) ? RelationTwoPersonResource::make($all_people[$node['id']]) : [];

                            if (in_array($node['id'], $mainPins)) {
                                $response['result']['data'][$key]['path_queries'][$order]['query']['nodes'][$key2]['isMain'] = true;
                            }

                            if (isset($response['result']['data'][$key]['path_queries'][$order]['query']['relationships'][$key2]['type']) &&
                                $response['result']['data'][$key]['path_queries'][$order]['query']['relationships'][$key2]['type'] == 'Workmate') {

                                $response['result']['data'][$key]['path_queries'][$order]['query']['relationships'][$key2]['properties']['workName'] = $workNames[$response['result']['data'][$key]['path_queries'][$order]['query']['relationships'][$key2]['properties']['code']] ?? $workName;
                            }
                        }
                    }
                }
            } else {
                return response()->json('Data Not Found', 404);
            }

            $collection = new Collection($response['result']['data']); // Convert the data to a collection

            $paginatedData = new LengthAwarePaginator(
                $collection,
                $response['total_count'] ?? 10,
                $per_page,
                $currentPage,
                ['path' => url('/relation/between-two-person-tabular')]
            );

            return response()->json($paginatedData)->setEncodingOptions(JSON_NUMERIC_CHECK);

        } catch (\Exception $exception) {
            return response()->json($exception->getMessage());
        }
    }

    public function getPersonSubGraph($request)
    {
        try {

            $params = [
                'node_id' => strtoupper($request['value']),
                'num_steps' => strtoupper($request['relation_level']),
            ];


            if (isset($request['types']) && !empty($request['types'])) {
                $params['rel_types'] = $request['types'];
            }

            $response = Http::withHeaders([
                'x-api-key' => self::RELATION_STATIC_TOKEN
            ])
                ->post($this->milvus_host.'/get_subgraph', $params)->json();

            if ($response['status'] && !empty($response['data'])) {
                $allPins = [];
                $findVoen = false;
                $workName = 'UNKNOWN';
                foreach ($response['data'] as $query) {
                    foreach ($query['query']['relationships'] as $relationship) {
                        $allPins[] = $relationship['dstId'];
                        $allPins[] = $relationship['srcId'];
                        if (!$findVoen && isset($relationship['properties']['type']) && $relationship['properties']['type'] == 'worker') {
                            try {
                                $getVoenData = VoenListService::run($relationship['properties']['code']);

                                if ($getVoenData['data']['faultCode'] != 0) {
                                    $workName = $getVoenData['data']['LegalPayerEnt']['fullName'];
                                }
                                $findVoen = true;
                            } catch (\Exception $exception) {
                            }
                        }
                    }
                }

                $params['pins'] = array_values(array_flip(array_flip($allPins)));

                $people_data = $this->personService->paginateListsByParams($params, 1500, 0);

//                return ['data' => $people_data];

                $all_people = [];
                foreach ($people_data->original as $person) {
                    $all_people[$person->pin] = $person;
                }

                foreach ($response['data'] as $key => $query) {
                    foreach ($query['query']['nodes'] as $key2 => $node) {
                        $response['data'][$key]['query']['nodes'][$key2]['properties'] = isset($all_people[$node['id']])
                            ? RelationTwoPersonResource::make($all_people[$node['id']])
                            : [];

                        if ($findVoen && isset($response['data'][$key]['query']['relationships'][$key2]['type']) &&
                            $response['data'][$key]['query']['relationships'][$key2]['type'] == 'Workmate') {

                            $response['data'][$key]['query']['relationships'][$key2]['properties']['workName'] = $workName;
                        }
                    }
                }

            }


            return $response;
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }

    }

    //shexsin elaqelerinin table formasinda gorunmesi
    #[ArrayShape(['status' => "bool", 'type' => "string", 'people' => "array"])]
    public function getPersonSubGraphWithTable($request)
    {

        try {

            $currentPage = $request['page_number'] ?? 0;

            $params = [
                'node_id' => strtoupper($request['value']),
                'page_size' => $request['per_page'] ?? 30,
                'page_number' => $currentPage - 1,
            ];

            if (isset($request['types']) && !empty($request['types'])) {
                $params['rel_types'] = $request['types'];
            }

            $response = Http::withHeaders([
                'x-api-key' => self::RELATION_STATIC_TOKEN
            ])
                ->post($this->milvus_host.'/get_first_degree_relations', $params)->json();

            $res = [
                'status' => false,
                'type' => 'Not Found',
                'people' => []
            ];

            if ($response['result']['status'] && !empty($response['result']['data'])) {
                $allPins = [];
                foreach ($response['result']['data'] as $query) {
                    foreach ($query['query']['relationships'] as $relationship) {
                        $allPins[] = $relationship['dstId'];
                        $allPins[] = $relationship['srcId'];
                    }
                }

                $params['pins'] = array_values(array_flip(array_flip($allPins)));

                $people_data = $this->personService->paginateListsByParams($params, 1000, 0);

                $all_people = [];
                foreach ($people_data->original as $person) {
                    $all_people[$person->pin] = $person;
                }

                foreach ($response['result']['data'] as $key => $query) {

                    foreach ($query['query']['nodes'] as $key2 => $node) {
                        $response['result']['data'][$key]['query']['nodes'][$key2]['properties'] = isset($all_people[$node['id']]) ? RelationTwoPersonResource::make($all_people[$node['id']]) : [];
                    }

                    foreach ($query['query']['relationships'] as $key2 => $relationship) {
                        if (isset($response['result']['data'][$key]['query']['relationships'][$key2]['type']) &&
                            $response['result']['data'][$key]['query']['relationships'][$key2]['type'] == 'Workmate') {

                            $response['result']['data'][$key]['query']['relationships'][$key2]['properties']['workName'] = $workNames[$response['result']['data'][$key]['query']['relationships'][$key2]['properties']['code']] ?? "";
                        }
                    }
                }
            }

            $perPage = $response['page_size'] ?? 30; // data count per page
            $collection = new Collection($response['result']['data']);

            $paginatedData = new LengthAwarePaginator(
                $collection,
                $response['total_count'] ?? 30,
                $perPage,
                $currentPage,
                ['path' => url('/relation/get-person-subgraph')]
            );

            return response()->json($paginatedData)->setEncodingOptions(JSON_NUMERIC_CHECK);

        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }

    public static function getListRelationTypes()
    {
//        try {
//            return Http::withHeaders([
//                'x-api-key' => self::RELATION_STATIC_TOKEN
//            ])
//                ->timeout(20)
//                ->get($this->milvus_host.'/relation_data')->json();
//
//        } catch (\Exception $exception) {
//            return $exception->getMessage();
//        }

        $user = auth('api')->user();
        $user->setRelation('permissions', $user->getAllPermissions());
        $canViewCdrCallCount = $user->checkPermissionTo('permission:relations:social-calls', 'api');


        $lang = request('lang');


        $translations = [
            'en' => [
                'Classmate' => 'Classmate',
                'Photo' => 'Photo(Social Media)',
                'Family' => 'Family',
                'Relative' => 'Relative',
                'Neighbor' => 'Neighbor',
                'University' => 'University',
                'Workmate' => 'Workmate',
                'Resident' => 'Resident',
                'cdr_call_count' => 'Social Call'
            ],
            'az' => [
                'Classmate' => 'Sinif yoldaşı',
                'Photo' => 'Foto(Sosial Media)',
                'Family' => 'Ailə',
                'Relative' => 'Qohum',
                'Neighbor' => 'Qonşu',
                'University' => 'Universitet yoldaşı',
                'Workmate' => 'İş yoldaşı',
                'Resident' => 'Rezident',
                'cdr_call_count' => 'Sosial Zəng'
            ],
            'ar' => [
                'Classmate' => 'زميل الدراسة',
                'Photo' => 'صورة (وسائل التواصل الاجتماعي)',
                'Family' => 'عائلة',
                'Relative' => 'قريب',
                'Neighbor' => 'جار',
                'University' => 'زميل جامعي',
                'Workmate' => 'زميل العمل',
                'Resident' => 'مقيم',
                'cdr_call_count' => 'مكالمة اجتماعية'
            ]
        ];

        if (!isset($translations[$lang])) {
            $lang = 'en';
        }

        $types = [
            [
                "type" => "Classmate",
                "description" => $translations[$lang]['Classmate']
            ],
            [
                "type" => "Photo",
                "description" => $translations[$lang]['Photo']
            ],
            [
                "type" => "Family",
                "description" => $translations[$lang]['Family']
            ],
            [
                "type" => "Relative",
                "description" => $translations[$lang]['Relative']
            ],
            [
                "type" => "Neighbor",
                "description" => $translations[$lang]['Neighbor']
            ],
            [
                "type" => "University",
                "description" => $translations[$lang]['University']
            ],
            [
                "type" => "Workmate",
                "description" => $translations[$lang]['Workmate']
            ],
            [
                "type" => "Resident",
                "description" => $translations[$lang]['Resident']
            ],
        ];

        if ($canViewCdrCallCount) {
            $types[] = [
                'type' => 'cdr_call_count',
                'description' => $translations[$lang]['cdr_call_count']
            ];
        }

        return $types;

    }


    private function translateRole($role): string
    {
        $translations = [
            'Family' => 'Ailə',
            'Relative' => 'Qohum',
            'Workmate' => 'İş yoldaşı',
            'Classmate' => 'Sinif yoldaşı',
            'Neighbor' => 'Qonşu',
            'University' => 'Universitet yoldaşı',
            'Resident' => 'Rezident',
            'Photo' => 'Foto(Sosial Media)',
            'cdr_call_count' => 'Sosial Zəng',
            'cdr_call_duration' => '',
        ];

        return $translations[$role] ?? $role;
    }

    // for php - new

    //shexsin elaqelerinin table formasinda gorunmesi
    #[ArrayShape(['status' => "bool", 'type' => "string", 'people' => "array"])]
    public function getPersonSubGraphNew($request)
    {

        try {

            $client = new NGraphClient(config('servers.ngraph'), 9669);

            $client->authenticate("app_beein", "T7gPa9eY");

            $query = 'USE beein_x;
                MATCH p = allShortestPaths( (a)-[e*..5]-(b) )
                WHERE id(a) == "8WDWGGZ" and id(b)  == "8NZWK7C"
                WITH count(p) as total_count
                MATCH p = allShortestPaths( (a)-[e*..5]-(b) )
                WHERE id(a) == "8WDWGGZ" and id(b)  == "8WDWGGZ"
                WITH total_count, p, nodes(p) AS pathNodes, relationships(p) AS pathEdges
                RETURN total_count, p,
                REDUCE(w = 0.0, idxE IN pathEdges | w + idxE.weight) AS pathWeight, hash(pathNodes) as pathNodesHash
                ORDER BY pathWeight ASC, pathNodesHash ASC
                SKIP 0 LIMIT 10;';

            $apiData = $client->executeJson($query);

            $apiData = json_decode($apiData, true);

            $newFormatData = [
                "page_number" => 0,
                "page_size" => 1,
                "total_count" => count($apiData['results'][0]['data']),
                "current_count" => count($apiData['results'][0]['data']),
                "result" => [
                    "status" => true,
                    "data" => []
                ]
            ];


            foreach ($apiData['results'] as $result) {
                $path_pins = [];
                $path_queries = [];

                foreach ($result['data'][0]['meta'][1] as $item) {
                    if ($item['type'] === 'vertex') {
                        $path_pins[] = $item['id'];
                    } elseif ($item['type'] === 'edge') {
                        $pin1 = $item['id']['src'];
                        $pin2 = $item['id']['dst'];
                        $query = [
                            "rel_weight" => $item['id']['type'],
                            "query" => [
                                "ids" => [1, 1],
                                "nodes" => [
                                    [
                                        "id" => $pin1,
                                        "labels" => ["Person"],
                                        "properties" => []
                                    ],
                                    [
                                        "id" => $pin2,
                                        "labels" => ["Person"],
                                        "properties" => []
                                    ]
                                ],
                                "relationships" => [
                                    [
                                        "id" => $item['id']['name'] . "-" . $pin1 . "-" . $pin2 . "neighbor",
                                        "srcId" => $pin1,
                                        "dstId" => $item['id']['dst'],
                                        "type" => $item['id']['name'],
                                        "description" => $item['id']['name'],
                                        "properties" => []
                                    ]
                                ]
                            ]
                        ];
                        $path_queries[] = $query;
                    }
                }

                $newFormatData['result']['data'][] = [
                    "path_weight" => $result['data'][0]['row'][2],
                    "path_pins" => $path_pins,
                    "path_queries" => $path_queries
                ];
            }

            return $newFormatData;
            return response()->json($newFormatData);

        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    }
}

