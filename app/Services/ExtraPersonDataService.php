<?php

namespace App\Services;

use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class ExtraPersonDataService
{

    private $dataTables = [
      "189" => "189_data",
      "binaaz" => "binaaz_data",
      "tapaz" => "tapaz_data",
//      "facebook" => "social_hub.facebook_id_releated_number",
    ];

    public function getDataByPhone($per_page, $phone, $section = "189"): JsonResponse
    {
        $query = DB::table($this->dataTables[$section]);

        if ($section == "tapaz") {
            $query->where('mobile_number', $phone);
        }

        else if ($section == "189") {
            $query->where('cli', $phone)->orWhere('dial', $phone);
        }

        else if ($section == "binaaz") {
            $query->where('mobile_number', 'LIKE', '%'.$phone.'%');
        }

//        else if($section == "facebook") {
//            $phone = "994".$phone;
//            $query->where('number', $phone);
//        }

        $results = $query->paginate($per_page);

        return response()->json($results);
    }
}
