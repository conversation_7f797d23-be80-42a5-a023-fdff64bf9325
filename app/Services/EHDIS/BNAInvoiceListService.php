<?php

namespace App\Services\EHDIS;


class BNAInvoiceListService extends AbstractEhdisService
{
    /**
     * @param string $pin
     * @param string $serviceId
     * @return array
     */
    public static function run(string $pin, string $serviceId): array
    {
        static::setAction("GetInvoiceList");

        static::setSoapData([
            'pin' => $pin,
            'serviceId' => $serviceId,
        ]);

        return static::response();
    }
}
