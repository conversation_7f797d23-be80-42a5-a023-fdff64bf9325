<?php

namespace App\Services\EHDIS;


class DoctorListASAService extends AbstractEhdisService
{
    /**
     * @param string $name
     * @param string $surname
     * @param string $fatherName
     * @return array
     */
    public static function run(string $name, string $surname, string $fatherName): array
    {
        static::setAction("GetDoctorListByAsa");

        static::setSoapData([
            'fathername' => $fatherName,
            'name' => $name,
            'surname' => $surname,
        ]);

        return static::response();
    }
}
