<?php

namespace App\Services\EHDIS;


class TQDKInfoService extends AbstractEhdisService
{
    /**
     * @param string $pin
     * @return array
     */
    public static function run(string $pin): array
    {
        static::setAction("getTqdkArayisByPin");

        static::setSoapData([
            'pin' => $pin
        ]);

        $response = static::response();

        if (isset($response['data']['edu_prof'])) {
            $response['data']['edu_prof'] =
                self::toMultiDimensionalArray($response['data']['edu_prof']);
        }

        return $response;
    }
}
