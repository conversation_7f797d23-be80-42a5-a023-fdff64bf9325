<?php

namespace App\Services\EHDIS;


class CrossingBorderInfoService extends AbstractEhdisService
{
    /**
     * @param string $passportNumber
     * @return array
     */
    public static function run(string $passportNumber): array
    {
        static::setAction("GetCrossingList");

        static::setSoapData([
            'passportNumber' => $passportNumber
        ]);

        return static::response();
    }
}
