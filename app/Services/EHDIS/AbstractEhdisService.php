<?php

namespace App\Services\EHDIS;

use App\Models\Person;
use App\Repository\PersonRepository;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Spatie\ArrayToXml\ArrayToXml;

abstract class AbstractEhdisService
{
    /**
     * @param string $param
     * @return array
     */
    protected const RESULT = 'Result';

//    protected static string $host = 'http://**********:8080/EHDISService1.asmx';

    protected static array $headers = [
         'Content-Type' => 'text/xml; charset=utf-8',
    ];

    protected static string $soapAction;
    protected static array $soapData;
    protected static string $soapActionPrefix  = 'http://tempuri.org/';

    public static function getPersonRepository(): PersonRepository
    {
        return new PersonRepository(new Person());
    }

    /**
     * @param String $action
     * @return void
     */
    public static function setAction(String $action):void {

          static::$soapAction = $action;
          static::$headers['SOAPAction'] = self::$soapActionPrefix . $action;
    }

    /**
     * @param array $data
     * @return void
     */
    public static function setSoapData(array $data): void
    {
        static::$soapData = $data;
    }


    public static function sendRequest()
    {
        $body = self::convertToXml();

        return Http::withBody($body, 'text/xml; charset=utf-8')
                 ->withHeaders(static::$headers)
//                 ->post(static::$host);
                 ->post(config('servers.ehdis').'/EHDISService1.asmx');


    }

    /**
     * @param $body
     * @return mixed
     * @throws \JsonException
     */
    public static function parse($body): mixed
    {
         $xml = simplexml_load_string($body);
         $data = $xml->xpath("//soap:Body/*")[0];

         $data = json_encode($data, JSON_THROW_ON_ERROR);
         $data = json_decode($data, true, 512, JSON_THROW_ON_ERROR);

         return $data[static::$soapAction.static::RESULT];
     }

     public static function convertToXml(): string
     {
         $root = [
             'rootElementName' => 'soap:Envelope',
             '_attributes' => [
                 'xmlns:xsi' => 'http://www.w3.org/2001/XMLSchema-instance',
                 'xmlns:xsd' => 'http://www.w3.org/2001/XMLSchema',
                 'xmlns:soap' => 'http://schemas.xmlsoap.org/soap/envelope/',
             ],
         ];

         $body = [
            'soap:Body' => [
                self::$soapAction => [
                    '_attributes' => [
                        'xmlns' => 'http://tempuri.org/'
                    ],
                    ...self::$soapData,
                ]
            ]
         ];

         return ArrayToXml::convert(
             $body, $root,true, 'UTF-8'
         );
     }

    /**
     * @throws \JsonException
     */
    public static function response(): array
     {
         $response = self::sendRequest();
         $data = [];

         if ($response->successful()) {
             $data = self::parse($response->body());
         }

         return [
             'status' => $response->status(),
             'message'=> $response->reason(),
             'data'=> $data,
         ];
     }

     protected static function toMultiDimensionalArray(array $data): array
     {
         if (!isset($data[0])) {
             $newData = $data;
             unset($data);
             $data[0] = $newData;
         }

         return $data;
     }
}
