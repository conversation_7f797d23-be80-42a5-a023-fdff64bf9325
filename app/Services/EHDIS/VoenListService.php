<?php

namespace App\Services\EHDIS;


class VoenListService extends AbstractEhdisService
{
    /**
     * @param string $tpn
     * @return array
     */
    public static function run(string $tpn): array
    {
//        static::setAction("GetLCListByTPN");
//        static::setAction("GetLCListByTPNReponse");
        static::setAction("getTaxpayerInfoByVoen");

        static::setSoapData([
           // 'tpn' => $tpn,
            'voen' => $tpn
        ]);

        return static::response();
    }
}
