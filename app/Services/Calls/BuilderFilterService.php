<?php

namespace App\Services\Calls;

use Illuminate\Database\Eloquent\Builder;

abstract class BuilderFilterService
{

    protected array $allowedOperators = [
        '=', '!=', '>', '<', '>=', '<=',
        'contains', 'doesNotContain', 'beginsWith', 'doesNotBeginWith',
        'endsWith', 'doesNotEndWith', 'in', 'notIn',
        'between', 'notBetween', 'null', 'notNull'
    ];

    public function applyFilters(Builder $query, array $filters): Builder
    {

        if (!isset($filters['rules'])) {
            return $query;
        }

        foreach ($filters['rules'] as $filter) {
            $column = $filter['field'];
            $operator = $filter['operator'];
            $value = $filter['value'] ?? null;

            if (is_null($value) || $value === '') {
                continue;
            }

            if($column == "end_time"){
                $column = "start_time";

            }

            if (in_array($column, $this->allowedColumns) && in_array($operator, $this->allowedOperators)) {
                switch ($operator) {
                    case 'contains':
                        $query->where($column, 'LIKE', '%' . $value . '%');
                        break;
                    case 'doesNotContain':
                        $query->where($column, 'NOT LIKE', '%' . $value . '%');
                        break;
                    case 'beginsWith':
                        $query->where($column, 'LIKE', $value . '%');
                        break;
                    case 'doesNotBeginWith':
                        $query->where($column, 'NOT LIKE', $value . '%');
                        break;
                    case 'endsWith':
                        $query->where($column, 'LIKE', '%' . $value);
                        break;
                    case 'doesNotEndWith':
                        $query->where($column, 'NOT LIKE', '%' . $value);
                        break;
                    case 'in':
                        $values = array_map('trim', explode(',', $value));
                        $query->whereIn($column, $values);
                        break;
                    case 'notIn':
                        $values = array_map('trim', explode(',', $value));
                        $query->whereNotIn($column, $values);
                        break;
                    case 'between':
                        $range = explode(',', $value);
                        if (count($range) == 2) {
                            $query->whereBetween($column, [$range[0], $range[1]]);
                        }
                        break;
                    case 'notBetween':
                        $range = explode(',', $value);
                        if (count($range) == 2) {
                            $query->whereNotBetween($column, [$range[0], $range[1]]);
                        }
                        break;
                    case 'null':
                        $query->whereNull($column);
                        break;
                    case 'notNull':
                        $query->whereNotNull($column);
                        break;
                    default:
                        if($column == 'gsm_caller_or_receiver'){
//                            $query->where(function ($q) use ($value) {
                            $query->where('caller_msisdn', $value)
                                    ->orWhere('called_msisdn',  $value);
//                            });
                        }
                        else if($column == 'cdr_caller_or_receiver'){
//                            $query->where(function ($q) use ($value) {
                            $query->where('msisdn1', $value)
                                    ->orWhere('msisdn2',  $value);
//                            });
                        }else{
                            $query->where($column, $operator, $value);
                        }
                        break;
                }
            }
        }

        return $query;
    }
}
