<?php

namespace App\Services\BeeinContact;


class SourceQueryService
{

    private static array $queries = [
    'ady' => "SELECT
        subquery.id,
        subquery.account_phone,
        subquery.account_name,
        subquery.account_surname,
        subquery.account_patronymic_name,
        subquery.account_email,
        subquery.account_serial,
        subquery.account_birth
    FROM (
        SELECT
            MIN(id) AS id,
            account_phone,
            account_name,
            account_surname,
            account_patronymic_name,
            account_email,
            account_serial,
            account_birth
        FROM ady_personal_account_and_bought_tickets
        WHERE account_phone IS NOT NULL
          AND  regexp_replace(account_phone, '[^0-9]', '', 'g') ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'

        GROUP BY account_phone, account_name, account_surname, account_patronymic_name, account_email, account_serial, account_birth
    ) AS subquery
    WHERE NOT EXISTS (
        SELECT 1
        FROM beein_contact_2 AS b
        WHERE b.source_id = subquery.id
          AND b.source = 'ady'
    )
    LIMIT 500;",

    'avtovagzal' => "SELECT
                subquery.id,
                subquery.phone,
                subquery.email,
                subquery.name
            FROM (
                SELECT
                    MIN(id) AS id,
                    phone,
                    email,
                    name
                FROM avtovagzal_passengers
                WHERE phone IS NOT NULL
                AND regexp_replace(phone, '[^0-9]', '', 'g') ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'
                GROUP BY phone, email, name
            )  AS subquery
            WHERE NOT EXISTS (
                SELECT 1
                FROM beein_contact_2 AS b
                WHERE b.source_id = subquery.id
                  AND b.source = 'avtovagzal'
            )
            LIMIT 500;
        ",

    'azeriqaz' => "SELECT
     subquery.id,
    subquery.contact,
    subquery.name,
    subquery.surname,
    subquery.middlename,
    subquery.fin,
    subquery.rayon,
    subquery.unvan,
    subquery.imtiyaz
            FROM (
                SELECT
                    MIN(id) AS id,
                    contact,
                    name,
                    surname,
                    middlename,
                    fin,
                    rayon,
                    unvan,
                    imtiyaz
                FROM azeriqaz_ehali
                WHERE contact IS NOT NULL
                AND regexp_replace(contact, '[^0-9]', '', 'g') ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'
                GROUP BY contact, name, surname,middlename,fin,rayon,unvan,imtiyaz
            )  AS subquery
            WHERE NOT EXISTS (
                SELECT 1
                FROM beein_contact_2 AS b
                WHERE b.source_id = subquery.id
                  AND b.source = 'azeriqaz'
            )
            LIMIT 3000;
            ",
        'azerisiq_baki'=> "SELECT
     subquery.id,
     subquery.contact,
     subquery.name,
     subquery.surname,
     subquery.middlename,
     subquery.rayon,
     subquery.full_adress
            FROM (
                SELECT
                    MIN(id) AS id,
                    contact,
                    name,
                    surname,
                    middlename,
                    rayon,
                    full_adress
                FROM azerisiq_baki
                WHERE contact IS NOT NULL
                AND regexp_replace(contact, '[^0-9]', '', 'g') ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'
                GROUP BY contact, name, surname,middlename,rayon,full_adress
            )  AS subquery
            WHERE NOT EXISTS (
                SELECT 1
                FROM beein_contact_2 AS b
                WHERE b.source_id = subquery.id
                  AND b.source = 'azerisiq_baki'
            ) LIMIT 1000;
        ",

    'azerisiq_region' => "SELECT
     subquery.id,
     subquery.contact,
     subquery.name,
     subquery.surname,
     subquery.middlename,
     subquery.rayon,
     subquery.full_adress
            FROM (
                SELECT
                    MIN(id) AS id,
                    contact,
                    name,
                    surname,
                    middlename,
                    rayon,
                    full_adress
                FROM azerisiq_region
                WHERE contact IS NOT NULL
                AND regexp_replace(contact, '[^0-9]', '', 'g') ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'
                GROUP BY contact, name, surname,middlename,rayon,full_adress
            )  AS subquery
            WHERE NOT EXISTS (
                SELECT 1
                FROM beein_contact_2 AS b
                WHERE b.source_id = subquery.id
                  AND b.source = 'azerisiq_region'
            ) LIMIT 2000;
    ",

    "azerpoct_money_transfer_cl" => "SELECT
     subquery.id,
     subquery.phonestr_cl,
     subquery.ptrfin_code,
     subquery.passnum_cl,
     subquery.txt_pay
            FROM (
                SELECT
                    MIN(id) AS id,
                    phonestr_cl,
                    ptrfin_code,
                    passnum_cl,
                    txt_pay
                FROM azerpoct_international_money_transfer
                WHERE phonestr_cl IS NOT NULL
                AND regexp_replace(phonestr_cl, '[^0-9]', '', 'g') ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'
                GROUP BY phonestr_cl, ptrfin_code, passnum_cl,txt_pay
            )  AS subquery
            WHERE NOT EXISTS (
                SELECT 1
                FROM beein_contact_2 AS b
                WHERE b.source_id = subquery.id
                  AND b.source = 'azerpoct_money_transfer_cl'
            ) LIMIT 1000;
    ",
        "azerpoct_money_transfer_cr" => "SELECT
     subquery.id,
     subquery.phonestr_cr,
     subquery.txt_ben
            FROM (
                SELECT
                    MIN(id) AS id,
                    phonestr_cr, 
                    txt_ben
                FROM azerpoct_international_money_transfer
                WHERE phonestr_cr IS NOT NULL
                AND regexp_replace(phonestr_cr, '[^0-9]', '', 'g') ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'
                GROUP BY phonestr_cr, txt_ben
            )  AS subquery
            WHERE NOT EXISTS (
                SELECT 1
                FROM beein_contact_2 AS b
                WHERE b.source_id = subquery.id
                  AND b.source = 'azerpoct_money_transfer_cr'
            ) LIMIT 1000;
       ",
       "azerpoct_mektub_baglama" => "SELECT
     subquery.id,
     subquery.rcpn,
     subquery.rcpn_phone
            FROM (
                SELECT
                    MIN(id) AS id,
                    rcpn,
                    rcpn_phone
                FROM azerpoct_mektub_baglama
                WHERE rcpn_phone IS NOT NULL
                AND regexp_replace(rcpn_phone, '[^0-9]', '', 'g') ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'
                GROUP BY rcpn, rcpn_phone
            )  AS subquery
            WHERE NOT EXISTS (
                SELECT 1
                FROM beein_contact_2 AS b
                WHERE b.source_id = subquery.id
                  AND b.source = 'azerpoct_mektub_baglama'
            ) LIMIT 1000;
            ",


            'bina_az' => "SELECT
       subquery.id,
     subquery.mobile_number,
     subquery.seller_name,
     subquery.seller_email,
     subquery.region,
     subquery.ip
FROM (
    SELECT
        MIN(id) AS id,
        mobile_number,
        seller_name,
        string_agg(DISTINCT seller_email, ', ') AS seller_email,
        string_agg(DISTINCT region, ', ') AS region,
        string_agg(DISTINCT ip, ', ') AS ip
    FROM binaaz_data
    WHERE  id > :last_id
    AND mobile_number <> ''
    GROUP BY mobile_number, seller_name
)  AS subquery
ORDER BY subquery.id ASC
LIMIT 1000;
",






            'tap_az' => "SELECT
    subquery.id,
    subquery.mobile_number,
    subquery.seller_name,
    subquery.seller_email,
    subquery.region,
    subquery.ip
FROM (
    SELECT
        MIN(id) AS id,
        mobile_number,
        seller_name,
        string_agg(DISTINCT seller_email, ', ') AS seller_email,
        string_agg(DISTINCT region, ', ') AS region,
        string_agg(DISTINCT ip, ', ') AS ip
    FROM (
        SELECT *
        FROM tapaz_data
        WHERE id BETWEEN 20616299 AND 28573277
        AND id > :last_id
        AND COALESCE(NULLIF(mobile_number, ''), '[]') <> '[]'
    ) AS limited_data
    GROUP BY mobile_number, seller_name
) AS subquery
ORDER BY subquery.id ASC
LIMIT 3000;
            ",




    'turbo_az' => "SELECT
            subquery.id,
     subquery.seller_phone_number,
     subquery.seller_name,
     subquery.seller_email,
     subquery.region,
     subquery.seller_ip
FROM (
    SELECT
        MIN(id) AS id,
        seller_phone_number,
        seller_name,
        string_agg(DISTINCT seller_email, ', ') AS seller_email,
        string_agg(DISTINCT region, ', ') AS region,
        string_agg(DISTINCT seller_ip, ', ') AS seller_ip
    FROM turboaz_data
            WHERE  id > :last_id
        AND    seller_phone_number <> ''
    GROUP BY seller_phone_number, seller_name
)   AS subquery
            ORDER BY subquery.id ASC
LIMIT 3000;
",

'food_orders' => "SELECT DISTINCT ON (phone, name, email)
    id,
    phone,
    name,
    email,
    data
FROM food_orders
WHERE id > :last_id
AND regexp_replace(phone, '[^0-9]', '', 'g') ~
    '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$'
ORDER BY phone, name, email, id ASC
LIMIT 3000;
",


'dsmf' => "SELECT MOBILE_PHONE, PIN, NAME, SURNAME, DOC_NUMBER, PATRONYMIC
FROM MV_EMAS_DTX_REPORT
WHERE TO_NUMBER(MOBILE_PHONE) > ?
 AND MOBILE_PHONE IS NOT NULL
GROUP BY MOBILE_PHONE, PIN, NAME, SURNAME, DOC_NUMBER, PATRONYMIC
ORDER BY TO_NUMBER(MOBILE_PHONE) ASC
  get FIRST 200 ROWS ONLY;
  ",


'tabib' => "SELECT RESULT_ID, PHONE, PIN, NAME_AZ, SURNAME_AZ, PATRONYMIC_AZ, BIRTHDATE, ADDRESS
FROM (
    SELECT
        N_LABORATORY.RESULT_ID,
        N_LABORATORY.PHONE,
        N_PERSON.PIN,
        N_PERSON.NAME_AZ,
        N_PERSON.SURNAME_AZ,
        N_PERSON.PATRONYMIC_AZ,
        N_PERSON.BIRTHDATE,
        N_PERSON.ADDRESS,
        ROW_NUMBER() OVER (PARTITION BY N_LABORATORY.PHONE ORDER BY N_LABORATORY.RESULT_ID ASC) AS RN
    FROM N_LABORATORY
    JOIN N_PERSON ON N_LABORATORY.PROTOCOL = N_PERSON.PROTOCOL
    WHERE N_LABORATORY.PHONE IS NOT NULL
    AND N_LABORATORY.RESULT_ID > :end_id
) WHERE RN = 1
ORDER BY RESULT_ID ASC
FETCH FIRST 300 ROWS ONLY;
"


    ];

    public function selectQuery(string $source): ?string
    {
        return self::$queries[$source] ?? null;

    }
    public $dsmf_select_query ="SELECT Distinct MOBILE_PHONE, NAME, SURNAME, PATRONYMIC, PIN, DOC_NUMBER, BIRTH_DATE, ADDRESS_PIN from MV_EMAS_DTX_REPORT
    where TO_NUMBER(MOBILE_PHONE)>?
          AND MOBILE_PHONE IS NOT NULL
          AND (
              (LENGTH(MOBILE_PHONE) = 12 AND MOBILE_PHONE LIKE '994%' AND MOBILE_PHONE NOT LIKE '9940%')
              OR (LENGTH(MOBILE_PHONE) = 9 AND MOBILE_PHONE NOT LIKE '0%')
              OR (LENGTH(MOBILE_PHONE) = 10 AND MOBILE_PHONE LIKE '0%' AND MOBILE_PHONE NOT LIKE '00%')
          )
          AND (
              MOBILE_PHONE LIKE '99450%'
              OR MOBILE_PHONE LIKE '99451%'
              OR MOBILE_PHONE LIKE '99455%'
              OR MOBILE_PHONE LIKE '99499%'
              OR MOBILE_PHONE LIKE '99410%'
              OR MOBILE_PHONE LIKE '99470%'
              OR MOBILE_PHONE LIKE '99477%'
              OR MOBILE_PHONE LIKE '99412%'
              OR MOBILE_PHONE LIKE '99460%'
              OR MOBILE_PHONE LIKE '99461%'
          )
    ORDER BY TO_NUMBER(MOBILE_PHONE) ASC
    get FIRST 200 ROWS ONLY;
    ";


    public $ady_phone_count_query = "SELECT COUNT(*)
    FROM (
        SELECT account_phone
        FROM ady_personal_account_and_bought_tickets
        WHERE account_phone IS NOT NULL
          AND account_phone ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61)[0-9]{7}$'
        GROUP BY account_phone
        HAVING COUNT(DISTINCT account_name) >= 1
    ) AS subquery;
";

// Avtovaglaz
public $avtovagzal_count_query = "SELECT
                count(1)
            FROM (
                SELECT
                    MIN(id) AS id,
                    phone,
                    email,
                    name
                FROM avtovagzal_passengers
                WHERE phone IS NOT NULL
                AND phone ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61)[0-9]{7}$'
                GROUP BY phone, email, name
            )  AS subquery
        ;";


public $azeriqaz_select_query ="";

public $azerisiq_baki_select_query ="";

public $azerisiq_region_select_query ="";

public $azerpoct_select_query ="";

public $azparking_select_query ="";

public $binaaz_select_query ="";

public $tapaz_select_query ="";

public $turboaz_select_query ="";

public $food_orders_select_query ="";

public $mobcontact_select_query ="";



public $tabib_select_query ="";

public $taksi_189_select_query ="";

public $uklon_select_query ="";

public $getcontact_select_query ="";

public $osint_select_query ="";


}
