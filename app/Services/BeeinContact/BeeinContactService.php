<?php

namespace App\Services\BeeinContact;

use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;

class BeeinContactService
{
    // Bulk Insert Beein Contact
    public function bulkInsert($data)
    {
        DB::beginTransaction();
        try {
            DB::table('beein_contact_2')->insertOrIgnore($data);
            DB::commit();
            return 'successful';
        } catch (QueryException $e) {
            DB::rollBack();
            return 'Database query error: ' . $e->getMessage();
        } catch (Exception $e) {
            DB::rollBack();
            return 'Unexpected error: ' . $e->getMessage();
        }
    }

    // Mapping
    private const WEIGHT_MAP = [
        'bina_az'    => 5,
        'tap_az'     => 5,
        'turbo_az'   => 5,
        'mobcontact' => 20,
        'food_orders' => 20,
        'azerisiq_baki'   => 30,
        'azerisiq_region'   => 30,
        'azeriqaz'   => 40,
        'azersu'     => 30,
        'azerpoct_money_transfer_cl'   => 30,
        'azerpoct_money_transfer_cr'   => 30,
        'azerpoct_mektub_baglama'   => 30,
        'avtovagzal' => 30,
        'ady'        => 30,
        'azparking'  => 30,
        'dsmf'       => 60,
        'tabib'      => 60,
    ];
    public function mapping(array $data): ?array
    {
        $phone = $this->normalizePhone($data['phone']);
    
        if (!$phone) {
            return null;
        }

        if (!isset(self::WEIGHT_MAP[$data['source']])) {
            return null;
        }

        return [
            'phone'      => $phone,
            'pin'        => $data['pin'] ?? '',
            'doc_number' => $data['doc_number'] ?? '',
            'name'       => $data['name'] ?? '',
            'surname'    => $data['surname'] ?? '',
            'father_name'=> $data['father_name'] ?? '',
            'email'      => $data['email'] ?? '',
            'birthday'   => $this->normalizeBirthday($data['birthday'] ?? null),
            'city'       => $data['city'] ?? '',
            'address'    => $data['address'] ?? '',
            'note'       => $data['note'] ?? '',
            'source_id'  => $data['source_id'],
            'source'     => $data['source'] ?? '',
            'weight'     => self::WEIGHT_MAP[$data['source']],
            'hash'       => $this->generateHash($data),
        ];
    }

    public function normalizePhone(string $phone): ?string
    {
        $cleanedPhone = preg_replace('/[^0-9]/', '', $phone);
    
        $pattern = '/^(?:\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$/';
    
        return preg_match($pattern, $cleanedPhone) ? '994'.(substr($cleanedPhone, -9)) : null;
    }
    private function normalizeBirthday(?string $birthday): ?string
    {
        return $birthday ? Carbon::parse($birthday)->format('Y-m-d') : null;
    }

    private function generateHash(array $data): string
    {

        $concatenated = ($data['phone'] ?? '') . ($data['pin'] ?? '') . ($data['doc_number'] ?? '') . 
        ($data['name'] ?? '') . ($data['surname'] ?? '') . ($data['father_name'] ?? '') . 
        ($data['email'] ?? '') . ($data['birthday'] ?? '') . ($data['city'] ?? '') . 
        ($data['address'] ?? '') . ($data['note'] ?? '') . 
        ($data['source'] ?? '');

 
        $slugged = Str::slug($concatenated);

    
        return md5($slugged);
    }

}
