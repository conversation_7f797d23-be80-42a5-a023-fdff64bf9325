<?php

namespace App\Services\BeeinContact;

use Illuminate\Support\Facades\DB;

class MigrationStatisticService
{
    protected $query;

    public function __construct(SourceQueryService  $query)
    {
        $this->query = $query;
    }

    public function getStatisticInfo($source)
    {
        return match($source){
            'ady' => $this->adyStats(),
            'avtovagzal' => $this->avtovagzalStats(),
            'azeriqaz' => $this->azeriqazStats(),
            'azerisiq_baki' => $this->azerisiqBakiStats(),
            'azerisiq_region' => $this->azerisiqRegionStats(),
            'azerpoct' => $this->azerpoctStats(),
            'azparking' => $this->azparkingStats(),
            'bina_az' => $this->binaAzStats(),
            'tap_az' => $this->tapAzStats(),
            'tubo_az' => $this->turboAzStats(),
            'food_orders' => $this->foodOrdersStats(),
            'mobcontact' => $this->mobcontactStats(),
            'dsmf' => $this->dsmfStats(),
            'tabib' => $this->tabibStats(),
            '189_taksi' => $this->taksi189Stats(),
            'uklon_taksi' => $this->taksiUklonStats(),
            'getcontact' => $this->getContactStats(),
            'osint' => $this->osintStats(),
        };
    }

    public function adyStats()
    {

        $total = DB::table('ady_personal_account_and_bought_tickets')->count();

        $uniquePhonesQuery = $this->query->ady_phone_count_query;

        $uniquePhones = DB::select(DB::raw($uniquePhonesQuery));
        $uniquePhonesCount = $uniquePhones[0]->count;

        $completed = DB::table('beein_contact_2')->where('source', 'ady')->count();

        return [
            'source' => 'ady',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($uniquePhonesCount),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($uniquePhonesCount - $completed),
            'status' => 'Completed',
        ];
    }

    public function avtovagzalStats()
    {
        $total = DB::table('avtovagzal_passengers')->count();
        $validPhonesQuery = "
            SELECT COUNT(*)
            FROM (
                SELECT phone
                FROM avtovagzal_passengers
                WHERE phone IS NOT NULL
                  AND phone ~ '^(\+994|994|0)?(50|51|55|99|10|70|77|12|60|61)[0-9]{7}$'
                AND length(name)>=3
                GROUP BY phone
                HAVING COUNT(DISTINCT phone) >= 1
            ) AS subquery;
        ";

        $validPhones = DB::select(DB::raw($validPhonesQuery));
        $validPhones = $validPhones[0]->count;

        $completed = DB::table('beein_contact_2')->where('source', 'ady')->count();

        return [
            'source' => 'avtovagzal',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function azeriqazStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'azeriqaz',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function azerisiqBakiStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'azerisiq_baki',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function azerisiqRegionStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'azerisiq_region',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function azerpoctStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'azerpoct',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function azparkingStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'azparking',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function getBinaAzStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'bina_az',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function binaAzStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'tap_az',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function tapAzStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'tubo_az',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function turboAzStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'food_orders',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function foodOrdersStats()
    {

        $total = DB::table('ady_personal_account_and_bought_tickets')->count();

        $uniquePhonesQuery = $this->query->ady_phone_count_query;

        $uniquePhones = DB::select(DB::raw($uniquePhonesQuery));
        $uniquePhonesCount = $uniquePhones[0]->count;

        $completed = DB::table('beein_contact_2')->where('source', 'ady')->count();

        return [
            'source' => 'ady',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($uniquePhonesCount),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($uniquePhonesCount - $completed),
            'status' => 'Completed',
        ];
    }

    public function mobcontactStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'mobcontact',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function dsmfStats()
    {
        $dsmf_count = DB::connection('oracle_job')
            ->table('MV_EMAS_DTX_REPORT')->count();

        $dsmf_sql_query = "SELECT COUNT(*) AS total FROM (
            SELECT DISTINCT MOBILE_PHONE
            FROM MV_EMAS_DTX_REPORT
            WHERE MOBILE_PHONE IS NOT NULL
              AND (
                  (LENGTH(MOBILE_PHONE) = 12 AND MOBILE_PHONE LIKE '994%' AND MOBILE_PHONE NOT LIKE '9940%')
                  OR (LENGTH(MOBILE_PHONE) = 9 AND MOBILE_PHONE NOT LIKE '0%')
                  OR (LENGTH(MOBILE_PHONE) = 10 AND MOBILE_PHONE LIKE '0%' AND MOBILE_PHONE NOT LIKE '00%')
              )
              AND (
                  MOBILE_PHONE LIKE '99450%' OR MOBILE_PHONE LIKE '99451%'
                  OR MOBILE_PHONE LIKE '99455%' OR MOBILE_PHONE LIKE '99499%'
                  OR MOBILE_PHONE LIKE '99410%' OR MOBILE_PHONE LIKE '99470%'
                  OR MOBILE_PHONE LIKE '99477%' OR MOBILE_PHONE LIKE '99412%'
                  OR MOBILE_PHONE LIKE '99460%' OR MOBILE_PHONE LIKE '99461%'
              )
        )";

        $dsmf_countResult = DB::connection('oracle_job')->select($dsmf_sql_query);
        $dsmf_unique_valid_phones = $dsmf_countResult[0]->total;

        $dsmf_complated = DB::table('beein_contact_2')->where('source', 'dsmf')->count();

        return [
            'source' => 'dsmf',
            'total' => formatNumber($dsmf_count),
            'validPhones' => formatNumber($dsmf_unique_valid_phones),
            'complated' => formatNumber($dsmf_complated),
            'pending' => formatNumber($dsmf_unique_valid_phones - $dsmf_complated),
            'status' => 'Inprogress',
        ];
    }

    public function tabibStats()
    {
        $totalCount = DB::connection('oracle_tabib')
            ->table('N_LABORATORY')->count();

        $validPhonesCount = DB::connection('oracle_tabib')
            ->table('N_LABORATORY')
            ->select('PHONE')
            ->join('N_PERSON', 'N_LABORATORY.PROTOCOL', '=', 'N_PERSON.PROTOCOL')
            ->whereNotNull('N_LABORATORY.PHONE')
            ->distinct('N_LABORATORY.PHONE')
            ->count();

        $completedCount = DB::table('beein_contact_2')
            ->where('source', 'covid')
            ->count();

        return [
            'source' => 'covid',
            'total' => formatNumber($totalCount),
            'validPhones' => formatNumber($validPhonesCount),
            'completed' => formatNumber($completedCount),
            'pending' => formatNumber($validPhonesCount - $completedCount),
            'status' => 'Inprogress',
        ];
    }

    public function taksi189Stats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => '189_data',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function taksiUklonStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'uklon',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

    public function getContactStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'getcontact',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }
    
    public function osintStats()
    {
        $total = 0;
        $validPhones = 0;
        $completed = 0;

        return [
            'source' => 'osint',
            'total' => formatNumber($total),
            'validPhones' => formatNumber($validPhones),
            'completed' => formatNumber($completed),
            'pending' => formatNumber($validPhones - $completed),
            'status' => 'Pending',
        ];
    }

}
