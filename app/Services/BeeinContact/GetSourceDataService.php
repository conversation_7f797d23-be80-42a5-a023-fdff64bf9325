<?php

namespace App\Services\BeeinContact;

use Illuminate\Support\Facades\DB;
use InvalidArgumentException;
use MongoDB\BSON\Regex;
class GetSourceDataService
{
    protected SourceQueryService $queryService;

    public function __construct(SourceQueryService $queryService)
    {
        $this->queryService = $queryService;
    }

    public function fetchData(string $source): array
    {       
        if ($source == 'azparking') {
            return $this->azparking();
        }
        if ($source == 'dsmf') {
            return $this->dsmf();
        }
        if($source=='tap_az' || $source=='turbo_az'  || $source=='bina_az' || $source=='food_orders' || $source=='dsmf' || $source=='tabib'){
            $last_id = DB::table('beein_contact_2')
            ->where('source', $source)
            ->orderBy('source_id', 'DESC')
            ->first()->source_id ?? 0;
           $last_id = (int)$last_id;
        }
 

        $query = $this->queryService->selectQuery($source);

        if (!$query) {
            throw new InvalidArgumentException("Invalid source: {$source}");
        }

        if (isset($last_id)) {
               

            if($source=='tabib'){
               
                $data = DB::connection('oracle_tabib')
                ->table('N_LABORATORY')
                ->select('RESULT_ID', 'PHONE', 'PIN', 'NAME_AZ', 'SURNAME_AZ', 'PATRONYMIC_AZ', 'BIRTHDATE', 'ADDRESS')
                ->join('N_PERSON', 'N_LABORATORY.PROTOCOL', '=', 'N_PERSON.PROTOCOL')
                ->whereNotNull('N_LABORATORY.PHONE')
                ->where('N_LABORATORY.RESULT_ID', '>', $last_id)
                ->orderBy('N_LABORATORY.RESULT_ID', 'ASC')
                ->limit(3000)
                ->distinct('N_LABORATORY.PHONE')
                ->get()->toArray();
    
            }else{
                $data = DB::select($query, ['last_id' => $last_id]);
            }
        } else {
            $data = DB::select($query);
        }

        return $data;
    }



    protected function dsmf()
    {
        $last_id = DB::table('beein_contact_2')->where('source', 'dsmf')->orderBy('source_id', 'DESC')->first()->source_id ?? 1;

        $sql_query = "Select Distinct MOBILE_PHONE, NAME, SURNAME, PATRONYMIC, PIN, DOC_NUMBER, BIRTH_DATE, ADDRESS_PIN from MV_EMAS_DTX_REPORT
where TO_NUMBER(MOBILE_PHONE)>?
      AND MOBILE_PHONE IS NOT NULL
      AND (
          (LENGTH(MOBILE_PHONE) = 12 AND MOBILE_PHONE LIKE '994%' AND MOBILE_PHONE NOT LIKE '9940%')
          OR (LENGTH(MOBILE_PHONE) = 9 AND MOBILE_PHONE NOT LIKE '0%')
          OR (LENGTH(MOBILE_PHONE) = 10 AND MOBILE_PHONE LIKE '0%' AND MOBILE_PHONE NOT LIKE '00%')
      )
      AND (
          MOBILE_PHONE LIKE '99450%'
          OR MOBILE_PHONE LIKE '99451%'
          OR MOBILE_PHONE LIKE '99455%'
          OR MOBILE_PHONE LIKE '99499%'
          OR MOBILE_PHONE LIKE '99410%'
          OR MOBILE_PHONE LIKE '99470%'
          OR MOBILE_PHONE LIKE '99477%'
          OR MOBILE_PHONE LIKE '99412%'
          OR MOBILE_PHONE LIKE '99460%'
          OR MOBILE_PHONE LIKE '99461%'
      )
ORDER BY TO_NUMBER(MOBILE_PHONE) ASC
FETCH FIRST 200 ROWS ONLY";

        $data = DB::connection('oracle_job')
            ->select($sql_query, [$last_id]);

            return $data;

    }


    protected function azparking()
    {
        $last_id = DB::table('beein_contact_2')
            ->where('source', 'azparking')
            ->orderBy('source_id', 'DESC')
            ->first()->source_id ?? 0;
        $last_id = (int)$last_id;

        // $data = DB::connection('mongodb')
        //     ->table('azparking_users')
        //     ->where('id', '>', $last_id)
        //     ->orderBy('id', 'ASC')
        //     ->take(500)
        //     ->get()->toArray();
        
        $data = DB::connection('mongodb')
            ->table('azparking_users')
            ->where('id', '>', $last_id)
            ->whereNotNull('msisdn')
            ->where('msisdn', 'regexp', new Regex('^(\\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$', 'i'))
            ->where(function ($query) {
                $query->where('firstname', '!=', '')
                      ->orWhere('lastname', '!=', '');
            })
            ->orderBy('id', 'ASC')
            ->take(500)
            ->get()
            ->toArray();

        $data = json_decode(json_encode($data));
        return $data;
    }


}
