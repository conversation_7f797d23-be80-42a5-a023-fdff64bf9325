<?php

namespace App\Services;

class PointInPolygon
{
    public function isPointInsidePolygon($lat, $lon, $vertices): bool
    {
        $intersections = 0;
        $numVertices = count($vertices);

        for ($i = 0; $i < $numVertices; $i++) {
            $currentVertex = $vertices[$i];
            $nextVertex = $vertices[($i + 1) % $numVertices];

            $lat1 = $currentVertex[0];
            $lon1 = $currentVertex[1];
            $lat2 = $nextVertex[0];
            $lon2 = $nextVertex[1];

            if (($lat1 <= $lat && $lat < $lat2) || ($lat2 <= $lat && $lat < $lat1)) {
                $lonIntersect = $lon1 + ($lon2 - $lon1) * ($lat - $lat1) / ($lat2 - $lat1);
                if ($lon < $lonIntersect) {
                    $intersections++;
                }
            }
        }

        return ($intersections % 2) == 1;
    }

    public function arePointsInsidePolygon($points, $polygon): array
    {
        $results = [];
        foreach ($points as $point) {
            $results[] = $this->isPointInsidePolygon($point[0], $point[1], $polygon);
        }
        return $results;
    }
}
