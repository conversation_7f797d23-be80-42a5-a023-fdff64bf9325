<?php

namespace App\Services;

use App\Models\Person;
use Illuminate\Support\Facades\Storage;
use JetBrains\PhpStorm\ArrayShape;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Str;

class SimilarityService extends MilvusService
{
    /**
     * @var string $api_uri
     */
    protected string $api_uri = 'docs/search';

    /**
     * @param \App\Services\PersonService $personService
     */
    public function __construct(
        protected PersonService $personService,
    ) {
    }

    /**
     * @throws \JsonException
     */
    public function search(array $params, int $searchType): array
    {
        $response = [
            'code' => Response::HTTP_OK,
            'data' => [],
            'msg' => ''
        ];

        $params['search_type'] = $searchType;

        $collection = $this->sendRequest(
            $this->collectParamsForMilvus($params),
            true
        );


        if (!is_array($collection) || !isset($collection['data'])) {
            $response['msg'] = $collection['message'] ?? 'Nothing found';

            if (isset($collection['status']) && (string) $collection['status'] === 'Error') {
                $response['code'] = Response::HTTP_INTERNAL_SERVER_ERROR;
            }

            return $response;
        }

        $type = $collection['type'] ?? 'single';



        if ($type == 'multiple') {
            return $this->getPersonsForMultiPhoto($collection, $params, $searchType);
        } else {
            return $this->collectPersonDataByPhoto($collection, $params, $searchType);
        }
    }

    #[ArrayShape(['type' => "string", 'response' => "array"])]
    public function getPersonsForMultiPhoto($collection, $params, $searchType): array
    {

        $lists = $collection['data'];
        $resData = [];
        foreach ($lists as $list) {
            if (isset($list['image'])) {
                $resData[] = $this->collectPersonDataByPhoto($list, $params, $searchType, 'multiple', $list['image']);
            }
        }

        // print_r($resData);die;
        return [
            'type' => 'multiple',
            'code' => Response::HTTP_OK,
            'msg' => 'OK',
            'data' => $resData
        ];
    }


    public function collectPersonDataByPhoto($collection, $params, $searchType, $type = 'single', $currentPersonImage = ''): array
    {
        $data = $collection['data'];

        $pins = array_column($data, 'fin');
        $similarities = array_column($data, 'distance');
        $similarityByPins = array_combine($pins, $similarities);
        $filterParams = request()->only($this->personService->getFilterParams());
        $filterParams = array_filter($filterParams);

        //    $fetchedPins = $this->personService->getPinsAsArray($pins);
        //    foreach (array_diff($pins, $fetchedPins) as $pin) {
        //        $data = $this->personService->getFromIamas($pin);
        //        if (is_array($data) && isset($data['Items']) && $data['Items']['Pin'] != '') {
        //            $this->personService->saveFromIamas($data);
        //        }
        //        sleep(2);
        //    }

        if (count($filterParams) === 1 && isset($filterParams['pin']) && !array_key_exists($filterParams['pin'], $similarityByPins)) {
            return [];
        }

        if (count($filterParams) === 1 && isset($filterParams['pin']) && array_key_exists($filterParams['pin'], $similarityByPins)) {
            $pins = (array) $filterParams['pin'];
        }

        if (empty($filterParams) || (count($filterParams) === 1 && isset($filterParams['pin']))) {

            if ($params['max_count'] == 3) {
                foreach ($pins as $pin) {
                    if (!Person::query()->where('pin', strtoupper($pin))->exists()) {
                        $this->personService->getOrSyncPersonAsSimpleVersion($pin);
                    }
                }
            }

            $people = $this->personService->getListsByPins($pins, $params['new_search']);
            $collectedPins = $people->pluck('pin')->toArray();
            $missedPins = array_diff($pins, $collectedPins);

            foreach ($missedPins as $missedPin) {
                $person = new Person;
                $person->fill([
                    'pin' => $missedPin
                ]);

                $people->push($person);
            }
        } else {
            $people = $this->personService->getListsByParams($filterParams, $pins);
        }

        collect($people)->filter(function ($person) use ($similarityByPins, $searchType) {
            $person->similarity = $similarityByPins[$person->pin];
            $person->doc_type_from_request = $searchType === 1 ? 'new' : 'old';
        });

        $response['data']['people'] = $this->sort($people);
        if ($type == 'multiple') {
            $response['time_interval'] = !empty($collection['time']) ? (secondToTime((floor($collection['time']) - 2)<0 ? 0 : (floor($collection['time']) - 2)).' - '.secondToTime(ceil($collection['time'])+2)) : '';
            $response['person_image'] = $currentPersonImage;
        }

        return $response;
    }




    /**
     * @param $data
     * @return mixed
     */
    public function sort($data): mixed
    {
        return $data->sortByDesc('similarity');
    }

    /**
     * @param array $params
     * @return array
     */
    protected function collectParamsForMilvus(array $params): array
    {
        return [
            'similarity' => $params['similarity'],
            'max_count' => $params['max_count'],
            'photo' => $params['photo'] ?? '',
            'search_type' => $params['search_type'],
            'do_face_detection' => $params['do_face_detection'] ?? '',
        ];
    }


    public function searchVideoPersons($params, $searchType)
    {
        $video_url = $this->s3Upload('videos', $params['video']);
        //$video_url ="";
        $data['video_path'] = $video_url;
        $data['video_id'] = (string) Str::uuid();
        $data['channel_id'] = (string) Str::uuid();
        // print_r($data);die;

        //$response = new VideoUploadService($params['video']);



        $response = $this->sendVideoRequestAI($data);

        // print_r($response[0]->time);
        // die;

        $collection = [];
        $collection['type'] = 'multiple';
        foreach ($response as $res) {
            $pins_arr = [];

                foreach ($res->pin_infos as $pin) {

                    $pins_arr[] = [
                        'fin' => $pin->pin,
                        'distance' => $pin->similarity,
                    ];
                }

            $collection['data'][] = [
                'number_of_people' => count($res->pin_infos),
                'time' => $res->time,
                'data' => $pins_arr,
                'image' => $res->face,
            ];
        }
        $params['photo'] = '';



        return $this->getPersonsForMultiPhoto($collection, $params, $searchType);
    }

    public function s3Upload($path, $file)
    {
        $uniqueFileName = uniqid() . '_' . $file->getClientOriginalName();
        $path = Storage::disk('s3')->put($path, $file, $uniqueFileName);
        $url = env('AWS_FILE_URL') . env('AWS_BUCKET') . '/' . $path;
        return $url;
    }
}
