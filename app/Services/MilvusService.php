<?php

namespace App\Services;

use App\Traits\ApiResponsible;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

abstract class MilvusService
{
    use ApiResponsible;

    /**
     * @var string $endpoint
     */
//    protected string $endpoint = 'http://10.14.70.23:8000/';
//    protected string $newEndpoint = 'http://10.14.70.24:8000/v2/';
//    protected string $videoEndPoint = 'http://10.14.70.24:8123/predict';

    /**
     * @param array $params
     * @return array
     */
    abstract protected function collectParamsForMilvus(array $params): array;

    /**
     * @throws \JsonException
     */
    protected function sendRequest(array $data, $multiImage = false)
    {
        if(isset($data['image']))
        {
            $data['photo'] = $this->convertBase64FromLink($data['image']);
        }

        $endPoint = $multiImage ? config('servers.gpu_api_v2') : config('servers.gpu_api_single');

        $response = Http::timeout(180)->put($endPoint . $this->api_uri, $data);

        return json_decode($response->body(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @throws \JsonException
     */
    protected function sendRequestV2(array $data, $multiImage = false)
    {
        if(isset($data['image']))
        {
            $data['photo'] = $this->convertBase64FromLink($data['image']);
        }
        if (isset($data['date_from']) && isset($data['date_to'])) {
            $data['from_time'] = strtotime($data['date_from']);
            $data['till_time'] = strtotime($data['date_to']);
            //$data['ramill'] = $data['camera_ids'];
            //$data['similarity'] = 0.3;
           // $data['max_count'] = 10;

        }

        //return response()->json($data);

        // $endPoint = $multiImage ? config('servers.gpu_api_v2') : config('servers.gpu_api_single');
        $endPoint = $multiImage ? config('servers.gpu_api_v2') : config('servers.gpu_api_v1');

        $response = Http::timeout(180)->put($endPoint . $this->api_uri, $data);

        //return response()->json($response->body());

        return json_decode($response->body(), true, 512, JSON_THROW_ON_ERROR);
    }

    protected function convertBase64FromLink($link): string
    {
        $imageData = file_get_contents($link);
        return base64_encode($imageData);
    }


    public function sendVideoRequestAI($data)
    {
        $response = Http::timeout(1800)->post(config('servers.gpu_api_predict'), $data);
        return json_decode($response->body());
    }
}
