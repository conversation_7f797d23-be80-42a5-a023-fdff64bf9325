<?php

namespace App\Services;

use Thrift\Exception\TTransportException;
use Thrift\Transport\TSocket;

class TTSocket extends TSocket
{
    public function read($len)
    {
        $null = null;
        $read = array($this->handle_);
        $readable = @stream_select(
            $read,
            $null,
            $null,
            $this->recvTimeoutSec_,
            $this->recvTimeoutUsec_
        );

        if ($readable > 0) {
            $data = fread($this->handle_, $len);
            if ($data === false) {
                throw new TTransportException('TSocket: Could not read ' . $len . ' bytes from ' .
                    $this->host_ . ':' . $this->port_);
            } elseif ($data == '' && feof($this->handle_)) {
                throw new TTransportException('TSocket read 0 bytes');
            }

            return $data;
        } elseif ($readable === 0) {
//            throw new TTransportException('TSocket: timed out reading ' . $len . ' bytes from ' .
//                $this->host_ . ':' . $this->port_);
        } else {
//            throw new TTransportException('TSocket: Could not read ' . $len . ' bytes from ' .
//                $this->host_ . ':' . $this->port_);
        }
    }
}
