<?php

namespace App\Services\LLM;

use Illuminate\Support\Facades\Http;

class LLMClient
{
    protected string $baseUrl;
    protected string $modelName;

    public function __construct()
    {
        $this->baseUrl = config('llm-model.base_url');
        $this->modelName = config('llm-model.model_name');
    }

    public function chat(string $systemPrompt, string $query): ?string
    {
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'user', 'content' => $query],
        ];

        $response = Http::withToken('alma')
            ->post("{$this->baseUrl}/chat/completions", [
                'model' => $this->modelName,
                'messages' => $messages,
                'temperature' => 0.1,
                'seed' => 1903,
            ]);

        if ($response->successful()) {
            return $response->json()['choices'][0]['message']['content'] ?? null;
        }

        return null;
    }


    public function chatStream(string $systemPrompt, string $query)
    {
        $messages = [
            ['role' => 'system', 'content' => $systemPrompt],
            ['role' => 'user', 'content' => $query],
        ];

        $response = Http::withToken('alma')
            ->withOptions(['stream' => true]) // Streaming aktivləşdir
            ->post("{$this->baseUrl}/chat/completions", [
                'model' => $this->modelName,
                'messages' => $messages,
                'temperature' => 0.5,
                'seed' => 1903,
                'stream' => true // Modelə stream istəyi göndər
            ]);

        return $response->toPsrResponse()->getBody();
    }
}
