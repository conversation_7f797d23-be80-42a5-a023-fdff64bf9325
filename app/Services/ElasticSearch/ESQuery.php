<?php

namespace App\Services\ElasticSearch;

use App\Models\Social\Photo;
use Elasticsearch\Exception\ClientResponseException;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Elasticsearch\ClientBuilder;

class ESQuery
{
  protected $client;

  public function __construct(array|string $hosts = 'hosts')
  {
    $this->client = ClientBuilder::create()
      ->setHosts(config('database.connections.elasticsearch.' . $hosts))
      ->setSSLVerification(false)
      ->build();
  }

  public function test()
  {
    $search_keys = [
      'pin',
      'doc_serial_number',
      'name',
      'surname',
      'father_name',
      'age',
      'eye_color',
      'height',
      'blood_group',
      'birth_date',
      'sex',
      'marital_status',
      'country',
      'city',
      'address',
      'foreign_passport_number',
      'driver_license',
      'vehicles_search_keys',
      'phones_search_keys',
      'job_keys',
      'studi_keys',
      'tqdk_keys'
    ];

    $search_text_array = ['ramil', 'huseynov'];
    $wildcard_queries = [];
    foreach ($search_text_array as $keyword) {
      foreach ($search_keys as $s_key) {
        $wildcard_queries[] = [
          'wildcard' => [
            'search_keys.' . $s_key => [
              'value' => "*$keyword*",
              'boost' => 1.0, // Opsiyonel olarak boost değeri belirtilebilir
              'rewrite' => 'constant_score' // Opsiyonel olarak rewrite modu belirtilebilir
            ]
          ]
        ];
      }
    }

    $params = [
      'index' => 'people_3',
      'body'  => [
        'from' => $from,
        'size' => $size,
        'sort' => ['updated_at' => 'ASC'],
        '_source' => ['pin', 'name', 'surname', 'father_name', 'image'],
        'query' => [
          'bool' => [
            'should' => $wildcard_queries
          ]
        ],
        'highlight' => [
          'fields' => [
            'search_keys.*' => (object)[],
          ]
        ]
      ]
    ];

    $response = $this->client->search($params);




    $filter_labels = [
      "search_keys.pin" => "Fin",
      "search_keys.doc_serial_number" => "ŞV Seriya no",
      "search_keys.name" => "Ad",
      "search_keys.surname" => "Soyad",
      "search_keys.father_name" => "Ata adı",
      "search_keys.age" => "Yaş",
      "search_keys.eye_color" => "Göz rəngi",
      "search_keys.height" => "Boy",
      "search_keys.blood_group" => "Qan qrupu",
      "search_keys.birth_date" => "Doğum tarixi",
      "search_keys.sex" => "Cins",
      "search_keys.marital_status" => "Ailə vəziyyəti",
      "search_keys.country" => "Ölkə",
      "search_keys.city" => "Şəhər",
      "search_keys.address" => "Ünvan",
      "search_keys.foreign_passport_number" => "Xarici Passport no",
      "search_keys.driver_license" => "Sürücülük vəsiqəsi",
      "search_keys.vehicles_search_keys" => "Nəqliyyat",
      "search_keys.phones_search_keys" => "Telefon nömrəsi",
      "search_keys.job_keys" => "İş yeri",
      "search_keys.studi_keys" => "Təhsil",
      "search_keys.tqdk_keys" => "TQDK məlumatı",
    ];

    $tab_labels = [
      'person' => 'Şəxslər',
      'social_calls' => 'Sosial app zəngləri',
      'social_media' => 'Sosial Media',
      'border_crossing' => 'Sərhəd keçmə',
      'cdr_calls' => 'CDR zənglər',
      'trajectoryTracking' => 'Trayektoriya'
    ];

    $tab_keys = [
      "search_keys.pin" => ['person', 'border_crossing', 'trajectoryTracking'],
      "search_keys.doc_serial_number" => ['person', 'border_crossing', 'trajectoryTracking'],
      "search_keys.name" => ["person", "social_media", 'border_crossing'],
      "search_keys.surname" => ["person", "social_media", 'border_crossing'],
      "search_keys.father_name" => ["person", "social_media", 'border_crossing'],
      "search_keys.age" => ['person'],
      "search_keys.eye_color" => ['person'],
      "search_keys.height" => ['person'],
      "search_keys.blood_group" => ['person'],
      "search_keys.birth_date" => ['person'],
      "search_keys.sex" => ['person'],
      "search_keys.marital_status" => ['person'],
      "search_keys.country" => ['person', 'social_calls', 'border_crossing'],
      "search_keys.city" => ['person', 'border_crossing'],
      "search_keys.address" => ['person'],
      "search_keys.foreign_passport_number" => ['person', 'border_crossing'],
      "search_keys.driver_license" => ['person', 'trajectoryTracking'],
      "search_keys.vehicles_search_keys" => ['person', 'trajectoryTracking'],
      "search_keys.phones_search_keys" => ['person', 'social_calls', 'cdr_calls', 'trajectoryTracking'],
      "search_keys.job_keys" =>  ['person'],
      "search_keys.studi_keys" => ['person'],
      "search_keys.tqdk_keys" => ['person'],
    ];

    foreach ($response['hits']['hits'] as $hit) {
      foreach ($hit['highlight'] as $key => $value) {
        $exp = explode('.', $key);
        $end = end($exp);
        if ($end != 'keyword') {
          $filters[] = [
            'name' =>  'filters',
            'value' => $key,
            'label' => $filter_labels[$key] ?? '',
          ];
          foreach ($tab_keys[$key] as $tab_key) {
            $tabs[] = [
              'name' =>  'tabs',
              'value' => $tab_key,
              'label' => $tab_labels[$tab_key],

            ];
          }
        }
      }
    }

    return [
      'filters' => array_unique($filters, SORT_REGULAR),
      'tabs' => array_unique($tabs, SORT_REGULAR)
    ];
  }

  public function count($index)
  {
    $params = [
      'index' => $index
    ];

    return $this->client->count($params);
  }

  public function index($index, array $body)
  {
    $params = [
      'index' => strtolower($index),
      'body'  => $body
    ];

    return $this->client->index($params);
  }

  public function bulk(array $params)
  {
    return $this->client->bulk($params);
  }


  public function search($index, array $body = [])
  {
    try {
      $response = $this->client->search([
        'index' => $index,
        'body' => $body
      ]);


      // print_r($response);die;

      if (isset($response['hits'])) {
        $hits = $response['hits']['hits'] ?? [];
        $total = $response['hits']['total']['value'] ?? 0;

        $data = new LengthAwarePaginator(
          $hits,
          $total,
          $body['size'],
          Paginator::resolveCurrentPage(),
          ['path' => Paginator::resolveCurrentPath()]
        );

        return $data;
      } else {
        throw new Exception('Elasticsearch response does not contain hits');
      }
    } catch (Exception $e) {
      echo 'Error: ' . $e->getMessage();
      return [];
    }
  }


  public function search_2($index, array $body = [])
  {
    $response = $this->client->search([
      'index' => $index,
      "body" => $body
    ]);

    return $response;
  }

  public function check_update($index, $pin)
  {
    $body = [
      "size" => 1,
      'query' => [
        'bool' => [
          'must' => [
            ['match' => ['pin' => $pin]],
            // ['range' => ['updated_at' => ['gte' => 'now-5d/d', 'lte' => 'now/d']]]
          ]
        ]
      ]
    ];
    $response = $this->client->search([
      'index' => $index,
      "body" => $body
    ]);

    return  $response['hits']['total']['value'] > 0 ? 1 : 0;
  }

  public function getPins($index, array $body = [])
  {
    $response = $this->client->search([
      'index' => $index,
      'body' => $body,
      'size' => 1000,
      'scroll' => '5m',
    ]);

    $scrollId = $response['_scroll_id'];
    $pins = [];

    $hits = $response['hits']['hits'];
    $pins = array_map(function ($hit) {
      return $hit['_source']['pin'];
    }, $hits);

    while (count($hits) > 0) {
      $response = $this->client->scroll([
        'scroll_id' => $scrollId,
        'scroll' => '5m',
      ]);

      $scrollId = $response['_scroll_id'];
      $hits = $response['hits']['hits'];
      foreach ($hits as $hit) {
        $pins[] = $hit['_source']['pin'];
      }
    }

    $this->client->clearScroll(['scroll_id' => $scrollId]);

    return $pins;
  }

  public function scrollSearch($index, $query, $size = 10, $scrollTime = '1m')
  {
    $response = $this->client->search([
      'index' => $index,
      'body' => $query,
      'scroll' => $scrollTime,
      'size' => $size,
    ]);

    $scrollId = $response['_scroll_id'];
    $results = $response['hits']['hits'];

    $allResults = $results;

    while (count($results) > 0) {
      $response = $this->client->scroll([
        'scroll_id' => $scrollId,
        'scroll' => $scrollTime,
      ]);

      $scrollId = $response['_scroll_id'];
      $results = $response['hits']['hits'];
      $allResults = array_merge($allResults, $results);
    }

    return $allResults;
  }

  public function update($params)
  {
    return $this->client->update($params);
  }

  public function delete($index, $id): bool
  {
    try {
      $response = $this->client->delete([
        'index' => $index,
        'id' => $id
      ]);

      return $response['acknowledge'] === 1;
    } catch (ClientResponseException | Exception $exception) {
      logger()->error($exception->getMessage());
      return false;
    }
  }



  public function social_photos()
  {
    $data = Photo::paginate(20);
    return $data;
  }


  public function simpleSearch($index, array $body = [])
  {
    $response = $this->client->search([
      'index' => $index,
      "body" => $body
    ]);

    return $response;
  }
}
