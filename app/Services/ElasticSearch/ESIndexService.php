<?php

namespace App\Services\ElasticSearch;

use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ESIndexService
{

    protected $es_query;

    public function __construct()
    {
        $this->es_query = new ESQuery('hosts_2');
    }

    public function missingPeople($pin)
    {
        $pin = trim($pin);

        if (!empty($pin) && strlen($pin) == 7) {
            try {
                DB::beginTransaction();
                $person = DB::table('people')->where('pin', $pin)->first();

                if (empty($person)) {
                    DB::table('missing_people')->insert(['fin' => $pin, 'created_at' => now()]);
                    DB::commit();
                    return $pin . ' əlavə edildi';
                } else {
                    DB::commit();
                    return $pin . ' mövcuddur';
                }
            } catch (\Exception $e) {
                DB::rollBack();
                return 'Xəta baş verdi: ' . $e->getMessage();
            }
        }
    }


    public function getAccounts()
    {
        return DB::connection('pgsql_social')->table('social_hub.accounts')
            ->select(
                'social_hub.social_medias.platform_name',

                'social_hub.accounts_pin.pin as pin',
                'social_hub.accounts.id',
                'social_hub.accounts.name',
                'social_hub.accounts.surname',
                'social_hub.accounts.username',
                'social_hub.accounts.photo_path',
                'social_hub.accounts.nationality',
                'social_hub.accounts.checked',
                'social_hub.accounts.insert_time',
                'social_hub.accounts.link'
            )
            ->distinct('accounts.id')
            ->leftJoin('social_hub.social_medias', 'social_hub.social_medias.id', '=', 'social_hub.accounts.platform_id')
            ->leftJoin('social_hub.accounts_pin', 'social_hub.accounts_pin.account_id', '=', 'social_hub.accounts.id')
            ->where('accounts.checked',1)  
            ->where('accounts.platform_id',1)  
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('social_hub.elastic_index_status')
                    ->whereRaw('social_hub.elastic_index_status.fk_id = social_hub.accounts.id')
                    ->where('social_hub.elastic_index_status.type', 'accounts');
                    // ->where('social_hub.elastic_index_status.status', '!=', 0);
            })
            // ->where('social_hub.accounts.id', '4761306')
            ->orderBy('social_hub.accounts.id', 'ASC')
            ->take(5000)
            ->get();

        // $data =DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->get();

        // return print_r($data);
    }

    public function getPosts()
    {
        return DB::connection('pgsql_social')->table('social_hub.posts')
            ->select(
                'social_hub.social_medias.platform_name as platform_name',
                'social_hub.accounts_pin.pin as account_pin',
                'social_hub.accounts.id as account_id',
                'social_hub.accounts.name as account_name',
                'social_hub.accounts.surname as account_surname',
                'social_hub.accounts.username as account_username',
                'social_hub.accounts.link as account_link',
                'social_hub.accounts.photo_path as account_photo_path',

                'social_hub.posts.id as id',
                'post_media.photo_path as post_photo_path',
                'social_hub.posts.link as post_link',
                'social_hub.posts.publish_time as post_publish_time',
                'social_hub.posts.source_type as post_source_type',
                'social_hub.posts.page_id as post_page_id',
                'social_hub.posts.comment_count as post_comment_count',
                'social_hub.posts.context as post_context',
            )
            ->distinct('social_hub.posts.id')
            ->leftJoin('social_hub.social_medias', 'social_hub.social_medias.id', 'social_hub.posts.platform_id')
            ->leftJoin('social_hub.accounts', 'social_hub.accounts.id', 'social_hub.posts.account_id')
            ->leftJoin('social_hub.accounts_pin', 'social_hub.accounts_pin.account_id', 'social_hub.accounts.id')
            ->leftJoin('social_hub.media as post_media', 'post_media.id', 'social_hub.posts.media_id')


            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('social_hub.elastic_index_status')
                    ->whereRaw('social_hub.elastic_index_status.fk_id = social_hub.posts.id')
                    ->where('social_hub.elastic_index_status.type', 'posts');
                    // ->where('social_hub.elastic_index_status.status', '!=', 0);
            })
            // ->where('posts.id','91129')
            ->orderBy('social_hub.posts.id', 'ASC')
            ->take(5000)->get();
    }

    public function getComments()
    {
        return DB::connection('pgsql_social')->table('social_hub.comments')
            ->select(
                'social_hub.accounts_pin.pin as pin',
                'social_hub.social_medias.platform_name as platform_name',

                'social_hub.accounts_pin.pin as account_pin',
                'social_hub.accounts.id as account_id',
                'social_hub.accounts.name as account_name',
                'social_hub.accounts.surname as account_surname',
                'social_hub.accounts.username as account_username',
                'social_hub.accounts.link as account_link',
                'social_hub.accounts.photo_path as account_photo_path',

                'social_hub.posts.id as post_id',
                'post_media.photo_path as post_photo_path',
                'social_hub.posts.link as post_link',
                'social_hub.posts.publish_time as post_publish_time',
                'social_hub.posts.source_type as post_source_type',
                'social_hub.posts.page_id as post_page_id',
                'social_hub.posts.comment_count as post_comment_count',
                'social_hub.posts.context as post_context',

                'social_hub.comments.id',
                'social_hub.comments.context as comment_context',
                'social_hub.comments.link as comment_link',
                'social_hub.comments.publish_time as comment_publish_time',

            )
            ->distinct('social_hub.comments.id')
            ->leftJoin('social_hub.social_medias', 'social_hub.social_medias.id', 'social_hub.comments.platform_id')
            ->leftJoin('social_hub.accounts', 'social_hub.accounts.id', 'social_hub.comments.account_id')
            ->leftJoin('social_hub.accounts_pin', 'social_hub.accounts_pin.account_id', 'social_hub.accounts.id')
            ->leftJoin('social_hub.posts', 'social_hub.posts.id', 'social_hub.comments.post_id')
            ->leftJoin('social_hub.media as post_media', 'post_media.id', 'social_hub.posts.media_id')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('social_hub.elastic_index_status')
                    ->whereRaw('social_hub.elastic_index_status.fk_id = social_hub.comments.id')
                    ->where('social_hub.elastic_index_status.type', 'comments');
                    // ->where('social_hub.elastic_index_status.status', '!=', 0);
            })
            ->orderBy('social_hub.comments.id', 'ASC')->take(5000)->get();
    }

    public function getDescriptions()
    {
        return DB::connection('pgsql_social')->table('social_hub.description')
            ->select(
                'social_hub.accounts_pin.pin as pin',
                'social_hub.social_medias.platform_name as platform_name',
                
                'social_hub.accounts_pin.pin as account_pin',
                'social_hub.accounts.id as account_id',
                'social_hub.accounts.name as account_name',
                'social_hub.accounts.surname as account_surname',
                'social_hub.accounts.username as account_username',
                'social_hub.accounts.link as account_link',
                'social_hub.accounts.photo_path as account_photo_path',


                'social_hub.description.id',
                'social_hub.media.photo_path as desc_photo_path',
                'social_hub.description.aze as desc_aze',
                'social_hub.description.eng as desc_eng',
                'social_hub.description.sort_order as desc_sort_order',
            )
            ->distinct('social_hub.description.id')
            ->leftJoin('social_hub.media', 'social_hub.media.id', 'social_hub.description.media_id')
            ->leftJoin('social_hub.social_medias', 'social_hub.social_medias.id', 'social_hub.media.platform_id')
            ->leftJoin('social_hub.accounts', 'social_hub.accounts.id', 'social_hub.media.account_id')
            ->leftJoin('social_hub.accounts_pin', 'social_hub.accounts_pin.account_id', 'social_hub.accounts.id')
            ->whereNotNull('media.photo_path')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('social_hub.elastic_index_status')
                    ->whereRaw('social_hub.elastic_index_status.fk_id = social_hub.description.id')
                    ->where('social_hub.elastic_index_status.type', 'descriptions')
                    ->where('social_hub.elastic_index_status.status', '!=', 0);
            })
            ->orderBy('social_hub.description.id', 'ASC')
            ->take(5000)
            ->get();
    }

    public function insertIndexStatus($collection, $type, $schema = null)
    {
        $insertData = [];
        foreach ($collection as $item) {
            $hash = md5(Str::slug($item->id . $type));
            $insertData[] = [
                'fk_id' => $item->id,
                'type' => $type,
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
                'hash'=> $hash,
            ];
        }

        if ($schema == 'social_hub') {
            DB::connection('pgsql_social')->table('social_hub.elastic_index_status')->insertOrIgnore($insertData);
        } else {
            DB::table('elastic_index_status')->insertOrIgnore($insertData);
        }

        return true;
    }

    public function updateIndexStatus($type, $ids, $status, $error_message = null, $schema = null)
    {
        try {
            if ($schema == 'social_hub') {
                $db = DB::connection('pgsql_social')->table('social_hub.elastic_index_status');
            } else {
                $db = DB::table('elastic_index_status');
            }
            $db->where('type', $type)
                ->whereIn('fk_id', $ids)
                ->update(['status' => $status, 'error_message' => $error_message, 'updated_at' => now()]);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }


    public function smParams($index, $item, $search_keys, $order_by)
    {
        $params['body'][] = [
            'index' => [
                '_index' => $index,
                '_id' => $item->id,
            ]
        ];
        $params['body'][] = [
            'pin' => $item->pin,
            'platform_name' => $item->platform_name,
            'data' => $item,
            'search_keys' => $search_keys,
            'order_by'   => $order_by,
            'created_at' => now()->format('Y-m-d H:i:s'),
            'updated_at' => now()->format('Y-m-d H:i:s'),
        ];
        return $params;
    }

    public function bulkInsert($collection, $params, $type, $schema = null)
    {
        $ids = $collection->pluck('id');
        try {
            $this->es_query->bulk($params);
            $this->updateIndexStatus($type, $ids, 2, null, $schema);
            return true;
        } catch (\Exception $e) {
            $this->updateIndexStatus($type, $ids, 0, $e->getMessage(), $schema = null);
            return $e->getMessage();
        }

        

        

        
    }

    public function updateEsIndexStatus($esResponse, $type, $schema = null){

        if($schema == 'social_hub'){
            $db = DB::connection('pgsql_social')->table('social_hub.elastic_index_status');
        }else{
            $db = DB::table('elastic_index_status');
        }
        $failed = [];
        $successIds = [];

        foreach ($esResponse['items'] as $item) {
            if (isset($item['index']['error'])) {
                $failed[] = [
                    'fk_id' => $item['index']['_id'],
                    'status' => 0,
                    'error_message' => $item['index']['error']['reason']
                ];
            } else {
                $successIds[] = $item['index']['_id'];
            }
        }

        if (!empty($failed)) {
            $db->where('type', $type)
            ->whereIn('fk_id', $failed)
            ->update([
                'status' => 0,
                'error_message' => $failed[0]['error_message'] ?? 'fail',
                'updated_at' => now()
            ]);
        }

        if (!empty($successIds)) {
                 $db->where('type', $type)
                ->whereIn('fk_id', $successIds)
                ->update([
                    'status' => 2,
                    'updated_at' => now()
                ]);
        }

        return [
            'successIds'=>$successIds,
            'failed'=>$failed,
        ];
    }


    public function dataBeeinContact($data)
    {
        $phone = $data['phone'];
        if (preg_match('/^\d+$/', $phone) && strlen($phone) >= 9) {
            $weight = [
                'bina.az' => 5,
                'tap.az' => 5,
                'turbo.az' => 5,
                'food_orders' => 10,
                'mobcontact' => 20,
                'azerisiq' => 30,
                'azeriqaz' => 30,
                'azersu' => 30,
                'azerpoct' => 30,
                'avtovagzal' => 30,
                'ady' => 30,
                'azparking' => 40,
            ];


            $phone = substr($phone, -9);

            $hash = Str::slug(
                ($data['pin'] ?? '') .
                ($data['phone'] ?? '') .
                ($data['name'] ?? '') .
                ($data['email'] ?? '') .
                ($data['source'] ?? '') .
                ($data['birthday'] ?? '') .
                ($data['city'] ?? '') .
                ($data['address'] ?? '').
                ($data['note'] ?? '')
            );
            $hash = md5($hash);
            
            return [
                'pin'        => $data['pin'] ?? '',
                'phone'      => isset($data['phone']) ? '994' . $phone : '',
                'name'       => $data['name'] ?? '',
                'email'      => $data['email'] ?? '',
                'birthday'   => !empty($data['birthday']) ? Carbon::parse($data['birthday'])->format('Y-m-d') : null,
                'city'       => $data['city'] ?? '',
                'address'    => $data['address'] ?? '',
                'note'       => $data['note'] ?? '',
                'source'     => $data['source'] ?? '',
                'weight'     => isset($weight[$data['source']]) ? $weight[$data['source']] : 0,
                'hash'       => $hash,
                'created_at' => now(),
            ];
        } else {

            return null;
        }
    }
    public function bulkInsertBeeinContact($data)
    {
        if (!empty($data)) {
            $data = $filteredData = array_filter($data);
            DB::table('beein_contact')->insertOrIgnore($data);
        }
    }
}
