<?php

namespace App\Services\Customs;

use App\Http\Resources\SearchForeignCustomsResource;
use App\Http\Resources\SearchLocalCustomsResource;
use App\Repository\LocalCustomsRepository;
use App\Services\MilvusService;
use Exception;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

class LocalCustomsService extends MilvusService
{
    /**
     * @var string $api_uri
     */
    protected string $api_uri = 'customs/search/local';

    /**
     * @param \App\Repository\LocalCustomsRepository $localCustomsRepository
     */
    public function __construct(
        protected LocalCustomsRepository $localCustomsRepository,
    ) {

    }

    /**
     * @param array $params
     * @param int $perPage
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(array $params, int $perPage): JsonResponse
    {
        try {
            $collection = $this->sendRequest($this->collectParamsForMilvus($params));
        } catch (Exception $e) {
            return $this->errorResponse($e->getCode(), $e->getMessage());
        }

        if (empty($collection['response']['data'])) {
            return $this->successResponse(Response::HTTP_OK, 'Nothing Found');
        }

        $data = $collection['response']['data'];
        $params['ids'] = array_column($data, 'record_id');
        $similarities = array_column($data, 'distance');
        $similarityByIds = array_combine($params['ids'], $similarities);

        $people = $this->localCustomsRepository->getPeopleFromOracleServer($params, $perPage);

        $people->each(function ($person) use ($similarityByIds) {
            $person->distance = $similarityByIds[$person->id];
        });

        return SearchLocalCustomsResource::collection($people)->response();
    }

    /**
     * @param $params
     * @param int $perPage
     * @return \Illuminate\Http\JsonResponse
     */
    public function paginateByParams($params, int $perPage): JsonResponse
    {
        $people = $this->localCustomsRepository->getPeopleFromOracleServer($params, $perPage);

        return SearchLocalCustomsResource::collection($people)->response();
    }

    /**
     * @param array $params
     * @return array
     */
    protected function collectParamsForMilvus(array $params): array
    {
        return [
            'similarity' => $params['similarity'],
            'max_count' => $params['max_count'],
            'photo' => $params['photo'],
        ];
    }
}
