<?php

namespace App\Services\Customs;

use App\Http\Resources\ForeignCitizen\EcnebiDataResource;
use App\Http\Resources\ForeignCitizen\ShowForeignCitizenResource;
use App\Http\Resources\ForeignCustomsResource;
use App\Models\ForeignCitizen;
use Exception;
use App\Services\MilvusService;
use Illuminate\Http\JsonResponse;
use App\Repository\ForeignCustomsRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Resources\SearchForeignCustomsResource;

class ForeignCustomsService extends MilvusService
{
    /**
     * @var string $api_uri
     */
    protected string $api_uri = 'customs/search/foreign';

    /**
     * @param \App\Repository\ForeignCustomsRepository $foreignCustomsRepository
     */
    public function __construct(
        protected ForeignCustomsRepository $foreignCustomsRepository,
    ) {

    }

    /**
     * @param array $params
     * @param int $perPage
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(array $params, int $perPage): JsonResponse
    {
        try {
            $collection = $this->sendRequest($this->collectParamsForMilvus($params));
        } catch (Exception $e) {
            return $this->errorResponse($e->getCode(), $e->getMessage());
        }

        if (empty($collection['response']['data'])) {
            return $this->successResponse(Response::HTTP_OK, 'Nothing Found');
        }

        $data = $collection['response']['data'];
        $params['ids'] = array_column($data, 'record_id');
        $similarities = array_column($data, 'distance');
        $similarityByIds = array_combine($params['ids'], $similarities);

        $people = $this->foreignCustomsRepository->getPeopleFromOracleServer($params, $perPage);

        $people->each(function ($person) use ($similarityByIds) {
            $person->distance = $similarityByIds[$person->id];
        });

        return SearchForeignCustomsResource::collection($people)->response();
    }

    /**
     * @param $params
     * @param int $perPage
     * @return \Illuminate\Http\JsonResponse
     */
    public function paginateByParams($params, int $perPage): JsonResponse
    {
        // Raw Query Without Paginate
        $people = $this->foreignCustomsRepository->getPeopleFromRawQuery($params, $perPage, (int) request()->input('page'));

        $count = $this->foreignCustomsRepository->getPeopleCount($params);

        $paginator = new LengthAwarePaginator($people, (int) $count[0]->count, $perPage, (int) request()->input('page'));

        return ForeignCustomsResource::collection($paginator)->response();

        //Raw Query With Paginate
        // $people = $this->foreignCustomsRepository->getPeopleFromRawQueryWithPaginate($params, $perPage);

        // Eloquent Version
        // $people = $this->foreignCustomsRepository->getPeopleFromRawQueryWithPaginate($params, $perPage);

        // return ForeignCustomsResource::collection($people)->response();
    }

    /**
     * @param $rowId
     * @return \Illuminate\Http\JsonResponse
     */
    public function findByRowId($rowId): JsonResponse
    {
        $row = $this->foreignCustomsRepository->getDocumentNumberFromOracleServer($rowId);

        $data = ForeignCitizen::query()->where('document_number', $row[0]->document_number)->get();


        //$filteredData = array_filter($data->data, function ($value) {
        //    if (($value['similar_percent'] <= (int)request()->get('similar_percent', 0)) &&
        //        ((float)(request()->get('similarity') ?? 0) < (float)$value['distance']) &&
        //        (request()->get('precinct') == '' || request()->get('precinct') == $value['menteqe'])
        //    ) {
        //        return $value;
        //    }
        //});
        //
        //$data->data = array_values($filteredData);

        return ShowForeignCitizenResource::collection($data)->response();
    }

    /**
     * @param $rowId
     * @return \Illuminate\Http\JsonResponse
     */
    public function findByRowIdOracle($rowId): JsonResponse
    {
        $row = $this->foreignCustomsRepository->getDocumentNumberFromOracleServer($rowId);

        return EcnebiDataResource::collection($row)->response();
    }


    /**
     * @param array $params
     * @return array
     */
    protected function collectParamsForMilvus(array $params): array
    {
        return [
            'similarity' => $params['similarity'],
            'max_count' => $params['max_count'],
            'photo' => $params['photo'],
        ];
    }
}
