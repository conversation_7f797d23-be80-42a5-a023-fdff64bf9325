<?php

namespace App\Services;

use App\Http\Resources\BlackList\BlacklistResource;
use Illuminate\Http\JsonResponse;
use App\Repository\BlacklistRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class BlacklistService extends AbstractService
{
    /**
     * @param \App\Repository\BlacklistRepository $blacklistRepository
     */
    public function __construct(
        public BlacklistRepository $blacklistRepository
    ) {
        parent::__construct($blacklistRepository);
    }

    /*
     * overriding destroy method from AbstractService
     */
    public function findAndDelete(int $id): JsonResponse
    {
        $this->blacklistRepository->find($id);

        return $this->delete($id);
    }

    /**
     * @param array $params
     * @return Collection
     */
    public function getListsByParams(array $params): Collection
    {
        return $this->blacklistRepository->getByParams($params)->get();
    }

    /**
     * @param array $params
     * @param int $limit
     * @return JsonResponse
     */
    public function paginate(array $params, int $limit = 10): JsonResponse
    {
        $data = $this->blacklistRepository
            ->setParams([
                'params' => $params,
            ])
            ->paginate($limit);

        return BlacklistResource::collection($data)->response();
    }
}
