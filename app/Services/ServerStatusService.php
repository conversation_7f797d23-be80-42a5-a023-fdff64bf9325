<?php

namespace App\Services;

use App\Models\Log;
use App\Models\Oracle\Azeri;
use App\Models\PersonData;
use Exception;
use Carbon\Carbon;
use App\Models\Person;
use Illuminate\Http\JsonResponse;
use App\Repository\PersonRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use App\Http\Resources\Person\PersonResource;
use App\Http\Resources\Person\PersonListResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use JetBrains\PhpStorm\ArrayShape;

class ServerStatusService
{



    public array $response = [
        'name' => 'Postgre Sql',
        'status' => 200,
        'message' => 'Connected',
        'type' => 'DB',
        'host' => '*.*.*.*'
    ];

    public function getALLStatuses(): array
    {
        return [
            $this->backendServer(),
//            $this->frontendServer(),
            $this->postgreSql(),
            $this->mongoDb(),
//            $this->oracleDB(),
//            $this->nebulaApi(),
        ];
    }

    public function postgreSql(): array
    {
        $this->response['name'] = 'Postgre Sql';
        $this->response['host'] = '*.*.*.28';
        try {
            Person::query()->exists();
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }

    public function mongoDb(): array
    {
        $this->response['name'] = 'Mongo DB';
        $this->response['host'] = '*.*.*.36';
        try {
            PersonData::query()->exists();
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }


    public function oracleDB(): array
    {
        $this->response['name'] = 'Oracle DB';
        $this->response['host'] = '*.*.*.106';
        try {
            Azeri::query()->exists();
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }

    public function backendServer(): array
    {
        $this->response['name'] = 'Backend Server';
        $this->response['type'] = 'Server';
        //$this->response['host'] = $this->servers['Backend'];
        try {
            $this->response['status'] = Http::get(config('servers.backend'))->status();
            $this->checkStatusForTelegramBot($this->response);
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }

    public function frontendServer(): array
    {
        $this->response['name'] = 'Frontend Server';
        $this->response['type'] = 'Server';
        //$this->response['host'] = $this->servers['Frontend'];
        try {
            $this->response['status'] = Http::get(config('servers.frontend'))->status();
            $this->checkStatusForTelegramBot($this->response);
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }

    public function milvus(): array
    {
        $this->response['name'] = 'Frontend Server';
        $this->response['type'] = 'Server';
       // $this->response['host'] = $this->servers['Frontend'];
        try {
            $this->response['status'] = Http::get(config('servers.milvus'))->status();
            $this->checkStatusForTelegramBot($this->response);
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }

    public function nebulaApi(): array
    {
        $this->response['name'] = 'Nebula Web Services (Relations)';
        $this->response['type'] = 'Services';
        $this->response['host'] = '*.*.*.70:9669';
        try {

            $client = new NGraphClient(config('servers.nebula_path'), 9669);

            $client->authenticate("app_beein", "T7gPa9eY");

            $query = "SHOW HOSTS;";
            $client->execute("USE beein_x");

            $result = $client->execute($query);

            if ($result->error_code && $result->error_code != 0) {
                $this->response['status'] = $result->error_code;
                $this->response['message'] = $result->error_msg ?? 'ERROR';
                sendServerStatusToTelegram($this->response);
            }

//            $this->response['status'] = Http::get('http://***********:5001/health')->status();
            $this->checkStatusForTelegramBot($this->response);
        } catch (Exception $exception) {
            $this->response['status'] = $exception->getCode();
            $this->response['message'] = $exception->getMessage();
            sendServerStatusToTelegram($this->response);
        }

        return $this->response;
    }

    public function checkStatusForTelegramBot($response): void
    {
        if ($response['status'] != 200) {
            sendServerStatusToTelegram($response);
        }
    }

    function runAndListenEloquentQuery($queryCallback, $listenCallback = null)
    {
        DB::listen(function ($query) use ($listenCallback) {
            $start = microtime(true);

            $end = microtime(true);
            $executionTime = $end - $start;

            if ($listenCallback) {
                call_user_func($listenCallback, $query, $executionTime);
            }
            if ($executionTime > 5) {
            }
        });
        return $queryCallback();
    }


}
