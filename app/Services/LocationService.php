<?php

namespace App\Services;
use Carbon\Carbon;
use DateTime;
use Guz<PERSON>Http\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class LocationService
{
    private mixed $apiKey;
    private string $baseUrl;

    public function __construct($apiKey = null, $baseUrl = null)
    {
        $this->apiKey = $apiKey ?? config('services.loc_api_key');
        $this->baseUrl = $baseUrl ?? config('services.loc_base_url');
    }


    /**
     * @throws GuzzleException
     */
    public function lastLocationsByPhone(int $phone, string $start, string $end)
    {
        $client = new Client();
        $response = $client->request('GET', $this->baseUrl . 'v2/different-locations-for-given-number', [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'json' => [
                "number" => $phone,
                "start" => $start,
                "end" => $end,
                "response_count" => 3,
                "small_radius" => 1.0,
                "big_radius" => 5.0
            ],
        ]);
        return json_decode($response->getBody(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function findLocation(int $phoneNumber, string $startTime, string $endTime, float $radius): \Psr\Http\Message\ResponseInterface
    {
        $client = new Client();
        return $client->post($this->baseUrl . 'v2/find-location', [
            'headers' => [
                'Accept' => 'application/json',
                'x-api-key' => $this->apiKey,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'phone_number' => $phoneNumber,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'radius' => $radius
            ],
        ]);
    }

    /**
     * @throws GuzzleException
     */
    public function findSimilarity($number, $start, $end, $matchPercentage, $breakTime, $responseCount = 20, $smallRadius = 1.0, $bigRadius = 4.0, $deltaTime = 100)
    {
        $client = new Client();
        try {
            $response = $client->request('GET', $this->baseUrl . 'v2/similarity', [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'number' => $number,
                    'start' => $start,
                    'end' => $end,
                    'response_count' => $responseCount,
                    'small_radius' => $smallRadius,
                    'big_radius' => $bigRadius,
                    'delta_time' => $deltaTime,
                    'break_time' => $breakTime,
                    'match_percentage' => $matchPercentage
                ],
            ]);
            return json_decode($response->getBody(), true);

        } catch (RequestException $e) {
            return [
                'error' => 'Request failed',
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ];
        } catch (\Exception $e) {
            return [
                'error' => 'An error occurred while processing your request.',
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * @throws GuzzleException
     */
    public function findCar($carNumber, $start, $end, $responseCount, $smallRadius, $bigRadius, $deltaTime, $matchPercentage, $perPage, $offset): array
    {
        $client = new Client();
        try {
            $response = $client->request('GET', $this->baseUrl . 'car', [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'car_number' => $carNumber,
                    'start' => $start,
                    'end' => $end,
                    'response_count' => $responseCount,
                    'small_radius' => $smallRadius,
                    'big_radius' => $bigRadius,
                    'delta_time' => $deltaTime,
                    'match_percentage' => $matchPercentage
                ],
            ]);
            $dataMap = json_decode($response->getBody(), true);
            if (empty($dataMap['data'])) {
                return ["dataMap" => [], "dataResult" => []];
            }

            $my_data = [];
            $phoneNumber = [];
            foreach ($dataMap['data'] as $item) {
                $phoneNumber[] = substr((string)$item['phone'], 3);
            }

            $query1 = DB::table('people')
                ->join('pin_phone', 'pin_phone.pin', '=', 'people.pin')
                ->select(
                    'pin_phone.pin',
                    'pin_phone.index',
                    DB::raw('CAST(pin_phone.monitoring AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('pin_phone.monitoring', $phoneNumber);

            $query2 = DB::table('people')
                ->join('phones', 'phones.pin', '=', 'people.pin')
                ->select(
                    'phones.pin',
                    'phones.id',
                    DB::raw('CAST(phones.phone AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('phones.phone', $phoneNumber);

            $combinedQuery = $query2->unionAll($query1);
            $combinedQuery->orderByRaw('1')
                ->chunk(100, function ($pins) use (&$my_data) {
                    foreach ($pins as $pin) {
                        if (!isset($my_data[$pin->contact])) {
                            $my_data[$pin->contact] = $pin;
                        }
                    }
                });

            foreach ($dataMap['data'] as &$item) {
                $phoneNumber = substr((string)$item['phone'], 3);
                $pin = $my_data[$phoneNumber] ?? [];
                if ($pin) {
                    $item['first_name'] = $pin->name ?? null;
                    $item['surname'] = $pin->surname ?? null;
                    $item['father_name'] = $pin->father_name ?? null;
                } else {
                    $item['first_name'] = null;
                    $item['surname'] = null;
                    $item['father_name'] = null;
                }
            }

            $uniquePoints = [];
            $points = [];

            foreach ($dataMap['data'] as $user) {
                foreach ($user['points'] as $point) {
                    $key = "{$point['lat']},{$point['lon']}";
                    if (!isset($uniquePoints[$key])) {
                        $uniquePoints[$key] = true;
                        $points[] = "({$point['lon']}, {$point['lat']})";
                    }
                }
            }
            $points = implode(',', $points);
            $sql = "SELECT lon, lat, max(ctd.name) name, max(ctd.description) description, max(ctd.radius) as radius, max(ctd.beam) as beam, max(ctd.hash) as antenna_hash
                    FROM radius_events.cell_towers_dictionary ctd
                    WHERE (ctd.lon, ctd.lat) in ($points)
                    GROUP BY ctd.lon, ctd.lat";
            $sqlDataMap = DB::connection('clickhouse_radius')->select($sql);
            $dataResult = [];
            foreach ($dataMap['data'] as &$dataItem) {
                unset($item);
                foreach ($dataItem['points'] as &$item) {
                    foreach ($sqlDataMap as $matchedData) {
                        if ($matchedData['lat'] == $item['lat'] && $matchedData['lon'] == $item['lon']) {
                            $dataResult[] = [
                                'phone' => $dataItem['phone'],
                                'lat' => $item['lat'],
                                'lon' => $item['lon'],
                                'name' => $matchedData['name'],
                                'description' => $matchedData['description'],
                                'radius' => $matchedData['radius'],
                                'beam' => $matchedData['beam'],
                                'antenna_hash' => $matchedData['antenna_hash'],
                            ];
                            $item['name'] = $matchedData['name'];
                            $item['description'] = $matchedData['description'];
                            $item['beam'] = $matchedData['beam'];
                            $item['radius'] = $matchedData['radius'];
                            break;
                        }
                    }
                }
                unset($item);
            }
            unset($dataItem);

            foreach ($dataResult as &$item) {
                $phoneNumber = substr((string)$item['phone'], 3);
                $pin = $my_data[$phoneNumber] ?? [];
                if ($pin) {
                    $item['first_name'] = $pin->name ?? null;
                    $item['surname'] = $pin->surname ?? null;
                    $item['father_name'] = $pin->father_name ?? null;
                } else {
                    $item['first_name'] = null;
                    $item['surname'] = null;
                    $item['father_name'] = null;
                }
            }
            return ["dataMap" => $dataMap['data'], "dataResult" => $dataResult];
        } catch (RequestException $e) {
            return ['error' => 'Request failed', 'message' => $e->getMessage(), 'code' => $e->getCode()];
        } catch (\Exception $e) {
            if ($e->getCode() == 0) {
                return ["dataMap" => [], "dataResult" => []];
            }
            return ['err' => $e->getMessage(), 'code' => $e->getCode()];
        }
    }

    /**
     * @throws GuzzleException
     */
    public function findMeetingPlaces($number1, $number2, $start, $end, $radius, $delta_time, $break_time): array
    {
        $client = new Client();
        try {
            $response = $client->request('GET', $this->baseUrl . 'v2/two-number', [
                'headers' => [
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'number1' => $number1,
                    'number2' => $number2,
                    'start' => $start,
                    'end' => $end,
                    'radius' => $radius,
                    'delta_time' => $delta_time,
                    'break_time' => $break_time,
                    'polygon' => []
                ],
            ]);
            $dataMap = json_decode($response->getBody(), true);
            if (empty($dataMap['data'])) {
                return ["dataMap" => [], "dataResult" => []];
            }
            $uniquePoints = [];
            $points = [];
            foreach ($dataMap['data'] as $user) {
                foreach ($user as $point) {
                    $key = "{$point['lat']},{$point['lon']}";
                    if (!isset($uniquePoints[$key])) {
                        $uniquePoints[$key] = true;
                        $points[] = "({$point['lon']}, {$point['lat']})";
                    }
                }
            }
            $points = implode(',', $points);
            $data_map = [];
            collect($dataMap["data"])->map(function ($item, $key) use (&$data_map) {
                collect($item)->map(function ($item2) use (&$item, $key, &$data_map) {
                    $data_map[] = [
                        'phone' => $key,
                        'lat' => $item2['lat'],
                        'lon' => $item2['lon'],
                        'start' => $item2['start'],
                        'end' => $item2['end'],
                    ];
                });
                return $data_map;
            })->toArray();
            $dataMap['data'] = $data_map;
            $sql = "SELECT lon, lat, max(ctd.name) name, max(ctd.description) description,max(ctd.radius) as radius,max(ctd.beam) as beam,max(ctd.hash) as antenna_hash
                    FROM radius_events.cell_towers_dictionary ctd
                    WHERE (ctd.lon,ctd.lat) in [$points]
                    GROUP BY ctd.lon, ctd.lat";
            $sqlResult = DB::connection('clickhouse_radius')->select($sql);
            $sqlDataMap = [];
            foreach ($sqlResult as $row) {
                $key = "{$row['lat']},{$row['lon']}";
                $sqlDataMap[$key] = $row;
            }
            $dataResult = [];
            foreach($dataMap['data'] as $dataItem) {
                $key = "{$dataItem['lat']},{$dataItem['lon']}";
                if(isset($sqlDataMap[$key])){
                    $matchedData = $sqlDataMap[$key];
                    $dataResult[] = [
                        'phone' => $dataItem['phone'],
                        'lat' => $dataItem['lat'],
                        'lon' => $dataItem['lon'],
                        'start' => $dataItem['start'],
                        'end' => $dataItem['end'],
                        'name' => $matchedData['name'],
                        'description' => $matchedData['description'],
                        'radius' => $matchedData['radius'],
                        'beam' => $matchedData['beam'],
                        'antenna_hash' => $matchedData['antenna_hash'],
                    ];
                }
            }
            return ["dataMap" => $dataMap['data'], "dataResult" => $dataResult];

        } catch (RequestException $e) {
            return ['error' => 'Request failed', 'message' => $e->getMessage(), 'code' => $e->getCode()];
        } catch (\Exception $e) {
            return ['error' => 'An error occurred while processing your request.', 'message' => $e->getMessage(), 'code' => $e->getFile() . $e->getLine()];
        }
    }

    /**
     * @throws GuzzleException
     */
    public function findIntervalSimilarity($response_count, $min_match, $points, $convertedPolygons): array
    {
        $client = new Client();
        try {
            $response = $client->request('GET', $this->baseUrl . 'v2/interval-similarity', [
                'headers' => ['Content-Type' => 'application/json'],
                'json' => compact('response_count', 'min_match', 'points', 'convertedPolygons'),
            ]);
            $dataMap = json_decode($response->getBody(), true);
            if (empty($dataMap['data'])) {
                return ['message' => 'Bu məlumatlara uyğun ehtimal tapılmadı.', 404];
            }
            $uniquePoints = [];
            $points = [];
            $my_data = [];
            $phoneNumber = [];
            foreach ($dataMap['data'] as $item) {
                $phoneNumber[] = substr((string)$item['number'], 3);
            }
            $this->fetchUserData($dataMap['data'],$my_data);
            $query1 = DB::table('people')
                ->join('pin_phone', 'pin_phone.pin', '=', 'people.pin')
                ->select(
                    'pin_phone.pin',
                    'pin_phone.index',
                    DB::raw('CAST(pin_phone.monitoring AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('pin_phone.monitoring', $phoneNumber);

            $query2 = DB::table('people')
                ->join('phones', 'phones.pin', '=', 'people.pin')
                ->select(
                    'phones.pin',
                    'phones.id',
                    DB::raw('CAST(phones.phone AS TEXT) AS contact'),
                    'people.name',
                    'people.surname',
                    'people.father_name'
                )
                ->whereIn('phones.phone', $phoneNumber);

            $combinedQuery = $query2->unionAll($query1);
            $combinedQuery->orderByRaw('1')
                ->chunk(100, function ($pins) use (&$my_data) {
                    foreach ($pins as $pin) {
                        if (!isset($my_data[$pin->contact])) {
                            $my_data[$pin->contact] = $pin;
                        }
                    }
                });
            foreach ($dataMap['data'] as &$item) {
                $phoneNumber = substr((string)$item['number'], 3);
                $pin = $my_data[$phoneNumber] ?? [];
                if ($pin) {
                    $item['first_name'] = $my_data[$phoneNumber]->name ?? null;
                    $item['surname'] = $my_data[$phoneNumber]->surname ?? null;
                    $item['father_name'] = $my_data[$phoneNumber]->father_name ?? null;
                } else {
                    $item['first_name'] = null;
                    $item['surname'] = null;
                    $item['father_name'] = null;
                }
            }
            foreach ($dataMap['data'] as $user) {
                foreach ($user['coords_of_point'] as $point) {
                    $key = "{$point['lat']},{$point['lon']}";
                    if (!isset($uniquePoints[$key])) {
                        $uniquePoints[$key] = true;
                        $points[] = "({$point['lon']}, {$point['lat']})";
                    }
                }
            }
            $points = implode(',', $points);
            $sql = "SELECT lon, lat, max(ctd.name) name, max(ctd.description) description,max(ctd.radius) as radius,max(ctd.beam) as beam,max(ctd.hash) as antenna_hash
                    FROM radius_events.cell_towers_dictionary ctd
                    WHERE (ctd.lon,ctd.lat) in [$points]
                    GROUP BY ctd.lon, ctd.lat";
            $sqlDataMap = DB::connection('clickhouse_radius')->select($sql);
            $dataResult = [];
            foreach ($dataMap['data'] as &$dataItem) {
                unset($item);
                foreach ($dataItem['coords_of_point'] as &$item) {
                    foreach ($sqlDataMap as $matchedData) {
                        if ($matchedData['lat'] == $item['lat'] && $matchedData['lon'] == $item['lon']) {
                            $dataResult[] = [
                                'phone' => $dataItem['number'],
                                'lat' => $item['lat'],
                                'lon' => $item['lon'],
                                'name' => $matchedData['name'],
                                'description' => $matchedData['description'],
                                'radius' => $matchedData['radius'],
                                'beam' => $matchedData['beam'],
                                'antenna_hash' => $matchedData['antenna_hash'],
                            ];
                            $item['name'] = $matchedData['name'];
                            $item['description'] = $matchedData['description'];
                            $item['beam'] = $matchedData['beam'];
                            $item['radius'] = $matchedData['radius'];
                            break;
                        }
                    }
                }
                unset($item);
            }
            unset($dataItem);
            foreach ($dataResult as &$item) {
                $phoneNumber = substr((string)$item['phone'], 3);
                $pin = $my_data[$phoneNumber] ?? [];
                if ($pin) {
                    $item['first_name'] = $my_data[$phoneNumber]->name ?? null;
                    $item['surname'] = $my_data[$phoneNumber]->surname ?? null;
                    $item['father_name'] = $my_data[$phoneNumber]->father_name ?? null;
                } else {
                    $item['first_name'] = null;
                    $item['surname'] = null;
                    $item['father_name'] = null;
                }
            }
            return ["dataMap" => $dataMap['data'], "dataResult" => $dataResult];
        } catch (RequestException $e) {
            return ['error' => 'Request failed', 'message' => $e->getMessage(), 'code' => $e->getCode()];
        } catch (\Exception $e) {
            return ['error' => 'An error occurred while processing your request.', 'message' => $e->getMessage() . $e->getFile() . $e->getLine()];
        }
    }

    public function getVehicleEntered($params)
    {
        $params['carNumber'] = str_replace([' ', '-'], '', strtoupper($params['carNumber']));

        return Http::withHeaders([
            'Authorization' => 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ1c2VyX3NzcyIsImlkIjoxOSwiZXhwIjoxOTExODgyNDY2LCJpYXQiOjE2NTI2ODI0NjZ9.G8D4wbBLrrB4gldT2QPfEoTUmhuoxurcDEj61izAojEs-7XcrKfWr7ykfs3c-RuB-LUlo4RmnOKmVM9kLriehg',
            'System-Id' => '9ae78b036a90487cbb5b04f1',
            'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
        ])
            ->get(config('servers.vehicle_second_host') . '/api/vehicle-number-recognition?vehicleNumber=' . $params['carNumber'] . '&dateFrom=' . $params['from'] .' 00:00' . '&dateTo=' . $params['to'].' 23:59')
            ->json();
    }

    public function getVehicleLocationImage($id, $carNumber): array
    {
        $carNumber = str_replace([' ', '-'], '', strtoupper($carNumber));

        $getImage = Http::withHeaders([
            'Authorization' => 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ1c2VyX3NzcyIsImlkIjoxOSwiZXhwIjoxOTExODgyNDY2LCJpYXQiOjE2NTI2ODI0NjZ9.G8D4wbBLrrB4gldT2QPfEoTUmhuoxurcDEj61izAojEs-7XcrKfWr7ykfs3c-RuB-LUlo4RmnOKmVM9kLriehg',
            'System-Id' => '9ae78b036a90487cbb5b04f1',
            'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
        ])
            ->get(config('servers.vehicle_second_host') . "/api/vehicle-number-recognition/get-photo?id=" . $id . "&vehicleNumber=" . $carNumber)
            ->json();
        if (isset($getImage['status']) && (in_array($getImage['status'],[404,500]))) {
            return [
                'status' => false,
                'message' => $getImage['message'] ?? "Error occurred while fetching image"
            ];
        }
        return [
            'status' => true,
            'image' => $getImage['imageBase64']
        ];
    }

    public function getAzParking(array $all): string
    {


        return [];


        $currentDateTime = new DateTime();
        $firstOfMonth = new DateTime('first day of this month');

        if (Str::contains($all['from'], ' ') || Str::contains($all['from'], '+')) {
            $dateFrom = Carbon::createFromFormat('Y-m-d H:i:s', $all['from'])->format('Y-m-d\TH:i:s.v\Z');
            $dateTo = Carbon::createFromFormat('Y-m-d H:i:s', $all['to'])->format('Y-m-d\TH:i:s.v\Z');
        } else {
            $dateFrom = Carbon::parse($all['from'])->format('Y-m-d\TH:i:s.v\Z');
            $dateTo = Carbon::parse($all['to'])->format('Y-m-d\TH:i:s.v\Z');
        }

        $response = Http::withHeaders([
            'accept' => '*/*',
            'Content-Type' => 'application/json',
        ])->post('http://10.11.17.1:6099/api/AzparkingCarForDate', [
            'carNumber' => $all['carNumber'],
            'date_from' => $dateFrom ?? $firstOfMonth->format('Y-m-d\TH:i:s.448\Z'),
            'date_to' => $dateTo ?? $currentDateTime->format('Y-m-d\TH:i:s.448\Z'),
        ]);

        return $response->body();
    }

    private function fetchUserData(array $phoneNumbers): array
    {
        $query1 = DB::table('people')
            ->join('pin_phone', 'pin_phone.pin', '=', 'people.pin')
            ->select('pin_phone.pin', 'pin_phone.index', DB::raw('CAST(pin_phone.monitoring AS TEXT) AS contact'),
                'people.name', 'people.surname', 'people.father_name')
            ->whereIn('pin_phone.monitoring', $phoneNumbers);

        $query2 = DB::table('people')
            ->join('phones', 'phones.pin', '=', 'people.pin')
            ->select('phones.pin', 'phones.id', DB::raw('CAST(phones.phone AS TEXT) AS contact'),
                'people.name', 'people.surname', 'people.father_name')
            ->whereIn('phones.phone', $phoneNumbers);

        $combinedQuery = $query2->unionAll($query1);
        $my_data = [];

        $combinedQuery->orderByRaw('1')
            ->chunk(100, function ($pins) use (&$my_data) {
                foreach ($pins as $pin) {
                    if (!isset($my_data[$pin->contact])) {
                        $my_data[$pin->contact] = $pin;
                    }
                }
            });

        return $my_data;
    }
}
