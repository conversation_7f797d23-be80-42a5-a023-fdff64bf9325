<?php

namespace App\Services\Cabinet;

use App\Http\Resources\Cabinet\AlertHistoryCollection;
use App\Http\Resources\Cabinet\AlertHistoryResource;
use App\Models\Cabinet\AlertHistoryLog;
use App\Traits\ApiResponsible;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;

class AlertHistoryService
{

    use ApiResponsible;

    public function getList($per_page, $platform, $status)
    {

        $list = AlertHistoryLog::query()
            ->where('user_id', Auth::id())
            ->where('app', $platform);

        if($status != 2)
        {
            $list = $list->where('status', (int)$status);
        }

        $list->orderBy("created_at","DESC");

        $list = $list->paginate($per_page);

        return new AlertHistoryCollection($list);
    }

    public function countUnreadNotify($app = 'beein_web'): int
    {
        return AlertHistoryLog::query()
            ->where([
                'user_id' => Auth::id(),
                'app' => $app,
                'status' => 0
            ])->count();
    }

    public function readNotify($id): void
    {
        AlertHistoryLog::query()
            ->where('user_id', Auth::id())
            ->where('_id', $id)
            ->update([
                'status' => 1
            ]);
    }

    public function readMultipleNotify($ids): void
    {

        AlertHistoryLog::query()
            ->where('user_id', Auth::id())
            ->whereIn('_id', $ids)
            ->update([
                'status' => 1
            ]);
    }

    public function readAll($status = 1): void
    {
        if($status == 0)
        {
            AlertHistoryLog::query()
                ->where('user_id',  Auth::id())
                ->update([
                    'status' => 0
                ]);
        }else {
            AlertHistoryLog::query()
                ->where('user_id', Auth::id())
                ->where('status', 0)
                ->update([
                    'status' => 1
                ]);
        }
    }

}
