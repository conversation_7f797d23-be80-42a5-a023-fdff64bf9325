<?php

namespace App\Services;

use App\Models\IPAccess;
use App\Models\User;
use Illuminate\Support\Facades\Cache;
use Illuminate\Database\Eloquent\Collection;
class IPAccessService
{
    public function getAllIpAccess(): Collection
    {
        return IPAccess::with(['user' => function($query) {
            $query->select('id', 'name', 'email', 'is_full_access');
        }])->get();
    }
    public function getIpAccessByUser(int $userId): array
    {
        $user = User::select('id', 'name', 'email', 'is_full_access')
            ->findOrFail($userId);

        $ipAccesses = IPAccess::where('user_id', $userId)->get();

        return [
            'user' => $user,
            'ip_addresses' => $ipAccesses,
        ];
    }
    public function create(array $data): IPAccess
    {
        $ipAccess = IPAccess::create($data);
        $this->clearUserCache($data['user_id']);
        return $ipAccess;
    }
    public function update(IPAccess $ipAccess, array $data): IPAccess
    {
        $ipAccess->update($data);
        $this->clearUserCache($ipAccess->user_id);
        return $ipAccess;
    }
    public function delete(IPAccess $ipAccess): bool
    {
        $this->clearUserCache($ipAccess->user_id);
        return $ipAccess->delete();
    }
    private function clearUserCache(int $userId): void
    {
        Cache::forget("ip_access_user_{$userId}");
    }
}
