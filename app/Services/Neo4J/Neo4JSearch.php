<?php

namespace App\Services\Neo4J;

class Neo4JSearch
{
    use Neo4JConnection;

    /**
     * @var string $filterType
     */
    protected string $filterType;

    /**
     * @var string $filterName
     */
    protected string $filterName;

    /**
     * @var string $filterValue
     */
    protected string $filterValue;

    public function run(string $query, array $data)
    {
        return $this->neo4JExecute($query, $data);
    }

    public function setFilterType(string $filterType): void
    {
        $this->filterType = $filterType;
        $this->setFilterName();
        $this->setFilterValue();
    }

    public function setFilterName(): void
    {
        $this->filterName = $this->filterTypeParams()[$this->filterType]['name'];
    }

    public function setFilterValue(): void
    {
        $this->filterValue = $this->filterTypeParams()[$this->filterType]['value'];
    }

    protected function filterTypeParams(): array
    {
        return [
            'pin' => [
                'name' => 'Person',
                'value' => 'person_fin',
            ],
            'phone' => [
                'name' => 'Phone',
                'value' => 'phone_number',
            ],
            'vehicle' => [
                'name' => 'Vehicle',
                'value' => 'vehicle_registration_plate',
            ],
            'company' => [
                'name' => 'Company',
                'value' => 'company_id',
            ],
        ];
    }
}
