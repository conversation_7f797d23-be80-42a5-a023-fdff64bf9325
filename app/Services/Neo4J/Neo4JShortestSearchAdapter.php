<?php

namespace App\Services\Neo4J;


class Neo4JShortestSearchAdapter implements Neo4JAdapterInterface
{
    private Neo4JShortestJSearch $neo4JShortestSearchService;

    /**
     * @param \App\Services\Neo4J\Neo4JShortestJSearch $neo4JShortestSearchService
     */
    public function __construct(Neo4JShortestJSearch $neo4JShortestSearchService)
    {
        $this->neo4JShortestSearchService = $neo4JShortestSearchService;
    }

    /**
     * @param array $params
     * @param array $personParams
     * @return mixed
     */
    public function processQuery(array $params, array $personParams): mixed
    {
        return $this->neo4JShortestSearchService->query($personParams);
    }
}
