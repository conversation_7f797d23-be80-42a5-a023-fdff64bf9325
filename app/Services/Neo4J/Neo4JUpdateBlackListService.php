<?php

namespace App\Services\Neo4J;

class Neo4JUpdateBlackListService
{
    use Neo4JConnection;

    public function execute(string $pin, bool $value)
    {
        $result = $this->neo4JExecute($this->query(),
            [
                'personId' => $pin,
                'blacklistValue' => $value,
            ]
        );

        return $result->toArray();
    }

    private function query(): string
    {
        return 'MATCH (p:Person {person_fin: $personId}) SET p.blacklist = $blacklistValue RETURN p';
    }
}
