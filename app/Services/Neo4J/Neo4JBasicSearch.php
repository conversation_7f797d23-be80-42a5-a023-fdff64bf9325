<?php

namespace App\Services\Neo4J;


class Neo4JBasicSearch extends Neo4JSearch
{
    /**
     * @param string $selector
     * @param int $level
     * @param array $personParams
     * @return mixed
     */
    public function query(string $selector, int $level, array $personParams): mixed
    {
        $this->setFilterType($personParams['filter_type']);
        $degree = $this->queryDegrees($selector)[$level];

        return $this->run(
            'match query = (:' . $this->filterName . ' {' . $this->filterValue . ': $value})' . $degree . ' return query',
            ['value' => $personParams['value']]
        );
    }

    /**
     * @param $selector
     * @return array
     */
    protected function queryDegrees($selector): array
    {
        return [
            0 => null,
            1 => "--($selector)",
            2 => "--($selector)-->(c)",
            3 => "--($selector)-->(c)-->(o)",
            4 => "--($selector)-->(c)-->(o)-->(j)",
        ];
    }
}
