<?php

namespace App\Services\Neo4J;


class Neo4JAllShortestJSearch extends Neo4JSearch
{
    /**
     * @param array $personParams
     * @return mixed
     */
    public function query(array $personParams): mixed
    {
        $this->setFilterType($personParams['filter_type']);
        $filterName = $this->filterName;
        $filterValue = $this->filterValue;

        $this->setFilterType($personParams['second_filter_type']);
        $secondFilterName = $this->filterName;
        $secondFilterValue = $this->filterValue;

        return $this->run(
            'match query = allshortestPaths(
                    (:' . $filterName . ' {' . $filterValue . ': $value})-[*]-(:' . $secondFilterName . ' {' . $secondFilterValue . ': $secondValue})
                ) return * limit 50',
            [
                'value' => $personParams['value'],
                'secondValue' => $personParams['second_value'],
            ]
        );
    }
}
