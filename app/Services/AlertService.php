<?php

namespace App\Services;

use App\Models\Phone;
use App\Models\SocialHunterCalls;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

class AlertService
{

    public function socialCallPins($request)
    {
        // $url = env('APP_URL') . '/api/v1/es-detailed-search';
        $url = $request->url;
        $params = $request->params;
        // print_r($params['social_call_msisdn_1']);die;
        unset($params['social_call_date_from']);
        unset($params['social_call_date_to']);
        unset($params['social_call_msisdn_1']);
        unset($params['social_call_msisdn_2']);
        unset($params['social_call_imei_1']);
        unset($params['social_call_imei_2']);
        unset($params['social_call_imsi_1']);
        unset($params['social_call_imsi_2']);
        unset($params['social_call_operator_1']);
        unset($params['social_call_operator_2']);
        unset($params['social_call_duration']);
        unset($params['social_call_loc_1']);
        unset($params['social_call_loc_2']);


        // print_r($params);die;

        // $token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYXBpLmJlZWluLmxvYy9hcGkvdjEvYXV0aC9sb2dpbiIsImlhdCI6MTY5NjM0NTI2OCwiZXhwIjoxNzY4OTIxMjY4LCJuYmYiOjE2OTYzNDUyNjgsImp0aSI6IkNWMm1qNTRxWXVWdU52VEciLCJzdWIiOiIxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyIsImlzVmVyaWZpZWQiOnRydWV9.YZkK-qw5bhJ02O_SsGftpIU0QFSo105L8m278aOxoTY';
        // $response = Http::withHeaders([
        //     'Authorization' => 'Bearer ' . $token,
        // ])->get($url, $params);
        // $response = $response->object();

        $response =  Route::dispatch(Request::create($url, 'GET', $params ?? []));
        $response = json_decode($response->getContent(),false);


        // print_r($response);die;

        $pins = $response->pins; //is array
        // print_r($pins);die;

        return json_encode($pins, true);
    }

    public function check_social_calls($alert)
    {
        $params = (array) json_decode($alert->params);
        $result_pins =  (array) json_decode($alert->result_pins);

        // print_r($result_pins);die;

        $params['social_call_date_from'] = $alert->call_start;
        $a = false;
        $b = false;
        $social_calls = SocialHunterCalls::select(['id', 'start', 'msisdn1', 'msisdn2']);
        if (isset($params['social_call_date_from'])) {
            $social_calls->where('start', '>', $params['social_call_date_from']);
        }
        if (isset($params['social_call_date_to'])) {
            $social_calls->where('start', '<', $params['social_call_date_to']);
        }
        if (isset($params['social_call_msisdn_1'])) {
            $a = true;
            $social_calls->where('msisdn1', $params['social_call_msisdn_1']);
        }
        if (isset($params['social_call_msisdn_2'])) {
            $b = true;
            $social_calls->where('msisdn2', $params['social_call_msisdn_2']);
        }
        if (isset($params['social_call_imei_1'])) {
            $a = true;
            $social_calls->where('imei1', $params['social_call_imei_1']);
        }
        if (isset($params['social_call_imei_2'])) {
            $b = true;
            $social_calls->where('imei2', $params['social_call_imei_2']);
        }
        if (isset($params['social_call_imsi_1'])) {
            $a = true;
            $social_calls->where('imsi1', $params['social_call_imsi_1']);
        }
        if (isset($params['social_call_imsi_2'])) {
            $b = true;
            $social_calls->where('imsi2', $params['social_call_imsi_2']);
        }

        if (isset($params['social_call_ip_1'])) {
            $a = true;
            $social_calls->where('ip1', $params['social_call_ip_1']);
        }
        if (isset($params['social_call_ip_2'])) {
            $b = true;
            $social_calls->where('ip2', $params['social_call_ip_2']);
        }

        if (isset($params['social_call_operator_1'])) {
            $social_calls->where('operator1', $params['social_call_operator_1']);
        }
        if (isset($params['social_call_operator_2'])) {
            $social_calls->where('operator2', $params['social_call_operator_2']);
        }
        if (isset($params['social_call_duration'])) {
            $social_calls->where('duration', $params['social_call_duration_operation'] ?? '=', $params['social_call_duration']);
        }

        if (isset($params['social_call_loc_1'])) {
            $social_call_loc_1 = $params['social_call_loc_1'];
            $social_calls->whereRaw("dictGet('packet_app.ip_info_dict', 'iso', tuple(ip1)) = '$social_call_loc_1'");
        }

        if (isset($params['social_call_loc_2'])) {
            $social_call_loc_2 = $params['social_call_loc_2'];
            $social_calls->whereRaw("dictGet('packet_app.ip_info_dict', 'iso', tuple(ip2)) = '$social_call_loc_2'");
        }

        // $social_calls->orderBy('start', 'DESC');
        $social_calls = $social_calls->orderBy('start', 'DESC')->take(100)->getRows();


        $filtered_social_calls = [];
        if (count($result_pins) > 0 && count($social_calls) > 0) {
            $phones = Phone::select('phone')->whereIn('pin', $result_pins)->pluck('phone')->toArray();

            if (($a === true && $b === false) || ($a === false && $b === true)) {
                if (count($phones) > 0) {
                    foreach ($social_calls as $item) {
                        if ($a === true && $b === false) {
                            // print_r($phones);die;
                            if (in_array(substr($item['msisdn2'],-9), $phones)) {
                                $filtered_social_calls[] = $item;
                            }
                        } elseif ($a === false && $b === true) {
                            if (in_array(substr($item['msisdn1'],-9), $phones)) {
                                $filtered_social_calls[] = $item;
                            }
                 }
                    }
                } else {
                    $filtered_social_calls = $social_calls;
                }
            } 
        } else {

            $filtered_social_calls = $social_calls;
        }

        // print_r($filtered_social_calls);die;
        // print_r($filtered_social_calls);die;
        return $filtered_social_calls;
    }
}
