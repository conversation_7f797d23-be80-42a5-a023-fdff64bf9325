<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\LLM\DeepSeekClient;
use App\Services\LLM\GemmaClient;
use App\Services\LLM\LLMClient;
use App\Services\RealPhoneUsers\RealPhoneUsersService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\Client\RequestException as LaravelRequestException;
use Exception;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\QueryException;

class PhoneRealUsersController extends Controller
{
    protected $service, $llmClient;

    public function __construct(RealPhoneUsersService  $phone_service, LLMClient $llmClient)
    {
        $this->service = $phone_service;
        $this->llmClient = $llmClient;
    }

    public function get(Request $request)
    {
        $input = $request->search_text;

        $cleanInput = preg_replace("/[^A-Z0-9]/", "", $input);

        $phoneRegex = "/^(?:\+994|994|0)?(50|51|55|99|10|70|77|12|60|61|22|24|25|27|29|32|33|35|36|37|38|40|41|42|43|44|45|46|47|48|49|51|52|53|54|56|57|58|59|61|62|63|64|65|66|67|68|69|70|71|72|73|74|75|76|78|79|80|81|82|83|84|85|86|87|88|89)[0-9]{7}$/";

        $pinRegex = "/^[A-Z0-9]{7}$/";


        if (preg_match($phoneRegex, $cleanInput)) {
            $type = "phone";
        } elseif (preg_match($pinRegex, $cleanInput)) {
            $type = "pin";
        } else {
            return [];
        }

        $query = DB::table('phones_real_users as pru')
            ->leftJoin('people as p', 'p.pin', '=', 'pru.pin');

        if ($type == 'phone') {
            $phone = '994' . substr($cleanInput, -9);
            $query->where('pru.phone', $phone);
        }

        if ($type == 'pin') {
            $pin = strtoupper($cleanInput);
            $query->where('pru.pin', $pin);
        }


        $query->select([
            'pru.pin',
            'pru.phone',
            DB::raw('COALESCE(p.doc_serial_number, null) as doc_serial_number'),
            DB::raw('COALESCE(p.name, pru.name) as name'),
            DB::raw('COALESCE(p.surname, pru.surname) as surname'),
            DB::raw('COALESCE(p.father_name, pru.father_name) as father_name'),
            DB::raw('COALESCE(p.birthdate, null) as birthdate'),
            DB::raw('COALESCE(p.address, null) as address'),
            DB::raw('COALESCE(p.city, null) as city'),
            DB::raw('COALESCE(p.marital_status, null) as marital_status'),
            DB::raw('COALESCE(p.blood_group,null) as blood_group'),
            DB::raw('COALESCE(p.eye_color, null) as eye_color'),
            DB::raw('COALESCE(p.height, null) as height'),
            DB::raw('COALESCE(p.authority_document, null) as authority_document'),
        ]);
        $query->orderBy('pru.weight', 'DESC');
        $query->orderBy('pru.id', 'DESC');
        $phone_user = $query->first();

        // if (!$phone_user) {
        //     return response()->json([], 404);
        // }


        $data = collect($phone_user)->toArray();

        $data['marital_status'] = $phone_user->marital_status === true ? 'Evli' : 'Subay';
        $data['new_image'] = personImagePath($phone_user->doc_serial_number, $phone_user->pin);
        return response()->json($data, 200);
    }

    public function azeriqaz()
    {
        // $last_id = DB::table('phones_real_users')
        // ->where('source','azeriqaz')
        // ->orderBy('beein_contact_id', 'ASC')
        // ->first()->beein_contact_id ?? 0;

        // $data = DB::table('beein_contact_2')
        //     // ->where('id','4575348')
        //     ->where('source', 'azeriqaz')
        //     ->where('id','>', $last_id)
        //     ->orderBy('id', 'ASC')
        //     ->take(500)
        //     ->get();

        $data = collect([
            (object) [
                'name' => 'Həmid',
                'surname' => 'Həmidli',
                'father_name' => 'Səmid'
            ]
        ]);

        if (count($data) == 0) {
            return 'empty';
        }

        try {
            foreach ($data as $item) {
                $f_exp = explode(' ', $item->father_name);
                $search_keys[] = [
                    'pin' => Str::slug($item->pin ?? ''),
                    'name'  => Str::slug($item->name),
                    'surname' => Str::slug($item->surname),
                    'father_name' => Str::slug($f_exp[0]),
                ];
            }
            $pins = $this->service->esFindPin($search_keys);

            print_r($pins);
            die;
            $this->service->updateStatus('azeriqaz', $useless_ids, 3, 'useless number');
            $this->service->updateStatus('azeriqaz', $very_result_ids, 5, 'very result');

            $result = $this->service->bulkInsert($data_phone_users);

            $this->service->updateStatus('azeriqaz', $result['success_ids'], 2);
            $this->service->updateStatus('azeriqaz', $result['fail_ids'], 0);

            DB::commit();
            return [
                $result,
                'useless_ids' => $useless_ids,
                'very_result_ids' => $very_result_ids,
            ];
        } catch (RequestException | LaravelRequestException | Exception $e) {
            DB::rollBack();

            return $e->getMessage();
        }
    }
    public function findWithPin()
    {
        $phones = DB::table('beein_contact_2 as b')
            ->select(
                'b.id',
                'b.phone',
                'b.pin',
                DB::raw('COALESCE(p.name, b.name) as name'),
                DB::raw('COALESCE(p.surname, b.surname) as surname'),
                DB::raw('COALESCE(p.father_name, b.father_name) as father_name'),
                'b.source',
                'b.source_id',
                'b.weight'
            )
            ->leftJoin('people as p', 'b.pin', '=', 'p.pin')
            ->where('b.phone_check', 0)
            ->whereNotNull('b.pin')
            ->where('b.pin', '!=', '')
            ->where('b.pin', '!=', '')
            ->whereRaw('LENGTH(b.pin) = 7')
            ->orderBy('b.id', 'ASC')
            ->limit(3000)
            ->get();

        if (count($phones) == 0) {
            return 'empty';
        }
        $data = [];

        foreach ($phones as $item) {
            $slugged = Str::slug($item->id . $item->phone . $item->pin . $item->name . $item->surname . $item->father_name);
            $hash = md5($slugged);
            $data[] = [
                'beein_contact_id' => $item->id,
                'phone' => $item->phone,
                'pin' => $item->pin,
                'name' => $item->name,
                'surname' => $item->surname,
                'father_name' => $item->father_name,
                'source' => $item->source,
                'source_id' => $item->source_id,
                'weight' => $item->weight,
                'hash' => $hash
            ];
        }

        DB::beginTransaction();
        try {
            if (!empty($data)) {
                DB::table('phones_real_users')->insertOrIgnore($data);
                $ids = collect($phones)->pluck('id')->toArray();
                DB::table('beein_contact_2')->whereIn('id', $ids)->update(['phone_check' => 1]);
            }

            DB::commit();
            return 'successful';
        } catch (QueryException $e) {
            DB::rollBack();
            return 'Database query error: ' . $e->getMessage();
        } catch (Exception $e) {
            DB::rollBack();
            return 'Unexpected error: ' . $e->getMessage();
        }
    }


    public function findWithNameSurnameFathername()
    {
        $prompt = <<<EOT
        You are the API that process the infotmation and return the structured JSON.
        I will send you an array containing the following fields (keys): name, surname, father_name, birthday, city, address, and weight.  
        The "name" and "surname" fields can be written in both Cyrillic and Latin alphabets, or the full name can appear as a single line under the "name" key only.  
        In some data, the "name" field may contain both first name and surname. Investigate how surnames are typically written in Azerbaijan and separate accordingly.  
        If Azerbaijani names are written in English format in some data, convert them back to their Azerbaijani form.  
        For example: "Shahdag" (city name, using "sh" instead of "ş" is incorrect), "Baki" (correct spelling is "Bakı"), "Chinar" (using "Ch" instead of "Ç" is incorrect),  
        "Ali" (correct spelling is "Əli"), "Ismayilli" (correct spelling is "İsmayıllı"), "Zafar" (correct spelling is "Zəfər"), "Mammadov" (correct spelling is "Məmmədov"),  
        "Kerim" (correct spelling is "Kərim"), "Guba" (correct spelling is "Quba"), "Sheki" (correct spelling is "Şəki").
        
        Your task:
        
        1. Detect if there are any Cyrillic characters in the input.
        2. If there are, convert them to the Latin alphabet.
        3. Separate the full name into first name and surname. If the "name" field consists of two or more words, research how surnames are typically written in Azerbaijan and split accordingly.
        4. If the "birthday" field is in `yyyy.mm.dd` format, convert it to `dd.mm.yyyy` format.
        5. If Azerbaijani names are written in English format, convert them to Azerbaijani form. For example: Xalid(AZ)=>Khalid(EN), Şəhla(AZ)=>Shahla, Çinar(AZ)=>Chinar, Məmmədov(AZ)=>Mammadov(EN), Əli(AZ)=>Ali(EN)
        6. In the address, identify the city name by researching Azerbaijani city names and determining it accordingly.
        7. From a collection of arrays, generate a final array based on the most reliable data determined by the "weight" value. Arrays with higher weight are considered more reliable. For entries with the same content, group them together and select the version that appears in the array with the highest weight.
        Return the result in the following JSON format:
        {
            'name': 'Modified_Name',  
            'surname': 'Modified_Surname',  
            'father_name': 'Modified_Father_Name',  
            'birthday': 'Modified_Birthday',  
            'city': 'Modified_City_Name'
        }
        
        Example 1 (Entered in Latin alphabet with a mixed name):
        Input:
        [
            "name" => 'Khalid Məmmədov',
            "surname" => '',
            "father_name" => 'Fazil',
            "birthday" => '1999.12.08',
            "city" => '',
            "address" => 'Bakı, Rüstəm Rüstəmov 131 Mənzil 10',
        ]
        
        Output:
        {
            'name': 'Xalid',
            'surname': 'Məmmədov',
            'father_name': 'Fazil',
            'birthday': '08.12.1999',
            'city': 'Bakı'
        }
        
        Example 2 (Entered in Cyrillic alphabet):
        Input:
        'Иван Петров'
        
        Input:
        [
            "name" => 'Иван Петров',
            "surname" => '',
            "father_name" => '',
            "birthday" => '',
            "city" => 'Sumqayıt',
            "address" => '',
        ]
        
        Output:
        {'name': 'Ivan', 'surname': 'Petrov'}
        
        Output:
        {
            'name': 'Ivan',
            'surname': 'Petrov',
            'father_name': '',
            'birthday': '',
            'city': 'Sumqayıt'
        }
        
        If the input is not in the correct format (e.g., the name is missing or cannot be separated), return the following error:
        {'error': 'Name format is incorrect'}
        EOT;
        

        $query = "[
            'name' => 'Khalilova Shahla Mammad qizi',
            'surname' => '',
            'father_name' => '',
            'birthday' => '1985.07.22',
            'city' => '',
            'address' => 'Nizami 45A Şərur Xətai 123 Haci',
        ]";

        // $responseText = $this->deepseekClient->chat($prompt, $query);
        $responseText = $this->llmClient->chat($prompt, $query);


        $cleaned = preg_replace('/```json|```/', '', $responseText);
        $cleaned = trim($cleaned);

        $data = json_decode(str_replace("'", '"', $cleaned), true);


        return response()->json([
            'response' => $data,
        ]);

        print_r('okk');
        die;
        $phones = DB::table('beein_contact_2 as b')
            ->select('name', 'surname', 'fathername')
            ->leftJoin('people as p', 'b.pin', '=', 'p.pin')
            ->where('b.phone_check', 0)
            ->whereNotNull('b.pin')
            ->where('b.pin', '!=', '')
            ->where('b.pin', '!=', '')
            ->whereRaw('LENGTH(b.pin) = 7')
            ->orderBy('b.id', 'ASC')
            ->limit(3000)
            ->get();

        if (count($phones) == 0) {
            return 'empty';
        }
        $data = [];

        foreach ($phones as $item) {
            $slugged = Str::slug($item->id . $item->phone . $item->pin . $item->name . $item->surname . $item->father_name);
            $hash = md5($slugged);
            $data[] = [
                'beein_contact_id' => $item->id,
                'phone' => $item->phone,
                'pin' => $item->pin,
                'name' => $item->name,
                'surname' => $item->surname,
                'father_name' => $item->father_name,
                'source' => $item->source,
                'source_id' => $item->source_id,
                'weight' => $item->weight,
                'hash' => $hash
            ];
        }

        DB::beginTransaction();
        try {
            if (!empty($data)) {
                DB::table('phones_real_users')->insertOrIgnore($data);
                $ids = collect($phones)->pluck('id')->toArray();
                DB::table('beein_contact_2')->whereIn('id', $ids)->update(['phone_check' => 1]);
            }

            DB::commit();
            return 'successful';
        } catch (QueryException $e) {
            DB::rollBack();
            return 'Database query error: ' . $e->getMessage();
        } catch (Exception $e) {
            DB::rollBack();
            return 'Unexpected error: ' . $e->getMessage();
        }
    }















    //     public function findPhonePin()
    //     {
    //         $query = "SELECT
    //        subquery.id,
    //        subquery.phone,
    //    subquery.pin,
    //    subquery.name,
    //    subquery.surname,
    //    subquery.father_name
    // from (
    // select
    //     MIN(b.id) AS id,
    //    b.phone,
    //    b.pin,
    //    b.name,
    //    b.surname,
    //    MIN(p.father_name) AS father_name

    // from
    //     beein_contact_2 as b
    // inner join people as p
    // on b.pin = p.pin
    // and b.name = p.name
    // and b.surname = p.surname
    // WHERE b.pin IS NOT NULL
    //   AND b.name IS NOT NULL
    //   AND b.surname IS NOT NULL
    // GROUP BY b.phone, b.pin, b.name, b.surname
    // ) as subquery
    //    WHERE NOT EXISTS (
    //                 SELECT 1
    //                 FROM phones_real_users AS pr
    //                 WHERE pr.beein_contact_id = subquery.id
    //             )ORDER BY subquery.id ASC
    // LIMIT 1000;
    //         ";


    //         $data = DB::select($query);

    //         $insert_data = array_map(function ($item) {

    //             return $this->mapping($item);
    //         }, $data);

    //         //return $insert_data;

    //         DB::beginTransaction();
    //         try {
    //             DB::table('phones_real_users')->insertOrIgnore($insert_data);
    //             DB::commit();
    //             return 'successful';
    //         } catch (QueryException $e) {
    //             DB::rollBack();
    //             return 'Database query error: ' . $e->getMessage();
    //         } catch (Exception $e) {
    //             DB::rollBack();
    //             return 'Unexpected error: ' . $e->getMessage();
    //         }
    //     }

    public function mapping($item)
    {
        $data_str = ($item->phone ?? '') . ($item->pin ?? '') . ($item->name ?? '') . ($item->surname ?? '') . ($item->father_name ?? '');
        $slugged = Str::slug($data_str);
        $hash = md5($slugged);

        return [
            "beein_contact_id" => $item->id,
            "phone" => $item->phone,
            "pin" => $item->pin,
            "name" => $item->name,
            "surname" => $item->surname,
            "father_name" => $item->father_name,
            "hash" => $hash,

        ];
    }

    public function findPin(Request $request)
    {
        $data = $this->getData($request);

        foreach ($data as $records) {
            $ddd[] = $this->prosessingRecords($records);
            // $pins = $this->findPinWithPhoneAndName($record);
        }
        return $ddd;
    }
    protected function findPinWithPhoneAndName($record)
    {
        $phone = $records[0]['phone'];
        $name = $records[0]['name'];
        $search_keys = [
            'phone' => $phone,
            'name' => $name,
        ];
        $pins = $this->service->esSearch($search_keys);

        return $pins;
    }

    protected function prosessingRecords($records)
    {
        $results = [];
        $total_weight = 0;
        foreach ($records as $item) {
            $name = $item['name'];
            $total_weight = $total_weight + $item['weight'];
            if (isset($results[$name])) {
                $results[$name]['weight'] += $item['weight'];
            } else {
                $results[$name] = [
                    "phone" => $item["phone"],
                    "pin" => $item["pin"],
                    "name" => $item["name"],
                    "surname" => $item["surname"],
                    "father_name" => $item["father_name"],
                    "birthday" => $item["birthday"],
                    "weight" => $item["weight"],
                ];
            }
        }

        $results = array_values($results);

        usort($results, function ($a, $b) {
            return $b['weight'] <=> $a['weight'];
        });

        $percentage = 0;
        $results = [];
        $total_weight = 0;
        foreach ($records as $item) {
            $name = $item['name'];
            $total_weight = $total_weight + $item['weight'];
            if (isset($results[$name])) {
                $results[$name]['weight'] += $item['weight'];
            } else {
                $results[$name] = [
                    "phone" => $item["phone"],
                    "pin" => $item["pin"],
                    "name" => $item["name"],
                    "surname" => $item["surname"],
                    "father_name" => $item["father_name"],
                    "birthday" => $item["birthday"],
                    "weight" => $item["weight"],
                ];
            }
        }

        $results = array_values($results);

        usort($results, function ($a, $b) {
            return $b['weight'] <=> $a['weight'];
        });

        $percentage = 0;
        if (count($results) > 0) {
            $percentage = round(($results[0]['weight'] / $total_weight) * 100);
            $results[0]['accuracy_percentage'] = $percentage;


            // print_r($records);
            // print_r($results);
            // die;
            return $results[0];
        } else {
            return [];
        }
    }

    protected function getData($phone)
    {

        $data = DB::table('beein_contact_2')
            ->select(
                'phone',
                DB::raw("json_agg(row(pin, name, surname, father_name, birthday, city, address, weight, source) ORDER BY weight DESC) AS items")
            )
            ->groupBy('phone')
            ->where('phone', $phone)
            // ->take(10)
            ->get();

        $records = [];
        foreach ($data as $key => $d) {
            $items =  json_decode($d->items);
            foreach ($items as $item) {
                $name = Str::slug($item->f2);
                $name_arr = explode('-', $name);

                $surname = '';
                if (count($name_arr) > 1) {
                    $soyad_regex = '/\b[A-ZƏÜİÖĞÇŞa-zəüöğçış]+(?:ov|ova|yev|yeva|lı|li|zade)\b/';

                    preg_match_all($soyad_regex, $name, $matches);
                    $surname = !empty($matches[0] && strlen($matches[0][0]) >= 5) ? $matches[0][0] : '';

                    $name = str_replace($surname, '', $name);
                    $name_exp = explode(' ', $name);
                    $name = $name_exp[0];
                }

                $surname = !empty($item->f3) ? Str::slug($item->f3) : $surname;


                $records[$key][] = [
                    'phone' => $d->phone,
                    'pin' => Str::slug($item->f1),
                    'name' => $name,
                    'surname' => $surname,
                    'father_name' => Str::slug($item->f4),
                    'birthday' => Str::slug($item->f5),
                    'city' => Str::slug($item->f6),
                    'address' => Str::slug($item->f7),
                    'weight' => Str::slug($item->f8),
                    'source' => Str::slug($item->f9),
                ];
            }
        }



        return $records;
    }
    public function searchPinWithNameAndPhone()
    {
        print_r('0');
        $data = DB::table('beein_contact_2')
            // ->select('phone', DB::raw('ARRAY_AGG(name) AS names'))

            ->where('source', '!=', 'azerisiq_baki')
            ->where('source', '!=', 'azerisiq_region')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('real_phone_users_analyz_status')
                    ->whereRaw('real_phone_users_analyz_status.fk_id = beein_contact_2.id');
                // ->where('real_phone_users_analyz_status.status', '!=', 0);
            })
            // ->groupBy('phone')
            ->orderBy('id', 'ASC')
            ->take(10)
            ->get();

        if (!empty($data)) {
            try {


                print_r('2');
                DB::beginTransaction();
                $this->service->insertStatus($data, 'searchPinWithNameAndPhone', ['name', 'phone']);

                print_r('3');

                $data_phone_users = [];
                $very_result_ids = [];
                $useless_ids = [];
                foreach ($data as $item) {
                    $search_keys = [
                        'phone' => Str::slug($item->phone),
                        'name' => Str::slug($item->name),
                    ];

                    $pins = $this->service->getPinWithNameAndPhone($item);



                    if (count($pins) == 1) {

                        $map_data = [
                            'phone'      => $item->phone,
                            'pin'        => $pins[0],
                            'accuracy_percentage' => 90,
                            'beein_contact_id'  => $item->id,
                            'source'     => $item->source,
                            'function_name' => 'searchPinWithNameAndPhone',
                            'search_keys' => json_encode($search_keys)
                        ];
                        $map = $this->service->mapping($map_data);
                        $data_phone_users[] = $map;
                    } elseif (count($pins) > 1) {
                        $very_result_ids[] = $item->id;
                    } else {
                        $useless_ids[] = $item->id;
                    }
                }

                print_r('4');
                $this->service->updateStatus($useless_ids, 3, 'useless number');

                print_r('5');
                $this->service->updateStatus($very_result_ids, 5, 'very result');

                print_r('6');
                $result = $this->service->bulkInsert($data_phone_users);

                print_r('7');
                $this->service->updateStatus($result['success_ids'], 2);

                print_r('8');
                $this->service->updateStatus($result['fail_ids'], 0);

                print_r('9');
                DB::commit();
                return [
                    $result,
                    'useless_ids' => $useless_ids,
                    'very_result_ids' => $very_result_ids,
                ];
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty searchPinWithNameAndPhone';
        }
    }

    public function azparking()
    {
        die;
        $data = DB::table('beein_contact_2')
            ->select('phone', DB::raw('ARRAY_AGG(name) AS names'))
            ->groupBy('phone')
            ->get();
        $array = [
            ['hemid', 'sdsd', 'sadsf', 'sfsafsaf'],
            ['semin', 'sdsd', 'sadsf', 'sfsafsaf'],
            ['dimeh', 'sdsd', 'sadsf', 'sfsafsaf'],
            ['hemid', 'sdsd', 'sadsf', 'sfsafsaf'],
            ['hemid', 'sdsd', 'sadsf', 'sfsafsaf'],
        ];

        // Ad kolunu əldə edirik
        $adColumn = array_column($array, 0);

        // Adların sayını hesablayırıq
        $counts = array_count_values($adColumn);

        // Ən çox təkrarlanan ad
        $maxAd = array_keys($counts, max($counts));

        print_r($maxAd);
        die;
        $data = DB::table('beein_contact_2')
            ->where('source', 'azparking')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('real_phone_users_analyz_status')
                    ->where('real_phone_users_analyz_status.type', 'azparking')
                    ->whereRaw('real_phone_users_analyz_status.fk_id = beein_contact_2.id')
                    ->where('real_phone_users_analyz_status.status', '!=', 0);
            })
            ->orderBy('beein_contact_2.id', 'DESC')
            ->take(500)
            ->get();





        if (!empty($data)) {
            try {

                DB::beginTransaction();
                $this->service->insertStatus($data, 'azparking', ['pin', 'phone', 'name', 'surname', 'birthday']);


                $data_phone_users = [];
                $very_result_ids = [];
                $useless_ids = [];
                foreach ($data as $item) {

                    $search_keys['phone'] = $item->phone;
                    $search_keys['pin'] = $item->pin;
                    $search_keys['name'] = $item->name;
                    $search_keys['surname'] = $item->surname;
                    $search_keys['birth_date'] = $item->birthday;
                    $pins = $this->service->findPinAzparking($item);

                    if (count($pins) == 1) {

                        $map_data = [
                            'phone'      => $item->phone,
                            'pin'        => $pins[0],
                            'source_id'  => $item->id,
                            'source'     => 'azparking',
                            'search_keys' => json_encode($search_keys)
                        ];
                        $map = $this->service->mapping($map_data);
                        $data_phone_users[] = $map;
                    } elseif (count($pins) > 1) {
                        $very_result_ids[] = $item->id;
                    } else {
                        $useless_ids[] = $item->id;
                    }
                }

                $this->service->updateStatus('azparking', $useless_ids, 3, 'useless number');
                $this->service->updateStatus('azparking', $very_result_ids, 5, 'very result');

                $result = $this->service->bulkInsert($data_phone_users);

                $this->service->updateStatus('azparking', $result['success_ids'], 2);
                $this->service->updateStatus('azparking', $result['fail_ids'], 0);

                DB::commit();
                return [
                    $result,
                    'useless_ids' => $useless_ids,
                    'very_result_ids' => $very_result_ids,
                ];
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty azparking';
        }
    }



    public function avtovagzal()
    {

        $data = DB::table('beein_contact_2')
            ->where('source', 'avtovagzal')
            ->whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('real_phone_users_analyz_status')
                    ->where('real_phone_users_analyz_status.type', 'avtovagzal')
                    ->whereRaw('real_phone_users_analyz_status.fk_id = beein_contact_2.id')
                    ->where('real_phone_users_analyz_status.status', '!=', 0);
            })
            ->orderBy('beein_contact_2.id', 'DESC')
            ->take(2000)
            ->get();





        if (!empty($data)) {
            try {

                DB::beginTransaction();
                $this->service->insertStatus($data, 'avtovagzal', ['name', 'surname', 'father_name', 'pin', 'city']);


                $data_phone_users = [];
                $very_result_ids = [];
                $useless_ids = [];
                foreach ($data as $item) {
                    $search_keys = [
                        'name' => Str::slug($item->name),
                        'surname' => Str::slug($item->surname),
                        'father_name' => $item->father_name,
                        'pin' => $item->pin,
                        'city' => $item->city,
                    ];

                    $pins = $this->service->findPinAzeriqaz($item);



                    if (count($pins) == 1) {

                        $map_data = [
                            'phone'      => $item->phone,
                            'pin'        => $pins[0],
                            'source_id'  => $item->id,
                            'source'     => 'azeriqaz',
                            'search_keys' => json_encode($search_keys)
                        ];
                        $map = $this->service->mapping($map_data);
                        $data_phone_users[] = $map;
                    } elseif (count($pins) > 1) {
                        $very_result_ids[] = $item->id;
                    } else {
                        $useless_ids[] = $item->id;
                    }
                }

                $this->service->updateStatus('azeriqaz', $useless_ids, 3, 'useless number');
                $this->service->updateStatus('azeriqaz', $very_result_ids, 5, 'very result');

                $result = $this->service->bulkInsert($data_phone_users);

                $this->service->updateStatus('azeriqaz', $result['success_ids'], 2);
                $this->service->updateStatus('azeriqaz', $result['fail_ids'], 0);

                DB::commit();
                return [
                    $result,
                    'useless_ids' => $useless_ids,
                    'very_result_ids' => $very_result_ids,
                ];
            } catch (RequestException | LaravelRequestException | Exception $e) {
                DB::rollBack();

                return $e->getMessage();
            }
        } else {
            return 'empty azeriqaz';
        }
    }



    public function statisticInfo()
    {

        return [

            'real_phone_users' => [
                'db_count' => DB::table('real_phone_users')->count(),
                'unique_phone' => DB::table('real_phone_users')->distinct('phone')->count(),
                'unique_pin' => DB::table('real_phone_users')->distinct('pin')->count(),

                'daily_count' => DB::table('real_phone_users')
                    ->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])
                    ->count(),


            ],
            'searchPinWithNameAndPhone' => [
                'db_count' => DB::table('beein_contact_2')->where('function_name', 'searchPinWithNameAndPhone')->count(),

                'prosess_count' => DB::table('real_phone_users_analyz_status')
                    ->where('function_name', 'searchPinWithNameAndPhone')
                    ->count(),



                'daily_prosess_count' => DB::table('real_phone_users_analyz_status')
                    ->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])
                    ->where('function_name', 'searchPinWithNameAndPhone')
                    ->count(),

                'status_0_fails' => DB::table('real_phone_users_analyz_status')
                    ->where('function_name', 'searchPinWithNameAndPhone')
                    ->where('status', 0)
                    ->count(),

                'status_1_progress' => DB::table('real_phone_users_analyz_status')
                    ->where('function_name', 'searchPinWithNameAndPhone')
                    ->where('status', 1)
                    ->count(),

                'status_2_success' => DB::table('real_phone_users_analyz_status')
                    ->where('function_name', 'searchPinWithNameAndPhone')
                    ->where('status', 2)
                    ->count(),
                'status_3_not_found' => DB::table('real_phone_users_analyz_status')
                    ->where('function_name', 'searchPinWithNameAndPhone')
                    ->where('status', 3)
                    ->count(),
                'status_5_very_result' => DB::table('real_phone_users_analyz_status')
                    ->where('function_name', 'searchPinWithNameAndPhone')
                    ->where('status', 5)
                    ->count(),

                'data_count_r' => DB::table('real_phone_users')->where('function_name', 'searchPinWithNameAndPhone')->count(),
                'unique_phone' => DB::table('real_phone_users')->where('function_name', 'searchPinWithNameAndPhone')->distinct('phone')->count(),
                'unique_pin' => DB::table('real_phone_users')->where('function_name', 'searchPinWithNameAndPhone')->distinct('pin')->count(),


            ],
            // 'azeriqaz' => [
            //     'db_count' => DB::table('beein_contact_2')->where('source', 'azeriqaz')->count(),

            //     'prosess_count' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azeriqaz')
            //         ->count(),



            //     'daily_prosess_count' => DB::table('real_phone_users_analyz_status')
            //         ->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])
            //         ->where('type', 'azeriqaz')
            //         ->count(),

            //     'status_0_fails' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azeriqaz')
            //         ->where('status', 0)
            //         ->count(),

            //     'status_1_progress' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azeriqaz')
            //         ->where('status', 1)
            //         ->count(),

            //     'status_2_success' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azeriqaz')
            //         ->where('status', 2)
            //         ->count(),
            //     'status_3_not_found' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azeriqaz')
            //         ->where('status', 3)
            //         ->count(),
            //     'status_5_very_result' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azeriqaz')
            //         ->where('status', 5)
            //         ->count(),

            //     'data_count_r' => DB::table('real_phone_users')->where('source', 'azeriqaz')->count(),
            //     'unique_phone' => DB::table('real_phone_users')->where('source', 'azeriqaz')->distinct('phone')->count(),
            //     'unique_pin' => DB::table('real_phone_users')->where('source', 'azeriqaz')->distinct('pin')->count(),


            // ],
            // 'azparking' => [
            //     'db_count' => DB::table('beein_contact_2')->where('source', 'azparking')->count(),

            //     'prosess_count' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azparking')
            //         ->count(),

            //     'daily_prosess_count' => DB::table('real_phone_users_analyz_status')
            //         ->whereBetween('created_at', [Carbon::today()->startOfDay(), Carbon::today()->endOfDay()])
            //         ->where('type', 'azparking')
            //         ->count(),

            //     'status_0_fails' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azparking')
            //         ->where('status', 0)
            //         ->count(),

            //     'status_1_progress' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azparking')
            //         ->where('status', 1)
            //         ->count(),

            //     'status_2_success' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azparking')
            //         ->where('status', 2)
            //         ->count(),
            //     'status_3_not_found' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azparking')
            //         ->where('status', 3)
            //         ->count(),
            //     'status_5_very_result' => DB::table('real_phone_users_analyz_status')
            //         ->where('type', 'azparking')
            //         ->where('status', 5)
            //         ->count(),

            //     'data_count_r' => DB::table('real_phone_users')->where('source', 'azparking')->count(),
            //     'unique_phone' => DB::table('real_phone_users')->where('source', 'azparking')->distinct('phone')->count(),
            //     'unique_pin' => DB::table('real_phone_users')->where('source', 'azparking')->distinct('pin')->count(),


            // ],


        ];
    }
}

