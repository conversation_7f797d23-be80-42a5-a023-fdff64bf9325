<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Http\JsonResponse;
use Spatie\Permission\Models\Role;
use App\Repository\UserRepository;
use App\Http\Resources\UserResource;
use Illuminate\Database\Eloquent\Model;

class UserService extends AbstractService
{
    /**
     * @param \App\Repository\UserRepository $userRepository
     */
    public function __construct(
        protected UserRepository $userRepository
    ) {
        parent::__construct($userRepository);
    }

    /**
     * @param array $params
     * @param int $limit
     * @return JsonResponse
     */
    public function paginate(array $params, int $limit): JsonResponse
    {
        $users = $this->userRepository
            ->setParams([
                'column' => $params['type'] ?? 'name',
                'q' => $params['search'] ?? null,
            ])
            ->paginate($limit);

        return UserResource::collection($users)->response();
    }

    /**
     * @param Model $user
     * @param array $paramRoles
     */
    public function assignRoles(Model $user, array $paramRoles): void
    {
        $roles = Role::query()
            ->whereIn('id', $paramRoles['roles'] ?? [])
            ->get();

        if ($roles->isNotEmpty()) {
            $user->syncRoles($roles->pluck('id'));
        }
    }

    /**
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        /**
         * @var $user User
         */
        $user = $this->userRepository->find($id);
        $user->setRelation('roles', $user->roles);

        return UserResource::make($user)->response();
    }
}
