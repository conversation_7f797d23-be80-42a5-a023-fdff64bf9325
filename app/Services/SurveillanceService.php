<?php

namespace App\Services;

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;

class SurveillanceService extends MilvusService
{
    /**
     * @var string $api_uri
     */
    protected string $api_uri = 'surveillance/search';

    /**
     * @throws \JsonException
     */
    public function search(array $params, array $cameraIds,$isNew=false)
    {
        $params['camera_ids'] = $cameraIds;
        $params['date_from'] = $params['date_from'] ?? date('Y-m-d 00:00:00', strtotime("-1 days"));
        $params['date_to'] = $params['date_to'] ?? date('Y-m-d 23:59:00');

//        return response()->json(
//            [
//                'params' => $params,
//                'isNew' => $isNew,
//            ]
//        );

       if (!$isNew){
           return $this->sendRequest($params);
       }else{
           return $this->sendRequestV2($params);
       }
    }

    /**
     * @param array $collection
     * @param $cameras
     * @param array $uuids
     * @return array
     */
    public function filterElasticListCollection(array $collection, $cameras, array $uuids): array
    {
        array_walk($collection, static function (&$value) use ($cameras, $uuids) {
            $value['camera'] = $cameras[$value['_source']['camera_id']][0] ?? null;
            $value['distance'] = round($uuids[$value['_source']['vector_id']] ?? 0, 3);
        });

        return $collection;
    }

    /**
     * @param array $collection
     * @param $cameras
     * @return array
     */
    public function filterElasticShowBySessionCollection(array $collection, $cameras): array
    {
        array_walk($collection, static function (&$value) use ($cameras) {
            $value['camera'] = $cameras[$value['_source']['camera_id']][0] ?? null;
            $value['distance'] = null;
        });

        return $collection;
    }

    /**
     * @return string
     */
    public static function deepFacePhotoEndpoint(): string
    {
        return config('servers.s3_beein').'/api/deep-face/';
    }

    /**
     * @return string
     */
    private static function originFacePhotoEndpoint(): string
    {
        return config('servers.s3_beein').'/api/frames/';
    }

    /**
     * @param string $photo
     * @return string
     */
    public static function filterOriginPhoto(string $photo): string
    {
        return self::originFacePhotoEndpoint() .
            Str::beforeLast($photo, '_') . '.' .
            Str::afterLast($photo, '.');
    }

    /**
     * @param array $params
     * @return array
     */
    protected function collectParamsForMilvus(array $params): array
    {
        return [
            'similarity' => $params['similarity'],
            'max_count' => $params['max_count'],
            'photo' => $params['photo'],
            'from_time' => strtotime($params['date_from']),
            'till_time' => strtotime($params['date_to']),
            'camera_ids' =>  $params['camera_ids']
        ];
    }

    /**
     * @throws \JsonException
     */
    public function find($data)
    {
        $response = Http::timeout(10)->put(  $this->endpoint . 'surveillance/search', $data);

        return json_decode($response->body(), true, 512, JSON_THROW_ON_ERROR);
    }
}
