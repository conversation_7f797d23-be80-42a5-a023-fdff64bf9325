<?php

namespace App\Services;

use Exception;
use PhpAmqpLib\Channel\AMQPChannel;
use PhpAmqpLib\Message\AMQPMessage;
use PhpAmqpLib\Exception\AMQPTimeoutException;
use PhpAmqpLib\Connection\AMQPStreamConnection;

class QueueService
{

    /**
     * @var string $queueName
     */
    public string $queueName;

    /**
     * @var \PhpAmqpLib\Channel\AMQPChannel $channel
     */
    protected AMQPChannel $channel;

    /**
     * @var \PhpAmqpLib\Connection\AMQPStreamConnection $connection
     */
    protected AMQPStreamConnection $connection;

    /**
     * @throws \Exception
     */
    public function __construct()
    {
        $this->connection = new AMQPStreamConnection(
            env("ENIGMA_RABBITMQ_HOST", '************'),
            env("ENIGMA_RABBITMQ_PORT", 15672),
            env("ENIGMA_RABBITMQ_USER", 'api'),
            env("ENIGMA_RABBITMQ_PASSWORD", 'd322f4Ddg65dSw3fTsxF42f'),
            env("ENIGMA_RABBITMQ_VHOST", '/')
        );
        $this->channel = $this->connection->channel();
    }

    /**
     * @param string $queueName
     * @return \App\Services\QueueService
     */
    public function declareQueue(string $queueName): QueueService
    {
        $this->queueName = $queueName;

        $this->channel->queue_declare(
            $this->queueName,
            false,
            false,
            false,
            false
        );

        return $this;
    }

    /**
     * @param array $data
     * @throws \JsonException
     * @return \App\Services\QueueService
     */
    public function addMessage(array $data): QueueService
    {
        $message = new AMQPMessage(json_encode($data, JSON_THROW_ON_ERROR));

        $this->channel->basic_publish($message, '', $this->queueName);

        return $this;
    }

    /**
     * @param string|null $queueName
     */
    public function basicConsume(?string $queueName = null): void
    {
        $callback = static function ($msg) {
            // TestJob
            // $msg->body;
        };

        $this->channel->basic_consume(
            $queueName ?? $this->queueName,
            '',
            false,
            true,
            false,
            false,
            $callback
        );

        while ($this->channel->is_open()) {
            $this->channel->wait();
        }
    }

    /**
     * @return \App\Services\QueueService
     */
    public function closeChannel(): QueueService {

        $this->channel->close();

        return $this;
    }

    /**
     * @return \App\Services\QueueService
     * @throws \Exception
     */
    public function closeConnection(): QueueService {

        $this->connection->close();

        return $this;
    }
}
