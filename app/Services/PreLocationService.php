<?php

namespace App\Services;

use App\DTO\MergedDataDTO;
use App\Models\SearchRequests;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\Pool;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
class PreLocationService
{
    private string|null $rustBaseURL;
    private mixed $apiKey;
    private mixed $userID;
    private mixed $authorizationHeader;
    private Client $client;
    const FIND_LOCATION = 'PHONE_SIMILARITY_SEARCH@FIND_INTERSECTED_LOCATION_BY_PHONE';
    const FIND_CAR = 'PHONE_SIMILARITY_SEARCH@CAR_BY_PHONE';
    const FIND_MEETING_PLACES = 'PHONE_SIMILARITY_SEARCH@TWO_NUMBER_SIMILARITY';
    const FIND_INTERVAL_SIMILARITY = 'PHONE_SIMILARITY_SEARCH@INTERVAL_SIMILARITY_BY_LOCATIONS';
    const FIND_SIMILARITY = 'PHONE_SIMILARITY_SEARCH@PHONE_BY_PHONE';
    const FIND_PHONE_TRAJECTORY = 'SEARCH_PHONE_NUMBER_TRAJECTORY';
    const FIND_NATIVE_STRANGERS = 'PHONE_SIMILARITY_SEARCH@NATIVE_AND_STRANGER';
    const FIND_TRAVEL = 'SEARCH_CAR_TRAJECTORY';
    const FIND_SEARCH_BY_AREA = 'PHONE_SIMILARITY_SEARCH@SEARCH_ON_AREA';

    public function __construct(string $rustBaseURL = null, string $apiKey = null)
    {
        $this->rustBaseURL = $rustBaseURL ?? config('services.rust_base_url');
        $this->apiKey = $apiKey ?? config('services.rust_api_key');
        $this->userID = auth('api')->user()?->id ?? null;
        $username = config('services.drill.username');
        $password = config('services.drill.password');
        $this->authorizationHeader = 'Basic ' . base64_encode("{$username}:{$password}");
        $this->client = new Client();
    }

    private function makeRequest(
        string $operationName,
        string $operationType,
        string $apiVersion,
        string $correlationID,
        array  $requestBody
    )
    {
        $response = $this->client->request('POST', $this->rustBaseURL, [
            'headers' => [
                'X-Api-Key' => $this->apiKey,
                'X-Corrrelation-Id' => $correlationID,
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'createdBy' => $this->userID,
                'operationResultName' => $operationName,
                'operationType' => $operationType,
                'requestApiVersion' => $apiVersion,
                'requestBody' => $requestBody,
            ],
        ]);

        return json_decode($response->getBody(), true);
    }

    /**
     * @throws GuzzleException
     */
    public function findLocation(int $phoneNumber, string $startTime, string $endTime, float $radius, array $polygon, string $correlationID, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_LOCATION,
            'v2',
            $correlationID,
            [
                'phone_number' => $phoneNumber,
                'start_time' => $startTime,
                'end_time' => $endTime,
                'radius' => $radius,
                'polygon' => $polygon,
            ]
        );
    }

    /**
     * @throws GuzzleException
     */
    public function findCar($carNumber, $start, $end, $responseCount, $smallRadius, $bigRadius, $deltaTime, $correlationID, $polygon, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_CAR,
            'v1',
            $correlationID,
            [
                'car_number' => formatCarNumber($carNumber),
                'start' => $start,
                'end' => $end,
                'response_count' => $responseCount,
                'small_radius' => $smallRadius,
                'big_radius' => $bigRadius,
                'delta_time' => $deltaTime,
                'polygon' => $polygon,
                'match_percentage' => 0.1
            ]
        );
    }

    /**
     * @throws GuzzleException
     */
    public function findMeetingPlaces($number1, $number2, $start, $end, $radius, $delta_time, $break_time, $correlationID, $polygon, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_MEETING_PLACES,
            'v2',
            $correlationID,
            [
                'number1' => $number1,
                'number2' => $number2,
                'start' => $start,
                'end' => $end,
                'radius' => $radius,
                'delta_time' => $delta_time,
                'break_time' => $break_time,
                'polygon' => $polygon,
            ]
        );
    }

    /**
     * @throws GuzzleException
     */
    public function findIntervalSimilarity($response_count, $min_match, $correlationID, $points, $convertedPolygons, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_INTERVAL_SIMILARITY,
            'v2',
            $correlationID,
            [
                'response_count' => $response_count,
                'min_match' => $min_match,
                'polygon' => $convertedPolygons,
                'points' => $points,
            ]
        );
    }

    /**
     * @throws GuzzleException
     */
    public function findSimilarity($number, $start, $end, $breakTime, $responseCount, $smallRadius, $bigRadius, $deltaTime, $correlationID, $polygon, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_SIMILARITY,
            'v2',
            $correlationID,
            [
                'number' => $number,
                'start' => $start,
                'end' => $end,
                'small_radius' => $smallRadius,
                'big_radius' => $bigRadius,
                'response_count' => $responseCount,
                'break_time' => $breakTime,
                'delta_time' => $deltaTime,
                'polygon' => $polygon,
            ]
        );
    }

    /**
     * @throws GuzzleException
     */
    public function findPhoneNumberTrajectory($pin, $perPage, $fromDate, $toDate, $correlationID, $polygon, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_PHONE_TRAJECTORY,
            'v1',
            $correlationID,
            [
                'phone_numbers' => $pin,
                'start' => $fromDate,
                'end' => $toDate,
                'per_page' => $perPage,
                'polygon' => $polygon,
            ]
        );
    }

    /**
     * @throws GuzzleException
     */
    public function findNativeAndStrangers($startTime, $endTime, $type, $frequencyCount, $radius, $correlationID, $polygon, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_NATIVE_STRANGERS,
            'v1',
            $correlationID,
            [
                'radius' => $radius,
                'start' => $startTime,
                'end' => $endTime,
                'kind' => $type,
                'polygon' => $polygon,
                'day_count' => $frequencyCount,
            ]
        );
    }

    /**
     * @throws GuzzleException
     */
    public function getVehicleEntered($carNumber, $start, $end, $correlationID, $polygon, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_TRAVEL,
            'v1',
            $correlationID,
            [
                'car_number' => formatCarNumber($carNumber),
                'start' => $start,
                'end' => $end,
                'polygon' => $polygon,
            ]
        );
    }

    /**
     * @throws GuzzleException
     */
    public function searchByArea($startTime, $endTime, $correlationID, $polygon, $search_head_name)
    {
        return $this->makeRequest(
            $search_head_name,
            self::FIND_SEARCH_BY_AREA,
            'v1',
            $correlationID,
            [
                'start' => $startTime,
                'end' => $endTime,
                'polygon' => $polygon,
            ]
        );
    }

    public function checkStatus(string $searchID, int $page = 1, int $perPage = 10): JsonResponse
    {
        try {
            $searchRequest = $this->getSearchRequest($searchID);
            if (!$searchRequest) {
                return response()->json(['error' => 'Nəticə tapılmadı']);
            }
            $result = json_decode($searchRequest->result, true);
            $offset = ($page - 1) * $perPage;
            if ($searchRequest->operation_status !== 200) {
                if ($searchRequest->operation_status == 150) {
                    return $this->handleMultipleResults($result, $page, $perPage, $offset, $searchRequest);
                }
                return $this->generateErrorResponse(json_decode($searchRequest));
            }
            if (is_array($result)) {
                if (array_key_exists('file', $result)) {
                    if ($result['has_data']) {
                        return $this->handleFileResult($result['file'], $page, $perPage, $offset, $searchRequest);
                    }
                    return $this->generateErrorResponse($result);
                } else {
                    return $this->handleMultipleResults($result, $page, $perPage, $offset, $searchRequest);
                }
            }
            return $this->handleSingleResult($result, $page, $perPage, $offset, $searchRequest);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Xəta baş verdi', 'error' => $e->getMessage().$e->getLine()]);
        }
    }

    private function handleFileResult(string $filePath, int $page, int $perPage, int $offset, $searchRequest): JsonResponse
    {
        try {
            $totalRecords = $this->getTotalRecords($filePath);
            $data = $this->fetchData($filePath, $perPage, $offset);
            return $this->generateSuccessResponse($searchRequest, $data, $totalRecords, $page, $perPage,$filePath);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Xəta baş verdi', 'error' => $e->getMessage()]);
        }
    }

    private function handleMultipleResults(array $result, int $page, int $perPage, int $offset, $searchRequest): JsonResponse
    {
        try {
            $totalRecords = 0;
            $responses = Http::pool(function (Pool $pool) use ($result, $offset, $perPage, &$totalRecords) {
                $requests = [];
                foreach ($result as $key => $value) {
                    if (is_array($value)) {
                        if ($value['has_data']) {
                            if ($key === 'din-api-result') {
                                $totalRecords = $this->getTotalRecords($value['file']);
                            }
                            $sqlQuery = "SELECT * FROM dfs.root.`{$value['file']}` d LIMIT $perPage OFFSET $offset";
                            $requests[$key] = $pool->as($key)->withHeaders([
                                'Content-Type' => 'application/json',
                                'Authorization' => $this->authorizationHeader
                            ])->post(config('services.drill_query_url'), [
                                'queryType' => 'SQL',
                                'query' => $sqlQuery
                            ]);
                        }
                    }
                }
                return $requests;
            });
            if ($responses != []) {
                return $this->combineResponses($responses, $totalRecords, $page, $perPage, $searchRequest);
            }
            return $this->generateErrorResponse($result);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Xəta baş verdi', 'error' => $e->getMessage()]);
        }
    }

    private function handleSingleResult(array $result, int $page, int $perPage, int $offset, $searchRequest): JsonResponse
    {
        try {
            if (isset($result['file']) && $result['has_data']) {
                return $this->handleFileResult($result['file'], $page, $perPage, $offset, $searchRequest);
            }
            return $this->generateErrorResponse($searchRequest);
        } catch (\Exception $e) {
            return response()->json(['message' => 'Xəta baş verdi', 'error' => $e->getMessage()]);
        }
    }

    private function getTotalRecords(string $filePath): int
    {
        try {
            $countQuery = "SELECT COUNT(*) as total FROM dfs.root.`{$filePath}` d";
            $countResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => $this->authorizationHeader
            ])->post(config('services.drill_query_url'), [
                'queryType' => 'SQL',
                'query' => $countQuery
            ]);
            return $countResponse->json()['rows'][0]['total'] ?? 0;
        } catch (\Exception $e) {
            throw new \Exception('Xəta baş verdi' . $e->getMessage());
        }
    }
    private function fetchData(string $filePath, int $perPage, int $offset)
    {
        try {
            $sqlQuery = "SELECT * FROM dfs.root.`{$filePath}` d LIMIT $perPage OFFSET $offset";
            $dataResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => $this->authorizationHeader
            ])->post(config('services.drill_query_url'), [
                'queryType' => 'SQL',
                'query' => $sqlQuery
            ]);
            $fileType = explode("/", $filePath)[3];
            $responseData = $dataResponse->json();
            $rows = $responseData['rows'] ?? [];
            switch ($fileType) {
                case 'search-on-area':
                case 'similarity':
                case 'two-number':
                case 'native-and-stranger':
                    $responseData['rows'] = $this->processRowsWithCoordinates($rows, 'end');
                    break;
                case 'search-phone-number-trajectory':
                    $responseData['rows'] = $this->processPhoneTrajectoryUngroup($rows);
                    break;
                case 'find-location':
                    $responseData['rows'] = $this->processRowsWithoutCoordinates($rows);
                    break;

                case 'interval-similarity':
                    $responseData['rows'] = $this->processRowsWithCoordinates($rows, 'date');
                    break;

                case 'car':
                    $responseData['rows'] = $this->processRowsForCarSimilarity($rows);
                    break;
                default:
                    return $responseData;
            }
            return $responseData;
        } catch (\Exception $e) {
            throw new \Exception('Xəta baş verdi: ' . $e->getMessage() . ' Line: ' . $e->getLine());
        }
    }
    private function processRowsWithCoordinates(array $rows, string $dateKey): array
    {
        foreach ($rows as &$row) {
            if (isset($row['coordinates'])) {
                $decodedCoordinates = base64_decode($row['coordinates']);
                $coordinatesArray = json_decode($decodedCoordinates, true);
                if ($coordinatesArray) {
                    $latestCoordinates = [];
                    foreach ($coordinatesArray as $coordinate) {
                        $latLonKey = $coordinate['lat'] . ',' . $coordinate['lon'];
                        if (!isset($latestCoordinates[$latLonKey])) {
                            $latestCoordinates[$latLonKey] = $coordinate;
                        } else {
                            if (strtotime($coordinate[$dateKey]) > strtotime($latestCoordinates[$latLonKey][$dateKey])) {
                                $latestCoordinates[$latLonKey] = $coordinate;
                            }
                        }
                    }
                    $row['coordinates'] = base64_encode(json_encode(array_values($latestCoordinates)));
                }
            }
        }
        return $rows;
    }
    private function processPhoneTrajectoryUngroup(array $rows): array
    {
        $expandedRows = [];
        foreach ($rows as $row) {
            if (isset($row['coordinates'])) {
                $decodedCoordinates = base64_decode($row['coordinates']);
                $coordinatesArray = json_decode($decodedCoordinates, true);
                if (is_array($coordinatesArray)) {
                    $uniqueCoordinates = [];
                    foreach ($coordinatesArray as $coordinate) {
                        $latLonKey = $coordinate['lat'] . ',' . $coordinate['lon'];
                        if (!isset($uniqueCoordinates[$latLonKey])) {
                            $uniqueCoordinates[$latLonKey] = $coordinate;
                        } else {
                            if (strtotime($coordinate['end']) > strtotime($uniqueCoordinates[$latLonKey]['end'])) {
                                $uniqueCoordinates[$latLonKey] = $coordinate;
                            }
                        }
                    }
                    foreach ($uniqueCoordinates as $uniqueCoordinate) {
                        $newRow = $row;
                        $newRow['coordinates'] = base64_encode(json_encode([$uniqueCoordinate]));
                        $expandedRows[] = $newRow;
                    }
                } else {
                    $expandedRows[] = $row;
                }
            } else {
                $expandedRows[] = $row;
            }
        }
        return $expandedRows;
    }
    private function processRowsWithoutCoordinates(array $rows): array
    {
        $latestCoordinates = [];
        foreach ($rows as $row) {
            $latLonKey = $row['lat'] . ',' . $row['lon'];
            if (!isset($latestCoordinates[$latLonKey])) {
                $latestCoordinates[$latLonKey] = $row;
            } else {
                if (strtotime($row['end']) > strtotime($latestCoordinates[$latLonKey]['end'])) {
                    $latestCoordinates[$latLonKey] = $row;
                }
            }
        }
        return array_values($latestCoordinates);
    }
    private function processRowsForCarSimilarity(array $rows): array
    {
        foreach ($rows as &$row) {
            if (isset($row['coordinates'])) {
                $decodedCoordinates = base64_decode($row['coordinates']);
                $coordinatesArray = json_decode($decodedCoordinates, true);
                if ($coordinatesArray) {
                    $latestCoordinates = [];
                    foreach ($coordinatesArray as $coordinate) {
                        $latLonKey = $coordinate['lat'] . ',' . $coordinate['lon'];
                        if (!isset($latestCoordinates[$latLonKey])) {
                            $latestCoordinates[$latLonKey] = $coordinate;
                        }
                    }
                    $row['coordinates'] = base64_encode(json_encode(array_values($latestCoordinates)));
                }
            }
        }
        return $rows;
    }
    private function combineResponses(array $responses, int $totalRecords, int $page, int $perPage, $searchRequest): JsonResponse
    {
        try {
            $combinedData = array_map(function ($response) {
                return $response->successful() ? $response->json() : 'Request failed with status: ' . $response->status();
            }, $responses);
            $dinApiData = $combinedData['din-api-result']['rows'] ?? [];
            $dinApiColumns = $combinedData['din-api-result']['columns'] ?? [];
            $rowsData = $combinedData['azparking-data']['rows'][0]['data']['data']['data'] ?? [];
            $count = $combinedData['azparking-data']['rows'][0]['data']['data']['count'] ?? 0;

            foreach ($rowsData as &$row) {
                if (isset($row['park']['center']['coordinates'])) {
                    $centerCoordinates = $row['park']['center']['coordinates'];
                    $row['park']['center'] = [
                        'lat' => $centerCoordinates[0],
                        'lon' => $centerCoordinates[1],
                        'type' => 'Point'
                    ];
                }

                if (isset($row['park']['coordinates']['coordinates'])) {
                    $lineCoordinates = $row['park']['coordinates']['coordinates'];
                    $newLineCoordinates = [];
                    foreach ($lineCoordinates as $coordinate) {
                        $newLineCoordinates[] = [
                            'lat' => $coordinate[0],
                            'lon' => $coordinate[1]
                        ];
                    }
                    $row['park']['coordinates'] = $newLineCoordinates;
                }
            }

            if (empty($rowsData) && $count === 0) {
                $azParkingData = [];
            } else {
                $azParkingData = [
                    'rows' => array_merge(
                        $rowsData,
                        [['count' => $count]]
                    )
                ];
            }
            return $this->generateSuccessResponse($searchRequest, [
                'azparking-data' => $azParkingData,
                'din-api-result' => [
                    'rows' => $dinApiData,
                    'total' => $totalRecords,
                    'columns' => $dinApiColumns,
                ],
            ], $totalRecords, $page, $perPage,null);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Xəta baş verdi: ' . $e->getMessage()]);
        }
    }
    private function generateSuccessResponse($searchRequest, array $data, int $totalRecords, int $page, int $perPage,$filePath): JsonResponse
    {
        $response_body = json_decode($searchRequest->request_body, true);
        if ($searchRequest->operation_type == self::FIND_LOCATION) {
            $radius = (int)($response_body['radius'] * 1000);
            $data['rows'] = array_map(function ($row) use ($radius) {
                $row['radius'] = $radius;
                return $row;
            }, $data['rows']);
        }
        else if ($searchRequest->operation_type == self::FIND_SIMILARITY){
            $countSumQuery = "SELECT SUM(count) as _total_sum FROM dfs.root.`{$filePath}`";
            $dataResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => $this->authorizationHeader
            ])->post(config('services.drill_query_url'), [
                'queryType' => 'SQL',
                'query' => $countSumQuery
            ]);
            $data['sum'] = $dataResponse->json()['rows'][0]['_total_sum'] ?? 0;
        }
        else if ($searchRequest->operation_type == self::FIND_CAR){
            $countSumQuery = "SELECT SUM(count) as _total_sum FROM dfs.root.`{$filePath}`";
            $dataResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => $this->authorizationHeader
            ])->post(config('services.drill_query_url'), [
                'queryType' => 'SQL',
                'query' => $countSumQuery
            ]);
            $data['sum'] = $dataResponse->json()['rows'][0]['_total_sum'] ?? 0;
        }
        $totalPages = (int)ceil($totalRecords / $perPage);
        $url = url('/api/v1/check-status');
        if (isset($data['rows']) && is_array($data['rows'])) {
            $data['rows'] = array_map(function ($item) use ($searchRequest) {
                $item['operation_type'] = $searchRequest->operation_type;
                return $item;
            }, $data['rows']);
        }
        elseif (isset($data['din-api-result']['rows']) && is_array($data['din-api-result']['rows'])) {
            $data['din-api-result']['rows'] = array_map(function ($item) use ($searchRequest) {
                $item['operation_type'] = $searchRequest->operation_type;
                return $item;
            }, $data['din-api-result']['rows']);
        }
        $filePath = json_decode($searchRequest->result, true)['file'] ?? null;
        if($searchRequest->operation_type == self::FIND_TRAVEL){
            $data = MergedDataDTO::fromArray($data);
            $data = $data->toarray();
        }
        return response()->json(array_merge($data, [
            "search_request" => $filePath,
            "search_result" =>json_decode($searchRequest->result,true),
            "operation_status" => $searchRequest->operation_status,
            "operation_type" => $searchRequest->operation_type,
            "current_page" => $page,
            "first_page_url" => "{$url}?page=1&searchID={$searchRequest->id}",
            "from" => ($page - 1) * $perPage + 1,
            "last_page" => $totalPages,
            "last_page_url" => "{$url}?page={$totalPages}&searchID={$searchRequest->id}",
            "links" => generatePaginationLinks($page, $totalPages, $url, $searchRequest->id, ''),
            "next_page_url" => $page < $totalPages ? "{$url}?page=" . ($page + 1) . "&searchID={$searchRequest->id}" : null,
            "prev_page_url" => $page > 1 ? "{$url}?page=" . ($page - 1) . "&searchID={$searchRequest->id}" : null,
            "per_page" => $perPage,
            "to" => min($totalRecords, $page * $perPage),
            "total" => $totalRecords,
        ]));
    }

    private function generateErrorResponse($searchRequest): JsonResponse
    {
        $searchRequest = (array)$searchRequest;
        if (!isset($searchRequest['operation_status'])) {
            $searchRequest['operation_status'] = 404;
        }
        return response()->json(['message' => 'Nəticə tapılmadı', 'result' => $searchRequest, 'operation_status' => $searchRequest['operation_status']]);
    }

    private function getSearchRequest(string $searchID): Model|Builder|null
    {
        return SearchRequests::query()
            ->where([
                ['id', '=', $searchID],
                ['is_deleted', '=', false]
            ])
            ->select('id', 'operation_type', 'operation_result_name', 'operation_status', 'request_body', 'created_at', 'updated_at', 'created_by', 'result')
            ->first();
    }

    public function buildPolygon(?array $polygonInput): array
    {
        if (empty($polygonInput)) {
            return [];
        }
        return array_map(static function ($point) {
            return [
                (float)$point['lng'],
                (float)$point['lat'],
            ];
        }, $polygonInput);
    }

    public function getLocations($parquet, $lat, $lon, $columns, $page, $perPage): JsonResponse
    {
        $columns = array_filter($columns, function ($item) {
            return $item !== 'coordinates';
        });
        $columns = array_map(function ($col) {
            return "d.$col";
        }, $columns);
//        $value = $type == 'pin' ? strtoupper($value) : $value;
        $offset = ($page - 1) * $perPage;
//        $fileType = explode("/", $parquet)[3];
        $sqlQuery = "SELECT * FROM ( SELECT " . implode(', ', $columns) . " ,FLATTEN(CONVERT_FROM(d.coordinates, 'JSON')) AS coordinates from dfs.root.`{$parquet}` d ) t
                     WHERE (t.coordinates['lat'] = $lat AND t.coordinates['lon'] = $lon) LIMIT {$perPage} OFFSET {$offset}";
        $dataResponse = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => $this->authorizationHeader
        ])->post(config('services.drill_query_url'), [
            'queryType' => 'SQL',
            'query' => $sqlQuery
        ]);
        $totalCount = $this->getLocationsTotalCount($sqlQuery);
        $totalPages = (int)ceil($totalCount / $perPage);
        $url = url('/api/v1/pre-location/get-locations');
        return response()->json(array_merge($dataResponse->json(), [
            "current_page" => $page,
            "first_page_url" => "{$url}?page=1",
            "from" => ($page - 1) * $perPage + 1,
            "last_page" => $totalPages,
            "last_page_url" => "{$url}?page={$totalPages}",
            "links" => generatePaginationLinks($page, $totalPages, $url, '', ''),
            "next_page_url" => $page < $totalPages ? "{$url}?page=" . ($page + 1) : null,
            "prev_page_url" => $page > 1 ? "{$url}?page=" . ($page - 1) : null,
            "per_page" => $perPage,
            "to" => min($totalCount, $page * $perPage),
            "total" => $totalCount,
        ]));
    }

    public function getLocationsTotalCount($query)
    {
        $query = preg_replace('/^SELECT \* /', 'SELECT COUNT(*) AS total_count ', $query, 1);
        $query = preg_replace('/\s*LIMIT\s+\d+\s*OFFSET\s+\d+/', '', $query);
        $dataResponse = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => $this->authorizationHeader
        ])->post(config('services.drill_query_url'), [
            'queryType' => 'SQL',
            'query' => $query
        ]);
        return $dataResponse->json()['rows'][0]['total_count'] ?? 0;
    }
    public function filterResults($parquet,$modifiedFilterModel,$columns,$lat,$lon,$page,$perPage): JsonResponse
    {
        if(!empty($modifiedFilterModel)){
            $columns = array_filter($columns, function ($item) {
                return $item !== 'coordinates';
            });
            $columns = array_map(function ($col) {
                return "d.$col";
            }, $columns);
            $offset = ($page - 1) * $perPage;
            $whereClauses = array_filter(array_map(
                fn($field, $filter) => (str_starts_with($field, 'response.') && !in_array($field, ['response.lat', 'response.lon']))
                    ? $this->buildFilterClause(substr($field, strlen('response.')), (array)$filter)
                    : null,
                array_keys($modifiedFilterModel),
                $modifiedFilterModel
            ));
//            if (isset($modifiedFilterModel['response.lat'], $modifiedFilterModel['response.lon'])) {
//                $lat = (float)$modifiedFilterModel['response.lat']['filter'];
//                $lon = (float)$modifiedFilterModel['response.lon']['filter'];
//                $whereClauses[] = "(t.coordinates['lat'] = $lat AND t.coordinates['lon'] = $lon)";
//            }
            $whereClause = !empty($whereClauses) ? 'WHERE ' . implode(' AND ', $whereClauses)  : '';
            $sqlQuery = "SELECT * FROM ( SELECT " . implode(', ', $columns) . " ,FLATTEN(CONVERT_FROM(d.coordinates, 'JSON')) AS coordinates
                      FROM dfs.root.`{$parquet}` d ) t {$whereClause} AND (t.coordinates['lat'] = {$lat} AND t.coordinates['lon'] = {$lon}) LIMIT {$perPage} OFFSET {$offset}";
            $dataResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => $this->authorizationHeader
            ])->post(config('services.drill_query_url'), [
                'queryType' => 'SQL',
                'query' => $sqlQuery
            ]);
            $totalCount = $this->getLocationsTotalCount($sqlQuery);
            $totalPages = (int)ceil($totalCount / $perPage);
            $url = url('/api/v1/pre-location/filter-results');
            return response()->json(array_merge($dataResponse->json(), [
                "current_page" => $page,
                "first_page_url" => "{$url}?page=1",
                "from" => ($page - 1) * $perPage + 1,
                "last_page" => $totalPages,
                "last_page_url" => "{$url}?page={$totalPages}",
                "links" => generatePaginationLinks($page, $totalPages, $url, '', ''),
                "next_page_url" => $page < $totalPages ? "{$url}?page=" . ($page + 1) : null,
                "prev_page_url" => $page > 1 ? "{$url}?page=" . ($page - 1) : null,
                "per_page" => $perPage,
                "to" => min($totalCount, $page * $perPage),
                "total" => $totalCount,
            ]));
        }
        return $this->getLocations($parquet, $lat, $lon, $columns, $page, $perPage);
    }
    private function buildFilterClause(string $fieldName, array $filter): string
    {
        if (isset($filter['conditions'])) {
            $operator = $filter['operator'] ?? 'AND';
            $subClauses = array_map(fn($condition) => $this->buildSingleFilter($fieldName, $condition), $filter['conditions']);
            return '(' . implode(" $operator ", $subClauses) . ')';
        }
        return $this->buildSingleFilter($fieldName, $filter);
    }
    private function buildSingleFilter(string $fieldName, array $filter): string
    {
        $filterType = $filter['filterType'];
        return match ($filterType) {
            'text' => $this->buildTextFilter($fieldName, $filter),
            'number' => $this->buildNumberFilter($fieldName, $filter),
            'date' => $this->buildDateFilter($fieldName, $filter),
            default => '',
        };
    }
    private function buildTextFilter(string $fieldName, array $filter): string
    {
        $filterType = $filter['type'];
        $filterValue = isset($filter['filter']) ? az_upper($filter['filter']) : null;
        return match ($filterType) {
            'equals' => "$fieldName = '$filterValue'",
            'contains' => "$fieldName LIKE '%$filterValue%'",
            'startsWith' => "$fieldName LIKE '$filterValue%'",
            'endsWith' => "$fieldName LIKE '%$filterValue'",
            'notContains' => "$fieldName NOT LIKE '%$filterValue%'",
            'notBlank' => "$fieldName IS NOT NULL",
            'isBlank' => "$fieldName IS NULL",
            default => '',
        };
    }
    private function buildNumberFilter(string $fieldName, array $filter): string
    {
        $filterType = $filter['type'];
        $filterValue = (int)$filter['filter'];
        return match ($filterType) {
            'equals' => "$fieldName = $filterValue",
            'notEqual' => "$fieldName != $filterValue",
            'lessThan' => "$fieldName < $filterValue",
            'lessThanOrEqual' => "$fieldName <= $filterValue",
            'greaterThan' => "$fieldName > $filterValue",
            'greaterThanOrEqual' => "$fieldName >= $filterValue",
            default => '',
        };
    }
    private function buildDateFilter(string $fieldName, array $filter): string
    {
        $filterType = $filter['type'];
        $dateFrom = $filter['start'];
        $dateTo = $filter['end'] ?? null;
        return match ($filterType) {
            'equals' => "DATE($fieldName) = '$dateFrom'",
            'notEqual' => "DATE($fieldName) != '$dateFrom'",
            'lessThan' => "DATE($fieldName) < '$dateFrom'",
            'lessThanOrEqual' => "DATE($fieldName) <= '$dateFrom'",
            'greaterThan' => "DATE($fieldName) > '$dateFrom'",
            'greaterThanOrEqual' => "DATE($fieldName) >= '$dateFrom'",
            'inRange' => "DATE($fieldName) BETWEEN '$dateFrom' AND '$dateTo'",
            default => '',
        };
    }
}
