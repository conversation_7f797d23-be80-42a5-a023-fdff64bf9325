<?php

namespace App\Services\SocialHub;

use App\Http\Resources\Social\AccountResource;
use App\Http\Resources\Social\CommentResource;
use App\Http\Resources\Social\DescriptionResource;
use App\Http\Resources\Social\ImageAccountResource;
use App\Http\Resources\Social\LicensePlateResource;
use App\Http\Resources\Social\MediaResource;
use App\Http\Resources\Social\PostResource;
use App\Http\Resources\Social\SocialPageResource;
use App\Models\Api\Social\LicensePlate;
use App\Models\Social\Account;
use App\Models\Social\Comment;
use App\Models\Social\Description;
use App\Models\Social\Media;
use App\Models\Social\Post;
use App\Models\Social\SocialPage;
use App\Traits\ApiResponsible;
use App\Traits\PlatformFilterTrait;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use JetBrains\PhpStorm\ArrayShape;

class SocialSearchService
{
    use ApiResponsible;

//  one day
//  private int $cacheTime = 24 * 3600;
    private int $cacheTime = 10 * 60;

    public function searchByProfile($request)
    {

        $text = $this->getText($request->text ?? "");

        $accounts = Account::query()
//            ->where('platform_id', $request->platform_id ?? 1)
            ->filterByPlatform(request())
            ->where('checked', 1)
            ->whereNotNull('photo_path');

        if ($text != "") {
            $accounts = $accounts->where(function ($query) use ($text) {
                $query->search('username', $text)
                    ->orSearch('name', $text)
                    ->orSearch('surname', $text);
            });
        }
        $accounts = $accounts->with(['following'])
            ->orderByDesc('accounts.activity_rank')
            ->paginate($request->per_page ?? 12);

        $accounts->getCollection()->transform(function ($account) {
            $account->is_following = $account->following !== null;
            $account->following_id = $account->following->id ?? 0;
//            $account->media = (object)['photo_path' => $account->photo_path ?? null];
            unset($account->following);
            return $account;
        });

//        return $accounts;


        $logData = [
            "description" => $text != "" ? $text . " adına görə sosialda profil axtardı" : " sosialda profil axtarış səhifəsinə daxil oldu",
            "type" => "social_hub"
        ];
        sendRequestLogToAudit($logData, "audit", false, false);

        return AccountResource::collection($accounts);

    }

    public function searchByPage($request)
    {
        $text = $this->getText($request->text ?? "");

        $rows = SocialPage::query()
//            ->where('platform_id', $request->platform_id ?? 1)
            ->filterByPlatform(request())
            ->with('following', 'media')
            ->whereNotNull('profile_picture')
            ->search('public_name', $text)
            ->orderByDesc('monitoring_id')
            ->paginate($request->per_page ?? 12);

        $rows->getCollection()->transform(function ($row) {
            if ($row->following !== null) {
                $row->is_following = true;
                $row->following_id = $row->following->id;
            } else {
                $row->is_following = false;
                $row->following_id = 0;
            }
            unset($row->following);
            return $row;
        });

        $logData = [
            "description" => $text != "" ? $text . " adına görə sosialda səhifə axtardı" : " sosialda səhifə axtarış bölməsinə daxil oldu",
            "type" => "social_hub"
        ];
        sendRequestLogToAudit($logData, "audit", false, false);

        return SocialPageResource::collection($rows);
    }

    public function searchByPost($request)
    {
        $text = $this->getText($request->text ?? "");

        $searchTerm = implode(' & ', explode(' ', $text));
        $searchTerm .= ':*';

        $rows = Post::query()
//            ->where('platform_id', $request->platform_id ?? 1)
            ->filterByPlatform(request())
            ->with('media');
        if ($text != "") {
            $rows->whereRaw("to_tsvector('simple', context) @@ to_tsquery('simple', ?)", [$searchTerm]);
        }
        $rows->whereNotNull('context')->where('context', '!=', '');

        if (isset($request->byDate)) {
            $rows->orderByDesc('publish_time');
        } else {
            $rows->orderByDesc('id');
        }

        $rows = $rows->paginate($request->per_page ?? 12);


        $logData = [
            "description" => $text != "" ? $text . " adına görə sosialda post axtardı" : " sosialda post axtarış səhifəsinə daxil oldu",
            "type" => "social_hub"
        ];
        sendRequestLogToAudit($logData, "audit", false, false);

        return PostResource::collection($rows);
    }

    public function searchByComment($request)
    {
        $text = $this->getText($request->text ?? "");
        $searchTerm = implode(' & ', explode(' ', $text));
        $searchTerm .= ':*';

        $comments = Comment::query()
            ->with('account.media')
            ->with('posts')
            ->filterByPlatform(request());
//            ->where('platform_id', $request->platform_id ?? 1);

        if ($text != "") {
            $comments->whereRaw("to_tsvector('simple', context) @@ to_tsquery('simple', ?)", [$searchTerm]);
        }

        if (isset($request->byDate)) {
            $comments->orderByRaw('publish_time IS NULL')->orderByDesc('publish_time');
        } else {
            $comments->orderByDesc('id');
        }

        $comments = $comments->paginate($request->per_page ?? 12);

        $logData = [
            "description" => $text != "" ? $text . " adına görə sosialda comment axtardı" : " sosialda comment axtarış səhifəsinə daxil oldu",
            "type" => "social_hub"
        ];
        sendRequestLogToAudit($logData, "audit", false, false);

        return CommentResource::collection($comments);
    }

    public function searchByPhoto($request, $type = 'face')
    {
        $file = $request->file('photo');
        $fileContents = file_get_contents($file->getRealPath());
        $base64Photo = base64_encode($fileContents);

        $distances = [];
        if (in_array(config('app.env'), ['development', 'local'])) {
            $distances = $this->getFakeMediaPath();
        } else {
            if ($type == 'face') {
                $searchFromAi = $this->getDataFromAiWithFace($base64Photo);

                $logData = [
                    "description" => "şəkil üzrə sosialda üz axtardı",
                    "type" => "social_hub"
                ];
            } else {
                $searchFromAi = $this->getDataFromAiWithImage($base64Photo);

                $logData = [
                    "description" => "şəkil üzrə sosialda axtarış etdi",
                    "type" => "social_hub"
                ];
            }
            $logData['photo'] = $base64Photo;
            sendRequestLogToAudit($logData, "audit", false, false);

            $getData = $this->extractPhotoPaths($searchFromAi);

//            $photoPaths = $getData['photoPaths'];
            $distances = $getData['distances'];
        }


        return $this->getAccountsByPhotoPath($distances, $request->per_page ?? 12);
    }

    #[ArrayShape(['photoPaths' => "array", 'distances' => "array"])]
    private function extractPhotoPaths($jsonData): array
    {
        $photoPaths = [];
        $distances = [];
        foreach ($jsonData as $data) {
            foreach ($data as $item) {
                if (isset($item['entity']['photo_path'])) {
                    $photoPaths[] = $item['entity']['photo_path'];
                    $distances[$item['entity']['photo_path']] = round($item['distance'], 2);
                }

            }
        }
        return ['photoPaths' => $photoPaths, 'distances' => $distances];
    }

    private function getDataFromAiWithFace($base64Image)
    {
        return Http::post(config('servers.social_face_embed'), [
            'image_base64' => $base64Image,
            "max_result" => 3
        ])->json();
    }

    private function getDataFromAiWithImage($base64Image)
    {
        return Http::post(config('servers.social_image_embed'), [
            'image_base64' => $base64Image,
            "max_result" => 3
        ])->json();
    }

    private function getFakeMediaPath()
    {
        return [
            '1-3081623-1789105.jpeg' => 1.2,
            '1-1221448-1789313.jpeg' => 1.5,
            '1-1064752-1789483.jpeg' => 0.5,
            '1-3081623-1789100.jpeg' => 1.4,
            '1-3081623-1789104.jpeg' => 0.3,
            '1-786175-1789286.jpeg' => 4.5
        ];
    }


    public function searchByPhotoText($request): AnonymousResourceCollection
    {
        $text = $this->getText($request->text ?? "");

        $lang = $request->lang ?? "az";

        $rows = Description::query()
            ->with('media', 'media.account');

        if ($text != "") {
            $searchTerm = implode(' & ', explode(' ', $text));
            $searchTerm .= ':*';

            if ($lang == "az") {

                $rows->whereRaw("to_tsvector('simple', aze) @@ to_tsquery('simple', ?)", [$searchTerm]);

            } else {
                $rows->whereRaw("to_tsvector('simple', eng) @@ to_tsquery('simple', ?)", [$searchTerm]);

//            $rows->search('eng', $text);
            }
        }

        $rows = $rows->when(request()->has('platform_id') || request()->has('platform_ids'), function ($query) {
            if (request()->has('platform_id')) {
                $platform_id = request()->get('platform_id');
                $query->whereHas('media', function ($q) use ($platform_id) {
                    $q->where('platform_id', $platform_id);
                });
            } elseif (request()->has('platform_ids')) {
                $platform_ids = request()->get('platform_ids');
                $query->whereHas('media', function ($q) use ($platform_ids) {
                    $q->whereIn('platform_id', $platform_ids);
                });
            }
        })->paginate($request->per_page ?? 12);


        $logData = [
            "description" => $text != "" ? $text . " - yazısı üzrə sosialda şəkil izahında axtarış etdi" : " sosialda şəkil izahı səhifəsinə daxil oldu",
            "type" => "social_hub"
        ];
        sendRequestLogToAudit($logData, "audit", false, false);

        return DescriptionResource::collection($rows);

    }

    private function getAccountsByPhotoPath($distances = [], $per_page = 12): AnonymousResourceCollection
    {

        $sortedPhotoPaths = collect($distances)
            ->sortDesc()
            ->keys()
//            ->skip($currentPage - 1)
//            ->take($per_page)
            ->toArray();

        $orderCase = 'CASE';
        foreach ($sortedPhotoPaths as $index => $path) {
            $orderCase .= " WHEN photo_path = '$path' THEN $index";
        }
        $orderCase .= ' END';

        $medias = Media::query()
            ->where('platform_id', 1)
            ->with('account')
            ->whereIn('photo_path', $sortedPhotoPaths)
            ->orderByRaw($orderCase)
            ->paginate($per_page);

        $medias->each(function ($media) {

            if ($media->account->following !== null) {
                $media->account->is_following = true;
                $media->account->following_id = $media->account->following->id;
            } else {
                $media->account->is_following = false;
                $media->account->following_id = 0;
            }
        });

        return MediaResource::collection($medias);
//        return ImageAccountResource::collection($medias);

    }

    public function getImageByCarNumber($request)
    {
        $patterns = [
            '/^[\d]{2,7}$/',
            '/^(?=.*[A-Za-z].*[A-Za-z])[A-Za-z\d]{2,7}$/'
        ];
        $number = $request->input('number');

        $validator = Validator::make($request->all(), [
            'number' => ['required', function ($attribute, $value, $fail) use ($patterns) {
                $matches = false;
                foreach ($patterns as $pattern) {
                    if (preg_match($pattern, $value)) {
                        $matches = true;
                        break;
                    }
                }
                if (!$matches) {
                    $fail('The ' . $attribute . ' format is invalid.');
                }
            }]
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $rows = LicensePlate::query()
            ->whereRaw('LENGTH(plate_number) = 7')
            ->where('plate_number', 'ILIKE', '%' . $number . '%')
            ->paginate($request->per_page ?? 12);


        $prepareAccountIds = [];
        foreach ($rows as $row) {
            $explodePath = explode("-", $row->photo_path);
            $prepareAccountIds[] = $explodePath[1];
        }


        $getAccounts = Account::query()
            ->with('media')
            ->whereIn('id', $prepareAccountIds)->get();


        //not using map because of pagination
        $rows->through(function ($row) use ($getAccounts) {
            $explodePath = explode("-", $row->photo_path);
            $thisAccount = null;
            if (isset($explodePath[1])){
                $thisAccount = $getAccounts->where('account_id', $explodePath[1])->first();
            }
            $row->account = $thisAccount;
            return $row;
        });


        $logData = [
            "description" => $number != "" ? $number . " - nömrəsinə görə sosialda axtarış etdi" : " sosialda maşın nömrəsi üzrə sosial axtarışa daxil oldu",
            "type" => "social_hub"
        ];
        sendRequestLogToAudit($logData, "audit", false, false);

        return LicensePlateResource::collection($rows);
    }

    private function getText($text): string
    {
        return strip_tags($text) ?? '';
    }


}
