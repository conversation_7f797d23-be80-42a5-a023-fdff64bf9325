<?php

namespace App\Services\SocialHub\Following;

use App\Enums\FollowingType;
use App\Http\Resources\Social\CommentResource;
use App\Http\Resources\Social\FollowingAccountResource;
use App\Http\Resources\Social\PostResource;
use App\Models\Social\Comment;
use App\Models\Social\Following;
use App\Models\Social\MonitoringPost;
use App\Models\Social\Post;
use App\Models\Social\UserPost;
use App\Repository\SocialHubRepository\SocialHubAccountRepository;
use App\Repository\SocialHubRepository\SocialHubFollowingRepository;
use App\Traits\ApiResponsible;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SocialPageFollowingService extends SocialFollowingService
{

    use ApiResponsible;

    //7 day
//    private int $cacheTime = 24 * 3600 * 7;
    private int $cacheTime = 10 * 60;

    protected SocialHubFollowingRepository $followingRepository;
    protected SocialHubAccountRepository $accountRepository;

    public function __construct(SocialHubFollowingRepository $followingRepository, SocialHubAccountRepository $accountRepository)
    {
        $this->followingRepository = $followingRepository;
        $this->accountRepository = $accountRepository;
    }

    public function list($request)
    {

        $text = request()->has('text') ? strip_tags(request()->input('text')) : "";

        $accounts = $this->followingRepository->buildQuery()
            ->with('pages.media')
            ->when($text, function ($query) use ($text) {
                $query->whereHas('pages', function ($query) use ($text) {
                    $query->where('public_name', 'ilike', '%' . $text . '%');
                });
            })
            ->whereHas('pages', function ($query) use ($request) {
                if ($request->has('platform_ids') && is_array($request->platform_ids) && !empty($request->platform_ids)) {
                    $query->whereIn('platform_id', $request->platform_ids);
                } else if ($request->has('platform_id')) {
                    $query->where('platform_id', $request->platform_id);
                }

                if ($request->filled('text')) {
                    $text = strip_tags($request->input('text'));
                    $query->where(function ($query) use ($text) {
                        $query->where('public_name', 'ILIKE', "%{$text}%");
                    });
                }
            })
            ->where('following_type', FollowingType::PAGE)
            ->get();

        return FollowingAccountResource::collection($accounts);
    }

    public function store($params): JsonResponse
    {
        $accountId = $params['account_id'];

        DB::beginTransaction();

        try {

            if ($this->followingRepository->existsByTypeIdAndFollowingType($accountId, FollowingType::PAGE->value)) {
                DB::rollBack();
                return $this->errorResponse(409, "error", ['Page also added']);
            }

            if (!$this->accountRepository->existsById($accountId)) {
                DB::rollBack();
                return $this->errorResponse(409, "error", ['Page not found']);
            }

            $account = $this->followingRepository->buildQuery()
                ->create([
                    'user_id' => Auth::id(),
                    'account_id' => $accountId,
                    'type' => 'DEFAULT',
                    'following_type' => FollowingType::PAGE,
                    'status' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

            DB::commit();

            return $this->successResponse(200, "success", $account->toArray());
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse(409, "error", ['An error occurred while saving the account']);
        }
    }

    public function page_posts($per_page, $text = ""): AnonymousResourceCollection
    {
        $followingPages = Following::query()
            ->where('following_type', FollowingType::PAGE)
            ->pluck('account_id');

        $posts = Post::query()
            ->with('page', 'media')
            ->filterByPlatform(request())
            ->whereIn('page_id', $followingPages);
        if ($text != "") {
            $posts->where('context', 'ilike', '%' . $text . '%');
        }
        $posts = $posts->orderByDesc('id')
            ->paginate($per_page);

        return PostResource::collection($posts);
    }

    public function page_comments($per_page, $text = ""): AnonymousResourceCollection
    {
        $followingPages = Following::query()->where('following_type', FollowingType::PAGE)
            ->pluck('account_id');

        $posts = Comment::query()
            ->with('account')
            ->filterByPlatform(request())
            ->whereHas('posts', function ($query) use ($followingPages) {
                $query->whereIn('id', $followingPages);
            });
        if ($text != "") {
            $posts->where('context', 'ilike', '%' . $text . '%');
        }
        $posts = $posts->orderByDesc('id')
            ->paginate($per_page);

        return CommentResource::collection($posts);
    }

    public function deleteSelected($ids = []): void
    {
        Following::query()
            ->whereIn('id', $ids)
            ->delete();
    }

}
