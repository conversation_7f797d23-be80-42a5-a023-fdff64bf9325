<?php

namespace App\Services\SocialHub\Following;

use App\Enums\FollowingType;
use App\Http\Resources\Social\CommentResource;
use App\Http\Resources\Social\DescriptionResource;
use App\Http\Resources\Social\PostResource;
use App\Models\Social\Comment;
use App\Models\Social\Description;
use App\Models\Social\Following;
use App\Models\Social\Post;
use App\Repository\SocialHubRepository\SocialHubAccountRepository;
use App\Repository\SocialHubRepository\SocialHubFollowingRepository;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SocialWordFollowingService extends SocialFollowingService
{

    use ApiResponsible;

    //7 day
//    private int $cacheTime = 24 * 3600 * 7;
    private int $cacheTime = 10 * 60;

    protected SocialHubFollowingRepository $followingRepository;
    protected SocialHubAccountRepository $accountRepository;

    public function __construct(SocialHubFollowingRepository $followingRepository, SocialHubAccountRepository $accountRepository)
    {
        $this->followingRepository = $followingRepository;
        $this->accountRepository = $accountRepository;
    }

    public function list($request): JsonResponse
    {

        $words = $this->followingRepository->buildQuery()
            ->where('following_type', FollowingType::WORD)
            ->when($request->filled('text'), function ($query) use ($request){
                $text = strip_tags($request->text);
                $query->where('following_params', 'ILIKE', "%{$text}%");
            })
            ->get()
            ->toArray();

        return $this->successResponse(200, "success", $words);
    }

    public function store($params): JsonResponse
    {

        $word = strtolower(strip_tags($params['word']));

        DB::beginTransaction();

        try {
            if ($this->followingRepository->existsByWordAndFollowingType($word, FollowingType::WORD->value)) {
                DB::rollBack();
                return $this->errorResponse(409, "error", ['Word also added']);
            }

            $account = $this->followingRepository->buildQuery()
                ->create([
                    'user_id' => Auth::id(),
                    'type' => 'DEFAULT',
                    'following_params' => $word,
                    'following_type' => FollowingType::WORD,
                    'status' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

            DB::commit();

            return $this->successResponse(200, "success", $account->toArray());
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse(409, "error", ['An error occurred while saving the account']);
        }
    }

    public function dataByFollowingWords($per_page, $type)
    {

        $followingWords = Following::query()
            ->where('following_type', FollowingType::WORD)
            ->pluck('following_params');

        if (empty($followingWords->toArray())) {
            return response()->json("Words is Empty", 400);
        }


        return match ($type) {
            "comment" => $this->dataFromComments($followingWords, $per_page),
            "post" => $this->dataFromPost($followingWords, $per_page),
            "image_description" => $this->dataFromImageDescription($followingWords, $per_page),
            default => response()->json("Bad Request", 400),
        };
    }

    private function dataFromComments($followingWords, $per_page)
    {
        $rows = Comment::query()
            ->filterByPlatform(request())
            ->with(['account', 'posts'])
            ->where(function ($query) use ($followingWords) {
                foreach ($followingWords as $word) {
                    $query->orWhere('context', 'ILIKE', '%' . $word . '%');
                }
            })
            ->when(request()->has('text'), function ($query) {
                $query->search('context', request()->get('text'));
            })
            //            ->orderByDesc('publish_time')
            ->paginate($per_page);

        return CommentResource::collection($rows);
    }

    private function dataFromPost($followingWords, $per_page): AnonymousResourceCollection
    {
        $rows = Post::query()
            ->filterByPlatform(request())
            ->with('account', 'media')
            ->where(function ($query) use ($followingWords) {
                foreach ($followingWords as $word) {
                    $query->orWhere('context', 'ILIKE', '%' . $word . '%');
                }
            })
            ->when(request()->has('text'), function ($query) {
                $query->search('context', request()->get('text'));
            })
            ->orderByDesc('id')
            ->paginate($per_page);

        return PostResource::collection($rows);
    }

    private function dataFromImageDescription($followingWords, $per_page): AnonymousResourceCollection
    {
        $rows = Description::query()
            ->with('media')
            ->where(function ($query) use ($followingWords) {
                foreach ($followingWords as $word) {
                    $searchTerm = implode(' & ', explode(' ', $word));
                    $searchTerm .= ':*';
                    $lang = request('lang') ?? "az";

                    if ($lang == "az") {
                        $query->orWhereRaw("to_tsvector('simple', aze) @@ to_tsquery('simple', ?)", [$searchTerm]);

                    } else {
                        $query->orWhereRaw("to_tsvector('simple', eng) @@ to_tsquery('simple', ?)", [$searchTerm]);
                    }
                }
            })
            ->when(request()->filled('text'), function ($query) {
                $text = request()->get('text');
                $lang = request('lang');

                $searchTerm = implode(' & ', explode(' ', $text));
                $searchTerm .= ':*';

                $query->where(function ($q) use ($text, $lang, $searchTerm) {
                    if ($lang == "az") {
                        $q->whereRaw("to_tsvector('simple', aze) @@ to_tsquery('simple', ?)", [$searchTerm]);
                    } else {
                        $q->whereRaw("to_tsvector('simple', eng) @@ to_tsquery('simple', ?)", [$searchTerm]);
                    }
                });
            })
            ->when(request()->has('platform_id') || request()->has('platform_ids'), function ($query) {
                if (request()->has('platform_id')) {
                    $platform_id = request()->get('platform_id');
                    $query->whereHas('media', function ($q) use ($platform_id) {
                        $q->where('platform_id', $platform_id);
                    });
                } elseif (request()->has('platform_ids')) {
                    $platform_ids = request()->get('platform_ids');
                    $query->whereHas('media', function ($q) use ($platform_ids) {
                        $q->whereIn('platform_id', $platform_ids);
                    });
                }
            })
            ->orderByDesc('id')
            ->paginate($per_page);

        return DescriptionResource::collection($rows);
    }

    public function deleteSelected($ids = []): void
    {
        Following::query()
            ->whereIn('id', $ids)
            ->delete();
    }
}
