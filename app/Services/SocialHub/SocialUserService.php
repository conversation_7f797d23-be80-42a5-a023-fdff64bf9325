<?php

namespace App\Services\SocialHub;

use App\Http\Resources\Social\AccountMediaResource;
use App\Http\Resources\Social\CommentResource;
use App\Http\Resources\Social\FacebookAccountResource;
use App\Http\Resources\Social\PostResource;
use App\Models\Social\Account;
use App\Models\Social\AccountPin;
use App\Models\Social\Comment;
use App\Models\Social\FacebookAccount;
use App\Models\Social\FacebookFriend;
use App\Models\Social\Media;
use App\Models\Social\Post;
use App\Traits\ApiResponsible;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SocialUserService
{

    use ApiResponsible;

    //one day
//    private int $cacheTime = 24 * 3600;
    private int $cacheTime = 10 * 60;

    public function userInfo($accountId)
    {
        $user = Account::query()->where([
            'id' => $accountId,
        ])->first();

        if (!$user) {
            return false;
        }

        $user->load(['facebook_friends' => function ($query) {
            $query->limit(7);
        }]);

        $user->load('following');


        if ($user->photo_path != null) {
            try {
                $user->photo_path = getSocialHubPhotoUrl($user->photo_path);
            }catch (\Exception $e){
                $user->whereHas('media');

                $user->photo_path = getSocialHubPhotoUrl($user->media->photo_path);
                unset($user->media);
            }
        }else{
            $user->whereHas('media');
            $user->photo_path = getSocialHubPhotoUrl($user->media?->photo_path);
            unset($user->media);
        }

        $user->facebook_friends_count = $this->userFacebookFriendsCount($accountId);
        $user->real_person = $this->getUserRealPersonWithCache($accountId);
        $user->posts = $this->userPosts($accountId, 1, []);
        $user->comments = $this->userComments($accountId);
        $user->bio = $this->getBio($accountId);

        $user->is_following = false;
        $user->following_id = 0;
        if ($user->following != null) {
            $user->is_following = true;
            $user->following_id = $user->following->id;
        }

        unset($user->following);


        $logData = [
            "description" => $user->name . " (ID:" . $user->id . ") - profilinə daxil oldu.",
            "type" => "social_hub"
        ];
        sendRequestLogToAudit($logData, "audit", false, false);

        return $user;
    }

    public function userPosts($accountId, $by_limit, $request): AnonymousResourceCollection
    {
        $limit = $request->limit ?? 7;

        if ($by_limit) {
            $rows = Cache::remember('user_posts_' . $accountId,
                $this->cacheTime, function () use ($accountId, $limit) {
                    return Post::query()
                        ->with('media')
                        ->where('account_id', $accountId)
                        ->orderByDesc('id')
                        ->limit($limit)
                        ->get();
                });
        } else {

            $page = $request->page ?? 1;
            $perPage = $request->per_page ?? 12;

            $rows = Post::query()
                    ->with('media')
                    ->where('account_id', $accountId)
                    ->when($request->filled('text'), function ($query) use($request) {
                        $query->where('context', 'ilike', '%'.strip_tags($request->text).'%');
                    })
                    ->orderByDesc('id')
                    ->paginate($perPage, ['*'], 'page', $page) ?? [];
        }


        return PostResource::collection($rows);
    }

    public function userComments($accountId, $byLimit = 1, $limit = 7)
    {
        if ($byLimit) {
            return Cache::remember('user_comments_' . $accountId,
                $this->cacheTime, function () use ($accountId, $limit) {
                    return Comment::query()
                        ->where('account_id', $accountId)
                        ->limit($limit)
                        ->toBase()
                        ->get();
                });
        } else {
            return Comment::query()
                ->where('account_id', $accountId)
                ->paginate(12);
        }
    }

    public function userCommentsByProfile($accountId, $byLimit = 1, $per_page = 7)
    {
        return Comment::query()
            ->with('posts.account')
            ->whereHas('posts', function ($query) {
                $query->where('source_type', 'person');
            })
            ->where('account_id', $accountId)
            ->paginate($per_page);
    }

    public function userCommentsByPage($accountId, $byLimit = 1, $per_page = 7)
    {

        return Comment::query()
            ->with('posts.account')
            ->whereHas('posts', function ($query) {
                $query->where('source_type', 'page');
            })
            ->where('account_id', $accountId)
            ->paginate($per_page);
    }

    public function userAllComments($accountId, $per_page = 7, $text = "")
    {

        $comments = Comment::query()
            ->with('posts.account')
            ->whereHas('posts', function ($query) {
                $query->where('source_type', 'person')->orWhere('source_type', 'page');
            })
            ->when($text != "", function ($query) use ($text){
                $query->where('context', 'ilike', '%'.$text.'%');
            })
            ->where('account_id', $accountId)
            ->paginate($per_page);

        return CommentResource::collection($comments);
    }

    public function userAllCommentsWithoutPost($accountId, $per_page = 7, $text = "")
    {
        $comments = Comment::query()
            ->with(['posts', 'posts.account'])
            ->where('account_id', $accountId)
            ->when($text != "", function ($query) use ($text){
                $query->where('context', 'ilike', '%'.$text.'%');
            })
            ->paginate($per_page);

        return CommentResource::collection($comments);
    }

    public function userFacebookFriendsCount($accountId)
    {
        return Cache::remember('user_facebook_friends_count_' . $accountId,
            $this->cacheTime, function () use ($accountId) {
                return FacebookFriend::query()->where('account_id', $accountId)->toBase()->count();
            });
    }

    public function userFacebookFriends($accountId, $request): AnonymousResourceCollection
    {
        $page = $request->page ?? 1;
        $perPage = $request->per_page ?? 12;

        $accounts = FacebookFriend::query()
                ->with('account')
                ->where('account_id', $accountId)
                ->paginate($perPage, ['*'], 'page', $page) ?? [];

        return FacebookAccountResource::collection($accounts);
    }

    public function getUserRealPersonWithCache($accountId)
    {
        return AccountPin::query()
                ->with('person')
                ->where('account_id', $accountId)
                ->first() ?? [];
    }

    public function getUserSharedPhotos($accountId, $request): AnonymousResourceCollection
    {
        $page = $request->page ?? 1;
        $perPage = $request->per_page ?? 12;

        $rows = Media::query()
                ->where('account_id', $accountId)
                ->orderByDesc('processed')
                ->paginate($perPage, ['*'], 'page', $page) ?? [];

        return AccountMediaResource::collection($rows);
    }

    public function statisticBySectionAndDate($accountId, $request)
    {

        $year = $request->year ?? date("Y");

        $from = Carbon::createFromDate($year)->startOfYear();
        $to = Carbon::createFromDate($year)->endOfYear();

        $section = $request->section ?? "user_posts";

        return match ($section) {
            'monitoring_posts' => $this->statisticMonitoringPosts($accountId, $from, $to),
            'monitoring_comments' => $this->statisticMonitoringPostComments($accountId, $from, $to),
            'user_posts' => $this->statisticPosts($accountId, $from, $to),
            'user_comments' => $this->statisticPostComments($accountId, $from, $to),
            default => 'not found section',
        };

    }

    public function statisticMonitoringPosts($accountId, $from, $to): JsonResponse
    {
        $posts = Post::query()
            ->select(DB::raw('EXTRACT(MONTH FROM publish_time) as month'), DB::raw('count(*) as count'))
            ->where('account_id', $accountId)
            ->whereBetween('publish_time', [$from, $to])
            ->groupBy(DB::raw('EXTRACT(MONTH FROM publish_time)'))
            ->get();


        $data = $this->preparingForMonths($posts);

        return response()->json($data);
    }

    public function statisticMonitoringPostComments($accountId, $from, $to): JsonResponse
    {
        $comments = Comment::query()
            ->select(DB::raw('EXTRACT(MONTH FROM publish_time) as month'), DB::raw('count(*) as count'))
            ->where('account_id', $accountId)
            ->whereBetween('publish_time', [$from, $to])
            ->groupBy(DB::raw('EXTRACT(MONTH FROM publish_time)'))
            ->get();

        $data = $this->preparingForMonths($comments);

        return response()->json($data);
    }

    public function statisticPosts($accountId, $from, $to): JsonResponse
    {
        $posts = Post::query()
            ->select(DB::raw('EXTRACT(MONTH FROM publish_time) as month'), DB::raw('count(*) as count'))
            ->where('account_id', $accountId)
            ->whereBetween('publish_time', [$from, $to])
            ->groupBy(DB::raw('EXTRACT(MONTH FROM publish_time)'))
            ->get();

        $data = $this->preparingForMonths($posts);

        return response()->json($data);
    }

    public function statisticPostComments($accountId, $from, $to): JsonResponse
    {
        $comments = Comment::query()
            ->select(DB::raw('EXTRACT(MONTH FROM publish_time) as month'), DB::raw('count(*) as count'))
            ->where('account_id', $accountId)
            ->whereBetween('publish_time', [$from, $to])
            ->groupBy(DB::raw('EXTRACT(MONTH FROM publish_time)'))
            ->get();

        $data = $this->preparingForMonths($comments);

        return response()->json($data);
    }

    private function preparingForMonths($data): array
    {
        $monthlyComments = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthlyComments[] = [
                'month' => $month,
                'count' => $data->firstWhere('month', $month)->count ?? 0
            ];
        }

        return $monthlyComments;
    }

    public function getBio($accountId)
    {
        return FacebookAccount::query()
            ->where('account_id', $accountId)
            ->toBase()
            ->first();
    }



}
