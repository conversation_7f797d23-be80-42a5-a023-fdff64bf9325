<?php

namespace App\Services\SocialHub;

use App\Models\Social\Account;
use App\Models\Social\Comment;
use App\Models\Social\MonitoringComment;
use App\Models\Social\MonitoringPost;
use App\Models\Social\Post;
use App\Models\Social\SocialPage;
use App\Traits\ApiResponsible;
use Illuminate\Support\Facades\Cache;

class SocialStatisticsService
{

    use ApiResponsible;

    //one day
//    private int $cacheTime = 24 * 3600;
    private int $cacheTime = 10 * 60;

    public function getData($platformId)
    {

        $getCounts = [
            'accounts' => $this->accountsCount($platformId),
            'posts' => $this->postCount($platformId),
//            'comments' => $this->commentsCount($platformId),
            'pages' => $this->pagesCount($platformId),
        ];

        return $this->successResponse(200, 'ok', $getCounts);
    }

    private function commentsCount($platformId)
    {

        return Cache::remember("comments_count_" . $platformId, $this->cacheTime, function () use ($platformId) {
            return Comment::query()
                ->whereHas('account', function ($query) use ($platformId) {
                    $query->where('platform_id', $platformId);
                })->count();
        });
    }

    private function postCount($platformId)
    {

        return Cache::remember("posts_count_" . $platformId, $this->cacheTime, function () use ($platformId) {
            return Post::query()->where('platform_id', $platformId)->count();

            //todo  account-a gore platforma ile isleyir. muveqqeti commente alinib

            /*         return MonitoringPost::query()
            //               ->whereHas('account', function ($query) use ($platformId) {
            //                   $query->where('platform_id', $platformId);
                          })->count(); */
        });
    }

    private function accountsCount($platformId)
    {
        return Cache::remember("accounts_count_" . $platformId, $this->cacheTime, function () use ($platformId) {
            return Account::query()->where('platform_id', $platformId)->count();
        });
    }

    private function pagesCount($platformId)
    {
        return Cache::remember("pages_count)" . $platformId, $this->cacheTime, function () use ($platformId) {
            return SocialPage::query()->where('platform_id', $platformId)->count();
        });
    }


}
