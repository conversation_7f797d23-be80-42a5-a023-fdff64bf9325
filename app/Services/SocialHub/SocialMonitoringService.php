<?php

namespace App\Services\SocialHub;

use App\Http\Resources\Social\AccountResource;
use App\Http\Resources\Social\ActiveUserResource;
use App\Http\Resources\Social\SocialPageResource;
use App\Models\Social\Comment;
use App\Models\Social\Post;
use App\Models\Social\SocialPage;
use App\Traits\ApiResponsible;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;
use function Clue\StreamFilter\fun;

class SocialMonitoringService
{

    use ApiResponsible;

    //one day
//    private int $cacheTime = 24 * 3600;
    private int $cacheTime = 10 * 60;

    public function getPageInfo($pageId): SocialPageResource
    {

        $row = SocialPage::query()
            ->with('media', 'following')
            ->where('id', $pageId)
            ->first();


        if ($row !== null) {
            if ($row->following !== null) {
                $row->is_following = true;
                $row->following_id = $row->following->id;
            } else {
                $row->is_following = false;
                $row->following_id = 0;
            }
            unset($row->following);
        }

        return SocialPageResource::make($row);
    }

    public function getPagePosts($pageId, $request): LengthAwarePaginator
    {
        return Post::query()
            ->where('page_id', $pageId)
            ->when($request->filled('text'), function ($query) use($request) {
                $query->where('context', 'ilike', '%'.strip_tags($request->text).'%');
            })
            ->orderByDesc('id')
            ->paginate($request->per_page ?? 12);
    }

    public function getPageActiveUsers($pageId, $request)
    {
        $perPage = $request->per_page ?? 6;
        $currentPage = LengthAwarePaginator::resolveCurrentPage();

        $page = SocialPage::query()->findOrFail($pageId);

        $activeUsers = $page->posts()
            ->with('comments.account')
            ->get()
            ->pluck('comments')
            ->flatten()
            ->groupBy('account_id')
            ->map(function ($comments, $accountId) {
                return [
                    'account_id' => $accountId,
                    'comments_count' => $comments->count(),
                    'account' => $comments->first()->account
                ];
            })
            ->filter(function ($user) {
                return $user['account'] !== null;
            })
            ->sortByDesc('comments_count')
            ->values();

        $paginatedActiveUsers = $activeUsers->slice(($currentPage - 1) * $perPage, $perPage)->values();

        $paginatedResults = new LengthAwarePaginator(
            $paginatedActiveUsers,
            $activeUsers->count(),
            $perPage,
            $currentPage,
            ['path' => LengthAwarePaginator::resolveCurrentPath()]
        );


        return ActiveUserResource::collection($paginatedResults);
    }

}
