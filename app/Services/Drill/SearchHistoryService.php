<?php

namespace App\Services\Drill;

use App\Models\SearchRequests;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Client\Pool;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class SearchHistoryService
{
    private mixed $authorizationHeader;

    public function __construct()
    {
        $username = config('services.drill.username');
        $password = config('services.drill.password');
        $this->authorizationHeader = 'Basic ' . base64_encode("{$username}:{$password}");
    }

    public function buildApacheDrillQuery($requestID, array $filterModel = [], array $polygon = null, array $columns = null, $key = null): string
    {
        if ($key == 'azparking-data') {
            return "SELECT * FROM dfs.root.`{$requestID}` t LIMIT 10000 OFFSET 0";
        }
        $columns = $columns ?: ['name', 'surname', 'father_name', 'birthday', 'pin', 'phone_number'];
        $selectColumns = array_map(function ($col) {
            if ($col === 'coordinates') {
                return "CONVERT_FROM(MAX(t.coordinates), 'UTF8') AS coordinates";
            } else {
                return "MAX(t.$col) AS $col";
            }
        }, $columns);
        $polygonHashCondition = $polygon ? $this->buildPolygonClause($polygon) : '';
        if ($polygon && empty($filterModel)) {
            $selectColumns = array_map(function ($col) {
                if ($col === 'coordinates') {
                    return "CONCAT('[', CONVERT_TO(COLLECT_TO_LIST_VARCHAR(CONVERT_TO(t.signal, 'JSON')), 'JSON'), ']') AS coordinates";
                } else {
                    return "MAX(t.$col) AS $col";
                }
            }, $columns);
            $subQueryColumns = array_map(function ($col) {
                if ($col === 'coordinates') {
                    return "d.coordinates,FLATTEN(CONVERT_FROM(d.coordinates, 'JSON')) AS signal";
                } else {
                    return "d.$col";
                }
            }, $columns);
            return "SELECT " . implode(', ', $selectColumns) . " FROM ( SELECT " . implode(', ', $subQueryColumns) . " FROM dfs.root.`$requestID` d ) AS t WHERE $polygonHashCondition GROUP BY t.pin, t.phone LIMIT 10";
        }
        if ($polygon && !empty($filterModel)) {
            $selectColumns = array_map(function ($col) {
                if ($col === 'coordinates') {
                    return "CONCAT('[', CONVERT_TO(COLLECT_TO_LIST_VARCHAR(CONVERT_TO(t.signal, 'JSON')), 'JSON'), ']') AS coordinates";
                } else {
                    return "MAX(t.$col) AS $col";
                }
            }, $columns);
            $subQueryColumns = array_map(function ($col) {
                if ($col === 'coordinates') {
                    return "d.coordinates,FLATTEN(CONVERT_FROM(d.coordinates, 'JSON')) AS signal";
                }
//                elseif ($col === 'detailed_signal_info') {
//                    return "d.detailed_signal_info AS coordinates, FLATTEN(CONVERT_FROM(d.detailed_signal_info, 'JSON')) AS signal";
//                }
                else {
                    return "d.$col";
                }
            }, $columns);
            $whereClauses = array_filter(array_map(fn($field, $filter) => str_starts_with($field, 'response.')
                    ? $this->buildFilterClause(substr($field, strlen('response.')), (array)$filter)
                    : null,
                    array_keys($filterModel),
                    $filterModel)
            );
            $whereClause = !empty($whereClauses) ? 'WHERE ' . implode(' AND ', $whereClauses) : '';
            return "SELECT " . implode(', ', $selectColumns) . " FROM ( SELECT " . implode(', ', $subQueryColumns) . " FROM dfs.root.`$requestID` d $whereClause ) AS t WHERE $polygonHashCondition GROUP BY t.pin, t.phone LIMIT 10";

        }
        if (!empty($filterModel) && !$polygon) {
            $whereClauses = array_filter(array_map(fn($field, $filter) => str_starts_with($field, 'response.')
                    ? $this->buildFilterClause(substr($field, strlen('response.')), (array)$filter)
                    : null,
                    array_keys($filterModel),
                    $filterModel)
            );
            $whereClause = !empty($whereClauses) ? 'WHERE ' . implode(' AND ', $whereClauses) : '';
            $subQueryColumns = array_map(function ($col) {
                if ($col === 'coordinates') {
                    return "d.coordinates,FLATTEN(CONVERT_FROM(d.coordinates, 'JSON')) AS signal";
                } else {
                    return "d.$col";
                }
            }, $columns);
            return "SELECT " . implode(', ', $selectColumns) . " FROM ( SELECT " . implode(', ', $subQueryColumns) . " FROM dfs.root.`$requestID` d $whereClause ) AS t GROUP BY t.pin, t.phone LIMIT 10";
        }
        return "SELECT " . implode(', ', $selectColumns) . " FROM dfs.root.`{$requestID}` t GROUP BY t.pin , t.phone LIMIT 1000 OFFSET 0";
    }

    private function buildFilterClause(string $fieldName, array $filter): string
    {
        if (isset($filter['conditions'])) {
            $operator = $filter['operator'] ?? 'AND';
            $subClauses = array_map(fn($condition) => $this->buildSingleFilter($fieldName, $condition), $filter['conditions']);
            return '(' . implode(" $operator ", $subClauses) . ')';
        }
        return $this->buildSingleFilter($fieldName, $filter);
    }

    private function buildSingleFilter(string $fieldName, array $filter): string
    {
        $filterType = $filter['filterType'];
        return match ($filterType) {
            'text' => $this->buildTextFilter($fieldName, $filter),
            'number' => $this->buildNumberFilter($fieldName, $filter),
            'date' => $this->buildDateFilter($fieldName, $filter),
            default => '',
        };
    }

    private function buildTextFilter(string $fieldName, array $filter): string
    {
        $filterType = $filter['type'];
        $filterValue = isset($filter['filter']) ? az_upper($filter['filter']) : null;
        return match ($filterType) {
            'equals' => "$fieldName = '$filterValue'",
            'contains' => "$fieldName LIKE '%$filterValue%'",
            'startsWith' => "$fieldName LIKE '$filterValue%'",
            'endsWith' => "$fieldName LIKE '%$filterValue'",
            'notContains' => "$fieldName NOT LIKE '%$filterValue%'",
            'notBlank' => "$fieldName IS NOT NULL",
            'isBlank' => "$fieldName IS NULL",
            default => '',
        };
    }
    private function buildNumberFilter(string $fieldName, array $filter): string
    {
        $filterType = $filter['type'];
        $filterValue = (int)$filter['filter'];
        return match ($filterType) {
            'equals' => "$fieldName = $filterValue",
            'notEqual' => "$fieldName != $filterValue",
            'lessThan' => "$fieldName < $filterValue",
            'lessThanOrEqual' => "$fieldName <= $filterValue",
            'greaterThan' => "$fieldName > $filterValue",
            'greaterThanOrEqual' => "$fieldName >= $filterValue",
            default => '',
        };
    }
    private function buildDateFilter(string $fieldName, array $filter): string
    {
        $filterType = $filter['type'];
        $dateFrom = $filter['dateFrom'];
        $dateTo = $filter['dateTo'] ?? null;
        return match ($filterType) {
            'equals' => "DATE($fieldName) = '$dateFrom'",
            'notEqual' => "DATE($fieldName) != '$dateFrom'",
            'lessThan' => "DATE($fieldName) < '$dateFrom'",
            'lessThanOrEqual' => "DATE($fieldName) <= '$dateFrom'",
            'greaterThan' => "DATE($fieldName) > '$dateFrom'",
            'greaterThanOrEqual' => "DATE($fieldName) >= '$dateFrom'",
            'inRange' => "DATE($fieldName) BETWEEN '$dateFrom' AND '$dateTo'",
            default => '',
        };
    }

    private function buildPolygonClause(array $polygon): string
    {
        $polygonQuery = $this->buildPolygonQuery($polygon);
        $results = DB::connection('clickhouse_radius')->select($polygonQuery);
        if (!empty($results)) {
            $coordinates = array_map(function ($hash) {
                return "({$hash['lon']}, {$hash['lat']})";
            }, $results);
            return "(signal['lon'], signal['lat']) IN (" . implode(', ', $coordinates) . ")";
        } else {
            return "(signal['lon'], signal['lat']) IN ((-1, -1))";
        }
    }

    private function buildPolygonQuery(array $polygon): string
    {
        $wktPolygon = convertPolygonToWKT($polygon);
        return "
            WITH cte_0 AS (
                SELECT 'POLYGON (($wktPolygon))' AS pgn_aswkt_str,
                       readWKTPolygon(pgn_aswkt_str) AS pgn_geom
            ),
            cte_1 AS (
                SELECT pgn_geom,
                       pgn_aswkt_str,
                       arrayJoin(flatten(pgn_geom)) AS corner_point,
                       tupleElement(corner_point, 1, 0) AS _lon_corner_point,
                       tupleElement(corner_point, 2, 0) AS _lat_corner_point
                FROM cte_0
            )
            SELECT DISTINCT ctd.lon, ctd.lat
            FROM cte_1
            CROSS JOIN radius_events.cell_towers_dictionary ctd
            WHERE h3PointDistM(_lat_corner_point, _lon_corner_point, ctd.lat, ctd.lon) <= 0
               OR pointInPolygon((ctd.lon, ctd.lat), cte_1.pgn_geom);
        ";
    }
    public function getResult(string $searchID, int $page = 1, int $perPage = 10): JsonResponse
    {
        $searchRequest = $this->getSearchRequest($searchID);
        if (!$searchRequest) {
            return response()->json(['error' => 'Nəticə tapılmadı']);
        }
        $result = json_decode($searchRequest->result, true);
        $offset = ($page - 1) * $perPage;
        if (is_array($result)) {
            if (array_key_exists('file', $result)) {
                if (!$result['has_data']) {
                    return $this->generateErrorResponse($result);
                }
                return $this->handleFileResult($result['file'], $page, $perPage, $offset, $searchRequest);
            } else {
                return $this->handleMultipleResults($result, $page, $perPage, $offset, $searchRequest);
            }
        }
        return $this->handleSingleResult($result, $page, $perPage, $offset, $searchRequest);

    }
    public function getSearchRequest(string $searchID): Model|Builder|null
    {
        return SearchRequests::query()
            ->where([
                ['id', '=', $searchID],
                ['is_deleted', '=', false],
            ])
            ->whereIn('operation_status', [200, 150])
            ->select('id', 'operation_type', 'operation_result_name', 'operation_status', 'created_at', 'updated_at', 'created_by', 'result')
            ->first();
    }
    private function handleFileResult(string $filePath, int $page, int $perPage, int $offset, $searchRequest): JsonResponse
    {
        $totalRecords = $this->getTotalRecords($filePath);
        $data = $this->fetchData($filePath, $perPage, $offset);
        return $this->generateSuccessResponse($searchRequest, $data, $totalRecords, $page, $perPage, $filePath);
    }
    private function handleMultipleResults(array $result, int $page, int $perPage, int $offset, $searchRequest): JsonResponse
    {
        $totalRecords = 0;
        $responses = Http::pool(function (Pool $pool) use ($result, $offset, $perPage, &$totalRecords) {
            $requests = [];
            foreach ($result as $key => $value) {
                if (is_array($value) && $value['has_data']) {
                    if ($key === 'din-api-result') {
                        $totalRecords = $this->getTotalRecords($value['file']);
                    }
                    $sqlQuery = "SELECT * FROM dfs.root.`{$value['file']}` d LIMIT $perPage OFFSET $offset";
                    $requests[$key] = $pool->as($key)->withHeaders([
                        'Content-Type' => 'application/json',
                        'Authorization' => $this->authorizationHeader
                    ])->post(config('services.drill_query_url'), [
                        'queryType' => 'SQL',
                        'query' => $sqlQuery
                    ]);
                }
            }
            return $requests;
        });
        if ($responses != []) {
            return $this->combineResponses($responses, $totalRecords, $page, $perPage, $searchRequest);
        }
        return $this->generateErrorResponse($result);
    }
    private function handleSingleResult(array $result, int $page, int $perPage, int $offset, $searchRequest): JsonResponse
    {
        if (isset($result['file']) && $result['has_data']) {
            return $this->handleFileResult($result['file'], $page, $perPage, $offset, $searchRequest);
        }

        return $this->generateErrorResponse($searchRequest);
    }
    private function getTotalRecords(string $filePath): int
    {
        $countQuery = "SELECT COUNT(*) as total FROM dfs.root.`{$filePath}` d";
        $countResponse = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => $this->authorizationHeader
        ])->post(config('services.drill_query_url'), [
            'queryType' => 'SQL',
            'query' => $countQuery,
            'options' => [
                'drill.exec.http.rest.errors.verbose' => true
            ]
        ]);
        return $countResponse->json()['rows'][0]['total'] ?? 0;
    }
    private function fetchData(string $filePath, int $perPage, int $offset): array
    {
        $sqlQuery = "SELECT * FROM dfs.root.`{$filePath}` d LIMIT $perPage OFFSET $offset";
        $dataResponse = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Authorization' => $this->authorizationHeader
        ])->post(config('services.drill_query_url'), [
            'queryType' => 'SQL',
            'query' => $sqlQuery
        ]);
        return $dataResponse->json();
    }
    private function combineResponses(array $responses, int $totalRecords, int $page, int $perPage, $searchRequest): JsonResponse
    {
        $combinedData = array_map(function ($response) {
            return $response->successful() ? $response->json() : 'Request failed with status: ' . $response->status();
        }, $responses);

        $dinApiData = $combinedData['din-api-result']['rows'] ?? [];
        $dinApiColumns = $combinedData['din-api-result']['columns'] ?? [];
        $rowsData = $combinedData['azparking-data']['rows'][0]['data']['data']['data'] ?? [];
        $count = $combinedData['azparking-data']['rows'][0]['data']['data']['count'] ?? 0;
        foreach ($rowsData as &$row) {
            if (isset($row['park']['center']['coordinates'])) {
                $centerCoordinates = $row['park']['center']['coordinates'];
                $row['park']['center'] = [
                    'lat' => $centerCoordinates[0],
                    'lon' => $centerCoordinates[1],
                    'type' => 'Point'
                ];
            }

            if (isset($row['park']['coordinates']['coordinates'])) {
                $lineCoordinates = $row['park']['coordinates']['coordinates'];
                $newLineCoordinates = [];
                foreach ($lineCoordinates as $coordinate) {
                    $newLineCoordinates[] = [
                        'lat' => $coordinate[0],
                        'lon' => $coordinate[1]
                    ];
                }
                $row['park']['coordinates'] = $newLineCoordinates;
            }
        }
        if (empty($rowsData) && $count === 0) {
            $azParkingData = [];
        } else {
            $azParkingData = [
                'rows' => array_merge(
                    $rowsData,
                    [['count' => $count]]
                )
            ];
        }
        return $this->generateSuccessResponse($searchRequest, [
            'azparking-data' => $azParkingData,
            'din-api-result' => [
                'rows' => $dinApiData,
                'total' => $totalRecords,
                'columns' => $dinApiColumns,
            ],
        ], $totalRecords, $page, $perPage);
    }
    private function generateSuccessResponse($searchRequest, array $data, int $totalRecords, int $page, int $perPage, string $filePath = null): JsonResponse
    {
        $totalPages = (int)ceil($totalRecords / $perPage);
        $url = url('/api/v1/personal-cabinet/drill/result/show');
        return response()->json(array_merge($data, [
            "operation_status" => $searchRequest->operation_status,
            "current_page" => $page,
            "first_page_url" => "{$url}?page=1&id={$searchRequest->id}",
            "from" => ($page - 1) * $perPage + 1,
            "last_page" => $totalPages,
            "last_page_url" => "{$url}?page={$totalPages}&id={$searchRequest->id}",
            "links" => generatePaginationLinks($page, $totalPages, $url, '', $searchRequest->id),
            "next_page_url" => $page < $totalPages ? "{$url}?page=" . ($page + 1) . "&id={$searchRequest->id}" : null, // 'searchID' yerine 'id'
            "prev_page_url" => $page > 1 ? "{$url}?page=" . ($page - 1) . "&id={$searchRequest->id}" : null, // 'searchID' yerine 'id'
            "per_page" => $perPage,
            "to" => min($totalRecords, $page * $perPage),
            "total" => $totalRecords,
        ]));

    }
    public function generateErrorResponse($searchRequest): JsonResponse
    {
        return response()->json(['message' => 'Nəticə tapılmadı', 'result' => $searchRequest, 'operation_status' => 404]);
    }
    public function generateIntersectQuery(array $resultIds, string $columnName): string
    {
        $searchRequests = $this->getSearchRequestsByIDs($resultIds);
        $results = $searchRequests->map(function ($request) {
            return json_decode($request->result, true);
        });
        $firstFile = null;
        $secondFile = null;
        foreach ($results as $result) {
            if (!is_array(current($result))) {
                $firstFile = $result;
            } else {
                foreach ($result as $key => $value) {
                    if (is_array($value) && isset($value['has_data']) && $value['has_data'] === true) {
                        if ($key !== 'azparking-data') {
                            if ($firstFile === null) {
                                $firstFile = $value;
                            } elseif ($secondFile === null) {
                                $secondFile = $value;
                            }
                        }
                    }
                }
            }
        }
        $extraSelect = ($columnName === 'pin') ? "MAX(d.phone) AS phone" : "MAX(d.pin) AS pin";
        $sqlQuery = "
        SELECT * FROM (
            SELECT d.{$columnName}, MAX(d.name) AS name, MAX(d.surname) AS surname,
                   MAX(d.father_name) AS father_name, MAX(d.count) AS count, MAX(d.coordinates) AS coordinates,
                   {$extraSelect}
            FROM dfs.root.`{$firstFile[key($firstFile)]}` d";

        foreach ($results as $index => $result) {
            if ($results[$index] != $firstFile) {
                if (is_array($result)) {
                    foreach ($result as $key => $value) {
                        if (is_array($value)) {
                            if (isset($value['has_data']) && $value['has_data'] === true && $key !== 'azparking-data') {
                                $fileValue = $value['file'];
                                $sqlQuery .= " JOIN dfs.root.`{$fileValue}` d{$index} ON d{$index}.{$columnName} = d.{$columnName}";
                            }
                        } else {
                            if ($key === 'file') {
                                $sqlQuery .= " JOIN dfs.root.`{$value}` d{$index} ON d{$index}.{$columnName} = d.{$columnName}";
                            }
                        }
                    }
                } else {
                    $key = key($result);
                    $value = $result[$key];
                    $sqlQuery .= " JOIN dfs.root.`{$value}` d{$index} ON d{$index}.{$columnName} = d.{$columnName}";
                }
            }
        }
        $sqlQuery .= " GROUP BY d.{$columnName}) t";
        return $sqlQuery;
    }
    public function getSearchRequestsByIDs(array $resultIds): Collection|array
    {
        return SearchRequests::query()
            ->where('is_deleted', false)
            ->whereIn('id', $resultIds)
            ->get();
    }
    public function getCommonColumns($resultIDs): array|string
    {
        $searchRequests = $this->getSearchRequestsByIDs($resultIDs);
        $results = $searchRequests->map(function ($request) {
            return json_decode($request->result, true);
        });
        $commonColumns = [];
        $decodedColumns = [];
        foreach ($results as $index => $result) {
            if (isset($result['file'])) {
                $filePath = $result['file'];
                $sqlQuery = "DESCRIBE (SELECT * FROM dfs.root.`{$filePath}`)";
                $response = Http::withHeaders([
                    'Content-Type' => 'application/json',
                    'Authorization' => $this->authorizationHeader
                ])->post(config('services.drill_query_url'), [
                    'queryType' => 'SQL',
                    'query' => $sqlQuery
                ]);
                if ($response->successful()) {
                    $parts = explode('saved-results/', $filePath);
                    if (isset($parts[1])) {
                        $afterSavedResults = $parts[1];
                        $idPart = explode('/', trim($afterSavedResults, '/'));
                        $type = reset($idPart);
                        $commonColumns[$index] = [$response->json(), $type];
                    }
                } else {
                    $commonColumns[$index] = ['error' => 'Request failed'];
                }
            } else {
                foreach ($result as $key => $value) {
                    if ($value['has_data'] && $key != 'azparking-data') {
                        $filePath = $value['file'];
                        $sqlQuery = "DESCRIBE (SELECT * FROM dfs.root.`{$filePath}`)";
                        $response = Http::withHeaders([
                            'Content-Type' => 'application/json',
                            'Authorization' => $this->authorizationHeader
                        ])->post(config('services.drill_query_url'), [
                            'queryType' => 'SQL',
                            'query' => $sqlQuery
                        ]);
                        if ($response->successful()) {
                            $commonColumns[$index] = [$response->json(), $key];
                        } else {
                            $commonColumns[$index] = ['error' => 'Request failed'];
                        }
                    }
                }
            }
        }
        if ($commonColumns != []) {
            foreach ($commonColumns as $index => $query) {
                foreach ($query[0]['rows'] as $row) {
                    $decodedData = json_decode($row['json'], true);
                    $schema = $decodedData['graph'][0]['schema']['columns'];
                    $decodedColumns[$index] = [$schema, $query[1]];
                }
            }
        }
        $staticColumns = ['phone', 'pin'];
        $filteredColumns = [];
        if (count($decodedColumns) > 1) {
            $allSchemas = array_column($decodedColumns, 0);
            $commonColumns = $allSchemas[0];
            foreach ($allSchemas as $schema) {
                $commonColumns = array_filter($commonColumns, function ($column) use ($schema) {
                    foreach ($schema as $schemaColumn) {
                        if ($schemaColumn['name'] === $column['name'] && $schemaColumn['type'] === $column['type']) {
                            return true;
                        }
                    }
                    return false;
                });
                if (empty($commonColumns)) {
                    break;
                }
            }
            foreach ($commonColumns as $column) {
                if (in_array($column['name'], $staticColumns)) {
                    $filteredColumns[] = [
                        'name' => $column['name'],
                        'type' => $column['type']
                    ];
                }
            }
        }
        if (!empty($filteredColumns)) {
            return [
                'filtered_columns' => $filteredColumns,
                'decoded_columns' => $decodedColumns
            ];
        }
        return [
            'filtered_columns' => [],
            'decoded_columns' => $decodedColumns,
        ];
    }
    public function deleteResults(array $resultIds): int
    {
        return SearchRequests::whereIn('id', $resultIds)
            ->where('is_deleted', false)
            ->update(['is_deleted' => true]);
    }
}
