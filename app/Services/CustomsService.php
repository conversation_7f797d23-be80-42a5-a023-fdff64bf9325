<?php

namespace App\Services;

use Exception;
use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Http;
use App\Repository\ForeignCustomsRepository;
use Symfony\Component\HttpFoundation\Response;
use App\Http\Resources\SearchForeignCustomsResource;

class CustomsService
{
    use ApiResponsible;

    /**
     * @param \App\Services\PersonService $personService
     * @param \App\Repository\ForeignCustomsRepository $customsRepository
     * @param string $endpoint
     */
    public function __construct(
        protected PersonService $personService,
        protected ForeignCustomsRepository $customsRepository,
        protected string $endpoint = 'http://10.14.70.23:8000/'
    ) {
    }

    /**
     * @throws \JsonException
     */
    public function findFromMilvus(array $params)
    {
        $data = [
            'similarity' => $params['similarity'],
            'max_count' => $params['max_count'],
            'photo' => $params['photo'],
        ];

        $response = Http::timeout(10)->put(config('servers.gpu_api_single') . '/customs/search/foreign', $data);
        return json_decode($response->body(), true, 512, JSON_THROW_ON_ERROR);
    }


    /**
     * @param array $params
     * @param int $perPage
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(array $params, int $perPage): JsonResponse
    {
        try {
            $collection = $this->findFromMilvus($params);
        } catch (Exception $e) {
            return $this->errorResponse($e->getCode(), $e->getMessage());
        }

        if (empty($collection['response']['data'])) {
            return $this->successResponse(Response::HTTP_OK, 'Nothing Found');
        }

        $data = $collection['response']['data'];
        $params['ids'] = array_column($data, 'record_id');
        $similarities = array_column($data, 'distance');
        $similarityByIds = array_combine($params['ids'], $similarities);

        $people = $this->customsRepository->getPeopleFromOracleServer($params, $perPage);

        $people->each(function ($person) use ($similarityByIds) {
            $person->distance = $similarityByIds[$person->id];
        });

        return SearchForeignCustomsResource::collection($people)->response();
    }

    /**
     * @param $params
     * @param int $perPage
     * @return \Illuminate\Http\JsonResponse
     */
    public function paginateByParams($params, int $perPage): JsonResponse
    {
        $people = $this->customsRepository->getPeopleFromOracleServer($params, $perPage);

        return SearchForeignCustomsResource::collection($people)->response();
    }
}
