<?php

namespace App\Services;

use App\Http\Resources\Person\PersonPhoneResource;
use App\Models\Social\Account;
use App\Models\Social\Telegram\Account as TelegramAccount;
use App\Models\FoodOrder;
use App\Models\ForTestingPin;
use App\Models\Person;
use App\Models\PersonPhone;
use App\Models\Phone;
use App\Models\Social\Photo;
use App\Models\Social\Telegram\PhotoDescription;
use App\Models\User;
use App\Repository\PersonDataRepository;
use App\Repository\PersonRepository;
use App\Services\EHDIS\PersonPhoneService;
use App\Services\EHDIS\ProtocolListService;
use App\Services\EHDIS\PunishInfoListService;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use GuzzleHttp\Client;
use Illuminate\Support\Str;

/**
 * @property array $whoUsePhone
 */
class ProfileSearchService extends AbstractService
{
    /**
     * @param PersonDataRepository $personDataRepository
     */

    const RELATIONS = 'relations';
    const PHONES = 'phones';
    const VOEN = 'voenVM';

    public function __construct(
        protected PersonDataRepository $personDataRepository,
    )
    {
        parent::__construct($personDataRepository);
    }

    public function getPersonDataByTypeFromDB(string $pin, string $type): ?Model
    {
        return $this->personDataRepository->getPersonDataByType($pin, $type);
    }

    public function insertPersonData(string $pin, string $type, array $data): Model
    {
        return $this->personDataRepository->insertPersonData($pin, $type, $data);
    }

    public function updatePersonData(string $pin, string $type, array $data): int
    {
        return $this->personDataRepository->updatePersonData($pin, $type, $data);
    }

    private function syncPersonData(string $pin, string $type, $serviceClassName, array $params)
    {

        /**
         * @var $person Model|Person
         */
        $person = $this->getPersonDataByTypeFromDB($pin, $type);

        if (!$person || ($person->updated_at < Carbon::today()->subDays(1))) {
            $response = $serviceClassName::run(...$params);

            if ($response['status'] != 200) {
                return $response;
            }

            if (!$person) {
                $this->insertPersonData($pin, $type, $response);
                return $response;
            }

            $this->updatePersonData($pin, $type, $response);
            return $response;
        }

        // bu kod relations-da olan duplicate datani silmek ucun muveqqeti olaraq yazilib.
        // mongo-da "person_data" collectionunun, "relations" type olanlarina update vermek lazimdir.

        if ($type == self::RELATIONS) {
            $uniqueData['RelationData'] = collect($person->data['data']['RelationData'] ?? [])->unique('pin')->values()->all();

            //TODO: for testing
            if (auth('api')->id()) {

                /**
                 * @var $user User
                 */
                $user = auth('api')->user();
                $user->setRelation('permissions', $user->getAllPermissions());
                $testerUser = $user->checkPermissionTo('permission:users:tester-assign', 'api');
                if ($testerUser) {
                    $forTestingPin = ForTestingPin::query()->get('pin')->pluck('pin')->toArray();
                    $uniqueData['RelationData'] = collect($uniqueData['RelationData'])->filter(function ($item) use ($forTestingPin) {
                        return in_array($item['pin'], $forTestingPin);
                    });
                }
            }


            return [
                'status' => 200,
                'message' => 'OK',
                'data' => $uniqueData,
            ];
        }

        return $person->data;
    }

    public function getPersonPhones(string $pin): JsonResponse
    {
        $serviceClassName = new PersonPhoneService;

        $data = $this->syncPersonData(
            $pin,
            self::PHONES,
            $serviceClassName,
            (array)$pin,
        );

        return PersonPhoneResource::make($data)->response();
    }

    public function searchPersonByMobileNumber($number)
    {
        $mobil_number = substr($number, -9);

        $person = Phone::query()->select('pin')->where('phone', $mobil_number)->first();

        if ($person) {
            $pin = $person->pin;
        } else {
            if (strlen($mobil_number) != 9) {
                $response = Http::post(config('servers.iamas_person') . '/api/home', [
                    'homeNumber' => $number
                ])->json();

                return !empty($response['data']['pin']) ? $response['data']['pin'] : null;
            } else {
                $response = Http::post(config('servers.iamas_person') . '/api/mobile', [
                    'mobileNumber' => $mobil_number
                ])->json();
            }

            $pin = !empty($response['data']['mobileNumbers'][0]['pin']) ? $response['data']['mobileNumbers'][0]['pin'] : null;
        }
        return $pin;

    }

    public function searchPersonsByMobileNumberFromDb($number): \Illuminate\Database\Eloquent\Collection|array
    {

        return Phone::query()
            ->select('people.id',
                'people.name',
                'people.surname',
                'people.father_name',
                'people.pin',
                'people.image',
                'people.doc_serial_number',
                'phones.phone'
            )
            ->join('people', 'people.pin', 'phones.pin')
            ->where('phone', 'like', '%' . $number . '%')
            ->get();

    }

    public function getPhonesByPins($pins = []): \Illuminate\Database\Eloquent\Collection|array
    {

        return Phone::query()
            ->select('people.id',
                'people.name',
                'people.surname',
                'people.father_name',
                'people.pin',
                'people.image',
                'people.doc_serial_number',
                'phones.phone'
            )
            ->join('people', 'people.pin', 'phones.pin')
            ->whereIn('people.pin', $pins)
            ->groupBy(
                'people.id',
                'people.name',
                'people.surname',
                'people.father_name',
                'people.pin',
                'people.image',
                'people.doc_serial_number',
                'phones.phone'
            )
            ->get();

    }

    public function getPersonUsePhones(string $phone): array
    {
        /**
         * @param $phone
         * @var $data PersonPhone
         * @property mixed wherePhone
         */
        $data = PersonPhone::query()->wherePhone($phone)->first();
        if (!$data) {
            return [];
        }
        $data = PersonPhone::query()->wherePin($data->pin)->get()->toBase();

        return collect($data)->map(function ($item) {
            $person_service = new PersonService(new PersonRepository(new Person()));
            $item->whoUsePhone = $person_service->getGallery($item->pin);
            return $item;
        })->toArray();
    }

    public function getPhones(string $phone, $similarPhones = false): array
    {
        /**
         * @var $data Phone
         */
        if ($similarPhones) {
            $data = Phone::query()->where('phone', 'like', '%' . $phone . '%')->first();
        } else {
            $data = Phone::query()->wherePhone($phone)->first();
        }

        if (!$data) {
            return [];
        }
        $data = Phone::query()->wherePin($data->pin)->get()->toBase();
        return collect($data)->map(function ($item) {
            $person_service = new PersonService(new PersonRepository(new Person()));
            $item->whoUsePhone = $person_service->getGallery($item->pin);
            return $item;
        })->toArray();
    }


    //TODO: for pagination
    public function getPersonSocialProfile(string $full_name)
    {
        /**
         * @var $accounts Account
         */
        $accounts = Account::with(['media' => function ($query) {
                $query->select('account_id', 'photo_path as path');
            }])
            ->where(DB::raw('upper(username)'), 'ILIKE', '%' . $full_name . '%');

        if (!request()->has('per_page')) {
            $data = $accounts->get()->toArray();
        } else {
            $data = $accounts->paginate(request()->get('per_page', 10))->toArray();
        }

        return $data;
    }

    public function getPersonSocialFindNamesByPosts(string $full_name)
    {
        $http = Http::asMultipart()
            ->get(config('servers.person_host') . '/find-names/posts/?name=' . $full_name, [
                'name' => $full_name,
            ]);

        return $http->json();
    }

    public function getPersonTelegramSocialFindNamesByPosts(string $full_name)
    {
        return TelegramAccount::query()->with('mainPhoto')
            ->search('fullname', $full_name)
            ->paginate(request()->get('per_page', 10));
    }

    public function getPersonTelegramSocialFindImage(string $full_name, int $page = 10)
    {

        /** @var  $PhotoDescription PhotoDescription */
        $PhotoDescription = PhotoDescription::query()->with('account')
            ->search('az_description_col', $full_name)
            ->orSearch('en_description_col', $full_name)
            ->paginate($page);

        return $PhotoDescription;

    }

    public function getPersonSocialFindPosts(string $full_name, int $page)
    {

        $response = Http::withHeaders([
            'accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post(config('servers.person_host') . '/find-posts-comments/', [
            'page_size' => 100,
            'page_number' => $page,
            'words' => [$full_name],
        ]);
        return $response->json();

    }

    public function getPersonSocialFindPostsByModel(string $full_name, int $page, $per_page): array
    {

        $pageSize = $per_page;
        $pageNumber = $page;
        $words = [$full_name];
        $offset = ($pageNumber - 1) * $pageSize;
        $condition1 = $condition2 = $params = [];
        foreach ($words as $word) {
            $condition1[] = "lower(p.context) LIKE ?";
            $condition2[] = "lower(c.context) LIKE ?";
            $params[] = '%' . strtolower($word) . '%';
            $params[] = '%' . strtolower($word) . '%';
        }

        $whereClause1 = implode(" OR ", $condition1);
        $whereClause2 = implode(" OR ", $condition2);

        $query = "
            WITH cte1 AS (
                SELECT p.id, a.link AS profile_link, LOWER(a.fullname) AS full_name, p.url AS context_link, p.context AS context, 'post' AS context_type
                FROM facebook.page_post p
                LEFT JOIN facebook.accounts a ON p.account_id = a.id
                WHERE $whereClause1
                UNION
                SELECT c.id, a.link AS profile_link, LOWER(a.fullname) AS full_name, d.url AS context_link, c.context AS context, 'comment' AS context_type
                FROM facebook.comments c
                LEFT JOIN facebook.accounts a ON c.account_id = a.id
                LEFT JOIN facebook.page_post d ON c.post_id = d.id
                WHERE $whereClause2
            )
            SELECT c1.*, COUNT(*) OVER() AS total_count
            FROM cte1 c1
            LIMIT $pageSize
            OFFSET $offset;
        ";

        $result = DB::select($query, $params);

        if ($result) {
            $total_count = $result[0]->total_count;
        } else {
            $total_count = 0;
            return ['detail' => 'No data found.'];
        }

        $current_count = min($pageSize, $total_count - $offset);
        $result_dict = array_map(function ($row) {
            return [
                'id' => $row->id,
                'profile_link' => $row->profile_link,
                'full_name' => $row->full_name,
                'context_link' => $row->context_link,
                'context' => $row->context,
                'context_type' => $row->context_type
            ];
        }, $result);

        return [
            'page_size' => $pageSize,
            'page_number' => $pageNumber,
            'total_count' => $total_count,
            'current_count' => $current_count,
            'result' => $result_dict
        ];
    }


    public function getPersonSocialFindImage(string $full_name, int $page, string $lang)
    {

        $response = Http::withHeaders([
            'accept' => 'application/json',
            'Content-Type' => 'application/json',
        ])->post(config('servers.person_host') . '/image-search/', [
//            'page_size' => 100,
//            'language' => "eng",
//            'page_number' => 100,
//            'words' => [$full_name],

            "language" => "en",
            "page_size" => 100,
            "page_number" => 1,
            "words" => [
                $full_name
            ]

        ]);

        return $response->json();

    }


    public function getPersonSocialFindImageModel(string $full_name, int $page, string $lang, $per_page)
    {

        // Extract required parameters
        $pageSize = $per_page;
        $pageNumber = $page;
        $words = [$full_name];

        // Construct where clause
        $whereClause = '';
        $params = [];
        foreach ($words as $word) {
            $whereClause .= "LOWER(d.az_description_col) LIKE ? OR LOWER(d.en_description_col) LIKE ? OR ";
            $params[] = '%' . strtolower($word) . '%';
            $params[] = '%' . strtolower($word) . '%';
        }
        // Remove the trailing ' OR '
        $whereClause = rtrim($whereClause, ' OR ');

        // Construct the base query
        $query = DB::table('facebook.description_table as d')
            ->select('d.id AS id', 'a.link', 'd.path_col', DB::raw('COUNT(*) OVER() as total_count'))
            ->distinct()
            ->leftJoin('facebook.photos as p', 'd.path_col', '=', 'p.path')
            ->leftJoin('facebook.accounts as a', 'p.account_id', '=', 'a.id')
            ->whereRaw($whereClause, $params)
            ->whereNotNull('a.link')
            ->orderBy('d.id', 'desc');

        // Add language-specific conditions
        if ($lang == 'aze') {
            $query->select('d.id AS id', 'a.link', 'd.path_col', 'd.az_description_col');
        } else {
            $query->select('d.id AS id', 'a.link', 'd.path_col', 'd.en_description_col');
        }

        // Execute the query with pagination
        $result = $query->paginate($pageSize);

        $result->through(function ($item) {
            $item->profile_link = $item->link;
            $item->image_path = $item->path_col;
            $item->description = $item->en_description_col ?? $item->az_description_col;
            return $item;
        });

        $results = $result->toArray();

        $results['result'] = $results['data'];
        unset($results['data']);


        // Customize the result
        $result_dict = $results;
        $result_dict['page_size'] = $pageSize;
        $result_dict['page_number'] = $pageNumber;
        $result_dict['current_count'] = count($result->items());
        $result_dict['total_count'] = $results['total'];

        return $result_dict;
    }


    /**
     * @throws GuzzleException
     */
    public function getPersonSocialFindComments(string $full_name)
    {

        $client = new Client();

        $response = $client->post(config('servers.person_host') . '/find-comments/', [
            'headers' => [
                'accept' => 'application/json',
                'Content-Type' => 'application/json'
            ],
            'json' => [$full_name]
        ]);
        $body = $response->getBody()->getContents();
        return json_decode($body, true);
    }


    public function searchVehicleOwnerPin($vehicleNumber)
    {
        try {
            $response_car = Http::withHeaders([
                'System-Id' => '9ae78b036a90487cbb5b04f1',
                'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
            ])
                ->timeout(10)
                ->get(config('endpoint.find-vehicle-by-pin') . strtoupper(trim($vehicleNumber)) . "/" . config('endpoint.find-vehicle-by-pin-pass'))
                ->json();
            if (isset($response_car['vehicle']) && $response_car['vehicle'] != null) {
                return $response_car;
            }
            return false;
        } catch (\Exception $exception) {
            return false;
        }

    }

    public function getLocationByPhone($phone): array
    {
        return [
            'phone' => $phone,
            'locations' => [

                [
                    'lat' => '40.3943105',
                    'long' => '49.8886127',
                    'icon' => config('app.url') . '/ico.png',
                    'date' => '2021-08-10 10:00:00',
                    'name' => 'Bakı, Ələkbər Əliyev küç., ev 3, mən. 21'
                ],
                [
                    'lat' => '40.5990294',
                    'long' => '49.6643598',
                    'icon' => config('app.url') . '/ico.png',
                    'date' => '2021-08-10 10:50:00',
                    'name' => 'Sumqayıt, Nizami küç., ev 3, mən. 21'
                ],
                [
                    'lat' => '40.0865632',
                    'long' => '49.3871958',
                    'icon' => config('app.url') . '/ico.png',
                    'date' => '2021-08-10 15:00:00',
                    'name' => 'Qobustan, Ələkbər Əliyev küç., ev 3, mən. 21'
                ],
                [
                    'lat' => '39.9261952',
                    'long' => '48.8828938',
                    'icon' => config('app.url') . '/ico.png',
                    'date' => '2021-08-10 17:00:00',
                    'name' => 'Şirvan, Ələkbər Əliyev küç., ev 3, mən. 21'
                ],
                [
                    'lat' => '41.3653282',
                    'long' => '48.4774982',
                    'icon' => config('app.url') . '/ico.png',
                    'date' => '2021-08-11 12:00:00',
                    'name' => 'Quba, Ələkbər Əliyev küç., ev 3, mən. 21'
                ],
            ]
        ];

    }

    public function getMultiplePersonLocation($phones): array
    {
        $array = [];
        foreach ($phones as $phone) {
            $array[] = [
                'phone' => $phone,
                'locations' => [
                    [
                        'phone' => $phone,
                        'latitude' => '40.39431' . rand(00, 99),
                        'longitude' => '49.8886' . rand(00, 99),
                        'icon' => config('app.url') . '/icons/telecom.svg',
                        'updated_at' => '2021-08-10 10:00:00',
                        'name' => 'Bakı, Ələkbər Əliyev küç., ev 3, mən. 21'
                    ],
                    [
                        'phone' => $phone,
                        'latitude' => '40.59902' . rand(00, 99),
                        'longitude' => '49.66435' . rand(00, 99),
                        'icon' => config('app.url') . '/icons/telecom.svg',
                        'updated_at' => '2021-08-10 10:50:00',
                        'name' => 'Sumqayıt, Nizami küç., ev 3, mən. 21'
                    ],
                    [
                        'phone' => $phone,
                        'latitude' => '40.08656' . rand(00, 99),
                        'longitude' => '49.38719' . rand(00, 99),
                        'icon' => config('app.url') . '/icons/telecom.svg',
                        'updated_at' => '2021-08-10 15:00:00',
                        'name' => 'Qobustan, Ələkbər Əliyev küç., ev 3, mən. 21'
                    ],
                    [
                        'phone' => $phone,
                        'latitude' => '39.92619' . rand(00, 99),
                        'longitude' => '48.88289' . rand(00, 99),
                        'icon' => config('app.url') . '/icons/telecom.svg',
                        'updated_at' => '2021-08-10 17:00:00',
                        'name' => 'Şirvan, Ələkbər Əliyev küç., ev 3, mən. 21'
                    ],
                    [
                        'phone' => $phone,
                        'latitude' => '41.36532' . rand(00, 99),
                        'longitude' => '48.47749' . rand(00, 99),
                        'icon' => config('app.url') . '/icons/telecom.svg',
                        'updated_at' => '2021-08-11 12:00:00',
                        'name' => 'Quba, Ələkbər Əliyev küç., ev 3, mən. 21'
                    ],
                ]
            ];
        }
        return $array;
    }

    public function getOrderByPhone($phone): array
    {
        return [
            'phone' => $phone,
            [
                'orders' => [
                    [
                        'type' => 'Yemək sifarişi',
                        'date' => '2021-08-10 10:00:00',
                        'source' => 'Bolt'
                    ],
                    [
                        'type' => 'Qan Analizi',
                        'date' => '2021-08-10 10:00:00',
                        'source' => 'Medical Plaza'
                    ],
                    [
                        'type' => 'Covid-19 PCR testi',
                        'date' => '2021-10-10 10:00:00',
                        'source' => 'Medical Plaza'
                    ],
                    [
                        'type' => 'Yemək sifarişi',
                        'date' => '2021-10-11 10:00:00',
                        'source' => 'Bolt'
                    ]
                ]
            ]

        ];
    }

    public function getInsuranceByVehicle(string $number): array
    {
        return [
            'vehicle' => [
                'number' => $number,
                'insurance' => [
                    [
                        'type' => 'Kasko',
                        'date_begin' => '2020-08-10 10:00:00',
                        'date_end' => '2021-08-10 10:00:00',
                        'source' => 'Pasha Insurance'
                    ],
                    [
                        'type' => 'Sığorta',
                        'date_begin' => '2020-07-10 10:00:00',
                        'date_end' => '2021-08-10 10:00:00',
                        'source' => 'Xalq Sığorta'
                    ],
                    [
                        'type' => 'Kasko',
                        'date_begin' => '2021-08-10 10:00:00',
                        'date_end' => '2022-08-10 10:00:00',
                        'source' => 'Pasha Insurance'
                    ],
                    [
                        'type' => 'Sığorta',
                        'date_begin' => '2021-07-10 10:00:00',
                        'date_end' => '2022-08-10 10:00:00',
                        'source' => 'Xalq Sığorta'
                    ],
                    [
                        'type' => 'Kasko',
                        'date_begin' => '2022-08-10 10:00:00',
                        'date_end' => '2023-08-10 10:00:00',
                        'source' => 'Pasha Insurance'
                    ],
                    [
                        'type' => 'Sığorta',
                        'date_begin' => '2022-07-10 10:00:00',
                        'date_end' => '2023-08-10 10:00:00',
                        'source' => 'Xalq Sığorta'
                    ],

                ]
            ]
        ];

    }

    public function getFinesByVehicle(string $pin)
    {
        $serviceClassName = new PunishInfoListService;
        return $this->syncPersonData(
            $pin,
            'fines',
            $serviceClassName,
            (array)$pin,
        );
    }

    public function getProtocolsByPin($pin)
    {
        $serviceClassName = new ProtocolListService;

        return $this->syncPersonData(
            $pin,
            'protocols',
            $serviceClassName,
            (array)$pin,
        );
    }

    public function getVehicleEntered($params)
    {
        return Http::withHeaders([
            'Authorization' => 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ1c2VyX3NzcyIsImlkIjoxOSwiZXhwIjoxOTExODgyNDY2LCJpYXQiOjE2NTI2ODI0NjZ9.G8D4wbBLrrB4gldT2QPfEoTUmhuoxurcDEj61izAojEs-7XcrKfWr7ykfs3c-RuB-LUlo4RmnOKmVM9kLriehg',
            'System-Id' => '9ae78b036a90487cbb5b04f1',
            'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
        ])
            ->get(config('servers.vehicle_second_host') . '/api/vehicle-number-recognition?vehicleNumber=' . $params['carNumber'] . '&dateFrom=' . $params['from'] . '&dateTo=' . $params['to'])
            ->json();


//        return [
//            'number' => $number,
//            'location' => [
//                [
//                    'name' => 'Qusar Otelə Giriş',
//                    'date' => '2022-09-01 12:00:00',
//                    'lat' => '40.' . rand(1000, 9999),
//                    'long' => '49.' . rand(1000, 9999),
//                    'icon' => config('app.url') . '/ico.png',
//                    'source' => 'CCTV',
//                ],
//            ]
//        ];
    }

    public function getMultiVehicleEntered($params): array
    {
        $vehiclesData = [];

        $key = 0;
        foreach ($params as $param) {
            $getVehicleEntered = $this->getVehicleEntered($param);

            if (isset($getVehicleEntered['status']) && $getVehicleEntered['status'] == 404) {
                $vehiclesData[] = 'Not Found';
            } else {
                foreach ($getVehicleEntered as $getVehicle) {
                    $cameraPointer = getCameraPointer($getVehicle['cameraName']);
                    $cameraPointer->vehicleNumber = $getVehicle['vehicleNumber'];
                    $cameraPointer->icon = config('app.url') . '/icon.png';
                    $cameraPointer->image = $this->getVehicleLocationImage($getVehicle['id'], $getVehicle['vehicleNumber']);
                    $cameraPointer->insertDate = $getVehicle['insertDate'];
                    $vehiclesData[$key]['locations'][] = [
                        "id" => $getVehicle['id'],
                        "vehicleNumber" => $getVehicle['vehicleNumber'],
                        "cameraNumber" => $getVehicle['cameraNumber'],
                        "cameraName" => $getVehicle['cameraName'],
                        "insertDate" => $getVehicle['insertDate'],
                        'cameraPointer' => $cameraPointer,
                    ];
                    $vehiclesData[$key]['vehicleNumber'] = $getVehicle['vehicleNumber'];
                }
            }
            $key++;

        }

        return $vehiclesData;
    }

    public function getVehicleLocationImage($id, $carNumber): array
    {
        $getImage = Http::withHeaders([
            'Authorization' => 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJ1c2VyX3NzcyIsImlkIjoxOSwiZXhwIjoxOTExODgyNDY2LCJpYXQiOjE2NTI2ODI0NjZ9.G8D4wbBLrrB4gldT2QPfEoTUmhuoxurcDEj61izAojEs-7XcrKfWr7ykfs3c-RuB-LUlo4RmnOKmVM9kLriehg',
            'System-Id' => '9ae78b036a90487cbb5b04f1',
            'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
        ])
            ->get(config('servers.vehicle_second_host') . "/api/vehicle-number-recognition/get-photo?id=" . $id . "&vehicleNumber=" . $carNumber)
            ->json();

        if (isset($getImage['status']) && $getImage['status'] == 404) {
            return [
                'status' => false,
                'message' => $getImage['message']
            ];
        }


        return [
            'status' => true,
            'image' => $getImage['imageBase64']
        ];


    }

    public function getVehicleImages(string $number): array
    {
        return [
            'number' => $number,
            'location' => [
                [
                    'name' => 'Haciqabul . 12',
                    'image' => 'car.jpeg',
                    'date' => '2022-09-01 12:00:00',
                    'lat' => '40.0350946',
                    'long' => '48.9018575',
                    'icon' => config('app.url') . '/ico.png',
                ],
                [
                    'name' => 'Kurdemir Ələkbər Əliyev 2',
                    'image' => 'car2.jpeg',
                    'date' => '2022-10-12 12:00:00',
                    'lat' => '40.3575136',
                    'long' => '48.1265967',
                    'icon' => config('app.url') . '/ico.png',
                ],
                [
                    'name' => 'Əhməd Rəcəbli 19.65',
                    'image' => 'car3.jpeg',
                    'date' => '2022-10-12 12:00:00',
                    'lat' => '40.4088806',
                    'long' => '49.8615856',
                    'icon' => config('app.url') . '/ico.png',
                ],
            ]
        ];
    }


    public function getPersonUsesPhones(string $fullName): array
    {
        return [
            'name' => $fullName,
            'phones' => [
                [
                    'phone' => '055 423 45 67',
                ],
                [
                    'phone' => '051 623 45 67',
                ],
                [
                    'phone' => '050 723 45 67',
                ],
                [
                    'phone' => '050 823 45 67',
                ]
            ]
        ];
    }


    public function getVoenByPin($veon)
    {
        $serviceClassName = new ProtocolListService;

        return $this->syncPersonData(
            $veon,
            self::VOEN,
            $serviceClassName,
            (array)$veon,
        );
    }

    public function getPersonUsesCars(string $full_name): array
    {
        return [
            'name' => $full_name,
            'cars' => [
                [
                    'model' => 'BMW x5',
                    'plate' => '90–XD–987',
                ],
                [
                    'model' => 'KIA OPTIMA',
                    'plate' => '77-BD-187',
                ],
                [
                    'model' => 'BWM z4',
                    'plate' => '10-EL-981',
                ],

            ]
        ];
    }

    public function getPersonOrders(string $full_name): array
    {


        return [
            'name' => $full_name,
            [
                'orders' => [
                    [
                        'type' => 'Yemək sifarişi',
                        'date' => '2021-08-10 10:00:00',
                        'source' => 'Bolt'
                    ],
                    [
                        'type' => 'Qan Analizi',
                        'date' => '2021-08-10 10:00:00',
                        'source' => 'Medical Plaza'
                    ],
                    [
                        'type' => 'Covid-19 PCR testi',
                        'date' => '2021-10-10 10:00:00',
                        'source' => 'Medical Plaza'
                    ],
                    [
                        'type' => 'Yemək sifarişi',
                        'date' => '2021-10-11 10:00:00',
                        'source' => 'Bolt'
                    ]
                ]
            ]

        ];
    }

    public function getPersonOrdersWithDatabase($type, $param, $pageSize = 10): LengthAwarePaginator
    {

        $orders = FoodOrder::query();

        $orders->when($type == 'phone', function ($query) use ($param) {

            //last 9 digits of phone number
            $param = substr($param, -9);
            return $query->orSearch('phone', $param);
        });

        $orders->when($type == 'name', function ($query) use ($param) {
            return $query->orSearch('name', $param);
        });

        $orders->when($type == 'email', function ($query) use ($param) {
            return $query->orSearch('email', $param);
        });

        $orders->when($type == 'address', function ($query) use ($param) {
            return $query->orSearch('address', $param);
        });

        $orders->when($type == 'date', function ($query) use ($param) {
            return $query->orSearch('order_date', $param);
        });

//        return [
//            'data' => $orders->toSql(),
//            'total' => $orders->getBindings()
//        ];
        return $orders->paginate($pageSize);

    }

    public function getPersonCovidInfo(string $fullName, int $page, int $perPage)
    {
//        $response = Http::withHeaders([
//            'accept' => 'application/json',
//            'Content-Type' => 'application/json',
//        ])->post('***********:87/api/v1/lab_result', [
//            'BeginDate' => "2010-01-01T00:00:00Z",
//            'EndDate' => "2050-01-01T00:00:00Z",
////            "FullName" => $fullName,
//            "Name" => $fullName,
//            "Page" => $page,
//            "Limit" => $perPage
//        ]);
//
//        return $response->json();

        $request = [
            'BeginDate' => "2010-01-01T00:00:00Z",
            'EndDate' => "2050-01-01T00:00:00Z",
            "Name" => $fullName,
            "Page" => $page,
            "Limit" => $perPage
        ];

        $request = request()->merge($request);

        return lab_resultv2($request);
    }

    public function getPersonCovidInfoForPhone(string $phone, int $page, int $perPage)
    {
//        $response = Http::withHeaders([
//            'accept' => 'application/json',
//            'Content-Type' => 'application/json',
//        ])->post('***********:87/api/v1/lab_result', [
//            'BeginDate' => "2010-01-01T00:00:00Z",
//            'EndDate' => "2050-01-01T00:00:00Z",
//            "Phone" => $phone,
//            "Page" => $page,
//            "Limit" => $perPage
//        ]);
//
//        return $response->json();

        $request = [
            'BeginDate' => "2010-01-01T00:00:00Z",
            'EndDate' => "2050-01-01T00:00:00Z",
            "Phone" => $phone,
            "Page" => $page,
            "Limit" => $perPage
        ];

        $request = request()->merge($request);

        return lab_resultv2($request);
    }
}
