<?php

namespace App\Services;

use Illuminate\Http\JsonResponse;
use App\Repository\BasketRepository;
use Illuminate\Database\Eloquent\Collection;
use App\Http\Resources\Basket\BasketResource;

class BasketService extends AbstractJenssegersService
{
    /**
     * @param \App\Repository\BasketRepository $basketRepository
     */
    public function __construct(
        public BasketRepository $basketRepository,
    ) {
        parent::__construct($basketRepository);
    }

    /**
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getListsByParams(array $params): Collection
    {
        return $this->basketRepository->getByParams($params)->get();
    }

    /**
     * @param array $params
     * @param int $limit
     * @return JsonResponse
     */
    public function paginate(array $params, int $limit = 10): JsonResponse
    {
        $baskets = $this->basketRepository
            ->setParams([
                'params' => $params,
            ])
            ->paginate($limit);

        return BasketResource::collection($baskets)->response();
    }
}
