<?php

namespace App\Services\AiProcessing;

use App\Models\MyJob;
use App\Models\Nebula\PersonData;
use App\Models\Person;
use Illuminate\Support\Facades\DB;
use App\Models\Nebula\Mezun;
use App\Models\Nebula\Student;
class GetDataService
{

    public function getPerson($pin)
    {
        $person = Person::select('name', 'surname', 'father_name', 'birthdate', 'pin', 'doc_serial_number', 'doc_number', 'sex', 'marital_status', 'blood_group', 'eye_color', 'height', 'address', 'country', 'city')->where('pin', $pin)->first();

        return $person ?? [];
    }

    public function getSocialMediaComments($pin){

        $comments = DB::connection('pgsql')->table('social_hub.accounts_pin')
        ->select('posts.context as post','comments.context as comment')
        ->where('pin', $pin)
        ->leftJoin('social_hub.accounts', 'social_hub.accounts.id', 'social_hub.accounts_pin.account_id')
        ->leftJoin('social_hub.comments', 'social_hub.comments.account_id', 'social_hub.accounts.id')
        ->leftJoin('social_hub.posts', 'social_hub.posts.id', 'social_hub.comments.post_id')
        ->orderBy('comments.id','DESC')
        ->take(value: 100)
        ->get();

        return $comments;

    }

    public function getPersonEduInfo(string $pin)
    {

        $pin = strtoupper($pin);
        $student = Student::query()
            ->select('soyad', 'ad', 'ata_adi', 'gender', 'b_date', 'educlass', 'edulang', 'esas', 'tabecilik', 'rayon', 'muessise', 'muessise_kodu',
                'sened', 'sv', 'pincode', 'sinifden_sinfe_kecme_status', 'tehsil_muessisesi_qebul')
            ->where('pincode', $pin)
            ->first();

        if (!$student) {
            $student = Mezun::query()
                ->select('soyad', 'ad', 'ata_adi', 'gender', 'b_date', 'educlass', 'edulang', 'esas', 'tabecilik', 'rayon', 'muessise', 'muessise_kodu',
                    'sened', 'sv', 'pincode', 'tehsil_muessisesi_qebul', 'mezun')
                ->where('pincode', $pin)
                ->first();
        }

        return [
            'status' => 'OK',
            'data' => $student
        ];

    }

    public function  getPersonRelations(string $pin)
    {
        try {
            $mongoData = PersonData::where('pin', $pin)
                ->where('type', 'relations')
                ->first();
            return $mongoData['data']['data']['RelationData']?? [];
        } catch (\Throwable $e) {
            return [
                "service_error" => $e->getMessage()
            ];
        }
    }


    public function getPersonMilitary(string $pin)
    {
        try {
            $mongoData = PersonData::where('pin', $pin)
                ->where('type', 'military')
                ->first();

            return $mongoData['data']['data']['response'] ?? [];
        } catch (\Throwable $e) {
            return [
                "service_error" => $e->getMessage()
            ];
        }
    }


    public function getBeeinContact($phones)
    {
        $beein_contact = DB::table('beein_contact_2')->select('phone', 'pin', 'name', 'surname', 'father_name', 'email', 'birthday', 'city', 'address', 'note')->whereIn('phone', $phones)->get();
        return $beein_contact;
    }

    public function getUsedPhoneNumbers($pin)
    {
        return DB::table('phones_real_users')->select('phone', 'pin', 'name', 'surname', 'father_name',)->where('pin', $pin)->pluck('phone')->toArray();
    }

    public function getWorkPlace($pin)
    {
        try {
            $data = MyJob::query()
                ->selectRaw(
                    'WORKPLACE as Name,
                     WORKPLACE as NameNew,
                     POSITION_MANUAL as PositionManual,
                     POSITION as Position,
                     EMPLOYER_NAME as Employer_Name,
                     CON_STATUS  as ConStatus,
                     EMPLOYER_TPN as Tpn,
                     CON_BEGIN_DATE as JobStartDate,
                     CON_END_DATE as ContractEndDate,
                     WORKPLACE_TYPE as WorkPlaceType'
                )
                ->where('PIN', $pin)
                ->orderBy('CON_REG_DATE', 'DESC')
                ->get();

            return $data ?? [];
        } catch (\Throwable $e) {
            return [];
        }
    }


    public function getPersonCrossingBorder(string $passportNumber, $page = 1, $per_page = 10)
    {
        $data = PersonData::where('pin', $passportNumber)
            ->where('type', 'crossing_border')
            ->first()?->data ?? null;

        // $data = ($serviceClassName::run($passportNumber));
        if (isset($data['data']['response']['response']['Result']['Crossing']['actionPerformed'])) {
            $crossing[] = $data['data']['response']['response']['Result']['Crossing'] ?? [];
        } else {
            $crossing = $data['data']['response']['response']['Result']['Crossing'] ?? [];
        }

        $collect = collect($crossing)->values()
            ->sortByDesc('crossId');

        $output = $collect->paginate($per_page, null, $page);

        $x_data = [];
        foreach ($output as $key => $value) {
            $x_data[] = $value;
        }

        return $x_data ?? [];
    }

    public function getPersonForeignPassport(string $pin)
    {
        $mongoData = PersonData::where('pin', $pin)
            ->where('type', 'foreign_passport')
            ->first();

        return $mongoData->data['data']['response']['response']['Result']['PassportNumber'] ?? '';
    }
}
