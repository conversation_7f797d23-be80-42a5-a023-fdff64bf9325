<?php

namespace App\Services;

use App\Http\Resources\Person\CrossingBorderListCollection;
use App\Http\Resources\Person\CrossingBorderResource;
use App\Http\Resources\Person\ForeignPassportResource;
use App\Http\Resources\Person\PersonFinesResource;
use App\Http\Resources\Person\PersonPhoneResource;
use App\Http\Resources\Person\PersonRelationResource;
use App\Http\Resources\Person\PersonTQDKInfoResource;
use App\Http\Resources\Person\PersonUsePhoneResource;
use App\Http\Resources\Person\PersonVehicleResource;
use App\Models\Nebula\Mezun;
use App\Models\Nebula\Student;
use App\Models\Person;
use App\Models\PersonData;
use App\Models\PersonPhone;
use App\Models\SocialHunterCalls;
use App\Models\SocialHunterDpi;
use App\Repository\PersonDataRepository;
use App\Services\EHDIS\CrossingBorderInfoService;
use App\Services\EHDIS\DriverLicenseService;
use App\Services\EHDIS\ForeignPassportInfoService;
use App\Services\EHDIS\PersonInfoService;
use App\Services\EHDIS\PersonMilitaryInfoService;
use App\Services\EHDIS\PersonPhoneService;
use App\Services\EHDIS\PersonRelationsService;
use App\Services\EHDIS\PersonVehicleService;
use App\Services\EHDIS\ProtocolListService;
use App\Services\EHDIS\PunishInfoListService;
use App\Services\EHDIS\TQDKInfoService;
use Carbon\Carbon;
use ClickHouseDB\Client;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use JetBrains\PhpStorm\ArrayShape;
use PhpClickHouseLaravel\RawColumn;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class PersonDataService extends AbstractService
{
    /**
     * @param PersonDataRepository $personDataRepository
     */

    const RELATIONS = 'relations';
    const VEHICLES = 'vehicles';
    const STUDIES = 'studies';
    const PHONES = 'phones';
    const FINES = 'fines';
    const PROTOCOLS = 'protocols';
    const MILITARY = 'military';
    const PERSONAL_INFO = 'personal_info';
    const DRIVER_LICENSE = 'driver_license';
    const FOREIGN_PASSPORT = 'foreign_passport';
    const BORDER_CROSSING = 'crossing_border';

    public function __construct(
        protected PersonDataRepository $personDataRepository,
    )
    {
        parent::__construct($personDataRepository);
    }

    public function getPersonInfo(string $pin): array
    {
        $serviceClassName = new PersonInfoService;

        return $this->syncPersonData(
            $pin,
            self::PERSONAL_INFO,
            $serviceClassName,
            (array)$pin,
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    private function syncPersonData(string $pin, string $type, $serviceClassName, array $params, bool $isForceUpdate = false)
    {
        if ($isForceUpdate) {
            $response = $serviceClassName::run(...$params);
            if ($response['status'] != 200) {
                return $response;
            }
            $this->updatePersonData($pin, $type, $response);
            return $response;
        }
        $cacheKey = $type . ':' . $pin;
        $isUpdatePinCalled = Cache::has($cacheKey) ? Cache::get($cacheKey) : null;
        /** @var  Person $person */
        $person = $this->getPersonDataByTypeFromDB($pin, $type);

        if (!$person ||
            ($person->updated_at < Carbon::today()->subDays(30) ||
                $isUpdatePinCalled)) {
            $response = $serviceClassName::run(...$params);
            if ($response['status'] != 200) {
                return $response;
            }
            if (!$person) {
                $this->insertPersonData($pin, $type, $response);
                return $response;
            }
            $this->updatePersonData($pin, $type, $response);
            return $response;
        }
        // bu kod relations-da olan duplicate datani silmek ucun muveqqeti olaraq yazilib.
        // mongo-da "person_data" collectionunun, "relations" type olanlarina update vermek lazimdir.
        if ($type == self::RELATIONS) {
            $uniqueData['RelationData'] = collect($person->data['data']['RelationData'] ?? [])->unique('pin')->values()->all();

            return [
                'status' => 200,
                'message' => 'OK',
                'debug' => 'Relations data has been updated',
                'data' => $uniqueData,
            ];
        }

        return $person->data;
    }

    public function getPersonDataByTypeFromDB(string $pin, string $type): ?Model
    {
        return $this->personDataRepository->getPersonDataByType($pin, $type);
    }

    public function insertPersonData(string $pin, string $type, array $data): Model
    {
        return $this->personDataRepository->insertPersonData($pin, $type, $data);
    }

    public function updatePersonData(string $pin, string $type, array $data): int
    {
        return $this->personDataRepository->updatePersonData($pin, $type, $data);
    }

    public function getPersonRelations(string $pin): JsonResponse
    {
        //TODO: do it sync from .env for EHDIS or not
        $mongoData = PersonData::where('pin', $pin)
            ->where('type', self::RELATIONS)
            ->first();
        if ($mongoData) {
            return PersonRelationResource::make($mongoData->data)->response();
        }
        return response()->json([
            'status' => 200,
            'message' => 'OK',
            'data' => [
                'data' => []
            ]
        ]);
        $serviceClassName = new PersonRelationsService;
        $data = $this->syncPersonData(
            $pin,
            self::RELATIONS,
            $serviceClassName,
            (array)$pin
        );
        return PersonRelationResource::make($data)->response();
    }

    public function getPersonPhones(string $pin)
    {
        $serviceClassName = new PersonPhoneService;
        $data = $this->syncPersonData(
            $pin,
            self::PHONES,
            $serviceClassName,
            (array)$pin,
            false
        );


        return PersonPhoneResource::make($data)->response();
    }

    public function getPersonUsePhones(string $pin): JsonResponse
    {
        $data = PersonPhone::query()->where('pin', strtoupper($pin))->get()->toBase();

        return PersonUsePhoneResource::collection($data)->response();
    }

    public function getPersonFines(string $pin): JsonResponse
    {
//        $serviceClassName = new PunishInfoListService;
//
//        $data = $this->syncPersonData(
//            $pin,
//            self::FINES,
//            $serviceClassName,
//            (array)$pin
//        );

        $mongoData = PersonData::where('pin', $pin)
        ->where('type', self::FINES)
        ->first();
        if ($mongoData) {
        $data = $mongoData->data['data'];
        $response = [
            "status" => 200,
            "message" => "OK",
            "data" => []
        ];
        if (isset($data['punish']) && is_array($data['punish'])) {
            $modifiedPunish = [];
            foreach ($data['punish'] as $item) {
                $person = [];
                if (isset($item['name'])) {
                    $person['name'] = $item['name'];
                    unset($item['name']);
                }
                if (isset($item['surName'])) {
                    $person['surname'] = $item['surName'];
                    unset($item['surName']);
                }
                if (isset($item['fatherName'])) {
                    $person['patronymic'] = $item['fatherName'];
                    unset($item['fatherName']);
                }
                $item['person'] = $person;
                if (isset($item['protocolNumber'])) {
                    $item['seriesNumber'] = $item['protocolNumber'];
                    unset($item['protocolNumber']);
                }
                if (isset($item['protocolCost'])) {
                    $item['amount'] = $item['protocolCost'];
                    unset($item['protocolCost']);
                }
                if (isset($item['protocolActionDate'])) {
                    $item['decisionDate'] = $item['protocolActionDate'];
                    unset($item['protocolActionDate']);
                }
                $modifiedPunish[] = $item;
            }
            $response["data"] = $modifiedPunish;
        }
        return response()->json($response);
        }

        return response()->json([
            'status' => 200,
            'message' => 'OK',
            'data' => []
        ]);

        //TODO: must be refactored!!!!!!!!!!!!!!!!
        $pin = strtoupper($pin);
        $response = Http::withHeaders([
            'accept' => '*/*',
        ])->post("http://**********:7099/api/Dyp/GetByDypPenaltiesForA2Z?pin={$pin}");
        return response()->json(json_decode($response->json('data'), true));

       //return PersonFinesResource::make($data)->response();
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getPersonProtocols(string $pin): array
    {
        $mongoData = PersonData::where('pin', $pin)
            ->where('type', self::PROTOCOLS)
            ->first();
        if ($mongoData) {
            return $mongoData->data;
        }
        return [
            'data' => []
        ];
        $serviceClassName = new ProtocolListService;

        return $this->syncPersonData(
            $pin,
            self::PROTOCOLS,
            $serviceClassName,
            (array)$pin
        );
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function getPersonCrossingBorder(string $passportNumber, $page = 1, $per_page = 10): array
    {
        $data = PersonData::where('pin', $passportNumber)
            ->where('type', self::BORDER_CROSSING)
            ->first()?->data ?? null;

//        $serviceClassName = new CrossingBorderInfoService;
//
//        $data = $this->syncPersonData(
//            $passportNumber,
//            self::BORDER_CROSSING,
//            $serviceClassName,
//            (array)$passportNumber
//        );

        // $data = ($serviceClassName::run($passportNumber));
         if (isset($data['data']['response']['response']['Result']['Crossing']['actionPerformed'])){
             $crossing[] = $data['data']['response']['response']['Result']['Crossing'] ?? [];
             }else{
             $crossing = $data['data']['response']['response']['Result']['Crossing'] ?? [];
         }

        $collect = collect($crossing)->values()
            ->sortByDesc('crossId');


       $output = $collect->paginate($per_page, null, $page);


       $x_data = [];
       foreach ($output as $key => $value) {
              $x_data[] = $value;
       }

       $Xoutput = [
            'total' => $collect->count(),
            'perPage' => $per_page,
            'currentPage' => $page,
            'lastPage' => $output->lastPage(),
            "path" => $output->path(),
            "prev_page_url" => $output->previousPageUrl(),
            "current_page_url" => $output->url($output->currentPage()),
            "next_page_url" => $output->nextPageUrl(),
            'from' => $output->firstItem(),
            'to' => $output->lastItem(),
        ];

        return [ 'status' => 200,
            'message' => 'OK',
            'data' => [
                'request' => $passportNumber,
                'result' => ['data' => $x_data, 'paginate' => $Xoutput],
                'service' => 'EHDISService'
            ]];


      //  return CrossingBorderResource::make($data)->response();
    }

    public function getPersonDriverLicense(string $pin)
    {
        $mongoData = PersonData::where('pin', $pin)
            ->where('type', self::DRIVER_LICENSE)
            ->first();
            if ($mongoData) {
                $data = $mongoData->data;
                if (isset($data['data']['dlCategory'])) {
                    $data['data']['categories'] = $data['data']['dlCategory'];
                    unset($data['data']['dlCategory']);
                }

                if (isset($data['data']['dlCode'])) {
                    $data['data']['code'] = $data['data']['dlCode'];
                    unset($data['data']['dlCode']);
                }

                if (isset($data['data']['createDate'])) {
                    $data['data']['issueDate'] = $data['data']['createDate'];
                    unset($data['data']['createDate']);
                }

                if (isset($data['data']['endDate'])) {
                    $data['data']['expireDate'] = $data['data']['endDate'];
                    unset($data['data']['endDate']);
                }
                return $data;
            }

        return response()->json([
            'data' => [
                'faultCode' => '0'
            ]
        ]);
        //TODO: must be refactored!!!!!!!!!!!!!!!!
        $pin = strtoupper($pin);
        $response = Http::withHeaders([
            'accept' => '*/*',
        ])->post("http://**********:7099/api/Dyp/GetByDypLicenseForA2Z?pin={$pin}");


        return response()->json(json_decode($response->json('data'), true));




//        $serviceClassName = new DriverLicenseService;
//
//
//        return $this->syncPersonData(
//            $pin,
//            self::DRIVER_LICENSE,
//            $serviceClassName,
//            (array)$pin
//        );
    }

    public function getPersonMilitary(string $pin): array
    {
        $mongoData = PersonData::where('pin', $pin)
            ->where('type', self::MILITARY)
            ->first();
        if ($mongoData) {
            return $mongoData->data;
        }
        return [
            'data' => [
                'response' => [
                    'faultCode' => '0'
                ]
            ]];
        $serviceClassName = new PersonMilitaryInfoService;

        return $this->syncPersonData(
            $pin,
            self::MILITARY,
            $serviceClassName,
            (array)$pin
        );
    }

    public function getPersonForeignPassport(string $pin): JsonResponse
    {
        $mongoData = PersonData::where('pin', $pin)
            ->where('type', self::FOREIGN_PASSPORT)
            ->first();
        if ($mongoData) {
            return ForeignPassportResource::make($mongoData->data)->response();
        }
        return response()->json([
//            'status' => 200,
//            'message' => 'OK',
            'data' => [
                'Result' => [
                    'PassportNumber' => null
                ]
            ]
        ]);
        $serviceClassName = new ForeignPassportInfoService;
        $data = $this->syncPersonData(
            $pin,
            self::FOREIGN_PASSPORT,
            $serviceClassName,
            (array)$pin
        );

        return ForeignPassportResource::make($data)->response();
    }

    public function getPersonVehicles(string $pin, $firstName, $lastName, $fatherName): JsonResponse
    {
        $serviceClassName = new PersonVehicleService;

        $data = $this->syncPersonData(
            $pin,
            self::VEHICLES,
            $serviceClassName,
            [$pin, $firstName, $lastName, $fatherName],
        );

        return PersonVehicleResource::make($data)->response();
    }

    #[ArrayShape(['status' => "int", 'message' => "string", 'data' => "array|mixed"])]
    public function getPersonVehiclesNewService(string $pin): array
    {
        $data = Http::withHeaders([
            'System-Id' => '9ae78b036a90487cbb5b04f1',
            'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
        ])
            ->get(config('servers.vehicle_host') . '/v1/eIntegration/out/StateSecurityServiceWS/getVehicleForSSSByPIN/' . strtoupper($pin) . '/123123')
            ->json();

        return [
            'status' => 200,
            'message' => 'OK',
            'data' => $data ?? []
        ];
    }

    public function getPersonStudies(string $pin): string
    {

        $pin = strtoupper($pin);

        $response = Http::withHeaders([
            'accept' => '*/*'
        ])->get('http://**********:7007/api/Tqdk/TqdkData?pin='.$pin);

        return $response->body();

//        $serviceClassName = new TQDKInfoService;
//        $data = $this->syncPersonData(
//            $pin,
//            self::STUDIES,
//            $serviceClassName,
//            (array)$pin
//        );
//        return PersonTQDKInfoResource::make($data)->response();

    }

    public function getPersonStudentInfo(string $pin)
    {

        $pin = strtoupper($pin);
        $student = Student::query()
            ->select('soyad', 'ad', 'ata_adi', 'gender', 'b_date', 'educlass', 'edulang', 'esas', 'tabecilik', 'rayon', 'muessise', 'muessise_kodu',
                'sened', 'sv', 'pincode', 'sinifden_sinfe_kecme_status', 'tehsil_muessisesi_qebul')
            ->where('pincode', $pin)
            ->first();

        if (!$student) {
            $student = Mezun::query()
                ->select('soyad', 'ad', 'ata_adi', 'gender', 'b_date', 'educlass', 'edulang', 'esas', 'tabecilik', 'rayon', 'muessise', 'muessise_kodu',
                    'sened', 'sv', 'pincode', 'tehsil_muessisesi_qebul', 'mezun')
                ->where('pincode', $pin)
                ->first();
        }

        return [
            'status' => 'OK',
            'data' => $student
        ];


        //return Http::get(config('endpoint.student').'?fin='.$pin)->json();
    }


//    public function getSocialCallLocationInfo($request): JsonResponse
//    {
//        $perPage = $request->input('per_page', 12);
//        $page = $request->input('page', 1);
//        $pin = $request->input('pin');
//        $fromDate = $request->input('from', today()->startOfDay());
//        $toDate = $request->input('to', today()->endOfDay());
//        $fromDateWithoutTime = Carbon::parse($fromDate)->format('Y-m-d');
//        $toDateWithoutTime = Carbon::parse($toDate)->format('Y-m-d');
//
//        $offset = ($page - 1) * $perPage;
//
//        $sql = /** @lang sql */
//            "SELECT
//                r.event_timestamp,
//                r.calling_station_id,
//                c.lat,
//                c.lon,
//                c.operator as operator,
//                c.lac as lac,
//                c.cell_id as cell_id,
//                c.radius,
//                c.beam,
//                c.description,
//                c.name,
//                max(r.framed_ip_address) as framed_ip_address,
//                max(r.`3gpp_rat_type`) as `3gpp_rat_type`,
//                max(r.`3gpp_imsi`) as `3gpp_imsi`,
//                max(r.`3gpp_imeisv`) as `3gpp_imeisv`
//            FROM
//                radius_events.dist_radius_log r
//                JOIN radius_events.cell_towers_dictionary c ON c.hash = r.hash
//            WHERE
//                r.event_timestamp BETWEEN :fromTime AND :toTime
//                AND r.calling_station_id = :pin
//                AND ((c.date_begin <= :from AND (c.date_end IS NULL OR c.date_end = '1970-01-01'))
//                     OR (c.date_begin >= :to AND (c.date_end <= :to OR c.date_end IS NULL OR c.date_end = '1970-01-01')))
//            GROUP BY r.event_timestamp, r.calling_station_id, c.lat, c.lon, c.operator, c.lac, c.cell_id, c.radius, c.beam, c.description, c.name
//            ORDER BY r.event_timestamp DESC
//            LIMIT :limit OFFSET :offset;";
//
//        $results = DB::connection('clickhouse_radius')->select($sql, ['from' => $fromDateWithoutTime, 'to' => $toDateWithoutTime, 'fromTime' => $fromDate, 'toTime' => $toDate, 'pin' => $pin, 'limit' => (int)$perPage, 'offset' => (int)$offset]);
//        if(empty($results))return response()->json(['message' => 'Məlumat tapılmadı.'], 403);
//        $countSql = /** @lang sql */
//            "SELECT COUNT(DISTINCT concat(r.event_timestamp, '_', r.calling_station_id, '_', c.lat, '_', c.lon)) as total
//                 FROM radius_events.dist_radius_log r
//                 JOIN radius_events.cell_towers_dictionary c ON c.hash = r.hash
//                 WHERE r.event_timestamp BETWEEN :fromTime AND :toTime
//                   AND r.calling_station_id = :pin
//                   AND ((c.date_begin <= :from AND (c.date_end IS NULL OR c.date_end = '1970-01-01'))
//                        OR (c.date_begin >= :to AND (c.date_end <= :to OR c.date_end IS NULL OR c.date_end = '1970-01-01')))";
//        $totalCount = DB::connection('clickhouse_radius')->select($countSql, ['from' => $fromDateWithoutTime, 'to' => $toDateWithoutTime, 'fromTime' => $fromDate, 'toTime' => $toDate, 'pin' => $pin])[0]['total'] ?? 0;
//        $lastPage = ceil($totalCount / $perPage);
//
//
//        $baseUrl = url("/api/v1/person/" . $pin . "/social-call=location");
//
//        $response=[
//            "status" => "Success",
//            "data" => [
//                "current_page" => $page,
//                "data" => $results,
//                "first_page_url" => $baseUrl . "?page=1",
//                "from" => (int)$offset + 1,
//                "last_page" => $lastPage,
//                "last_page_url" => $baseUrl . "?page=" . $lastPage,
//                "next_page_url" => $page < $lastPage ? $baseUrl . "?page=" . ($page + 1) : null,
//                "prev_page_url" => $page > 1 ? $baseUrl . "?page=" . ($page - 1) : null,
//                "per_page" => (int)$perPage,
//                "to" => (int)$offset + count($results),
//                "total" => (int)$totalCount,
//            ],
//            "code" => 200,
//            "message" => "response ok"
//        ];
//        if((!$request->get('page') || $request->get('page') == 1)  && $request->get('search_head_name') !== null && $request->get('search_head_name') && !$request->filled('from_Job')){
//            $queue = [
//                'name'=> $request->get('search_head_name') ?? 'manually',
//                'search_params' => $request->all(),
//                'pins' => [],
//                'count' => count($results),
//                'user_id' => auth('api')->user()->id,
//                'search_type' => 'get_social_call_location',
//                'search_tab' => [[
//                    'value' => 'get_social_call_location',
//                    'name' => 'tab',
//                    'label' => 'Trayektoriyalar (Telefon nömrəsi ilə)',
//                ]],
//                'search_text' => 'get_social_call_location',
//            ];
//            Log::emergency('SaveSocialCallLocation: '.json_encode($queue));
//            SaveAdvanceSearchLogsJob::dispatchSync($queue);
//        }
//        return response()->json($response);
//    }

    public function getSocialCallInfo(string $pin)
    {
//        if (!in_array($pin, [
//            '994502560816',
//            '994509956663',
//            '994502810415',
//            '994502782268',
//            '994505923818',
//            '994509641420',
//            '994516299674',
//        ]) && config('endpoint.social_calls_enabled') ) {
//            return collect([]);
//        }

        $logData = [
            "description" => $pin . " - nömrəli sosial zəngi açdı",
            "type" => "sosial_call",
        ];
        sendRequestLogToAudit($logData, "audit");


        $perPage = request('per_page', 10);
        $page = request('page', 1);

        $offset = ($page - 1) * $perPage;

        try {


            if (request('filter') == 'both') {

                $query = SocialHunterCalls::select([
                    new RawColumn("dictGet('packet_app.ip_info_dict', 'iso', tuple(ip1))", "country1"),
                    new RawColumn("dictGet('packet_app.ip_info_dict', 'iso', tuple(ip2))", "country2"),
                    'msisdn1', 'msisdn2', 'start', 'duration', 'attributes', 'operator1', 'operator2', 'ip1', 'ip2'
                ])
                    ->where(function ($query) use ($pin) {
                        $query->where('msisdn1', $pin)
                            ->where('msisdn2', '!=', '')
                            ->where('msisdn2', '!=', 'server');
                    })
                    ->orWhere(function ($query) use ($pin) {
                        $query->where('msisdn2', $pin)
                            ->where('msisdn2', '!=', '')
                            ->where('msisdn2', '!=', 'server');
                    })
                    ->orderBy('start', 'DESC');
            } else {
                $query = SocialHunterCalls::select([new RawColumn("dictGet('packet_app.ip_info_dict', 'iso', tuple(ip1))", "country1"), new RawColumn("dictGet('packet_app.ip_info_dict', 'iso', tuple(ip2))", "country2"), 'msisdn1', 'msisdn2', 'start', 'duration', 'attributes', 'operator1', 'operator2', 'ip1', 'ip2'])
                    ->where('msisdn1', $pin)
                    ->orWhere(function ($query) use ($pin) {
                        $query->where('msisdn2', $pin);
                    })
                    ->orderBy('start', 'DESC');
            }

            $calls = $query->limit($perPage, $offset)->getRows();

            $data = clickhouseCallMap($calls);

            if (request('filter') == 'both') {
                $countQuery = /** @lang sql */
                    "SELECT count(*) as total FROM  calls WHERE ((msisdn1 = :pin  AND msisdn2 != '' AND msisdn2 != 'server' AND msisdn2 IS NOT NULL)  OR (msisdn2 = :pin  AND msisdn2 != '' AND msisdn2 != 'server' AND msisdn2 IS NOT NULL)) ";
            } else {
                $countQuery = /** @lang sql */
                    "SELECT count(*) as total FROM calls WHERE msisdn1 = :pin OR msisdn2 = :pin";
            }


            $totalCountResult = DB::connection('clickhouse')->select($countQuery, ['pin' => $pin]);

            $totalCount = $totalCountResult[0]['total'] ?? 1;

            $lastPage = ceil($totalCount / $perPage);
            $baseUrl = url("/api/v1/person/" . $pin . "/social-call");


            return [
                "status" => "Success",
                "data" => [
                    "current_page" => $page,
                    "data" => $data,
                    "first_page_url" => $baseUrl . "?page=1",
                    "from" => ($page - 1) * $perPage + 1,
                    "last_page" => $lastPage,
                    "last_page_url" => $baseUrl . "?page=" . $lastPage,
                    "links" => generatePaginationLinks($page, $lastPage, $baseUrl),
                    "next_page_url" => $page < $lastPage ? $baseUrl . "?page=" . ($page + 1) : null,
                    "path" => $baseUrl,
                    "per_page" => $perPage,
                    "prev_page_url" => $page > 1 ? $baseUrl . "?page=" . ($page - 1) : null,
                    "to" => min(($page - 1) * $perPage + $perPage, $totalCount),
                    "total" => $totalCount
                ],
                "code" => 200,
                "message" => "response ok"
            ];

        } catch (\Exception $e) {


            $query = SocialHunterCalls::select([new RawColumn("id", "country1"), new RawColumn("id", "country2"), 'msisdn1', 'msisdn2', 'start', 'duration', 'attributes', 'operator1', 'operator2', 'ip1', 'ip2'])
                ->where(function ($query) use ($pin) {
                    $query->where('msisdn1', $pin)
                        ->where('msisdn2', '!=', '')
                        ->where('msisdn2', '!=', 'server');
                })
                ->orWhere(function ($query) use ($pin) {
                    $query->where('msisdn2', $pin)
                        ->where('msisdn2', '!=', '')
                        ->where('msisdn2', '!=', 'server');

                })
//                ->orWhere('msisdn2', $pin)
                ->orderBy('start', 'DESC');


            $calls = $query->limit($perPage, $offset)
                ->getRows();


            $data = clickhouseCallMap($calls);

            $countQuery = /** @lang sql */
                "SELECT count(*) as total FROM calls WHERE msisdn1 = :pin OR msisdn2 = :pin";

            if (request('filter') == 'both') {
                $countQuery = /** @lang sql */
                    "SELECT count(*) as total FROM calls WHERE (msisdn1 = :pin OR msisdn2 = :pin) AND (msisdn2 != '' OR msisdn2 != 'server')";
            }

            $totalCountResult = DB::connection('clickhouse')->select($countQuery, ['pin' => $pin]);

            $totalCount = $totalCountResult[0]['total'] ?? 1;

            $lastPage = ceil($totalCount / $perPage);
            $baseUrl = url("/api/v1/person/" . $pin . "/social-call");


            return [
                "status" => "Success",
                "data" => [
                    "current_page" => (int)$page,
                    "data" => $data,
                    "first_page_url" => $baseUrl . "?page=1",
                    "from" => ($page - 1) * $perPage + 1,
                    "last_page" => $lastPage,
                    "last_page_url" => $baseUrl . "?page=" . $lastPage,
                    "links" => generatePaginationLinks($page, $lastPage, $baseUrl),
                    "next_page_url" => $page < $lastPage ? $baseUrl . "?page=" . ($page + 1) : null,
                    "path" => $baseUrl,
                    "per_page" => (int)$perPage,
                    "prev_page_url" => $page > 1 ? $baseUrl . "?page=" . ($page - 1) : null,
                    "to" => min(($page - 1) * $perPage + $perPage, $totalCount),
                    "total" => (int)$totalCount
                ],
                "code" => 200,
                "message" => "response ok"
            ];
        }

    }

//    public function getSocialCallLocationWithOthersNew($request): JsonResponse
//    {
//        $request->validate([
//            'from' => 'required|date_format:Y-m-d H:i:s',
//            'to' => 'required|date_format:Y-m-d H:i:s'
//        ],
//            [
//                'from.required' => 'Başlanğıc tarix boşdur.',
//                'from.date_format' => 'Başlanğıc tarixin formatı səhvdir.',
//                'to.required' => 'Son tarix boşdur.',
//                'to.date_format' => 'Son tarixin formatı səhvdir.',
//            ]
//        );
//        $pin = $request->input('pin');
//        $phoneRegex = '/^994[1-9][0-9]{8}$/';
//        $isPhoneNumber = preg_match($phoneRegex, $pin);
//        if (!$isPhoneNumber) {
//            return response()->json([
//                'error' => 'Telefon nömrəsinin formatı uyğun deyil. Format : 994XXXXXXXXX kimi olmalıdır'
//            ], 422);
//        };
//        $optionalValidationRules = [];
//        $optionalFields = ['top', 'delta_time'];
//        foreach ($optionalFields as $field) {
//            if ($request->has($field) && !is_null($request->input($field))) {
//                $optionalValidationRules[$field] = 'integer';
//            }
//        }
//        if ($optionalValidationRules) {
//            $request->validate($optionalValidationRules);
//        }
//        ini_set('memory_limit', -1);
//        $given = $pin;
//        $start_date = $request->input('from', '2024-05-15 09:00:00');
//        $end_date = $request->input('to', '2024-05-15 23:00:00');
//        $response_count = (int)$request->input('top', 20);
//        $small_radius = (float)$request->input('min_radius', 1.0);
//        $big_radius = (float)$request->input('max_radius', 4.0);
//        if ($small_radius <= 0 || $big_radius <= 0) {
//            return response()->json([
//                'message' => 'Radius dəyərləri mənfi ola bilməz.'
//            ], 400);
//        }
//        $delta_time = (int)$request->input('delta_time', 100);
//        $perPage = (int)$request->input('per_page', 10);
//        $page = (int)$request->input('page', 1);
//        $offset = ($page - 1) * $perPage;
//        try {
//            $data = findSimilarity((int)$given, $start_date, $end_date, $response_count, $small_radius, $big_radius, $delta_time);
//            if (empty($data['data'])) {
//                return response()->json(["message" => "Oxşar nömrələr tapılmadı"], 404);
//            }
//            $phoneNumbers = array_map(function ($item) {
//                return $item[0];
//            }, $data['data']);
//            $phoneNumbersString = implode(',', $phoneNumbers);
//            $sql = "SELECT DISTINCT groupUniqArray((t.lon, t.lat, t.hash)) OVER (
//            PARTITION BY calling_station_id
//            ORDER BY event_timestamp ASC
//            ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING
//        ) AS signal_records,
//        d.calling_station_id
//    FROM radius_events.dist_radius_log d
//    JOIN radius_events.cell_towers_dictionary t ON t.hash = d.hash
//    WHERE event_timestamp >= '$start_date'
//        AND event_timestamp <= '$end_date'
//        AND calling_station_id IN ($phoneNumbersString)
//    LIMIT $perPage OFFSET $offset";
//            $data = DB::connection('clickhouse_radius')->select($sql);
//            $data = collect($data)->map(function ($item) {
//               return collect($item['signal_records'])->map(function ($subItem) {
//                  return [
//                      'lat' => $subItem[1],
//                      'lon' => $subItem[0],
//                      'hash' => $subItem[2],
//                  ];
//               })->mergeRecursive(['calling_station_id' => $item['calling_station_id']]);
//            });
//            $totalCount = count($data);
//            $lastPage = (int)ceil($totalCount / $perPage);
//            $url = url('/api/v1/person/' . $given . '/social-call-location-with-others');
//            $response = [
//                "status" => "Success",
//                "data" => [
//                    "current_page" => $page,
//                    "data" => $data,
//                    "first_page_url" => $url . "?page=1",
//                    "from" => $offset + 1,
//                    "last_page" => $lastPage,
//                    "last_page_url" => $url . "?page=" . $lastPage,
//                    "links" => generatePaginationLinks($page, $lastPage, $url),
//                    "next_page_url" => $page < $lastPage ? $url . "?page=" . ($page + 1) : null,
//                    "prev_page_url" => $page > 1 ? $url . "?page=" . ($page - 1) : null,
//                    "per_page" => $perPage,
//                    "to" => min($totalCount, ($page - 1) * $perPage + $perPage),
//                    "total" => $totalCount,
//                ],
//                "code" => 200,
//                "message" => "response ok",
//            ];
//            if((!$request->get('page') || $request->get('page') == 1)  && $request->get('search_head_name') !== null && $request->get('search_head_name') && !$request->filled('from_Job')){
//                $queue = [
//                    'name'=> $request->get('search_head_name') ?? 'manually',
//                    'search_params' => $request->all(),
//                    'pins' => [],
//                    'count' => count($data),
//                    'user_id' => auth('api')->user()->id,
//                    'search_type' => 'similarity_by_number',
//                    'search_tab' => [[
//                        'value' => 'similarity_by_number',
//                        'name' => 'tab',
//                        'label' => 'Oxşar trayektoriyalar (Telefon nömrəsi ilə)',
//                    ]],
//                    'search_text' => 'similarity_by_number',
//                ];
//                Log::emergency('SaveSimilarityByNumber: '.json_encode($queue));
//                SaveAdvanceSearchLogsJob::dispatchSync($queue);
//            }
//
//            return response()->json($response);
//        } catch (\Exception $e) {
//            return response()->json([
//                'error' => 'An error occurred while processing your request.',
//                'msg' => $e->getMessage()
//            ], 422);
//        }
//    }

    public function getSocialCallLocationWithOthers($pin): JsonResponse
    {
        ini_set('memory_limit', -1);
        $given = $pin;
        $start_date = request('from', '2024-04-10 00:00:00');
        $end_date = request('to', '2024-04-20 23:59:59');


//        $query = "WITH main AS (SELECT lat, lon, hash FROM radius_events.cell_towers_dictionary as c WHERE c.hash IN (
//                SELECT hash FROM radius_events.dist_radius_log as r WHERE r.calling_station_id='$given' AND r.event_timestamp between '$start_date' AND '$end_date'
//            )),
//            needed_towers AS (
//                SELECT distinct c.hash FROM radius_events.cell_towers_dictionary as c, main as m WHERE
//                6371.0 * acos(cos(pi() * (90.0 - c.lat) / 180.0) * cos(pi() * (90.0 - m.lat) / 180.0) + sin(pi() * (90.0 - c.lat) / 180.0) * sin(pi() * (90.0 - m.lat) / 180.0) * cos(pi() * (c.lon - m.lon) / 180.0)) <= 1.0
//            )
//            SELECT calling_station_id, event_timestamp, lat, lon FROM radius_events.dist_radius_log as r, radius_events.cell_towers_dictionary as c WHERE r.event_timestamp between '$start_date' AND '$end_date' AND r.hash IN (SELECT hash FROM needed_towers) AND r.hash=c.hash;";
//
//        $results = DB::connection('clickhouse_radius')->select($query) ?? [];

        $start = 0;
        $step = 500000;
        $result = [];
        while (true) {
            $query = "WITH main AS (SELECT lat, lon, hash FROM radius_events.cell_towers_dictionary as c WHERE c.hash IN (
                SELECT hash FROM radius_events.dist_radius_log as r WHERE r.calling_station_id='$given' AND r.event_timestamp between '$start_date' AND '$end_date'
            )),
            needed_towers AS (
                SELECT distinct c.hash FROM radius_events.cell_towers_dictionary as c, main as m WHERE
                6371.0 * acos(cos(pi() * (90.0 - c.lat) / 180.0) * cos(pi() * (90.0 - m.lat) / 180.0) + sin(pi() * (90.0 - c.lat) / 180.0) * sin(pi() * (90.0 - m.lat) / 180.0) * cos(pi() * (c.lon - m.lon) / 180.0)) <= 1.0
            )
            SELECT DISTINCT calling_station_id, event_timestamp, lat, lon FROM radius_events.dist_radius_log as r, radius_events.cell_towers_dictionary as c, needed_towers WHERE r.event_timestamp between '$start_date' AND '$end_date'
            -- AND r.hash IN (SELECT hash FROM needed_towers)
            AND r.hash=needed_towers.hash
            AND r.hash=c.hash LIMIT $step OFFSET $start;";

            // print "fecth start: ".date('H:i:s').PHP_EOL;

            $start += $step;
            $res = DB::connection('clickhouse_radius')->select($query) ?? [];
            $result = array_merge($result, $res);
            $count = count($res);
            // print "count: $count". PHP_EOL;
            // print "fecth end: ".date('H:i:s').PHP_EOL;
            if ($count < $step) {
                break;
            }
        }

        // $data = processData($results);
        $data = findSimilarNumberTrajectory($result, $given);

        return response()->json([
            "status" => "Success",
            "data" => $data,
            "code" => 200,
            "message" => "response ok"
        ]);


    }

    public function getSocialCallInfo2(string $pin): Collection
    {
        if (!in_array($pin, [
                '994502560816',
                '994509956663',
                '994502810415',
                '994502782268',
                '994505923818',
                '994509641420',
                '994516299674',
            ]) && config('endpoint.social_calls_enabled')) {
            return collect([]);
        }

        //$calls =  SocialHunterCalls::select(DB::raw("calls.*,dictGet('packet_app.ip_info_dict', 'iso', tuple(ip1)) AS country,dictGet('packet_app.ip_info_dict', 'iso', tuple(ip2)) AS country_2"))->where('msisdn1',$pin)
        $calls = SocialHunterCalls::where('msisdn1', $pin)
            ->orWhere('msisdn2', $pin)
            ->orderBy('start', 'DESC')
            ->getRows();


        return collect($calls)->map(function ($item) {
            $attributes = json_decode($item['attributes'], true);
            $attributes = current($attributes);
            if ($attributes == '1') {
                $item['attributes'] = 'whatsapp';
            } elseif ($attributes == '2') {
                $item['attributes'] = 'telegram';
            } elseif ($attributes == '3') {
                $item['attributes'] = 'signal';
            } elseif ($attributes == '4') {
                $item['attributes'] = 'skype';
            } elseif ($attributes == '5') {
                $item['attributes'] = 'instagram';
            } elseif ($attributes == '6') {
                $item['attributes'] = 'threema';
            } elseif ($attributes == '7') {
                $item['attributes'] = 'wechat';
            } elseif ($attributes == '8') {
                $item['attributes'] = 'viber';
            } elseif ($attributes == '0') {
                $item['attributes'] = 'Məlumat yoxdur';
            }

            $operator1 = $item['operator1'];
            if ($operator1 == '5') {
                $item['operator1'] = 'Azercell';
            } else {
                $item['operator1'] = 'Məlumat yoxdur';
            }
            $operator2 = $item['operator2'];
            if ($operator2 == '5') {
                $item['operator2'] = 'Azercell';
            } else {
                $item['operator2'] = 'Məlumat yoxdur';
            }

            return $item;
        });

    }


    public function getSocialDip(): array
    {

//        $logData = [
//            "description" => $pin . " - nömrəli sosial zəngi açdı",
//            "type" => "sosial_call",
//        ];

//        sendRequestLogToAudit($logData, "audit");

        $filters = request('filters', []);
        $perPage = request('per_page', 10);
        $page = request('page', 1);
        $offset = ($page - 1) * (int)$perPage;

        try {

            $hasLocationFilter = false;
            $eventTimestampStart = Carbon::now()->firstOfMonth()->toDateTimeString();
            $eventTimestampEnd = Carbon::now()->endOfMonth()->toDateTimeString();

            foreach ($filters['rules'] ?? [] as $rule) {
                if ($rule['field'] === 'location') {
                    $hasLocationFilter = true;
                }
                if ($rule['field'] === 'start_time') {
                    $eventTimestampStart = Carbon::parse($rule['value'])->toDateTimeString();
                }
                if ($rule['field'] === 'end_time') {
                    $eventTimestampEnd = Carbon::parse($rule['value'])->toDateTimeString();
                }

            }

            if ($hasLocationFilter && $eventTimestampStart && $eventTimestampEnd) {
                $filter_rules = $filters['rules'] ?? [];
                $filter_rules = array_filter($filter_rules, function ($rule) {
                    return $rule['field'] === 'location';
                });

                $polygonPoints = $filter_rules[0]['value'];
                if (is_string($polygonPoints)) {
                    $polygonPoints = json_decode($polygonPoints, true);
                }
                if (!is_array($polygonPoints)) {
                    throw new \Exception('Invalid polygon points');
                }
                $formattedPoints = array_map(function ($point) {
                    return sprintf('(%f, %f)', $point['lng'], $point['lat']);
                }, $polygonPoints);
                $polygonString = implode(', ', $formattedPoints);

                $cteQuerySub = OptimizedQueryBuilder::apply(query: '', filter: $filters, raw: 'raw');

                $cteQuery = "WITH sig as (
                            SELECT CAST(calling_station_id AS VARCHAR) as calling_station_id, event_timestamp, lat, lon
                            FROM radius_events.dist_radius_log as l, radius_events.cell_towers_dictionary as d
                                WHERE match(calling_station_id, '^[0-9]+$') AND l.hash=d.hash AND event_timestamp BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd'
                                AND pointInPolygon((lon, lat), [$polygonString])
                        ),
                        dpi as (
                            SELECT dpi.* FROM radius_events.dist_dpi_5tuples_log as dpi
                                WHERE match(subscriber_id, '^[0-9]+$') AND (dpi.start_time BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd'
                                        OR dpi.end_time BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd' )
                                        $cteQuerySub

                        )

                        SELECT dpi.* FROM sig, dpi
                        WHERE sig.calling_station_id=dpi.subscriber_id AND
                            sig.event_timestamp BETWEEN toStartOfMinute(dpi.start_time - (dpi.start_time % 5) * 60) AND
                              toStartOfMinute(dpi.end_time + (dpi.end_time % 5) * 60)";


                $cteQuery .= " ORDER BY dpi.start_time DESC LIMIT $perPage OFFSET $offset";


                /** @var Client $db */
                $db = DB::connection('clickhouse_radius')->getClient();
                $statement = $db->select($cteQuery);
                $data = $statement->rows();


                $cteQuerySub = OptimizedQueryBuilder::apply(query: '', filter: $filters, raw: 'raw');
                $cteQueryTotal = "WITH sig as (
                            SELECT CAST(calling_station_id AS VARCHAR) as calling_station_id, event_timestamp, lat, lon
                            FROM radius_events.dist_radius_log as l, radius_events.cell_towers_dictionary as d
                                WHERE match(calling_station_id, '^[0-9]+$') AND l.hash=d.hash AND event_timestamp BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd'
                                AND pointInPolygon((lon, lat), [$polygonString])
                        ),
                        dpi as (
                            SELECT dpi.* FROM radius_events.dist_dpi_5tuples_log as dpi
                                WHERE match(subscriber_id, '^[0-9]+$') AND (dpi.start_time BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd'
                                        OR  dpi.end_time BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd' )
                                        $cteQuerySub

                        )
                        SELECT count(1) as count FROM sig, dpi
                        WHERE sig.calling_station_id=dpi.subscriber_id AND
                            sig.event_timestamp BETWEEN toStartOfMinute(dpi.start_time - (dpi.start_time % 5) * 60) AND
                              toStartOfMinute(dpi.end_time + (dpi.end_time % 5) * 60)";

                $statementTotal = $db->select($cteQueryTotal);
                $statementCount = $statementTotal->rows();


            } else {
                $query = SocialHunterDpi::select();
                $query->whereRaw('match(subscriber_id, \'^[0-9]+$\' )');
                if (!empty($filters)) {
                    OptimizedQueryBuilder::apply($query, $filters);
                }
                $query->orderBy('start_time', 'DESC');
                $countQuery = $query->getCountQuery()->getRows();
                $data = $query->limit($perPage, $offset)->getRows();
                $statementCount = $countQuery;


            }


            $totalCount = $statementCount[0]['count'] ?? 1;
            $lastPage = ceil($totalCount / $perPage);
            $baseUrl = url("/api/v1/social-dip/social-dip");


            return [
                "status" => "Success",
                "data" => [
                    "current_page" => (int)$page,
                    "data" => $data,
                    "first_page_url" => $baseUrl . "?page=1",
                    "from" => ($page - 1) * $perPage + 1,
                    "last_page" => $lastPage,
                    "last_page_url" => $baseUrl . "?page=" . $lastPage,
                    "links" => generatePaginationLinks($page, $lastPage, $baseUrl),
                    "next_page_url" => $page < $lastPage ? $baseUrl . "?page=" . ($page + 1) : null,
                    "path" => $baseUrl,
                    "per_page" => (int)$perPage,
                    "prev_page_url" => $page > 1 ? $baseUrl . "?page=" . ($page - 1) : null,
                    "to" => min(($page - 1) * $perPage + $perPage, $totalCount),
                    "total" => (int)$totalCount
                ],
                "code" => 200,
                "message" => "response ok"
            ];


        } catch (\Exception $e) {

            return [
                "status" => "Success",
                "data" => [],
                "code" => 200,
                "message" => "response ok",
                "error" => $e->getMessage(),
                "line" => $e->getLine(),
                "file" => $e->getFile()
            ];

        }

    }

    /**
     * @throws \Exception
     */
    public function getSocialDipLocation(): array
    {

        $filters = request('filters', []);

        try {

            $hasLocationFilter = false;
            $eventTimestampStart = Carbon::now()->subHour()->toDateTimeString();
            $eventTimestampEnd = Carbon::now()->toDateTimeString();

            foreach ($filters['rules'] ?? [] as $rule) {

                if ($rule['field'] === 'location') {
                    $hasLocationFilter = true;
                }

                if ($rule['field'] === 'start_time') {
                    $eventTimestampStart = Carbon::parse($rule['value'])->toDateTimeString();
                }
                if ($rule['field'] === 'end_time') {
                    $eventTimestampEnd = Carbon::parse($rule['value'])->toDateTimeString();
                }
            }

            $polygonString = '';

            if ($hasLocationFilter && $eventTimestampStart && $eventTimestampEnd) {
                $filter_rules = $filters['rules'] ?? [];
                $filter_rules = array_filter($filter_rules, function ($rule) {
                    return $rule['field'] === 'location';
                });

                $polygonPoints = $filter_rules[0]['value'];
                if (is_string($polygonPoints)) {
                    $polygonPoints = json_decode($polygonPoints, true);
                }
                if (!is_array($polygonPoints)) {
                    throw new \Exception('Invalid polygon points');
                }
                $formattedPoints = array_map(function ($point) {
                    return sprintf('(%f, %f)', $point['lng'], $point['lat']);
                }, $polygonPoints);
                $polygonString = implode(', ', $formattedPoints);
            }

            $cteQuery = /**  @lang  sql */
                <<<SQL
            WITH sig as (
                SELECT CAST(calling_station_id AS VARCHAR) as calling_station_id, event_timestamp, lat, lon
                FROM radius_events.dist_radius_log as l, radius_events.cell_towers_dictionary as d
                    WHERE match(calling_station_id, '^[0-9]+$') AND l.hash=d.hash AND event_timestamp BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd'
                    AND pointInPolygon((lon, lat), [$polygonString])
            ),
            dpi as (
                SELECT service_id, subscriber_id, start_time, end_time,external_ip,internal_ip,internal_port,external_port,ip_protocol,octets_in,octets_out  FROM radius_events.dist_dpi_5tuples_log as dpi
                    WHERE match(subscriber_id, '^[0-9]+$') AND (dpi.start_time BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd' OR dpi.end_time BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd')
            )
            SELECT subscriber_id, service_id,
                    toJSONString(arrayMap(x->map('lat', CAST(x.1 AS VARCHAR), 'lon', CAST(x.2 AS VARCHAR), 'start', CAST(x.3 AS VARCHAR), 'end', CAST(x.4 AS VARCHAR)), arraySort(x -> x.4, groupArray((sig.lat, sig.lon, dpi.start_time, dpi.end_time))))) AS aggregated_data
            FROM sig, dpi
            WHERE sig.calling_station_id=dpi.subscriber_id AND
                sig.event_timestamp BETWEEN toStartOfMinute(dpi.start_time - (dpi.start_time % 3) * 60) AND
                  toStartOfMinute(dpi.end_time + (dpi.end_time % 3) * 60)
            SQL;

            $cteQuery .= OptimizedQueryBuilder::apply(query: $cteQuery, filter: $filters, raw: 'raw');
            $cteQuery .= "  GROUP BY service_id, subscriber_id order by subscriber_id";
            /** @var Client $db */
            $db = DB::connection('clickhouse_radius')->getClient();
            $statement = $db->select($cteQuery);
            $data = $statement->rows();


            return [
                "status" => "Success",
                "data" => $data,
                "code" => 200,
                "message" => "response ok"
            ];


        } catch (\Exception $e) {

            return [
                "status" => "Success",
                "data" => [],
                "code" => 200,
                "message" => "response ok",
                "error" => $e->getMessage(),
                "line" => $e->getLine(),
                "file" => $e->getFile()
            ];

        }

    }

    public function getSocialDipLocationWithPin(): array
    {

        $filters = request('filters', []);
        $lat = request('lat');
        $lon = request('lon');


        if (!$filters) {
            return [
                "status" => "Request Failed",
                "data" => [],
                "code" => 422,
                "message" => "response failed",
                "error" => 'filters required'
            ];
        }


        if (!$lat || !$lon) {
            return [
                "status" => "Request Failed",
                "data" => [],
                "code" => 422,
                "message" => "response failed",
                "error" => 'lat and lon required'
            ];
        }
        try {

            $eventTimestampStart = Carbon::now()->subHour()->toDateTimeString();
            $eventTimestampEnd = Carbon::now()->toDateTimeString();
            foreach ($filters['rules'] ?? [] as $rule) {
                if ($rule['field'] === 'start_time') {
                    $eventTimestampStart = Carbon::parse($rule['value'])->toDateTimeString();
                }
                if ($rule['field'] === 'end_time') {
                    $eventTimestampEnd = Carbon::parse($rule['value'])->toDateTimeString();
                }
            }
            $cteQuery = /** @lang sql */
                <<<SQL
                    WITH sig as (
                    SELECT event_timestamp, formatDateTime(toStartOfMinute(event_timestamp), '%F %R') as event_time,
                           lat, lon, CAST(calling_station_id as VARCHAR) as calling_station_id
                    FROM radius_events.dist_radius_log as log
                    GLOBAL JOIN radius_events.cell_towers_dictionary as d ON log.hash=d.hash
                    PREWHERE log.event_timestamp BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd'
                    WHERE match(calling_station_id, '^[0-9]+$') AND lat='$lat' AND lon='$lon'
                ), dpi as (
                    SELECT log.*,
                           formatDateTime(toStartOfMinute(start_time - (start_time % 10) * 60), '%F %R') as _start_time,
                           formatDateTime(toStartOfMinute(end_time + (end_time % 10) * 60), '%F %R') as _end_time
                    FROM radius_events.dist_dpi_5tuples_log as log
                    GLOBAL JOIN sig ON subscriber_id=calling_station_id
                        WHERE match(subscriber_id, '^[0-9]+$') AND log.start_time BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd' OR
                                 log.end_time BETWEEN '$eventTimestampStart' AND '$eventTimestampEnd')
                SELECT service_id, subscriber_id FROM dpi, sig WHERE (event_time BETWEEN _start_time AND _end_time)
                SQL;


            $cteQuery .= OptimizedQueryBuilder::apply(query: $cteQuery, filter: $filters, raw: 'raw');

            $cteQuery .= "  GROUP BY service_id, subscriber_id";


            /** @var Client $db */
            $db = DB::connection('clickhouse_radius')->getClient();
            $statement = $db->select($cteQuery);
            $data = $statement->rows();


            return [
                "status" => "Success",
                "data" => $data,
                "code" => 200,
                "message" => "response ok"
            ];


        } catch (\Exception $e) {

            return [
                "status" => "Success",
                "data" => [],
                "code" => 200,
                "message" => "response ok",
                "error" => $e->getMessage(),
                "line" => $e->getLine(),
                "file" => $e->getFile()
            ];

        }

    }


    public function getSocialDipApplications(): array
    {
        $query = SocialHunterDpi::select('service_id')
            ->groupBy('service_id')
            ->getRows();
        return collect($query)->map(function ($item) {
            return [
                'service_id' => mb_strtolower(Str::slug($item['service_id'])),
                'service_name' => ($item['service_id']=="") ? "Other" : $item['service_id']
            ];

        })->toArray();
    }


}

