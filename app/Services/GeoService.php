<?php

namespace App\Services;

use App\Models\Camera;

class GeoService
{
    public const CIRCLE = 'circle';
    public const POLYGON = 'polygon';
    public const RECTANGEL = 'rectangle';

    /**
     * @var array $cameraPoints
     */
    public array $cameraPoints = [];

    /**
     * @var bool $pointOnVertex
     */
    public bool $pointOnVertex = true;

    /**
     * @var array $detectedCameraIds
     */
    public array $detectedCameraIds = [];

    /**
     * GeoService constructor
     */
    public function __construct()
    {
        $this->cameraPoints = Camera::select(['camera_id', 'lat', 'lng'])->get()->toArray();
    }

    /**
     * @param $shapes
     * @return array
     */
    public function handle($shapes): array
    {
        foreach ($shapes as $shape) {

            if ($shape['shapeType'] === self::CIRCLE) {

                $coordinates = $shape['coordinates'];

                $radius = (float) $shape['radius'];

                $returnedCameraIds = [];

                foreach ($this->cameraPoints as $cameraPoint) {

                    $distance = self::calculateDistance($coordinates['lat'], $coordinates['lng'], $cameraPoint['lat'], $cameraPoint['lng']);


                    if ($radius >= $distance) {
                        $returnedCameraIds[] = $cameraPoint['camera_id'];
                    }
                }

                $this->detectedCameraIds = array_merge($returnedCameraIds, $this->detectedCameraIds);
            }
            elseif ($shape['shapeType'] === self::POLYGON) {

                $coordinates = $shape['coordinates'];

                $condinatesTransformated = [];

                foreach ($coordinates as $coordinate) {
                    $condinatesTransformated[] = $coordinate['lat'] . ' ' . $coordinate['lng'];
                }

                $cameraPointsTransformated = [];

                foreach ($this->cameraPoints as $coordinate) {
                    $cameraPointsTransformated[$coordinate['camera_id']] = $coordinate['lat'] . ' ' . $coordinate['lng'];
                }

                $this->detectedCameraIds = array_merge($this->check($cameraPointsTransformated, $condinatesTransformated), $this->detectedCameraIds);
            }
             elseif ($shape['shapeType'] === self::RECTANGEL) {

                $coordinates = $shape['coordinates'];

                $topLeft = $coordinates['topLeft'];
                $bottomRight = $coordinates['bottomRight'];
                $bottomLeft = ["lat" => $bottomRight['lat'], "lng" => $topLeft['lng']];
                $topRight = ["lat" => $topLeft['lat'], "lng" => $bottomRight['lng']];


                $coordinatesNews = [];

                $coordinatesNews[] = $topLeft;
                $coordinatesNews[] = $topRight;
                $coordinatesNews[] = $bottomRight;
                $coordinatesNews[] = $bottomLeft;
                $coordinatesNews[] = $topLeft;

                $condinatesTransformated = [];

                foreach ($coordinatesNews as $coordinate) {
                    $condinatesTransformated[] = $coordinate['lat'] . ' ' . $coordinate['lng'];
                }

                $cameraPointsTransformated = [];

                foreach ($this->cameraPoints as $coordinate) {
                    $cameraPointsTransformated[$coordinate['camera_id']] = $coordinate['lat'] . ' ' . $coordinate['lng'];
                }

                $this->detectedCameraIds = array_merge($this->check($cameraPointsTransformated, $condinatesTransformated), $this->detectedCameraIds);
            }
        }

        return array_unique($this->detectedCameraIds);
    }

    /**
     * @param $latutideA
     * @param $longitudeA
     * @param $latitudeB
     * @param $longitudeB
     * @return float|int
     */
    public static function calculateDistance($latutideA, $longitudeA, $latitudeB, $longitudeB): float|int
    {
        $delta_lat = $latitudeB - $latutideA;
        $delta_lon = $longitudeB - $longitudeA;

        $earth_radius = 6372.795477598;

        $alpha = $delta_lat / 2;
        $beta = $delta_lon / 2;
        $a = sin(deg2rad($alpha)) * sin(deg2rad($alpha)) + cos(deg2rad($latutideA)) * cos(deg2rad($latitudeB)) * sin(deg2rad($beta)) * sin(deg2rad($beta));
        $c = asin(min(1, sqrt($a)));
        $distance = 2 * $earth_radius * $c;
        $distance = round($distance, 4);

        return (float) substr($distance, 0, 4) * 1000;
    }

    /**
     * @param $points
     * @param $polygon
     * @return array
     */
    public function check($points, $polygon): array
    {
        
        //    $points = array("40.518958 49.31163", "21.43567582 72.5811816", "22.367582117085913 70.71181669186944", "22.275334996986643 70.88614147123701", "22.36934302329968 70.77627818998701");
        //    $polygon = array(
        //        "40.711873951908125 48.94409179687501",
        //        "40.53258931069557 49.60601806640626",
        //        "40.176774799905445 49.02648925781251",
        //        "40.711873951908125 48.94409179687501",
        //    );

        $responses = [];

        foreach ($points as $key => $point) {
            if ((int) $this->pointInPolygon($point, $polygon) === 1) {
                $responses[] = $key;
            }
        }

        return $responses;
    }

    /**
     * @param $point
     * @param $polygon
     * @param bool $pointOnVertex
     * @return int|string
     */
    public function pointInPolygon($point, $polygon, bool $pointOnVertex = true): int|string
    {
        $vertices = [];

        $this->pointOnVertex = $pointOnVertex;

        $point = $this->pointStringToCoordinates($point);

        foreach ($polygon as $vertex) {
            $vertices[] = $this->pointStringToCoordinates($vertex);
        }

        if ($this->pointOnVertex === true && $this->pointOnVertex($point, $vertices) === true) {
            return 1;
        }

        $intersections = 0;
        $vertices_count = count($vertices);

        for ($i = 1; $i < $vertices_count; $i++) {
            $vertex1 = $vertices[$i - 1];
            $vertex2 = $vertices[$i];
            if ($vertex1['y'] === $vertex2['y'] && $vertex1['y'] === $point['y'] && $point['x'] > min($vertex1['x'], $vertex2['x']) && $point['x'] < max($vertex1['x'], $vertex2['x'])) {
                return "boundary";
            }
            if ($point['y'] > min($vertex1['y'], $vertex2['y']) && $point['y'] <= max($vertex1['y'], $vertex2['y']) && $point['x'] <= max($vertex1['x'], $vertex2['x']) && $vertex1['y'] !== $vertex2['y']) {
                $xinters = ($point['y'] - $vertex1['y']) * ($vertex2['x'] - $vertex1['x']) / ($vertex2['y'] - $vertex1['y']) + $vertex1['x'];
                if ($xinters === $point['x']) {
                    return "boundary";
                }
                if ($vertex1['x'] === $vertex2['x'] || $point['x'] <= $xinters) {
                    $intersections++;
                }
            }
        }

        if ($intersections % 2 !== 0) {
            return 1;
        }

        return 0;
    }

    /**
     * @param $pointString
     * @return array
     */
    public function pointStringToCoordinates($pointString): array
    {
        $coordinates = explode(" ", $pointString);
        return array("x" => $coordinates[0], "y" => $coordinates[1]);
    }

    /**
     * @param $point
     * @param $vertices
     * @return bool
     */
    public function pointOnVertex($point, $vertices): bool
    {
        foreach ($vertices as $vertex) {
            if ($point === $vertex) {
                return true;
            }
        }

        return false;
    }
}
