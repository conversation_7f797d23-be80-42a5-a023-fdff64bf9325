<?php

namespace App\Services;

use App\Models\User;
use App\Models\Hierarchy;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\QueryException;

class HierarchyService
{

    /**
     * @param $children
     * @return void
     */
    public function transformChildUsersWithPivot($children): void
    {
        foreach ($children as $child) {
            $this->transformUsersForThisChild($child);
            if ($child->childrenRecursive && $child->childrenRecursive->isNotEmpty()) {
                $this->transformChildUsersWithPivot($child->childrenRecursive);
            }
        }
    }

    /**
     * @param Hierarchy $hierarchy
     * @return void
     */
    public function transformHierarchyUsers(Hierarchy $hierarchy): void
    {
        $transformedUsers = $hierarchy->users->map(function ($user) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'surname' => $user->surname,
                'email' => $user->email,
                'role' => $user->pivot->role,
                'position' => $user->pivot->position
            ];
        });

        $hierarchy->setRelation('users', $transformedUsers);

        if ($hierarchy->childrenRecursive) {
            foreach ($hierarchy->childrenRecursive as $child) {
                $this->transformHierarchyUsers($child);
            }
        }
    }

    /**
     * @param User|Authenticatable $loggedUser
     * @param bool $onlyMe - Only include users from current user's level and below
     * @return array
     */

    public function getSubHierarchyUserIds(User|Authenticatable $loggedUser, bool $onlyMe = false): array
    {
        $userHierarchies = $loggedUser->hierarchies;

        $processedHierarchyIds = [];
        $collectedUserIds = [];

        if ($onlyMe) {
            foreach ($userHierarchies as $hierarchy) {
                $collectedUserIds[] = $loggedUser->id;

                $childHierarchies = $hierarchy->children;
                foreach ($childHierarchies as $childHierarchy) {
                    $this->findSubHierarchyUsersRecursive(
                        $childHierarchy,
                        $processedHierarchyIds,
                        $collectedUserIds
                    );
                }
            }
        } else {
            foreach ($userHierarchies as $hierarchy) {
                $this->findSubHierarchyUsersRecursive(
                    $hierarchy,
                    $processedHierarchyIds,
                    $collectedUserIds
                );
            }
        }

        $loggedUserId = $loggedUser->id;
        $collectedUserIds = array_filter($collectedUserIds, function ($userId) use ($loggedUserId) {
            return $userId != $loggedUserId;
        });

        return array_values(array_unique($collectedUserIds));
    }

    /**
     * @param User|Authenticatable $loggedUser
     * @param bool $onlyMe
     * @return array
     */
    public function getSubHierarchyUsers(User|Authenticatable $loggedUser, $search, bool $onlyMe = false): array
    {
        $userIds = $this->getSubHierarchyUserIds($loggedUser, $onlyMe);

        if (empty($userIds)) {
            return [];
        }

        /** @User $user */
        $users = User::query()->whereIn('id', $userIds)
            ->select('id', 'name', 'surname', 'email')
            ->with(['hierarchies' => function ($query) {
                $query->select('hierarchies.id', 'hierarchies.name', 'hierarchies.description')
                    ->withPivot('role', 'position');
            }])
            ->when(isset($search['name']), function ($query) use ($search) {
                $query->search('name', $search['name']);
            })
            ->when(isset($search['surname']), function ($query) use ($search) {
                $query->search('surname', $search['surname']);
            })
            ->get()
            ->map(function ($user) {
                $hierarchies = $user->hierarchies->map(function ($hierarchy) {
                    return [
                        'id' => $hierarchy->id,
                        'name' => $hierarchy->name,
                        'description' => $hierarchy->description,
                        'role' => $hierarchy->pivot->role,
                        'position' => $hierarchy->pivot->position,
                    ];
                });

                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'surname' => $user->surname,
                    'email' => $user->email,
                    'hierarchies' => $hierarchies,
                ];
            })
            ->toArray();

        return collect($users)->sortBy(function ($user) {
            return $user['hierarchies'][0]['id'] ?? 0;
        })->values()->toArray();
    }

    /**
     * @param Hierarchy $hierarchy Current hierarchy to process
     * @param array $processedHierarchyIds Track processed hierarchies to avoid duplicates and loops
     * @param array $collectedUserIds Collected user IDs (passed by reference)
     * @return void
     */
    private function findSubHierarchyUsersRecursive(
        Hierarchy $hierarchy,
        array     &$processedHierarchyIds,
        array     &$collectedUserIds
    ): void
    {
        $processedHierarchyIds[] = $hierarchy->id;
        $users = $hierarchy->users()->pluck('users.id')->toArray();
        $collectedUserIds = array_merge($collectedUserIds, $users);
        $childHierarchies = $hierarchy->children;
        foreach ($childHierarchies as $childHierarchy) {
            if (in_array($childHierarchy->id, $processedHierarchyIds)) {
                continue;
            }
            $this->findSubHierarchyUsersRecursive(
                $childHierarchy,
                $processedHierarchyIds,
                $collectedUserIds
            );
        }
    }


    /**
     * @param User $user
     * @param Hierarchy $hierarchy
     * @param null $role
     * @param int $position
     * @return void
     * @throws \Exception
     */
    public function addUserToHierarchy(User $user, Hierarchy $hierarchy, $role = null, int $position = 0): void
    {
        try {
            $user->hierarchies()->attach($hierarchy->id, [
                'role' => $role,
                'position' => $position
            ]);
        } catch (QueryException $e) {
            if ($e->errorInfo[1] == 23505) { // PostgreSQL unique constraint violation code
                $this->updateUserInHierarchy($user, $hierarchy, $role, $position);
            }
            throw $e;
        }
    }


    /**
     * @param User $user
     * @param Hierarchy $hierarchy - Current hierarchy
     * @param string|null $role
     * @param int|null $position
     * @param int|null $newHierarchyId - New hierarchy ID if changing hierarchies
     * @return bool
     * @throws \Exception
     */
    public function updateUserInHierarchy(
        User      $user,
        Hierarchy $hierarchy,
        ?string   $role = null,
        ?int      $position = null,
        ?int      $newHierarchyId = null
    ): bool
    {
        if (!$this->isUserInHierarchy($user, $hierarchy)) {
            throw new \Exception("User is not in this hierarchy");
        }

        if ($newHierarchyId !== null && $newHierarchyId != $hierarchy->id) {
            $newHierarchy = Hierarchy::find($newHierarchyId);
            if (!$newHierarchy) {
                throw new \Exception("New hierarchy not found");
            }
            $this->removeUserFromHierarchy($user, $hierarchy);
            $this->addUserToHierarchy(
                $user,
                $newHierarchy,
                $role ?? $hierarchy->pivot->role ?? null,
                $position ?? $hierarchy->pivot->position ?? 0
            );

            return true;
        }
        $data = [];
        if ($role !== null) {
            $data['role'] = $role;
        }
        if ($position !== null) {
            $data['position'] = $position;
        }

        if (!empty($data)) {
            $user->hierarchies()->updateExistingPivot($hierarchy->id, $data);
        }

        return true;
    }


    /**
     * @param User $user
     * @param Hierarchy $hierarchy
     * @return int
     */
    public function removeUserFromHierarchy(User $user, Hierarchy $hierarchy): int
    {
        return $user->hierarchies()->detach($hierarchy->id);
    }


    /**
     * @param Hierarchy $hierarchy
     * @return array
     */
    public function getAllUsersInHierarchyTree(Hierarchy $hierarchy): array
    {
        $result = [
            'hierarchy' => [
                'id' => $hierarchy->id,
                'name' => $hierarchy->name,
                'description' => $hierarchy->description
            ],
            'users' => [],
            'children' => []
        ];

        $users = $hierarchy->users()
            ->select('users.id', 'users.name', 'users.surname', 'users.email')
            ->withPivot('role', 'position')
            ->get();

        $result['users'] = $users->map(function ($user) use ($hierarchy) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'surname' => $user->surname,
                'email' => $user->email,
                'hierarchy_role' => $user->pivot->role,
                'position' => $user->pivot->position,
                'hierarchy_id' => $hierarchy->id,
                'hierarchy_name' => $hierarchy->name
            ];
        })->toArray();

        foreach ($hierarchy->children as $child) {
            $result['children'][] = $this->getAllUsersInHierarchyTree($child);
        }

        return $result;
    }

    public function getAllUsersInHierarchyTreeFlat(Hierarchy $hierarchy): Collection|\Illuminate\Support\Collection
    {
        $users = $hierarchy->users()
            ->select('users.id', 'users.name', 'users.surname', 'users.email')
            ->withPivot('role', 'position')
            ->get();

        $transformedUsers = $users->map(function ($user) use ($hierarchy) {
            return [
                'id' => $user->id,
                'name' => $user->name,
                'surname' => $user->surname,
                'email' => $user->email,
                'hierarchy_role' => $user->pivot->role,
                'position' => $user->pivot->position,
                'hierarchy_id' => $hierarchy->id,
                'hierarchy_name' => $hierarchy->name
            ];
        });

        $childUsers = collect([]);
        foreach ($hierarchy->children as $child) {
            $childUsers = $childUsers->merge($this->getAllUsersInHierarchyTreeFlat($child));
        }

        return $transformedUsers->merge($childUsers)->unique('id');
    }

    /**
     * @param User $user
     * @return array
     */
    public function getUserHierarchyPaths(User $user): array
    {
        $paths = [];

        foreach ($user->hierarchies as $hierarchy) {
            $path = [];
            $current = $hierarchy;

            while ($current) {
                array_unshift($path, $current);
                $current = $current->parent;
            }

            $paths[] = [
                'hierarchy' => $hierarchy,
                'position' => $hierarchy->pivot->position,
                'role' => $hierarchy->pivot->role,
                'path' => $path
            ];
        }

        return $paths;
    }

    /**
     * @param User $user
     * @param Hierarchy $hierarchy
     * @return bool
     */
    public function isUserInHierarchy(User $user, Hierarchy $hierarchy): bool
    {
        return $user->hierarchies()->where('hierarchy_id', $hierarchy->id)->exists();
    }


    /**
     * @param Hierarchy $hierarchy
     * @param $allHierarchies
     * @return array
     */
    public function buildHierarchyTree(Hierarchy $hierarchy, $allHierarchies): array
    {
        $result = $hierarchy->toArray();
        $result['children'] = $allHierarchies->where('parent_id', $hierarchy->id)
            ->map(function ($child) use ($allHierarchies) {
                return $this->buildHierarchyTree($child, $allHierarchies);
            })
            ->values()
            ->toArray();

        return $result;
    }

    /**
     * @param mixed $child
     * @return void
     */
    public function transformUsersForThisChild(mixed $child): void
    {
        if ($child->users && $child->users->isNotEmpty()) {
            $transformedUsers = $child->users->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'surname' => $user->surname,
                    'email' => $user->email,
                    'role' => $user->pivot->role ?? null,
                    'position' => $user->pivot->position ?? null
                ];
            });

            $child->setRelation('users', $transformedUsers);
        }
    }
}
