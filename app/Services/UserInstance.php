<?php

namespace App\Services;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Facades\Auth;

class UserInstance
{
    private static ?UserInstance $instance = null;
    private ?Authenticatable $user;

    private function __construct()
    {
        $this->user = auth('api')->user();
    }

    public static function getInstance(): ?UserInstance
    {
        if (self::$instance === null) {
            self::$instance = new UserInstance();
        }

        return self::$instance;
    }

    public function getUser(): ?Authenticatable
    {
        return $this->user;
    }
}
