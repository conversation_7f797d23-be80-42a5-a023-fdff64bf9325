<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;

class IamasService{

    /**
     * @param $pin
     * @return Response
     */
    public static function parse($pin): Response
    {
        return Http::get(config('servers.dtx_webservice').'/api/v1/webservices/idcard/pin?Pin='. strtoupper($pin));
//        return Http::post('http://10.2.161.23:90/api/v1/iamas_data', ['pin' => strtoupper($pin)]);
    }

    /**
     * @param $pin
     * @return mixed
     * @throws \JsonException
     */
    public static function collect($pin): mixed
    {
        return json_decode(self::parse($pin)->body(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * @param string|null $date
     * @return \Carbon\Carbon|null
     */
    public static function stringToDate(?string $date): Carbon|null
    {
        return $date !== '' ? Carbon::createFromFormat('d.m.Y', $date) : null;
    }

    /**
     * @param string $base64Txt
     * @param string $pin
     * @param string $docSerialNumber
     * @throws \Illuminate\Contracts\Filesystem\FileNotFoundException
     */
    public static function saveBase64asImage(string $base64Txt, string $pin, string $docSerialNumber): void
    {
        $docType = PersonService::getDocType($docSerialNumber);
        $file = PersonService::getImgPath($pin, $docType);

        S3Service::saveBase64asImage($file, $base64Txt);
    }
}
