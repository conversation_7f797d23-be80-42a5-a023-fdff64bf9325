<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class VideoUploadService
{

    public function __construct($request)
    {
        if ($request) {
            $file = $request;
            $client = new Client();
            try {
                $response = $client->request('POST', config('servers.gpu_api_predict_file'), [
                    'headers' => [
                        'Accept' => 'application/json',
                    ],
                    'multipart' => [
                        [
                            'name' => 'media_file',
                            'contents' => fopen($file->getPathname(), 'r'),
                            'filename' => $file->getClientOriginalName(),
                            'headers'  => ['Content-Type' => $file->getMimeType()]
                        ]
                    ]
                ]);
                $data = json_decode($response->getBody()->getContents(), true);
                return response()->json($data);

            } catch (\Exception $e) {
                return response()->json(['error' => $e->getMessage()], 500);
            } catch (GuzzleException $e) {
                return response()->json(['error' => $e->getMessage()], 500);
            }
        } else {
            return response()->json(['error' => 'No file uploaded.'], 400);
        }
    }
}
