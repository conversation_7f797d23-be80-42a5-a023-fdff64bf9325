<?php

namespace App\Exceptions;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    public function render($request, Throwable $e)
    {
        if ($e instanceof ModelNotFoundException && $request->wantsJson()) {

            return response()->json([
                'message' => '404 not found',
            ], 404);
        }

        if ($e instanceof \Yajra\Pdo\Oci8\Exceptions\Oci8Exception && $request->wantsJson()) {

            return response()->json([
                'message' => 'Database error Oracle',
            ], 500);

        }

        if ($e instanceof \Yajra\Oci8\Connectors\OracleConnector && $request->wantsJson()) {

            return response()->json([
                'message' => 'Database error unknown Oracle',
            ], 500);

        }

        if ($e instanceof \Yajra\Pdo\Oci8 && $request->wantsJson()  || $e instanceof \Yajra\Pdo\Oci8\Exceptions\Oci8Exception && $request->wantsJson()) {

            return response()->json([
                'message' => 'Database error Oracle',
            ], 500);

        }

        return parent::render($request, $e);
    }
}
