<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ExcelParse extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'excel:parse';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info(PHP_EOL . 'Start' . PHP_EOL);
        $channel_name = 'PJA2';
        $channel = 'PJA';
        $this->info('Start parse ' . $channel_name);
        $chunked_files = glob(public_path('order/chunk/*.csv'));
        $this->info('Chunked files count: ' . count($chunked_files));
       // $file = public_path('order/chunk/chunk190.csv');
        foreach ($chunked_files as $key => $file){
            orderParse($file, $channel);
            $this->info('Count ' . $key . ' of ' . count($chunked_files));
            $this->info('End chunk ' . $file);
       }
        $this->info('End parse ' . $channel_name);
        $this->info(PHP_EOL . 'End' . PHP_EOL);
    }
}
