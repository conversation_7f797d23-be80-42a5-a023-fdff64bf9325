<?php

namespace App\Console\Commands;

use App\Models\Person;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ChakiVectorSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'chaki:csv';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync CSV file with missing person pins from the database';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        ini_set('memory_limit', '16G'); // You may not need more than 2GB if streaming

        $csvFile = public_path('person_pins.csv');
        $newCsvFile = public_path('new_person_pins.csv');

        // Array to store pins from CSV
        $pinsInCsv = [];

        // If the CSV file exists, read it and get the pins
        if (File::exists($csvFile)) {
            $this->info('Found person_pins.csv file. Checking for missing pins...');

            if (($handle = fopen($csvFile, 'r')) !== false) {
                while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                    $pinsInCsv[] = $data[0];  // Collect all pins from CSV
                }
                fclose($handle);
            } else {
                $this->error('Could not open the original CSV file.');
                return 1;
            }
        } else {
            // If the CSV file does not exist, warn the user and proceed
            $this->warn('CSV file does not exist. Fetching all pins from the Person model.');
        }

        // Open new CSV file for appending
        if (($newFileHandle = fopen($newCsvFile, 'a')) !== false) {
            // Use cursor to stream data chunk by chunk
            $query = Person::when(!empty($pinsInCsv), function ($query) use ($pinsInCsv) {
                return $query->whereNotIn('pin', $pinsInCsv);
            });

            foreach ($query->cursor() as $person) {
                // Append each pin to the new CSV file without loading all records at once
                fputcsv($newFileHandle, [$person->pin]);
            }

            fclose($newFileHandle);
            $this->info('Missing person pins have been appended to new_person_pins.csv.');
        } else {
            $this->error('Could not create or append to new_person_pins.csv.');
            return 1;
        }

        return 0;
    }
}
