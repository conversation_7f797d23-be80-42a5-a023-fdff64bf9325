<?php

namespace App\Console\Commands\ServerStatus;

use App\Services\ServerStatusService;
use Illuminate\Console\Command;

class ServerStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:servers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Server status checking';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(protected ServerStatusService $serverStatusService)
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->serverStatusService->getALLStatuses();
        return 0;
    }
}
