<?php

namespace App\Console\Commands;

use App\Models\Person;
use App\Repository\PersonRepository;
use App\Services\IamasService;
use App\Services\PersonService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use phpseclib3\Crypt\EC;

class GetAdressesFromTabib extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get:adressesfromtabib';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        protected PersonService $personService,
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $people_count = 5000;
        $successLog = public_path('log_pin_db_emas.txt');

        $start = microtime(true);

        echo "Started\n";

        $people = Person::query()->where(['address' => ''])->limit($people_count)->get();

        $ids = $people->pluck('id');

        print_r($ids);

        Person::query()->whereIn('id', $ids)->update([
            'lock' => true
        ]);

        if ($people)
        {

            $x = 0;

            foreach ($people as $person)
            {
                // $rand = rand(2, 5);
                $pin = strtoupper($person->pin);
				$retryCount = 0;
                $x++;


				do {
					try {
						$data = Http::post(config('servers.iamas_host').'/api/v1/iamas_data', ['pin' => $pin]);

						if (isset($data['Items']) || $data['Items']['Pin'] != '' || $data['Items']['DocNumber'] != '') {
							if ($data['Items']['Photo'] != "") {
								IamasService::saveBase64asImage($data['Items']['Photo'] ?? '', $pin, $data['Items']['DocSerialNumber'] ?? '');
							}

							Person::query()->where(['pin' => $pin])->update($this->mapData($data));
	//                            $person->save($this->mapData($data));
	//                            $person->refresh();
							$logData = $x.") - ".$pin." - saved\n";
							echo $x.") - ".$pin." - saved".PHP_EOL;

						} else {
							$logData = $pin." - not found\n";

							echo $x.") - ".$pin." - not found api".PHP_EOL;
						}
						// file_put_contents($successLog, $logData, FILE_APPEND);

					} catch (Exception $e) {
						echo $x.") - ".$pin." - not saved - Retrying...".PHP_EOL;
						// $logData = PHP_EOL."CRASH - ".$pin." - ".$e->getMessage().PHP_EOL;
						// file_put_contents($successLog, $logData, FILE_APPEND);
						$retryCount++;
						sleep(2);
					}
				} while (isset($data['ResultCode']) && $data['ResultCode'] != "S0000" && $retryCount <= 5);

                // sleep($rand ?? 3);

            }

                $x++;
        }

            echo 'Total : '.$x;

            $end = microtime(true);

            $executionTime = ceil(($end - $start) / 60);

            $this->info("Funksiyanin isleme muddeti: " . $executionTime . " dəq");

        return 0;
    }

    public function mapData($data): array
    {
        return [
            'name' => $data['Items']['Name'],
            'surname' => $data['Items']['Surname'],
            'father_name' => $data['Items']['FatherName'],
            'birthdate' => IamasService::stringToDate($data['Items']['BirthDay']),
            'pin' => $data['Items']['Pin'],
            'doc_serial_number' => $data['Items']['DocSerialNumber'],
            'doc_number' => $data['Items']['DocNumber'],
            'doc_given_date' => IamasService::stringToDate($data['Items']['DocGivenDate']),
            'doc_valid_date' => IamasService::stringToDate($data['Items']['DocumentValidityDate']),
            'sex' => array_search($data['Items']['Sex'], Person::$gender, true),
            'marital_status' => in_array(strtolower($data['Items']['Maritalstatus']), ['single', 'subay']) ? 0 : 1,
            'blood_group' => $data['Items']['BloodGroup'],
            'eye_color' => $data['Items']['EyeColor'],
            'height' => (int) $data['Items']['Height'],
            'address' => $data['Items']['Address'],
            'country' => $data['Items']['Country'],
            'city' => $data['Items']['City'],
            'district' => $data['Items']['Distric'] === '-1',
            'district_city' => $data['Items']['DistricCity'],
            'city_district' => $data['Items']['CityDistric'],
            'authority_document' => $data['Items']['AuthorityDocument'],
            'lock' => false,
			'image' => $data['Items']['Photo'] != "" ? 1 : 0
        ];
    }
}
