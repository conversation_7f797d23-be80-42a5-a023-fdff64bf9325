<?php

namespace App\Console\Commands;

use App\Models\Person;
use App\Repository\PersonRepository;
use App\Services\IamasService;
use App\Services\PersonService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use phpseclib3\Crypt\EC;

class GeUnknownPinsSyncEams extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:unknownpins';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync unknown pins from IAMAS to Beein DB';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        protected PersonService $personService,
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $people_count = 50000;
//        $people_count = 1;

        $start = microtime(true);

        $this->info("Started\n");

        $people = Person::query()
            ->where(function ($query) {
                $query->where('name', '=', 'UNKNOWN')
                    ->orWhere('surname', '=', 'UNKNOWN')
                    ->orWhere('father_name', '=', 'UNKNOWN');
            })
            ->limit($people_count)->get();

        $this->info("People count: " . count($people));

        if ($people) {
            $x = 0;
            foreach ($people as $person) {
                $pin = strtoupper($person->pin);

                $x++;

                try {
                    DB::beginTransaction();
                    $data = IamasService::collect($pin);

                    $this->info("Data\n");
                    $this->warn(json_encode($data));
                    $this->comment("Data end\n");

                    $data = $data['Data'];

                    if (isset($data) && ( isset($data['Person']) || $data['Person']['Pin'] != '' || $data['Person']['DocNumber'] != '') ){
                        if ($data['Person']['Images'][0]['Image'] != "") {

                            $this->info("Saving image\n");
                            $this->warn($data['Person']['Images'][0]['Image']);
                            $this->comment("Saving image end\n");
                            $this->info("img ",json_encode($data));
                            $this->line("Saving image end\n");

                            IamasService::saveBase64asImage($data['Person']['Images'][0]['Image'] ?? '', $pin, $data['Document']['Seria'] ?? "");
                        }

                        $dataX['Data'] = $data;

                        Person::query()->where(['pin' => $pin])->update($this->mapData($dataX));

                        DB::commit();

                        $logData = $x . ") - " . $pin . " - saved\n";

                    } else {
                        $logData =  $x . ") - " . $pin . " - not found api" . PHP_EOL;
                    }

                    $this->info($logData);

                } catch (Exception $e) {

                    DB::rollBack();
                    $logData = $x . ") - " . $pin . " - not found db" . PHP_EOL;
                    $this->info($logData);
                    $this->info("Error: " . $e->getMessage() .' - '. $e->getLine());

                }

            }

            $x++;
            $this->info('Inserted : ' . $x.' records');

        }

        $end = microtime(true);

        $executionTime = ceil(($end - $start) / 60);

        $this->info("Execution Time: " . $executionTime . " seconds\n");

        return 0;
    }

    public function mapData($data): array
    {

        $this->info("Mapping data\n");
        $this->warn(json_encode($data));
        $this->comment("Mapping data end\n");
        $this->line("Mapping data end\n");

        $mapFields = [
            'name' => $data['Data']['Person']['NameAz'] ?? "",
            'surname' => $data['Data']['Person']['SurnameAz'] ?? "",
            'father_name' => $data['Data']['Person']['PatronymicAz'] ?? "",
            'birthdate' => IamasService::stringToDate($data['Data']['Person']['BirthDate']),
            'pin' => $data['Data']['Person']['PIN'] ?? "",
            'doc_serial_number' => $data['Data']['Document']['Seria'] ?? "",
            'doc_number' => $data['Data']['Document']['Number'] ?? "",
            'doc_given_date' => IamasService::stringToDate($data['Data']['Document']['GivenDate'] ?? ''),
            'doc_valid_date' => IamasService::stringToDate($data['Data']['Document']['ExpireDate'] ?? ''),
            'sex' => array_search($data['Data']['Person']['Gender']['Description'], Person::$gender, true),
            'marital_status' => in_array(strtolower($data['Data']['Person']['MaritalStatus']['Description']), ['single', 'subay']) ? 0 : 1,
            'blood_group' => $data['Data']['Person']['BloodType']['Description'] ?? "",
            'eye_color' => $data['Data']['Person']['EyeColor']['Description'] ?? "",
            'height' => (int)$data['Data']['Person']['Height'] ?? "",
            //'address' => $data['Data']['Person']['IAMASAddress']['FullAddress'] ?? "",
            'country' => $data['Data']['Person']['BirthAddress']['Country']['NameAz'] ?? "",
            'city' => $data['Data']['Person']['BirthAddress']['City'] ?? "",
            'district' => '-1',
            'district_city' => '-1',
            'city_district' => '-',
            'authority_document' => $data['Data']['Document']['GivenOrganization'] ?? "",
        ];

        if (isset($data['Data']['Person']['IAMASAddress']['FullAddress']) && $data['Data']['Person']['IAMASAddress']['FullAddress'] != null) {
            $mapFields['address'] = $data['Data']['Person']['IAMASAddress']['FullAddress'];
        }

        $this->info("Transformed data\n");
        $this->warn(json_encode($mapFields));
        $this->comment("Transformed data end\n");
        $this->line("Transformed data end\n");


        return $mapFields;

    }
}
