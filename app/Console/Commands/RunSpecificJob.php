<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SaveAdvanceSearchLogsJob;

class RunSpecificJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'job:run-specific {job}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run a specific job';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $job = $this->argument('job');

        switch ($job) {
            case 'SaveAdvanceSearchLogsJob':
                SaveAdvanceSearchLogsJob::dispatch()->onConnection('database')->onQueue('save');
                $this->info('SaveAdvanceSearchLogsJob has been dispatched.');
                break;

            // Add more cases here for other jobs if needed

            default:
                $this->error('Job not found.');
                break;
        }

        return 0;
    }
}
