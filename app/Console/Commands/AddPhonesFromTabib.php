<?php

namespace App\Console\Commands;

use App\Models\Person;
use App\Models\PersonPhone;
use App\Repository\PersonRepository;
use App\Services\IamasService;
use App\Services\PersonService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use phpseclib3\Crypt\EC;

class AddPhonesFromTabib extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'add:phonestabib';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $filePath = public_path('tabib/old.csv');

//        $data = "";
//
//        $logPath = public_path('newpinslog.txt');
//        $new50Person = public_path('pin1_50.txt');
//
//        $successLog = public_path('log_pin1_50.txt');

        $start = microtime(true);

        echo "Started\n";

        $arr = [];

        if (file_exists($filePath)) {

            $fileContent = file_get_contents($filePath);
            $explodedContent = explode("\n", $fileContent);

            $x = 0;
            foreach ($explodedContent as $content) {
                $data = explode(",", $content);
                try {
                    if ((isset($data[0]) && isset($data[1])) && !empty($data[1] && !empty($data[0]))) {

                        $arr[$data[0]] = $data[1];
//                        if (PersonPhone::query()->where([
//                            'pin' => $data[0],
//                            'phone' => $data[1],
//                        ])->doesntExist()) {
//                            PersonPhone::query()->create([
//                                'pin' => $data[0],
//                                'phone' => $data[1],
//                            ]);
////                            echo $x.") ".$data[0]." - saved".PHP_EOL;
//                        }
//                        else{
//                            echo $x.") ".$data[0]." - PASSED".PHP_EOL;
//                        }

                        $x++;
                    }
                }catch (Exception $exception){
                    echo $exception->getMessage().PHP_EOL;
                }
            }

            print_r(count($arr));
            echo 'DONE';
            return 'DONE';

            return 0;
        }
    }
}
