<?php

namespace App\Console\Commands;

use App\Models\ForeignCitizen;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ScriptUpdateDistanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'run:update_distance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function calculate($x): float|int
    {
        return $x > 1 ? 1 : asinh($x) + 0.11;
    }

    public function handle()
    {

//        $x = 1;
////        for ($i = 0; $i <= 504123; $i += 50000) {
//        for ($i = 550000; $i <= 554123; $i += 1000) {
//
////        $i = 250000;
////        $i = 0;
////        $take = $i + 5000;
//
//            $rows = ForeignCitizen::query()->select('_id', 'data')->skip($i)->take(1000)->get()->toBase();
////            $rows = ForeignCitizen::query()->select('_id', 'data')->get();
//
//        echo "Skip - ".$i.".. - Take ".($i + 50000).PHP_EOL;
//        if (!empty($rows->toArray())) {
//            foreach ($rows as $row) {
//                $data = [];
//                foreach ($row->data as $key2 => $arr) {
//                    $data[0][$key2] = $arr;
////                if ($arr['old_distance']==""){
////                    //$data[0][$key2]['old_distance'] = $arr['distance'];
////                }
//
//                    if (isset($arr['old_distance']) && $arr['old_distance'] != "") {
////                    $data[0][$key2]['distance'] = $arr['old_distance'];
//                        continue;
//                    } else {
//                        if (isset($arr['old_distance']) && $arr['old_distance'] == "") {
//                            $data[0][$key2]['old_distance'] = $arr['distance'];
//                        } else {
//                            $data[0][$key2]['old_distance'] = $arr['distance'];
//                            $data[0][$key2]['distance'] = $this->calculate($arr['distance']);
//                        }
//                    }
//                }
//                $data['id'] = $row->id;
//                File::append(public_path('distance_change.txt'), json_encode($data));
//                ForeignCitizen::query()->where('_id', $row->id)->update(['data' => $data[0]]);
//                echo "Update - row - " . $x++ . ". ID - " . $row->id . PHP_EOL;
//            }
//        }
//
//        }
//        echo 'SUCCESS';
    }
}
