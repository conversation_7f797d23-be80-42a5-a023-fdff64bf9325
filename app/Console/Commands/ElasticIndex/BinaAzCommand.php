<?php

namespace App\Console\Commands\ElasticIndex;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class BinaAzCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'es_index:bina_az';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Elastic index bina.az';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try{
            App::call('\App\Http\Controllers\Api\ElasticIndexController@binaAz');
            $this->info('success');

        }catch(\Exception $e){
            $msg = $this->info($e->getMessage());
            Log::info('es_index:bina_az -> '.$msg);
        }
    }
}
