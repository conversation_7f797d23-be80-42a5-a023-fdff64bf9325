<?php

namespace App\Console\Commands;

use App\Models\ForeignCitizenTransfer;
use Illuminate\Console\Command;
use GuzzleHttp\Client;
use App\ECNEBI2020;
use App\ForeignCitizen;
use Illuminate\Support\Facades\DB;


class ParserCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'run:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        dd('permission danied');
        $startTime = microtime(true);

        ini_set('memory_limit', '-1');

        $limit = 1000;

        $arrayLength = 100;

        $max_count = 1000;

        $similarity = 0.5;

        $data = DB::connection('oracle')
            ->select(DB::raw("SELECT GIVEN_NAME, SURNAME, DOCUMENT_NUMBER, VETENDASHLIGI, STATUS, count from(SELECT count(document_number) as count, GIVEN_NAME, SURNAME, DOCUMENT_NUMBER, VETENDASHLIGI, STATUS FROM ECNEBI2020 e WHERE STATUS = 0 AND ROWNUM <= " . $limit . " GROUP BY GIVEN_NAME, SURNAME, DOCUMENT_NUMBER, VETENDASHLIGI, STATUS ORDER BY DOCUMENT_NUMBER ASC)"));

        $array = [];

        echo "Getting CVS file data";
        echo PHP_EOL;
        echo "Progress :                               ";

        $i = 0;

        $count = count($data);

        if ($count > 0) {
            $lockArray = [];

            foreach ($data as $key => $value) {

                $array[] = [
                    "name" => $value->given_name,
                    "surname" => $value->surname,
                    "document_number" => $value->document_number,
                    "vetendashligi" => $value->vetendashligi
                ];

                $lockArray[] = $value->document_number;

                $i++;
                echo "\033[20D";
                echo str_pad($i . ' / ' . $count, 20, ' ', STR_PAD_LEFT);
            }

            // Locked rows
            ECNEBI2020::whereIn('DOCUMENT_NUMBER', $lockArray)->update(['STATUS' => 1]);

            echo PHP_EOL;
            echo PHP_EOL;

            $arrayChunk = array_chunk($array, $arrayLength);

            echo "Starting inserted process";

            echo PHP_EOL;
            echo PHP_EOL;

            // $client = new Client(['base_uri' => "http://10.14.70.23:8000", 'verify' => false]);
            $client = new Client(['base_uri' => config('servers.gpu_api'), 'verify' => false]);

            foreach ($arrayChunk as $key => $value) {

                $f = 0;

                $keys = $key + 1;

                echo "Progress " . $keys . ":                               ";

                $processedDataCount = count($value);

                foreach ($value as $item) {

                    $response = $client->request('GET', "/customs/search", [
                        'query' => [
                            'passport_id' => $item['document_number'],
                            'max_count' => $max_count,
                            'similarity' => $similarity
                        ]
                    ]);

                    $body = $response->getBody();

                    $stringBody = (string)$body;

                    $data = json_decode($stringBody, true);

                    if ($data['success'] == "1") {
                        // $item['data'] = json_encode($data['response'][0]['data']);
                        $item['data'] = json_encode($data['response']['data']);
                    } else {
                        $item['data'] = json_encode([]);
                    }


                    $item['max_count'] = $max_count;

                    $item['similarity'] = $similarity;
                    // ForeignCitizen::create($item);
                    ForeignCitizenTransfer::create($item);

                    $f++;

                    echo "\033[20D";
                    echo str_pad($f . ' / ' . $processedDataCount, 20, ' ', STR_PAD_LEFT);
                }

                echo PHP_EOL;

                $exeTime = microtime(true) - $startTime;
                echo 'Execute Time: ' . $exeTime . PHP_EOL;

                echo PHP_EOL;
            }

            // Complate rows
            ECNEBI2020::whereIn('DOCUMENT_NUMBER', $lockArray)->update(['STATUS' => 2]);

            echo PHP_EOL;

            $exeTime = microtime(true) - $startTime;
            echo 'All Progress Execute Time: ' . $exeTime . PHP_EOL;
        }
    }


}
