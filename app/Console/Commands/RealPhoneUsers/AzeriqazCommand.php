<?php

namespace App\Console\Commands\RealPhoneUsers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

use Illuminate\Console\Command;


class AzeriqazCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'real_phone_users:azeriqaz';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try{
            App::call('\App\Http\Controllers\Api\PhoneRealUsersController@azeriqaz');
            $this->info('success');

        }catch(\Exception $e){
            $msg = $this->info($e->getMessage());
            Log::info('real_phone_users:azeriqaz -> '.$msg);
        }
    }
}
