<?php

namespace App\Console\Commands\Notification;

use App\Models\AlertHistory;
use App\Models\Cabinet\AlertHistoryLog;
use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class CheckAlertCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:alert';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {

//            for ($i = 0; $i <= 30; $i++) {
//                AlertHistoryLog::query()
//                    ->create([
//                        'alert_content' => "Yeni məlumat var $i:
//                                             Fin: [
//                                             68GPXEJ
//                                             ]",
//                        'alert_id' => 128,
//                        'alert_title' => 'Hemid Test '.rand(0,99),
//                        'app' => 'beein_web',
//                        'user_id' => 119,
//                        'status' => 0
//                    ]);
//            }
//
//            die;


//            $en1 = 'en1back.a2z.az';
//            $en2 = 'api.a2z.az';
//
//            $alerts = AlertHistory::query()
//                ->select('id', 'alert_title', 'alert_content', 'enigma_phones', 'user_ids')
//                ->where('status', 0)
//                ->get()
//                ->toBase();
//
//            $alertIds = $alerts->pluck('id');
//
//            foreach ($alerts as $alert)
//            {
//
//                $userIds = json_decode($alert->user_ids);
//
//                $users = User::query()
//                    ->select('id', 'phone_number')
//                    ->whereIn('id', $userIds)
//                    ->get()
//                    ->toBase();
//
//                $notifyData['title'] = $alert->alert_title;
//                $notifyData['body'] = $alert->alert_content;
//                foreach ($users as $user)
//                {
//                    $notifyData['to'] = $user->phone_number;
//
//                    Http::post('https://'.$en1.'/api/v1/service/notifications', $notifyData);
//                }
//            }
//
//            AlertHistory::query()->whereIn('id', $alertIds)->update([
//                'status' => 1,
//                'updated_at' => now()
//            ]);
            //

            $alerts = AlertHistoryLog::query()
                ->select('id', 'alert_title', 'alert_content', 'enigma_phones', 'user_ids')
                ->where([
                    'status' => 0,
                    'app' => 'enigma'
                ])
                ->get()
                ->toBase();

            $ids = $alerts->pluck('_id');

            foreach ($alerts as $alert) {

                $text = 'Alert' . PHP_EOL;
                $text .= '-------------------------' . PHP_EOL;
                $text .= 'Title: ' . $alert->alert_title . PHP_EOL;
                $text .= '----' . PHP_EOL;
                $text .= 'Content: ' . $alert->alert_content . PHP_EOL;
                $text .= '----' . PHP_EOL;

                Http::post('https://api.telegram.org/bot6606035328:AAEoktgSTnr6slOKaFLp6G3pY8AgxQrfhpQ/sendMessage', [
                    'chat_id' => '-1002061635129',
                    'text' => $text
                ]);
            }

            AlertHistoryLog::query()
                ->whereIn('_id', $ids)
                ->where('app', 'enigma')
                ->update([
                    'status' => 1
                ]);


        } catch (Exception $exception) {
            echo $exception->getMessage();
        }

        return 0;
    }
}
