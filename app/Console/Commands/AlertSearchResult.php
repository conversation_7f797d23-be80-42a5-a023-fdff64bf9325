<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class AlertSearchResult extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'check:alert-search-result';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Alert search result';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        \App::call('\App\Http\Controllers\Api\AlertController@checkAlertSearchResult');
        \App::call('\App\Http\Controllers\Api\AlertController@checkSocialCall');
        $this->info('success');
    }
}
