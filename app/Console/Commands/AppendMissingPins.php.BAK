<?php

namespace App\Console\Commands;


namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Person;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use PhpOffice\PhpSpreadsheet\Calculation\Database\DVar;

class AppendMissingPins extends Command
{
    protected $signature = 'pins:append-missing';

    protected $description = 'Append missing pins from the database to new_person_file.csv';

    public function handle()
    {
        try {
            // Paths to files

            $csvFile = public_path('fins_only.csv');
            $newCsvFile = public_path('fins_only_NEW.csv');

            // Check if CSV file exists
            if (!file_exists($csvFile)) {
                $this->error("CSV file does not exist at $csvFile");
                return 1;
            }

            $this->info("Reading pins from person_file.csv...");

            // Drop and create the temporary table
            Schema::dropIfExists('temp_csv_pins');
            Schema::create('temp_csv_pins', function ($table) {
                $table->bigIncrements('id'); // Sequence-based id column
                $table->string('pin')->index(); // Index for faster lookups
                $table->boolean('is_processed')->default(false);
            });

            // Insert pins from CSV into the temporary table
            if (($handle = fopen($csvFile, 'r')) !== false) {
                $insertData = [];
                $batchSize = 1000; // Adjust batch size as needed
                while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                    $pin = strtoupper($data[1]);
                    $insertData[] = ['pin' => $pin];

                    // Insert in batches to optimize performance
                    if (count($insertData) >= $batchSize) {
                        DB::table('temp_csv_pins')->insert($insertData);
                        $insertData = [];
                    }
                }
                // Insert any remaining data
                if (!empty($insertData)) {
                    DB::table('temp_csv_pins')->insert($insertData);
                }
                fclose($handle);
            } else {
                $this->error("Error opening $csvFile");
                return 1;
            }

            $this->info("Pins from person_file.csv have been loaded into a temporary table.");

            // Open the new CSV file for appending
            if (($newFileHandle = fopen($newCsvFile, 'a')) === false) {
                $this->error("Cannot open $newCsvFile for writing.");
                return 1;
            }

            $this->info("Processing database records...");

            // Process database records in chunks
            $chunkSize = 1000; // Adjust as needed
            Person::chunk($chunkSize, function ($persons) use ($newFileHandle) {
                foreach ($persons as $person) {
                    $pin = $person->pin;

                    // Check if the pin is not in the temp_csv_pins table
                    $existsInCsv = DB::table('temp_csv_pins')->where('pin', $pin)->exists();

                    if (!$existsInCsv) {
                        // Append the pin and other fields to the new CSV file
                        fputcsv($newFileHandle, [$person->pin, $person->name, $person->email]); // Adjust fields as needed
                    }

                    DB::table('temp_csv_pins')->where('pin', $pin)->update(['is_processed' => true]);
                }

            });

            // Close the new CSV file handle
            fclose($newFileHandle);

            // Drop the temporary table
            //Schema::dropIfExists('temp_csv_pins');

            $this->info("Missing pins have been appended to new_person_file.csv.");

            return 0; // Success
        } catch (\Exception $e) {
            $this->error("An error occurred: " . $e->getMessage());
            return 1; // Error
        }
    }
}
