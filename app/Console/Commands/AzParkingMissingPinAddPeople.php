<?php

namespace App\Console\Commands;

use App\Models\MissingPeople;
use App\Models\Person;
use App\Services\IamasService;
use App\Services\PersonService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class AzParkingMissingPinAddPeople extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'miss-pin-sync:azparking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get missing images of pins in S3 bucket and sync them to S3 bucket';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        protected PersonService $personService
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */


    public function handle()
    {


        $pin = '73EXFMY';

        $data = Http::get(config('servers.dtx_webservice') . '/api/v1/webservices/idcard/pin?Pin=' . $pin);
        $data = json_decode($data->body(), true, 512, JSON_THROW_ON_ERROR);
        dd($data);

        $start = microtime(true);

        $i=0;

//        MissingPeople::query()
//            ->whereNull('is_sync')
//            ->chunk(100, function ($missing_pins) use (&$i) {
//                $missing_pins->map(function ($missing_pin) use (&$i) {
//                    $missing_pin->update(['is_lock' => 1, 'locked_at' => now()]);
//                    $i++;
//                });
//                $this->syncMissingPins($missing_pins);
//            });


        MissingPeople::query()
            //->whereNull('is_sync')
            ->where('fin','1RKQ5QW')
            ->chunkById(100, function ($missing_pins) use (&$i) {
                $ids = $missing_pins->pluck('id')->all();
                MissingPeople::whereIn('id', $ids)->update(['is_lock' => 1, 'locked_at' => now()]);
                $missing_pins->each(function () use (&$i) {
                    $i++;
                });
                $this->syncMissingPins($missing_pins);
                MissingPeople::whereIn('id', $ids)->update(['is_sync' => 1]);
            });



        $end = microtime(true);

        $executionTime = gmdate("H:i:s", $end - $start);

        $this->info("Funksiyanin isleme muddeti: " . $executionTime);
        $this->info("Sync olanlarin sayi: " . $i);

        return 1;
    }


    private function syncMissingPins($missing_pins)
    {
      //  dd($missing_pins);
        foreach ($missing_pins as $missing_pin) {
            $pin = strtoupper($missing_pin->fin);
            try {
                $data = Http::get(config('servers.dtx_webservice') . '/api/v1/webservices/idcard/pin?Pin=' . $pin);
                $data = json_decode($data->body(), true, 512, JSON_THROW_ON_ERROR);
                if (isset($data['Data']['Document']['Number']) && isset($data['Data']['Person']['PIN'])) {
                    if (isset($data['Data']['Person']['Images'][0]['Image'])) {
                        IamasService::saveBase64asImage($data['Data']['Person']['Images'][0]['Image'], $pin, $data['Data']['Document']['Seria'] ?? '');
                    }
                    Person::query()->updateOrCreate(['pin' => $pin], $this->mapData($data));
                    MissingPeople::query()->where('fin', $pin)->update(['is_sync' => 1, 'synced_at' => now()]);
                    $this->info("Updated PIN: " . $pin);
                } else {
                    $this->error("Missing expected data for PIN: $pin");
                    continue;
                }
            } catch (Exception $e) {
                dd([
                    'error' => $e->getMessage(),
                    'line' => $e->getLine(),
                    'file' => $e->getFile(),
                    'pin' => $pin
                ]);
            }
            sleep(1);
        }
    }


    public function mapData($data): array
    {

        if (isset($data['Data']['Person']['MaritalStatus']['Description'])) {
            $marital_status = in_array(strtolower($data['Data']['Person']['MaritalStatus']['Description']), ['single', 'subay']) ? 0 : 1;
        }else{
            $marital_status = 0;
        }


        return [
            'name' => $data['Data']['Person']['NameAz'] ?? "",
            'surname' => $data['Data']['Person']['SurnameAz'] ?? "",
            'father_name' => $data['Data']['Person']['PatronymicAz'] ?? "",
            'birthdate' => IamasService::stringToDate($data['Data']['Person']['BirthDate']),
            'pin' => $data['Data']['Person']['PIN'] ?? "",
            'doc_serial_number' => $data['Data']['Document']['Seria'] ?? "",
            'doc_number' => $data['Data']['Document']['Number'] ?? "",
            'doc_given_date' => IamasService::stringToDate($data['Data']['Document']['GivenDate'] ?? ''),
            'doc_valid_date' => IamasService::stringToDate($data['Data']['Document']['ExpireDate'] ?? ''),
            'sex' => array_search($data['Data']['Person']['Gender']['Description'], Person::$gender, true),
            'marital_status' => $marital_status,
            'blood_group' => $data['Data']['Person']['BloodType']['Description'] ?? "",
            'eye_color' => $data['Data']['Person']['EyeColor']['Description'] ?? "",
            'height' => (int)$data['Data']['Person']['Height'] ?? "",
            'address' => $data['Data']['Person']['IAMASAddress']['FullAddress'] ?? "",
            'country' => $data['Data']['Person']['BirthAddress']['Country']['NameAz'] ?? "",
            'city' => $data['Data']['Person']['BirthAddress']['City'] ?? "",
            'district' => '-1',
            'district_city' => '-1',
            'city_district' => '-',
            'authority_document' => $data['Data']['Document']['GivenOrganization'] ?? "",
            'created_at' => now(),
        ];
    }

}

