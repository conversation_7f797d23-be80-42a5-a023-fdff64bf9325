<?php

namespace App\Console\Commands\BeeinContact;
use Illuminate\Support\Facades\App;

use Illuminate\Console\Command;

class TapazCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bc_migration:tap_az';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try{
            App::call('\App\Http\Controllers\Api\BeeinContactController@migrateSourceData', ['source' => 'tap_az']);
            $this->info('success');

        }catch(\Exception $e){
            $this->info($e->getMessage());
        }
    }
}
