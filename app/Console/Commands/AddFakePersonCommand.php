<?php

namespace App\Console\Commands;

use App\Models\Person;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class AddFakePersonCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'insert:fake_person';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Insert fake person';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Read names and surnames from files

        $names = $this->readFile(public_path('names.txt'));
        $surnames = $this->readFile(public_path('surnames.txt'));

        if (empty($names) || empty($surnames)) {
            $this->error('Names or surnames file is empty or missing.');
            return 1;
        }

        $bloodGroups = ["I(O)Rh+", "I(O)Rh-", "II(A)Rh+", "II(A)Rh-", "III(B)Rh+", "III(B)Rh-", "IV(AB)Rh+", "IV(AB)Rh-"];


        // Parse names and surnames into arrays with gender association
        $parsedNames = $this->parseData($names);
        $parsedSurnames = $this->parseData($surnames);

//        $totalUsers = 3000000;
        $totalUsers = Cache::get('remaining_users', 3000000);
        $chunkSize = 500;

        for ($i = 0; $i < $totalUsers; $i += $chunkSize) {
            $chunkData = [];

            for ($j = 0; $j < $chunkSize; $j++) {
                $randomName = $parsedNames[array_rand($parsedNames)];
                $gender = $randomName['gender'];

                // Filter surnames by gender
                $filteredSurnames = array_filter($parsedSurnames, function ($surname) use ($gender) {
                    return $surname['gender'] === $gender;
                });

                if (empty($filteredSurnames)) {
                    $this->error('No matching surnames found for the selected name.');
                    continue;
                }

                $randomSurname = $filteredSurnames[array_rand($filteredSurnames)]['value'];

                do {
                    $pin = '9' . strtoupper(substr(md5(uniqid()), 0, 5)) . '9';
                } while (Person::query()->where('pin', $pin)->exists());


                // Generate a father name
                $fatherName = $parsedNames[array_rand(array_filter($parsedNames, fn($name) => $name['gender'] === 1))]['value'];
                $fatherName .= $gender === 1 ? ' OĞLU' : ' QIZI';

                // Generate a random birthdate
                $birthdate = date('Y-m-d', mt_rand(strtotime('1950-01-01'), strtotime('2020-12-31')));

                $imageUrl = 'https://thispersondoesnotexist.com/';
                $imageContent = Http::get($imageUrl)->body();
                $imageName = $pin . '.png';
                $imagePath = 'imgs_new/' . $imageName;

                $this->saveImageToS3($imagePath, $imageContent);

                $age = date('Y') - date('Y', strtotime($birthdate));
                $maritalStatus = $age > 30;

                $bloodGroup = $bloodGroups[array_rand($bloodGroups)];

                $time = now();
                $chunkData[] = [
                    'name' => $randomName['value'],
                    'surname' => $randomSurname,
                    'father_name' => $fatherName,
                    'pin' => $pin,
                    'birthdate' => $birthdate,
                    'doc_number' => mt_rand(10000000, 99999999),
                    'eye_color' => $this->getEyeColor(mt_rand(1,6)),
                    'sex' => $gender,
                    'marital_status' => $maritalStatus,
                    'doc_given_date' => '2016-09-19',
                    'doc_valid_date' => '2026-09-19',
                    'address' => 'BAKI ŞƏHƏRİ',
                    'city' => 'BAKI',
                    'country' => 'Azərbaycan Respublikası',
                    'authority_document' => 'Asan 1',
                    'image' => $imagePath,
                    'blood_group' => $bloodGroup,
                    'height' => mt_rand(150, 195),
                    'is_fake' => true,
                    'created_at' => $time,
                    'updated_at' => $time,
                ];

                echo $i." - added".PHP_EOL;
            }

            // Insert chunk into database
            Person::query()->insert($chunkData);

            $totalUsers -= $chunkSize;
            Cache::put('remaining_users', $totalUsers);

            $this->info("Inserted chunk: " . ($i + $chunkSize));
        }

        $this->info('3 million random persons inserted successfully.');

        return 0;
    }

    private function saveImageToS3(string $file, string $imageContent): void
    {
        Storage::disk('s3')->put($file, $imageContent);
    }



    /**
     * Read file content and return an array of lines.
     *
     * @param string $filePath
     * @return array
     */
    protected function readFile(string $filePath): array
    {
        if (!file_exists($filePath)) {
            $this->error("File not found: $filePath");
            return [];
        }

        return array_filter(array_map('trim', file($filePath)));
    }

    /**
     * Parse data into an associative array with value and gender.
     *
     * @param array $lines
     * @return array
     */
    protected function parseData(array $lines): array
    {
        $parsed = [];

        foreach ($lines as $line) {
            [$value, $gender] = explode(' - ', $line);
            $parsed[] = ['value' => $value, 'gender' => (int) $gender];
        }

        return $parsed;
    }

    private function getEyeColor($num): string
    {
        $colors = [
            'Müəyyənləşməmiş',
            'Qara',
            'Qəhvəyi',
            'Yaşıl',
            'Göy',
            'Boz',
            'Qarışıq',
        ];

        return $colors[$num];
    }

}
