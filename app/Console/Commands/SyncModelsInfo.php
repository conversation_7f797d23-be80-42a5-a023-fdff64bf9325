<?php

namespace App\Console\Commands;

use Doctrine\DBAL\Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;
use ReflectionClass;

class SyncModelsInfo extends Command
{
    protected $signature = 'sync:models-info';
    protected $description = 'Sync models and list columns with types';

    /**
     * @throws Exception
     */

    public function handle()
    {
        $modelPath = app_path('Models');
        $modelFiles = scandir($modelPath);

        $modelsInfo = [];

        foreach ($modelFiles as $modelFile) {
            if ($modelFile == '.' || $modelFile == '..') continue;
            $className = '\\App\\Models\\' . pathinfo($modelFile, PATHINFO_FILENAME);
            if (!class_exists($className)) continue;
            $reflectionClass = new ReflectionClass($className);
            if ($reflectionClass->isSubclassOf(Model::class) && !$reflectionClass->isAbstract()) {
                $modelInstance = new $className;
                $tableName = $modelInstance->getTable();
                $connectionName = $modelInstance->getConnectionName() ?? config('database.default');
                try {
                    $foreignKeys = Schema::getConnection()->getDoctrineSchemaManager()->listTableForeignKeys($tableName);
                } catch (\Exception $e) {
                    continue;
                }
                $foreignKeysInfo = [];
                foreach ($foreignKeys as $foreignKey) {
                    $foreignKeysInfo[] = [
                        'name' => $foreignKey->getName(),
                        'local_columns' => $foreignKey->getLocalColumns(),
                        'foreign_table' => $foreignKey->getForeignTableName(),
                        'foreign_columns' => $foreignKey->getForeignColumns(),
                        'options' => [
                            'onUpdate' => $foreignKey->onUpdate(),
                            'onDelete' => $foreignKey->onDelete()
                        ]
                    ];
                }
                $columns = Schema::getColumnListing($tableName);
                $columnsInfo = [];
                foreach ($columns as $column) {
                    try {
                        $columnsInfo[$column] = Schema::getColumnType($tableName, $column);
                    } catch (\Exception $e) {
                        continue;
                    }
                }
                $modelsInfo[$className] = [
                    'connection' => $connectionName,
                    'table' => Str::camel($tableName),
                    'foreign_keys' => $foreignKeysInfo,
                    'columns' => $columnsInfo
                ];
            }
        }

        $this->line(json_encode($modelsInfo, JSON_PRETTY_PRINT));
    }


    public function handle2()
    {
        $modelPath = app_path('Models');
        $modelFiles = scandir($modelPath);

        $modelsInfo = [];

        foreach ($modelFiles as $modelFile) {
            if ($modelFile == '.' || $modelFile == '..') continue;

            $className = '\\App\\Models\\' . pathinfo($modelFile, PATHINFO_FILENAME);

            if (!class_exists($className)) continue;

            $reflectionClass = new ReflectionClass($className);
            if ($reflectionClass->isSubclassOf(Model::class) && !$reflectionClass->isAbstract()) {
                $modelInstance = new $className;
                $tableName = $modelInstance->getTable();
                $connection = $modelInstance->getConnectionName() ?? config('database.default');

                $foreignKeys = Schema::getConnection()->getDoctrineSchemaManager()->listTableForeignKeys($tableName);

                $columns = Schema::getColumnListing($tableName);

                $columnsInfo = [];
                foreach ($columns as $column) {

                    try {
                        $columnsInfo[$column] = Schema::getColumnType($tableName, $column);
                    } catch (\Exception $e) {
                        continue;
                    }


                }

                $modelsInfo[Str::title($className)] = [
                    'connection' => $connection,
                    'table' => Str::camel($tableName),
                    'foreign_keys' => $foreignKeys,
                    'columns' => $columnsInfo
                ];

            }
        }

        $this->line(json_encode($modelsInfo, JSON_PRETTY_PRINT));
    }
}
