<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Person;

class GetSyncPinNebula extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'miss-sync-pin:nebula';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get missing images of pins in S3 bucket and sync them to S3 bucket';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $my_database_pins = [];
        $batch_size = 100;

        Person::query()->select('pin')->lazy($batch_size)
            ->each(function ($person) use (&$my_database_pins) {
                $my_database_pins[] = $person->pin;
            });

        $my_database_pins = array_flip($my_database_pins);

        $this->info('Total pins in my database: ' . count($my_database_pins));


        $person_pins = public_path('person_pins.csv');
        if (!file_exists($person_pins)) {
            $this->error('File not found: ' . $person_pins);
            return 1;
        }

        if (($file = fopen($person_pins, 'r')) === FALSE) {
            $this->error('Unable to open file: ' . $person_pins);
            return 1;
        }

        $not_in_database_file = public_path('not_in_database_pins.csv');
        $file_handle = fopen($not_in_database_file, 'w');
        $line_count = 0;
        while (($line = fgetcsv($file)) !== FALSE) {
            $pin = $line[0];
            $line_count++;
            if (!isset($my_database_pins[$pin])) {
                fputcsv($file_handle, [$pin]);
            }

            if ($line_count % 1000 === 0) {
                $this->info("Processed $line_count lines...");
            }
        }

        fclose($file);
        fclose($file_handle);

        $this->info("Total processed lines: $line_count");
        $this->info("Pins not found in database are stored in: $not_in_database_file");

        return 0;
    }
}

