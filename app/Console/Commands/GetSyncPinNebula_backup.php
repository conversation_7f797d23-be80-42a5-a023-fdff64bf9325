<?php


namespace App\Console\Commands;

use Illuminate\Console\Command;

class GetSyncPinNebula_backup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'miss-sync-pin:nebulabkb';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get missing images of pins in S3 bucket and sync them to S3 bucket';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Start syncing missing images of pins to S3 bucket');

        $person_pins = public_path('person_pins.csv');

        if (!file_exists($person_pins)) {
            $this->error('File not found: ' . $person_pins);
            return 1;
        }

        if (($file = fopen($person_pins, 'r')) === FALSE) {
            $this->error('Unable to open file: ' . $person_pins);
            return 1;
        }

        $line_count = 0;

        $pins_to_sync = array_fill(0, 10, []);

        while (($line = fgetcsv($file)) !== FALSE) {
            $pin = $line[0];
            $line_count++;

            $bucket_index = $line_count % 10;
            $pins_to_sync[$bucket_index][] = $pin;

            if ($line_count % 1000 === 0) {
                $this->info("Processed $line_count lines...");
            }
        }

        fclose($file);

        $this->info("Total processed lines: $line_count");

        foreach ($pins_to_sync as $index => $pins) {
            $this->info("Total pins to sync in bucket $index: " . count($pins));
        }

        return 0;
    }
}
