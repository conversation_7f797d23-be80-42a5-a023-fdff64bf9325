<?php

namespace App\Console\Commands;

use App\Models\Person;
use App\Repository\PersonRepository;
use App\Services\IamasService;
use App\Services\PersonService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use phpseclib3\Crypt\EC;
use App\Services\PersonDataService;

class GetRelationsData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get:relations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        protected PersonService $personService,
        protected PersonDataService $personDataService,
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $people_count = 1000;
        $successLog = public_path('log_pin_db_relations.txt');

        $start = microtime(true);

        echo "Started\n";

        $people = Person::query()->where(['get_relations' => '0'])->limit($people_count)->get();

        $ids = $people->pluck('id');

        print_r($ids);

        Person::query()->whereIn('id', $ids)->update([
            'get_relations' => '2'
        ]);


        if ($people) {
            $x = 0;
            foreach ($people as $person) {
                $pin = strtoupper($person->pin);
                $x++;

                try {
                    $curl = curl_init();

                    curl_setopt_array($curl, array(
                        CURLOPT_URL => "http://127.0.0.1/api/v1/person/".$pin."/relations",
                        CURLOPT_RETURNTRANSFER => true,
                        CURLOPT_ENCODING => '',
                        CURLOPT_MAXREDIRS => 10,
                        CURLOPT_TIMEOUT => 0,
                        CURLOPT_FOLLOWLOCATION => true,
                        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                        CURLOPT_CUSTOMREQUEST => 'GET',
                        CURLOPT_HTTPHEADER => array(
                            'Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwOi8vYXBpLmJlZWluLmxvYy9hcGkvdjEvYXV0aC9sb2dpbiIsImlhdCI6MTY5NjY4NTM3OSwiZXhwIjoxNzY5MjYxMzc5LCJuYmYiOjE2OTY2ODUzNzksImp0aSI6IjVpQUYxVlhTY1RTT2RaWUsiLCJzdWIiOiIxIiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyIsImlzVmVyaWZpZWQiOnRydWV9.qmxU6KUIl3zLv1tSEpuFft1ZO3QIcoXCwQh-WCzyj_Q'
                        ),
                    ));

                    $response = curl_exec($curl);
                    $http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);

                    curl_close($curl);

                    if ($http_status == 200) {
                        Person::query()->where(['pin' => $pin])->update(['get_relations' => '1']);
                        $logData = $pin . " - saved\n";
                        echo $x . ") - saved" . PHP_EOL;
                    } else {
                        $logData = $pin . " - not found\n";

                        echo $x . ") - " . $pin . " - not found api" . PHP_EOL;
                    }
                } catch (Exception $e) {
                    echo $x . ") - not saved" . PHP_EOL;
                    $logData = PHP_EOL . "CRASH - " . $pin . " - " . $e->getMessage() . PHP_EOL;
                    file_put_contents($successLog, $logData, FILE_APPEND);
                }

                // sleep(1);

            }

            $x++;
        }

        echo 'Total : ' . $x;
        $end = microtime(true);
        $executionTime = ceil(($end - $start) / 60);
        $this->info("Funksiyanin isleme muddeti: " . $executionTime . " dəq");

        return 0;
    }
}
