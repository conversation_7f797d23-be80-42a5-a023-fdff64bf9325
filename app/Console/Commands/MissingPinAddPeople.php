<?php

namespace App\Console\Commands;

use App\Models\NebulaFin;
use App\Models\Person;
use App\Services\IamasService;
use App\Services\PersonService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;

class MissingPinAddPeople extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'miss-pin-sync:iamas';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get missing images of pins in S3 bucket and sync them to S3 bucket';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(
        protected PersonService $personService
    )
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */


    public function handle()
    {


        $pin = '6V7DE7R';

        $data = Http::get(config('servers.dtx_webservice') . '/api/v1/webservices/idcard/pin?Pin=' . $pin);
        $data = json_decode($data->body(), true, 512, JSON_THROW_ON_ERROR);
        dd($data);


        $missing_pins = public_path('missing_pins.csv');

        $missing_pins = File::get($missing_pins);

        $missing_pins = explode("\n", $missing_pins);

        $missing_pins = collect($missing_pins)->map(function ($pin) {
            return [
                'fin' => str_ireplace('"', "", $pin)
            ];
        });

        $missing_pins = $missing_pins->filter(function ($pin) {
            return strlen($pin['fin']) === 7;
        });


        $start = microtime(true);

        $pins = $missing_pins->pluck('fin')->toArray();
        foreach ($pins as $pin) {
            $pin = strtoupper($pin);
            try {
                $data = Http::get(config('servers.dtx_webservice') . '/api/v1/webservices/idcard/pin?Pin=' . $pin);
                $data = json_decode($data->body(), true, 512, JSON_THROW_ON_ERROR);
                if (isset($data['Data']['Document']['Number']) && isset($data['Data']['Person']['PIN'])) {

                    if (isset($data['Data']['Person']['Images'][0]['Image'])) {
                        IamasService::saveBase64asImage($data['Data']['Person']['Images'][0]['Image'], $pin, $data['Data']['Document']['Seria'] ?? '');
                    }

                    Person::query()->updateOrCreate(['pin' => $pin], $this->mapData($data));
                    $this->info("Updated PIN: " . $pin);
                } else {
                    // Handle the case where the necessary data is missing
                    $this->error("Missing expected data for PIN: $pin");
                    continue;
                }

            } catch (Exception $e) {

                dd([
                    'error' => $e->getMessage(),
                    'line' => $e->getLine(),
                    'file' => $e->getFile(),
                    'pin' => $pin
                ]);

            }
            sleep(3);
        }


        $end = microtime(true);

        $executionTime = ceil(($end - $start) / 60);

        $this->info("Funksiyanin isleme muddeti: " . $executionTime . " dəq");

        return 1;
    }


    public function mapData($data): array
    {
        return [
            // 'id' => Person::query()->max('id') + 1,
            'name' => $data['Data']['Person']['NameAz'] ?? "",
            'surname' => $data['Data']['Person']['SurnameAz'] ?? "",
            'father_name' => $data['Data']['Person']['PatronymicAz'] ?? "",
            'birthdate' => IamasService::stringToDate($data['Data']['Person']['BirthDate']),
            'pin' => $data['Data']['Person']['PIN'] ?? "",
            'doc_serial_number' => $data['Data']['Document']['Seria'] ?? "",
            'doc_number' => $data['Data']['Document']['Number'] ?? "",
            'doc_given_date' => IamasService::stringToDate($data['Data']['Document']['GivenDate'] ?? ''),
            'doc_valid_date' => IamasService::stringToDate($data['Data']['Document']['ExpireDate'] ?? ''),
            'sex' => array_search($data['Data']['Person']['Gender']['Description'], Person::$gender, true),
            'marital_status' => in_array(strtolower($data['Data']['Person']['MaritalStatus']['Description']), ['single', 'subay']) ? 0 : 1,
            'blood_group' => $data['Data']['Person']['BloodType']['Description'] ?? "",
            'eye_color' => $data['Data']['Person']['EyeColor']['Description'] ?? "",
            'height' => (int)$data['Data']['Person']['Height'] ?? "",
            'address' => $data['Data']['Person']['IAMASAddress']['FullAddress'] ?? "",
            'country' => $data['Data']['Person']['BirthAddress']['Country']['NameAz'] ?? "",
            'city' => $data['Data']['Person']['BirthAddress']['City'] ?? "",
            'district' => '-1',
            'district_city' => '-1',
            'city_district' => '-',
            'authority_document' => $data['Data']['Document']['GivenOrganization'] ?? "",
            'created_at' => now(),
        ];
    }

}

