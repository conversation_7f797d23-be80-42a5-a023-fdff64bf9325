<?php

namespace App\Console\Commands;

use App\Models\Person;
use App\Models\Phone;
use Illuminate\Console\Command;
use Symfony\Component\Console\Command\Command as CommandAlias;

class AddFakePhoneCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'insert:fake_phone';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $prefixes = ['55', '50', '51', '10', '70'];
        $newNumbers = [];

        $people = Person::query()
            ->select('id','pin','name','surname','father_name','doc_number', 'birthdate', 'address')
            ->where([
            'is_fake' => true,
            'added_phone' => false
        ])->limit(500)->inRandomOrder()->get();

        $peopleIds = $people->pluck('id');

        foreach ($people as $person) {
            $phoneCount = mt_rand(1, 3);

            for ($i = 0; $i < $phoneCount; $i++) {
                $prefix = $prefixes[array_rand($prefixes)];

                $randomNumber = $prefix . '0' . mt_rand(1000000, 1999999);

                $otherField = $this->generateOtherField($person);

                if (!Phone::query()->where('phone', $randomNumber)->exists()) {
                    $newNumbers[] = [
                        'phone' => $randomNumber,
                        'pin' => $person->pin,
                        'other' => $otherField
                    ];
                }
            }
        }

        if (!empty($newNumbers)) {
            Phone::query()->insert($newNumbers);

            Person::query()->whereIn('id', $peopleIds)->update([
                'added_phone' => true
            ]);

            $this->info(count($newNumbers) . " new phone numbers added.");
        } else {
            $this->info("No new phone numbers were added.");
        }

        return CommandAlias::SUCCESS;


    }

    private function generateOtherField($person): string
    {
        return $person->name . " " .
                $person->surname . " " .
                $person->father_name . " " .
                $person->address . " " .
                $person->birthdate . " " .
                $person->doc_number;
    }
}
