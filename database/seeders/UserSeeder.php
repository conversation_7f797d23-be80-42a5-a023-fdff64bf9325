<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Role;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        $user = User::create([
            'name' => '<PERSON><PERSON>',
            'surname' => 'Deo',
            'email' => '<EMAIL>',
            'password' => bcrypt('123456')
        ]);

        $role = Role::where(['name' => 'admin'])->first();

        $user->assignRole([$role->id]);
    }
}
