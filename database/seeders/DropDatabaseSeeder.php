<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class DropDatabaseSeeder extends Seeder
{
    /**
     * @var string $connection1
     */
    protected string $connection1 = 'pgsql';

    /**
     * @var string $connection2
     */
    protected string $connection2 = 'mongodb';

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        Schema::connection($this->connection1)->disableForeignKeyConstraints();

        // DB::connection($this->connection1)->statement('SET FOREIGN_KEY_CHECKS=0;');

        Schema::connection($this->connection1)->dropIfExists('password_resets');
        Schema::connection($this->connection1)->dropIfExists('personal_access_tokens');
        Schema::connection($this->connection1)->dropIfExists('model_has_permissions');
        Schema::connection($this->connection1)->dropIfExists('model_has_roles');
        Schema::connection($this->connection1)->dropIfExists('role_has_permissions');
        Schema::connection($this->connection1)->dropIfExists('migrations');
        Schema::connection($this->connection1)->dropIfExists('failed_jobs');
        Schema::connection($this->connection1)->dropIfExists('permissions');
        Schema::connection($this->connection1)->dropIfExists('roles');
        Schema::connection($this->connection1)->dropIfExists('users');
        Schema::connection($this->connection1)->dropIfExists('people');
        Schema::connection($this->connection1)->dropIfExists('cameras');
        Schema::connection($this->connection1)->dropIfExists('camera_types');
        Schema::connection($this->connection2)->dropIfExists('scanned_faces');
        Schema::connection($this->connection2)->dropIfExists('surveillance_faces');
        Schema::connection($this->connection1)->dropIfExists('objects');
        Schema::connection($this->connection1)->dropIfExists('object_types');
        Schema::connection($this->connection1)->dropIfExists('blacklists');
    }
}
