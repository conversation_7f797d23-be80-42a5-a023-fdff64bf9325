<?php

namespace Database\Seeders;

use App\Models\Permission;
use Illuminate\Database\Seeder;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     */
    public function run(): void
    {
        Permission::truncate();

        $permissions =  [
            'permission:permission-index',
            'permission:permission-show',
            'permission:permission-create',
            'permission:permission-update',
            'permission:permission-destroy',
            'permission:permission-store',
            'permission:permission-edit',

            'permission:role-index',
            'permission:role-show',
            'permission:role-create',
            'permission:role-update',
            'permission:role-destroy',
            'permission:role-store',
            'permission:role-edit',

            'permission:user-index',
            'permission:user-show',
            'permission:user-create',
            'permission:user-update',
            'permission:user-destroy',
            'permission:user-store',
            'permission:user-edit',

            'permission:person-index',
            'permission:person-show',
            'permission:person-create',
            'permission:person-update',
            'permission:person-destroy',
            'permission:person-store',
            'permission:person-edit'
        ];

        foreach ($permissions as $permission) {
            Permission::create([
                'guard_name' => 'web',
                'name' => $permission
            ]);
        }
    }
}
