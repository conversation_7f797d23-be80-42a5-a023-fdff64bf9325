<?php

namespace Database\Seeders;

use Exception;
use Faker\Factory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Faker\Generator as Faker;

class PeopleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     * @throws Exception
     */
    public function run(): void
    {
        $faker = Factory::create();

        DB::table('people')->insert([
            'name' => $faker->firstName(),
            'surname' => $faker->lastName(),
            'phone' => $faker->e164PhoneNumber,
            'birthdate' => $faker->date,
            'pin' => random_int(1111111, 9999999),
            'image' => null
        ]);
    }
}
