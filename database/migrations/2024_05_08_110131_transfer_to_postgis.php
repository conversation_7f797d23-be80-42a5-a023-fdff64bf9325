<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class TransferToPostgis extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('pgsql_postgis')->create('polygons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('name');
            $table->text('description')->nullable();
            $table->timestamps();
        });

        Schema::connection('pgsql_postgis')->create('shapes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('polygon_id');
            $table->string('type');
            $table->json('coordinates');
            $table->float('area')->nullable();
            $table->float('radius')->nullable();
            $table->geometry('polygon');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
