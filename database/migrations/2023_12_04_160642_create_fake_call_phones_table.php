<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFakeCallPhonesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('fake_call_phones', function (Blueprint $table) {
            $table->id(); // Assuming you want an auto-incrementing primary key
            $table->timestamp('event_timestamp'); // Using timestamps with time zone information
            $table->string('calling_station_id');
            $table->string('operator', 255); // Adjust the length based on your needs
            $table->integer('lac');
            $table->integer('cell_id');
            $table->double('lon', 15, 8); // Assuming Float64 equivalent
            $table->double('lat', 15, 8); // Assuming Float64 equivalent
            $table->integer('radius');
            $table->integer('beam');
            $table->string('name', 255); // Adjust the length based on your needs
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('fake_call_phones');
    }
}
