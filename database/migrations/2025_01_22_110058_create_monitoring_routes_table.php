<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMonitoringRoutesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('monitoring_routes', function (Blueprint $table) {
            $table->id();
            $table->string('url');
            $table->string('method'); // GET, POST, PUT, DELETE
            $table->json('payload')->nullable(); // JSON
            $table->json('headers')->nullable(); // Special headers
            $table->boolean('status')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('monitoring_routes');
    }
}
