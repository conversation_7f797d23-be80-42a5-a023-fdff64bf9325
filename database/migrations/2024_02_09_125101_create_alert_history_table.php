<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAlertHistoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('alert_history', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fk_id_alert');
            $table->foreign('fk_id_alert')->references('id')->on('alert');
            $table->string('alert_title')->nullable();
            $table->text('alert_content')->nullable();
            $table->text('user_ids')->nullable();
            $table->text('enigma_phones')->nullable();
            $table->tinyInteger('status')->default(0)->comment('0:new 1:sent');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('alert_history');
    }
}
