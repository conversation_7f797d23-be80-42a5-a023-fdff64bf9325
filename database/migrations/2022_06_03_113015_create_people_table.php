<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePeopleTable extends Migration
{
    /**
     * @var string
     */
    protected $connection = 'pgsql';

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('people', static function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('surname');
            $table->string('father_name');
            $table->string('birthdate')->nullable();
            $table->string('pin')->unique();
            $table->string('doc_serial_number', 5)->nullable();
            $table->string('doc_number');
            $table->date('doc_given_date');
            $table->date('doc_valid_date')->nullable();
            $table->enum('sex', [1,2]);
            $table->boolean('marital_status');
            $table->string('blood_group')->nullable();
            $table->string('eye_color')->nullable();
            $table->unsignedTinyInteger('height')->default(0);
            $table->string('address')->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->string('district')->nullable();
            $table->string('district_city')->nullable();
            $table->string('city_district')->nullable();
            $table->string('authority_document')->nullable();
            $table->string('email')->nullable();
            $table->string('image')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('people');
    }
}
