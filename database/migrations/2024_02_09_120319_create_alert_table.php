<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAlertTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('alert', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->tinyInteger('type')->comment('1:axtarş üzrə, 2:zəng üzrə, 3:sərhədkeçmə üzrə');
            $table->string('title')->nullable();
            $table->text('user_ids')->nullable();
            $table->text('enigma_phones')->nullable();
            $table->string('url')->nullable();
            $table->text('params')->nullable();
            $table->longText('result_pins')->nullable();
            $table->tinyInteger('status')->default(0)->comment('1:active 0:deactive');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('alert');
    }
}
