<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFollowingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('social_hub.followings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedInteger('account_id')->default(0);
            $table->smallInteger('social_media_id')->index()->default(1);
            $table->string('following_type')->nullable();
            $table->string('type')->nullable();
            $table->string('title')->nullable();
            $table->text('following_params')->nullable();
            $table->text('user_ids')->nullable();
            $table->text('enigma_phones')->nullable();
            $table->string('url')->nullable();
            $table->longText('result_pins')->nullable();
            $table->tinyInteger('status')->default(0)->comment('1:active 0:deactive');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('followings');
    }
}
