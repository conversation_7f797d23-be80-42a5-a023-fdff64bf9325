<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateBeeinContactTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('beein_contact', function (Blueprint $table) {
            $table->id();
            $table->string('pin', 10)->nullable();
            $table->string('phone', 50)->nullable();
            $table->string('name', 100)->nullable();
            $table->string('email', 100)->nullable();
            $table->string('source', 50)->nullable();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->integer('weight')->nullable();
            $table->text('search_keys')->nullable();
            $table->smallInteger('elastic_index_status')->default(0);
            $table->string('hash', 255)->unique();

        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('beein_contact');
    }
}
