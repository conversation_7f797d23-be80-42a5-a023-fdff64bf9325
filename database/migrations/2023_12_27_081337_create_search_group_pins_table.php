<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSearchGroupPinsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('search_group_pins', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('group_id');
            $table->char('pin', 7)->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('pin')->references('pin')->on('people')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('search_group_pins');
    }
}
