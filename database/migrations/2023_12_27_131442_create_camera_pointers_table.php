<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCameraPointersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('camera_pointers', function (Blueprint $table) {
            $table->id();
            $table->bigInteger('camera_type_id')->unsigned();
            $table->string('name');
            $table->string('latitude', 50);
            $table->string('longitude', 50);
            $table->string('altitude', 50);
            $table->string('heading', 50);
            $table->string('tilt', 50);
            $table->string('range', 50);
            $table->string('altitudeMode', 50);
            $table->string('coordinates_latitude', 50);
            $table->string('coordinates_longitude', 50);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('camera_pointers');
    }
}
