<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLabelPins extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('label_pins', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('fk_id_label');
            $table->foreign('fk_id_label')->references('id')->on('labels');
            $table->string('pin',10);
            $table->integer('degree');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('label_pins');
    }
}
