<?php

use App\Exports\Export;
use App\Models\CameraPointer;
use App\Models\ExactMMKPoints;
use App\Models\FoodOrder;
use App\Models\Person;
use App\Services\ElasticSearch\ESService;
use App\Services\IamasService;
use App\Services\PersonService;
use App\Services\S3Service;
use App\Services\UserInstance;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

if (!function_exists('getBaseDomain')) {
    /**
     * @return string
     */
    function getBaseDomain(): string
    {
        return config('app.env') === 'production' ? 'localhost' : '';
    }
}

if (!function_exists('cmp_timestamp_asc')) {
    /**
     * @param $a
     * @param $b
     * @return int
     */
    function cmp_timestamp_asc($a, $b): int
    {
        if ((int)$a['_source']['timestamp'] === (int)$b['_source']['timestamp']) {
            return 0;
        }
        return ((int)$a['_source']['timestamp'] < (int)$b['_source']['timestamp']) ? -1 : 1;
    }
}

if (!function_exists('cmp_timestamp_desc')) {
    /**
     * @param $a
     * @param $b
     * @return int
     */
    function cmp_timestamp_desc($a, $b): int
    {
        if ((int)$a['_source']['timestamp'] === (int)$b['_source']['timestamp']) {
            return 0;
        }
        return ((int)$a['_source']['timestamp'] < (int)$b['_source']['timestamp']) ? 1 : -1;
    }
}

if (!function_exists('cmp_distance_asc')) {
    /**
     * @param $a
     * @param $b
     * @return int
     */
    function cmp_distance_asc($a, $b): int
    {
        if ((float)$a['_source']['timestamp'] === (float)$b['_source']['timestamp']) {
            return 0;
        }
        return ((float)$a['_source']['timestamp'] < (float)$b['_source']['timestamp']) ? -1 : 1;
    }
}

if (!function_exists('cmp_distance_desc')) {
    /**
     * @param $a
     * @param $b
     * @return int
     */
    function cmp_distance_desc($a, $b): int
    {
        if ((float)$a['_source']['timestamp'] === (float)$b['_source']['timestamp']) {
            return 0;
        }
        return ((float)$a['_source']['timestamp'] < (float)$b['_source']['timestamp']) ? 1 : -1;
    }
}

if (!function_exists('string_to_int_from_array')) {
    /**
     * @param array $array
     * @return array
     */
    function string_to_int_from_array(array $array): array
    {
        return array_map(static function ($value) {
            return (int)$value;
        }, $array);
    }
}

if (!function_exists('neo4j_selector_generator')) {
    /**
     * @param array $array
     * @return string
     */
    function neo4j_selector_generator(array $array): string
    {
        if (count($array) === 0) {
            return 'm';
        }

        $data = in_array('all', $array, true)
            ? 'm'
            : $array;

        if (is_array($data)) {
            $array = array_unique($array);
            $array = array_map('ucfirst', $array);
            $selector = implode(' | ', $array);
            $selector = implode("", [":", $selector]);
        } else {
            $selector = $data;
        }

        return $selector;
    }
}

if (!function_exists('localFileToBase64')) {
    /**
     * @throws FileNotFoundException
     */
    function localFileToBase64(string $file): string
    {
        $contents = Storage::disk('public')->get($file);

        return base64_encode($contents);
    }
}

if (!function_exists('base64ImageSave')) {
    /**
     * @param string $path
     * @param string $base64
     */
    function base64ImageSave(string $path, string $base64): void
    {
        $gdImage = imagecreatefromstring(base64_decode($base64));

        Storage::disk('public')->put($path, $gdImage);

//        dd(storage_path($path));

        imagepng($gdImage, storage_path('app/public/'.$path));
    }
}

if (!function_exists('localFileSaveS3')) {
    /**
     * @throws FileNotFoundException
     */
    function localFileSaveS3(string $file): void
    {
        $contents = Storage::disk('public')->get($file);

        Storage::disk('s3')->put($file, $contents);
    }
}

if (!function_exists('filePathGenerator')) {
    /**
     * @param string $path
     * @param string $prefix
     * @param string $format
     * @return string
     */
    function filePathGenerator(string $path, string $prefix, string $format): string
    {
        return $path . uniqid($prefix, true) . '-' . time() . $format;
    }
}

if (!function_exists('json_validate')) {


    /**
     * @throws JsonException
     */
    function json_validate($string): bool
    {
        $json = json_decode($string, false, 512, JSON_THROW_ON_ERROR);
        return $json && $string !== $json;
    }
}

if (!function_exists('is_json')) {


    /**
     * @param $string
     * @return bool
     */
    function is_json($string): bool
    {
        return !empty($string) && is_string($string) && is_array(json_decode($string, true)) && json_last_error() == 0;
    }
}

if (!function_exists('intToSex')) {
    /**
     * @param int $value
     * @return string|null
     */
    function intToSex(int $value): ?string
    {
        if ($value === 1) {
            return 'Kişi';
        }

        if ($value === 2) {
            return 'Qadın';
        }

        return null;
    }
}

if (!function_exists('sexToInt')) {

    /**
     * @param string $value
     * @return int|null
     */
    function sexToInt(string $value): ?int
    {
        if ($value === 'Kişi' || $value === 'Kisi' || $value === 'Kishi' || $value === 'Men' || $value === 'Man') {
            return 1;
        }

        if ($value === 'Qadın' || $value === 'Qadin' || $value === 'Women' || $value === 'Woman') {
            return 2;
        }

        return null;
    }
}

if (!function_exists('convertDirectionStatusToInt')) {

    /**
     * @param string $direction
     * @return int
     */
    function convertDirectionStatusToInt(string $direction): int
    {
        if ($direction === "Cixish") {
            return 1;
        }

        if ($direction === "Girish") {
            return 2;
        }

        return 0;
    }
}


function findCityByCode($phone)
{
    $city_code_arr = [
        '12' => 'Bakı',
        '88' => 'Bakı',
        '18' => 'Sumqayıt',
        '20' => [
            '20' => 'Bərdə',
            '21' => 'Ucar',
            '22' => 'Ağsu',
            '23' => 'Ağdaş',
            '24' => 'Qobustan',
            '25' => 'Kürdəmir',
            '26' => 'Şamaxı',
            '27' => 'Göyçay',
            '28' => 'İsmayıllı',
            '29' => 'Zərdab',
        ],
        '21' => [
            '20' => 'Hacıqabul',
            '21' => 'Şirvan',
            '22' => 'Beyləqan',
            '23' => 'Sabirabad',
            '24' => 'İmişli',
            '25' => 'Salyan',
            '26' => 'Neftçala',
            '27' => 'Ağcabədi',
            '28' => 'Saatlı'
        ],
        '22' => [
            '25' => 'Gəncə',
            '26' => 'Gəncə',
            '42' => 'Gəncə || Axstafa || Yevlax',
            '20' => 'Göygöl',
            '21' => 'Daşkəsən',
            '22' => 'Axstafa',
            '23' => 'Tərtər',
            '24' => 'Goranboy',
            '27' => 'Samux',
            '29' => 'Qazax',
            '30' => 'Şəmkir',
            '31' => 'Tovuz',
            '32' => 'Gədəbəy',
            '33' => 'Yevlax',
            '35' => 'Naftalan',
        ],
        '23' => [
            '30' => 'Siyəzan',
            '31' => 'Xızı',
            '32' => 'Xaçmaz',
            '33' => 'Quba',
            '35' => 'Şabran',
            '38' => 'Qusar'
        ],
        '24' => [
            '20' => 'Qəbələ',
            '21' => 'Oğuz',
            '22' => 'Zaqatala',
            '24' => 'Şəki',
            '25' => 'Qax',
            '27' => 'Mingəçevir',
            '29' => 'Balakən',
        ],
        '25' => [
            '20' => 'Yardımlı',
            '21' => 'Masallı',
            '22' => 'Astara',
            '24' => 'Cəlilabad',
            '25' => 'Lənkəran',
            '27' => 'Lerik',
            '29' => 'Biləsuvar',
        ],
        '26' => [
            '20' => 'Xocalı',
            '21' => 'Laçın',
            '22' => 'Xankəndi',
            '23' => 'Qubadlı',
            '24' => 'Əsgəran',
            '25' => 'Zəngilan',
            '26' => 'Şuşa',
            '27' => 'Kəlbəcər',
            '28' => 'Ağdərə',
            '29' => 'Xocavənd',
            '30' => 'Hadrut',
            '31' => 'Füzuli',
            '32' => 'Ağdam',
            '38' => 'Cəbrayıl',
        ],
        '36' => [
            '544' => 'Naxçıvan',
            '550' => 'Naxçıvan',
            '554' => 'Naxtel şəbəkəsi üzrə',
            '541' => 'Babek',
            '542' => 'Şərur',
            '552' => 'Şərur',
            '543' => 'Şahbuz',
            '546' => 'Culfa',
            '547' => 'Ordubad',
            '548' => 'Kəngərli',
            '549' => 'Sədərək'
        ],
    ];


    if (strlen((string)$phone) == 7) {
        $phone = '12' . $phone;
    }

    // ilk 2 rəqəm region kodudur
    $reg_code = substr($phone, 0, 2);

    if (in_array($reg_code, [12, 88, 18])) {
        $city_name = $city_code_arr[$reg_code] . '(0' . $reg_code . ')';
    } elseif ($reg_code >= 20 && $reg_code <= 29) {
        // region kodundan sonrakı 2 rəqəm şəhər kodun götürürəm
        $city_code = substr($phone, 2, 2);
        $city_name = $city_code_arr[$reg_code][$city_code] . '(0' . $city_code . ')';
    } elseif ($reg_code == 36) {
        // Naxçıvan kodu istisna olaraq 3 rəqəmlidir
        $city_code = substr($phone, 2, 3);
        $city_name = $city_code_arr[$reg_code][$city_code] . '(' . $city_code . ')';
    } else {
        $city_name = "Müəyyən edilmədi";
    }

    return [
        'phone' => $phone,
        'city' => $city_name
    ];
}

function arrayToPagination($items, $page = 1, $perPage = 10)
{
    $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);
    $total = count($items);
    $currentpage = $page;
    $offset = ($currentpage * $perPage) - $perPage;
    $itemstoshow = array_slice($items, $offset, $perPage);
    return new LengthAwarePaginator($itemstoshow, $total, $perPage);
}

function secondToTime($seconds)
{
    $hours = floor($seconds / 3600);
    $mins = floor($seconds / 60 % 60);
    $secs = floor($seconds % 60);
    return sprintf('%02d:%02d:%02d', $hours, $mins, $secs);
}

function getHTTPResponseStatusCode($url)
{
    $status = null;

    $headers = @get_headers($url, 1);
    if (is_array($headers)) {
        $status = substr($headers[0], 9, 3);
    }

    return $status;
}

function uniqueByValue($array, $key = null): array
{
    if (null === $key) {
        return array_unique($array);
    }
    $keys = [];
    $ret = [];
    foreach ($array as $elem) {
        $arrayKey = (is_array($elem)) ? $elem[$key] : $elem->$key;
        if (in_array($arrayKey, $keys)) {
            continue;
        }
        $ret[] = $elem;
        $keys[] = $arrayKey;
    }
    return $ret;
}


function csvToArrayOld($filename = '', $delimiter = ';'): array
{
    if (file_exists($filename) || is_readable($filename)) {
        $header = NULL;
        $data = [];
        if (($handle = fopen($filename, 'r')) !== FALSE) {
            while (($row = fgetcsv($handle, 1000, $delimiter)) !== FALSE) {
                if (!$header) {
                    $header = $row;
                } else {
                    $row = array_slice($row, 0, count($header));
                    if (count($row) == count($header)) {
                        $data[] = array_combine($header, $row);
                    }
                }
            }
            fclose($handle);
        }
        return $data;
    } else {
        return [];
    }
}

function csvToArray($filename = '', $delimiter = ';'): array
{
    if (file_exists($filename) || is_readable($filename)) {
        $header = NULL;
        $data = [];
        if (($handle = fopen($filename, 'r')) !== FALSE) {
            while (($row = fgetcsv($handle, 1000, $delimiter)) !== FALSE) {
                if (!$header) {
                    $header = $row;
                } else {
                    // Ensure the data row has the same number of elements as the header
                    $row = array_slice($row, 0, count($header));
                    if (count($row) == count($header)) {
                        $data[] = array_combine($header, $row);
                    }
                }
            }
            fclose($handle);
        }
        return $data;
    } else {
        return [];
    }
}

function splitCsvFileWithHeaderAndData($inputFilename, $outputDirectory, $chunkSize = 500): void
{
    $delimiter = ';'; // Change this to the delimiter used in your CSV file
    $lineCount = 0;
    $chunkNumber = 1;
    $outputFilename = "{$outputDirectory}/chunk{$chunkNumber}.csv";
    $header = null;
    $dataRows = [];

    if (($inputHandle = fopen($inputFilename, 'r')) !== false) {
        if (!is_dir($outputDirectory)) {
            mkdir($outputDirectory, 0777, true);
        }

        while (($row = fgetcsv($inputHandle, 0, $delimiter)) !== false) {
            if ($lineCount === 0) {
                // Store the header
                $header = $row;
            } else {
                // Store data rows
                $dataRows[] = $row;
            }

            if ($lineCount >= $chunkSize) {
                // Start a new chunk
                $outputFilename = "{$outputDirectory}/chunk{$chunkNumber}.csv";

                // Write the header to the new chunk file
                $outputHandle = fopen($outputFilename, 'w');
                fputcsv($outputHandle, $header, $delimiter);

                // Write data rows to the new chunk file
                foreach ($dataRows as $dataRow) {
                    fputcsv($outputHandle, $dataRow, $delimiter);
                }

                fclose($outputHandle);

                $chunkNumber++;
                $outputFilename = "{$outputDirectory}/chunk{$chunkNumber}.csv";
                $lineCount = 0;
                $dataRows = [];
            }
            $lineCount++;
        }

        fclose($inputHandle);
    }
}


function orderParse($channel_name, $channel)
{

    $orders = csvToArray($channel_name, ';');
    $orders = collect($orders)->map(function ($item) {
        return collect($item)->mapWithKeys(function ($value, $key) {
            return [Str::slug($key) => $value];
        });
    })->toArray();

    echo count($orders);

    DB::beginTransaction();
    try {
        foreach ($orders as $order) {
            if (isset($order['last-visit']) && $order['last-visit'] != '' && isTimeOrString($order['last-visit'])) {
                if (str_contains($order['last-visit'], '/')) {
                    $order['order-date'] = Carbon::createFromFormat('d/m/Y H:i', $order['last-visit'])->format('Y-m-d H:i:s');
                } else {
                    if (str_contains($order['last-visit'], ' ')) {
                        $order['order-date'] = Carbon::createFromFormat('d.m.Y H:i', $order['last-visit'])->format('Y-m-d H:i:s');
                    } else {
                        $order['order-date'] = Carbon::createFromFormat('d.m.Y', $order['last-visit'])->format('Y-m-d H:i:s');
                    }
                }
            } else {
                $order['order-date'] = null;
            }

            $orderFood = FoodOrder::query()->where('channel', $channel);
            if (isset($order['last-visit']) && $order['last-visit'] != '') {
                $orderFood->where('order_date', $order['order-date']);
            }
            $orderFood = $orderFood->where('phone', $order['phone'])->exists();
            if (!$orderFood) {
                if (str_contains($order['phone'], ',')) {
                    $phones = explode(',', $order['phone']);
                } else {
                    $phones = explode(';', $order['phone']);
                }
                foreach ($phones as $phone) {
                    $orderx = [
                        'channel' => $channel,
                        'name' => $order['name'] ?? null . ' ' . $order['surname'] ?? null,
                        'email' => $order['email'] ?? null,
                        'address' => $order['address'] ?? null,
                        'phone' => trim($phone) ?? null,
                        'data' => json_encode($order),
                    ];


                    if (isset($order['last-visit']) && $order['last-visit'] != '' && isTimeOrString($order['last-visit'])) {
                        if (str_contains($order['last-visit'], '/')) {
                            $order['order-date'] = Carbon::createFromFormat('d/m/Y H:i', $order['last-visit'])->format('Y-m-d H:i:s');
                        } else {
                            if (str_contains($order['last-visit'], ' ')) {
                                $order['order_date'] = Carbon::createFromFormat('d.m.Y H:i', $order['last-visit'])->format('Y-m-d H:i:s');
                            } else {
                                $order['order_date'] = Carbon::createFromFormat('d.m.Y', $order['last-visit'])->format('Y-m-d H:i:s');
                            }
                        }
                    }
                    FoodOrder::create($orderx);
                }
            }
        }
        DB::commit();
    } catch (\Exception $exception) {
        DB::rollBack();
        dd($exception);
    }

    echo 'done';
}

function isTimeOrString($input): bool
{
    if (preg_match('/^\d{4}[-\/]\d{2}[-\/]\d{2}(\s\d{2}:\d{2}:\d{2})?$/', $input)) {
        return true;
    } else {
        return false;
    }
}


if (!function_exists('mb_ucfirst')) {
    function mb_ucfirst($string, $encoding): string
    {
        $firstChar = mb_substr($string, 0, 1, $encoding);
        $then = mb_substr($string, 1, null, $encoding);
        return mb_strtoupper($firstChar, $encoding) . $then;
    }

}


function generateFakeHex($length = 32): string
{
    $characters = '0123456789abcdef';
    $charactersLength = strlen($characters);
    $randomString = '';

    for ($i = 0; $i < $length; $i++) {
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }

    return $randomString;
}


if (!function_exists('sendServerStatusToTelegram')) {

    function sendServerStatusToTelegram($request): void
    {
        try {
            $text = '-------------------------' . PHP_EOL;
            $text .= '-------------------------' . PHP_EOL;
            $text .= 'Name: ' . $request['name'] ?? 'Unknown' . PHP_EOL;
            $text .= '----' . PHP_EOL;
            $text .= 'Host: ' . $request['host'] ?? 'Unknown' . PHP_EOL;
            $text .= '----' . PHP_EOL;
            $text .= 'Type: ' . $request['type'] ?? 'Unknown' . PHP_EOL;
            $text .= '----' . PHP_EOL;
            $text .= 'Reason: ' . $request['message'] ?? 'Unknown' . PHP_EOL;
            $text .= '----' . PHP_EOL;
            $text .= 'Status: ' . $request['status'] ?? 'Unknown' . PHP_EOL;
            $text .= '-------------------------' . PHP_EOL;
            $text .= '-------------------------' . PHP_EOL;

            Http::post('https://api.telegram.org/bot6606035328:AAEoktgSTnr6slOKaFLp6G3pY8AgxQrfhpQ/sendMessage', [
                'chat_id' => '-1002061635129',
                'text' => $text
            ]);

        } catch (Exception $exception) {
            echo $exception->getMessage();
        }
    }
}


if (!function_exists('getCameraPointer')) {
    function getCameraPointer($targetString): ?object
    {
        $camera = ExactMMKPoints::where('name', $targetString)->first();
        if ($camera != null) {
            $camera->coordinates_latitude = $camera->latitude;
            $camera->coordinates_longitude = $camera->longitude;
        } else {
            $cameraPointer = CameraPointer::query()->toBase();
            $cameraPointer = $cameraPointer->get()->toArray();
            $bestMatch = null;
            $highestSimilarity = 0;
            foreach ($cameraPointer as $item) {
                similar_text($item->name, $targetString, $percent);
                if ($percent > $highestSimilarity) {
                    $highestSimilarity = $percent;
                    $bestMatch = $item;
                }
            }
            return $bestMatch;
        }
        return $camera;
    }

    function getCameraPointer_old_2($targetString): ?object
    {
        $cameraPointer = CameraPointer::query()->toBase();
        $cameraPointer = $cameraPointer->get()->toArray();
        $bestMatch = null;
        $highestSimilarity = 0;

        foreach ($cameraPointer as $item) {
            // Calculate similarity percentage
            similar_text($item->name, $targetString, $percent);
            // Check if this is the best match so far
            if ($percent > $highestSimilarity) {
                $highestSimilarity = $percent;
                $bestMatch = $item;
            }
        }
        return $bestMatch;
    }

    function getCameraPointer_old($targetString): ?object
    {
        $cameraPointer = CameraPointer::query()->toBase()->get()->toArray();

        $bestMatch = null;
        $highestSimilarity = 0;

        $targetWords = explode(" ", $targetString);

        foreach ($cameraPointer as $item) {
            $itemWords = explode(" ", $item->name);

            $totalPercent = 0;
            $comparisons = 0;

            foreach ($targetWords as $targetWord) {
                foreach ($itemWords as $itemWord) {
                    $percent = 0;
                    similar_text($targetWord, $itemWord, $percent);
                    $totalPercent += $percent;
                    $comparisons++;
                }
            }

            $averageSimilarity = $comparisons > 0 ? $totalPercent / $comparisons : 0;

            if ($averageSimilarity > $highestSimilarity) {
                $highestSimilarity = $averageSimilarity;
                $bestMatch = $item;
            }
        }

        return $bestMatch;
    }


}

if (!function_exists('cvsToPin')) {
    function cvsToPin($targetString): array
    {
        $array = [];
        if (($open = fopen($targetString, "r")) !== false) {
            while (($data = fgetcsv($open, 1000, ",")) !== false) {
                foreach ($data as $key => $value) {
                    $array[] = $value;
                }
            }
            fclose($open);
        }
        $arrayX = [];
        foreach ($array as $key => $value) {
            $pattern = '/\b([A-Za-z0-9]+)\b/';
            preg_match($pattern, $value, $matches);
            foreach ($matches as $match) {
                if (strlen($match) == 7) {
                    $arrayX[] = strtoupper($match);
                }
            }
            $arrayX = array_unique($arrayX);
        }

        return $arrayX;
    }

}

if (!function_exists('ociConvertor')) {

    function ociConvertor($stid): array|string
    {
        $result = [
            "Name" => 'WORK_PLACE_NAME',
            "NameNew" => 'WORK_PLACE_NAME_NEW',
            "PositionManual" => 'POSITION_MANUAL',
            "Position" => 'position',
            "ConStatus" => 'CON_STATUS',
            "Tpn" => 'EMPLOYER_TPN',
            "JobStartDate" => 'CON_BEGIN_DATE_EMAS',
            "ContractEndDate" => 'CON_END_DATE',
            "WorkPlaceType" => 'WORKPLACE_TYPE'
        ];

        $result = array_flip($result);
        return str_replace(array_keys($result), array_values($result), $stid);
    }

}


if (!function_exists('lab_resultv2')) {

    function lab_resultv2($request)
    {
        $request->merge([
            'BeginDate' => $request->BeginDate ?? '2010-01-01T00:00:00Z',
            'EndDate' => $request->EndDate ?? '2050-01-01T00:00:00Z',
            'Limit' => $request->Limit ?? 100,
            'Page' => $request->Page ?? 1
        ]);
        if ($request->has('from') && $request->filled('from')) {
            $request->merge(['from' => $request->from]);
        }
        if ($request->BeginDate && $request->EndDate) {
            if ($request->Name || $request->Surname || $request->Patronymic || $request->Phone) {

                $result = \App\Models\EtabibUsers::select([
                    'N_PERSON.PIN',
                    'N_PERSON.NAME_AZ',
                    'N_PERSON.SURNAME_AZ',
                    'N_PERSON.PATRONYMIC_AZ',
                    'N_PERSON.ADDRESS',
                    'N_LABORATORY.PHONE',
                    'N_PERSON.BIRTHDATE',
                    'N_LABORATORY.BARCODEDATE',
                    'N_LABORATORY.RESULTDATE',
                    'N_LABORATORY.RESULT'
                ])
                    ->join('N_LABORATORY', function ($join) use ($request) {
                        $join->on('N_PERSON.PROTOCOL', '=', 'N_LABORATORY.PROTOCOL');
                        $join->on('N_LABORATORY.BARCODEDATE', '>', DB::raw("to_timestamp('" . date("Y-m-d H:i:s", strtotime($request->BeginDate)) . "', 'YYYY-MM-DD HH24:MI:SS')"));
                        $join->on('N_LABORATORY.BARCODEDATE', '<', DB::raw("to_timestamp('" . date("Y-m-d H:i:s", strtotime($request->EndDate)) . "', 'YYYY-MM-DD HH24:MI:SS')"));
                    });

                if ($request->Name) {
                    $result = $result->where('N_PERSON.NAME_AZ', 'like', '%' . $request->Name . '%');
                }

                if ($request->Surname) {
                    $result = $result->where('N_PERSON.SURNAME_AZ', 'like', '%' . $request->Surname . '%');
                }
                if ($request->Patronymic) {
                    $result = $result->where('N_PERSON.PATRONYMIC_AZ', 'like', '%' . $request->Patronymic . '%');
                }
                if ($request->Phone) {
                    $result = $result->where('N_LABORATORY.PHONE', 'like', '%' . $request->Phone . '%');
                }
                if ($request->from) {
                    $result = $result->where('N_LABORATORY.BARCODEDATE', '>=', $request->from);
                }
                if ($request->to) {
                    $result = $result->where('N_LABORATORY.BARCODEDATE', '<=', $request->to);
                }
                if ($request->approval_from) {
                    $result = $result->where('N_LABORATORY.RESULTDATE', '>=', $request->approval_from);
                }
                if ($request->approval_to) {
                    $result = $result->where('N_LABORATORY.RESULTDATE', '<=', $request->approval_to);
                }
                if ($request->order_by) {
                    $result = $result->orderBy($request->order_by, $request->order_direction ?? 'asc');
                } else {
                    $result = $result->orderBy('N_LABORATORY.BARCODEDATE', 'asc');
                }
                $result = $result->paginate($request->Limit ?? 1000, ['*'], 'page', $request->Page);

            } else if (strlen($request->Pin) == 7 || strlen($request->Pin) == 6 || strlen($request->Pin) == 5) {

                $result = \App\Models\EtabibUsers::select([
                    'N_PERSON.PIN',
                    'N_PERSON.NAME_AZ',
                    'N_PERSON.SURNAME_AZ',
                    'N_PERSON.PATRONYMIC_AZ',
                    'N_PERSON.ADDRESS',
                    'N_LABORATORY.PHONE',
                    'N_PERSON.BIRTHDATE',
                    'N_LABORATORY.BARCODEDATE',
                    'N_LABORATORY.RESULTDATE',
                    'N_LABORATORY.RESULT'
                ])
                    ->join('N_LABORATORY', function ($join) use ($request) {
                        $join->on('N_PERSON.PROTOCOL', '=', 'N_LABORATORY.PROTOCOL');
                        $join->on('N_LABORATORY.BARCODEDATE', '>', DB::raw("to_timestamp('" . date("Y-m-d H:i:s", strtotime($request->BeginDate)) . "', 'YYYY-MM-DD HH24:MI:SS')"));
                        $join->on('N_LABORATORY.BARCODEDATE', '<', DB::raw("to_timestamp('" . date("Y-m-d H:i:s", strtotime($request->EndDate)) . "', 'YYYY-MM-DD HH24:MI:SS')"));
                    })
                    ->where('N_PERSON.PIN', '=', $request->Pin)
                    ->when($request->from, function ($query) use ($request) {
                        $query->where('N_LABORATORY.BARCODEDATE', '>=', $request->from);
                    })
                    ->when($request->to, function ($query) use ($request) {
                        $query->where('N_LABORATORY.BARCODEDATE', '<=', $request->to);
                    })
                    ->when($request->approval_from, function ($query) use ($request) {
                        $query->where('N_LABORATORY.N_LABORATORY.RESULTDATE', '>=', $request->approval_from);
                    })
                    ->when($request->approval_to, function ($query) use ($request) {
                        $query->where('N_LABORATORY.N_LABORATORY.RESULTDATE', '<=', $request->approval_to);
                    })
                    ->when($request->has('order_by'), function ($query) use ($request) {
                        $query->orderBy($request->order_by, $request->order_direction ?? 'asc');
                    }, function ($query) {
                        $query->orderBy('N_LABORATORY.BARCODEDATE', 'asc');
                    })
                    ->paginate($request->Limit ?? 1000, ['*'], 'page', $request->Page);

            } else {
                $result = \App\Models\EtabibUsers::select([
                    'N_PERSON.PIN',
                    'N_PERSON.NAME_AZ',
                    'N_PERSON.SURNAME_AZ',
                    'N_PERSON.PATRONYMIC_AZ',
                    'N_PERSON.ADDRESS',
                    'N_LABORATORY.PHONE',
                    'N_PERSON.BIRTHDATE',
                    'N_LABORATORY.BARCODEDATE',
                    'N_LABORATORY.RESULTDATE',
                    'N_LABORATORY.RESULT'
                ])
                    ->join('N_LABORATORY', function ($join) use ($request) {
                        $join->on('N_PERSON.PROTOCOL', '=', 'N_LABORATORY.PROTOCOL');
                        $join->on('N_LABORATORY.BARCODEDATE', '>', DB::raw("to_timestamp('" . date("Y-m-d H:i:s", strtotime($request->BeginDate)) . "', 'YYYY-MM-DD HH24:MI:SS')"));
                        $join->on('N_LABORATORY.BARCODEDATE', '<', DB::raw("to_timestamp('" . date("Y-m-d H:i:s", strtotime($request->EndDate)) . "', 'YYYY-MM-DD HH24:MI:SS')"));
                    })
                    ->where('N_PERSON.PROTOCOL', '=', $request->Pin)
                    ->when($request->from, function ($query) use ($request) {
                        $query->where('N_LABORATORY.BARCODEDATE', '>=', $request->from);
                    })
                    ->when($request->to, function ($query) use ($request) {
                        $query->where('N_LABORATORY.BARCODEDATE', '<=', $request->to);
                    })
                    ->when($request->approval_from, function ($query) use ($request) {
                        $query->where('N_LABORATORY.N_LABORATORY.RESULTDATE', '>=', $request->approval_from);
                    })
                    ->when($request->approval_to, function ($query) use ($request) {
                        $query->where('N_LABORATORY.N_LABORATORY.RESULTDATE', '<=', $request->approval_to);
                    })
                    ->when($request->has('order_by'), function ($query) use ($request) {
                        $query->orderBy($request->order_by, $request->order_direction ?? 'asc');
                    }, function ($query) {
                        $query->orderBy('N_LABORATORY.BARCODEDATE', 'asc');
                    })
                    ->paginate($request->Limit ?? 1000, ['*'], 'page', $request->Page);
            }

            $items = array();

            foreach ($result as $item) {

                $res = 'Neqative (-)';

                if ($item->result == 2) {
                    $res = 'Pozitiv (+)';
                }

                $items[] = [
                    "PatientInfo" => [
                        "Pin" => $item->pin,
                        "Name" => $item->name_az,
                        "Surname" => $item->surname_az,
                        "FatherName" => $item->patronymic_az,
                        "FullName" => ($item->name_az ?? '') . " " . ($item->surname_az ?? '') . " " . ($item->patronymic_az),
                        "ResidenceAddress" => $item->address,
                        "RegistrationAddress" => $item->address,
                        "Mobile" => $item->phone,
                        "Sex" => null,
                        "Age" => null,
                        "BirthDate" => date("d.m.Y H:i:s", strtotime($item->birthdate)),
                        "RegistrationPlace" => null
                    ],
                    "PatientLabResult" => [
                        [
                            "Order" => 0,
                            "ResultApprovalDate" => date("d.m.Y H:i:s", strtotime($item->resultdate)),
                            "BarcodeDate" => date("d.m.Y H:i:s", strtotime($item->barcodedate)),
                            "Result" => $res,
                            "LaboratoryName" => null
                        ]
                    ]
                ];
            }

            return [
                'ResultCode' => "S0000",
                'Messages' => [[
                    "Field" => "",
                    "Message" => "The transaction has been successfully completed!",
                ]],
                'Items' => [
                    'Items' => $items,
                    "PageCount" => ceil($result->total() / $result->perPage()),
                    "TotalItemCount" => $result->total(),
                    "PageIndex" => $result->currentPage() - 1,
                    "PageNumber" => $result->currentPage(),
                    "PageSize" => $result->perPage(),
                    "HasPreviousPage" => $result->currentPage() > 1,
                    "HasNextPage" => $result->currentPage() < ceil($result->total() / $result->perPage()),
                    "IsFirstPage" => $result->currentPage() == 1,
                    "IsLastPage" => $result->currentPage() == $result->lastPage()
                ],
            ];
        } else {
            return [
                'Messages' => [[
                    "Message" => "Technical Problems Occur During Processing. Please try again later !",
                    "Field" => ""
                ]],
                'ResultCode' => "F0001"
            ];
        }
    }

}
if (!function_exists('iamasMap')) {

    /**
     * @throws FileNotFoundException
     */
    function iamasMapAsArray(array $data): array
    {

        $mapFields = [
            'name' => $data['Data']['Person']['NameAz'] ?? "",
            'surname' => $data['Data']['Person']['SurnameAz'] ?? "",
            'father_name' => $data['Data']['Person']['PatronymicAz'] ?? "",
            'birthdate' => IamasService::stringToDate($data['Data']['Person']['BirthDate']),
            'pin' => $data['Data']['Person']['PIN'] ?? "",
            'doc_serial_number' => $data['Data']['Document']['Seria'] ?? "",
            'doc_number' => $data['Data']['Document']['Number'] ?? "",
            'doc_given_date' => IamasService::stringToDate($data['Data']['Document']['GivenDate'] ?? ''),
            'doc_valid_date' => IamasService::stringToDate($data['Data']['Document']['ExpireDate'] ?? ''),
            'sex' => array_search($data['Data']['Person']['Gender']['Description'], Person::$gender, true),
            'marital_status' => in_array(strtolower($data['Data']['Person']['MaritalStatus']['Description']), ['single', 'subay']) ? 0 : 1,
            'blood_group' => $data['Data']['Person']['BloodType']['Description'] ?? "",
            'eye_color' => $data['Data']['Person']['EyeColor']['Description'] ?? "",
            'height' => (int)$data['Data']['Person']['Height'] ?? "",
            //'address' => $data['Data']['Person']['IAMASAddress']['FullAddress'] ?? "",
            'country' => $data['Data']['Person']['BirthAddress']['Country']['NameAz'] ?? "",
            'city' => $data['Data']['Person']['BirthAddress']['City'] ?? "",
            'district' => '-1',
            'district_city' => '-1',
            'city_district' => '-',
            'authority_document' => $data['Data']['Document']['GivenOrganization'] ?? "",
        ];

        if (isset($data['Data']['Person']['IAMASAddress']['FullAddress']) && $data['Data']['Person']['IAMASAddress']['FullAddress'] != null) {
            $mapFields['address'] = $data['Data']['Person']['IAMASAddress']['FullAddress'];
        }

        // Save image but maybe I will handle it
        saveBase64asImage($data['Data']['Person']['Images'][0]['Image'], $mapFields["pin"], $data['Data']['Document']['Seria'] ?? "");

        return $mapFields;
    }

}

if (!function_exists('saveBase64asImage')) {
    /**
     * @throws FileNotFoundException
     */
    function saveBase64asImage(string $base64Txt, string $pin, string $docSerialNumber): void
    {
        $docType = PersonService::getDocType($docSerialNumber);
        $file = PersonService::getImgPath($pin, $docType);
        S3Service::saveBase64asImage($file, $base64Txt);
    }
}


if (!function_exists('generatePaginationLinks')) {
    function generatePaginationLinksOld($currentPage, $lastPage, $baseUrl): array
    {
        $links = [];
        for ($i = 1; $i <= $lastPage; $i++) {
            $links[] = [
                "url" => $baseUrl . "?page=" . $i,
                "label" => (string)$i,
                "active" => ($i === $currentPage)
            ];
        }

        return $links;
    }

    function generatePaginationLinks($currentPage, $lastPage, $baseUrl, string $searchID = '', string $ID = ''): array
    {
        $links = [];
        if ($searchID != '') {
            $links[] = [
                "url" => $baseUrl . "?page=1&searchID=" . $searchID,
                "label" => "1",
                "active" => ($currentPage === 1)
            ];
        } else if ($ID != '') {
            $links[] = [
                "url" => $baseUrl . "?page=1&id=" . $ID,
                "label" => "1",
                "active" => ($currentPage === 1)
            ];
        } else {
            $links[] = [
                "url" => $baseUrl . "?page=1",
                "label" => "1",
                "active" => ($currentPage === 1)
            ];
        }
        if ($currentPage > 4) {
            $links[] = [
                "url" => null,
                "label" => "...",
                "active" => false
            ];
        }

        $start = max(2, $currentPage - 1);
        $end = min($lastPage - 1, $currentPage + 1);

        if ($currentPage <= 4) {
            $start = 2;
            $end = min(5, $lastPage - 1);
        }
        if ($currentPage >= $lastPage - 3) {
            $start = max($lastPage - 4, 2);
            $end = $lastPage - 1;
        }

        for ($i = $start; $i <= $end; $i++) {
            if ($searchID != '') {
                $links[] = [
                    "url" => $baseUrl . "?page=" . $i . "&searchID=" . $searchID,
                    "label" => (string)$i,
                    "active" => ($i === $currentPage)
                ];
            } else if ($ID != '') {
                $links[] = [
                    "url" => $baseUrl . "?page=" . $i . "&id=" . $ID,
                    "label" => (string)$i,
                    "active" => ($i === $currentPage)
                ];
            } else {
                $links[] = [
                    "url" => $baseUrl . "?page=" . $i,
                    "label" => (string)$i,
                    "active" => ($i === $currentPage)
                ];
            }
        }

        if ($currentPage < $lastPage - 3) {
            $links[] = [
                "url" => null,
                "label" => "...",
                "active" => false
            ];
        }

        if ($lastPage > 1) {
            if ($searchID != '') {
                $links[] = [
                    "url" => $baseUrl . "?page=" . $lastPage . "&searchID=" . $searchID,
                    "label" => (string)$lastPage,
                    "active" => ($currentPage === $lastPage)
                ];
            } else if ($ID != '') {
                $links[] = [
                    "url" => $baseUrl . "?page=" . $lastPage . "&id=" . $ID,
                    "label" => (string)$lastPage,
                    "active" => ($currentPage === $lastPage)
                ];
            } else {
                $links[] = [
                    "url" => $baseUrl . "?page=" . $lastPage,
                    "label" => (string)$lastPage,
                    "active" => ($currentPage === $lastPage)
                ];
            }
        }
        return $links;
    }

}

function custom_paginate($data, $total, $per_page)
{
    try {
        return new LengthAwarePaginator(
            $data,
            $total,
            $per_page,
            Paginator::resolveCurrentPage(),
            ['path' => Paginator::resolveCurrentPath()]
        );
    } catch (\Exception $e) {
        return $e->getMessage();
    }
}

function es_custom_slug($text, $separator = '-')
{
    $markedText = str_replace('*', 'qwertyiop', $text);
    // print_r($markedText);die;

    $slug = Str::slug($markedText, $separator);

    $finalSlug = str_replace('qwertyiop', '*', $slug);

    return $finalSlug;
}


function formatNumber($number){
    return number_format($number, 0, '.', ' ');
 }

function convertToEnglishDate($dateString)
{
    $months = [
        'yanvar' => 'January',
        'fevral' => 'February',
        'mart' => 'March',
        'aprel' => 'April',
        'may' => 'May',
        'iyun' => 'June',
        'iyul' => 'July',
        'avqust' => 'August',
        'sentyabr' => 'September',
        'oktyabr' => 'October',
        'noyabr' => 'November',
        'dekabr' => 'December',
    ];

    foreach ($months as $local => $english) {
        $dateString = str_replace($local, $english, $dateString);
    }
    return $dateString;
}

function clickhouseCallMap($calls): Collection
{
    return collect($calls)->map(function ($item) {
        $attributes = json_decode($item['attributes'], true);
        $attributes = current($attributes);
        if ($attributes == '1') {
            $item['attributes'] = 'whatsapp';
        } elseif ($attributes == '2') {
            $item['attributes'] = 'telegram';
        } elseif ($attributes == '3') {
            $item['attributes'] = 'signal';
        } elseif ($attributes == '4') {
            $item['attributes'] = 'skype';
        } elseif ($attributes == '5') {
            $item['attributes'] = 'instagram';
        } elseif ($attributes == '6') {
            $item['attributes'] = 'threema';
        } elseif ($attributes == '7') {
            $item['attributes'] = 'wechat';
        } elseif ($attributes == '8') {
            $item['attributes'] = 'viber';
        } elseif ($attributes == '0') {
            $item['attributes'] = 'Məlumat yoxdur';
        }
        $item['attributes'] = '';
        $operator1 = $item['operator1'];
        if ($operator1 == '5') {
            $item['operator1'] = 'Azercell';
        } else {
            $item['operator1'] = 'Məlumat yoxdur';
        }
        $operator2 = $item['operator2'];
        if ($operator2 == '5') {
            $item['operator2'] = 'Azercell';
        } else {
            $item['operator2'] = 'Məlumat yoxdur';
        }
        return $item;
    });
}

function eventsOverlap($event1, $event2): bool
{
    return ($event1['start'] <= $event2['start'] && $event2['start'] <= $event1['end']) ||
        ($event1['start'] <= $event2['end'] && $event2['end'] <= $event1['end']);
}

function identifyCommonPoints(array $data, $small_radius, $big_radius): array
{
    $result = [];
    foreach ($data as $primaryId => $primaryData) {
        foreach ($data as $secondaryId => $secondaryData) {
            if ($primaryId === $secondaryId) {
                continue; // Skip self comparisons
            }

            foreach ($primaryData['list'] as $pEvent) {
                foreach ($secondaryData['list'] as $sEvent) {
                    if (eventsOverlap($pEvent, $sEvent) &&
                        getMetersBetweenPoints($pEvent['lat'], $pEvent['lon'], $sEvent['lat'], $sEvent['lon']) <= $small_radius) {
                        $result[$primaryId][$secondaryId][] = [
                            'event_time' => $sEvent['start'],
                            'location' => ['lat' => $sEvent['lat'], 'lon' => $sEvent['lon']]
                        ];
                    }
                }
            }
        }
    }
    return $result;
}

function getMetersBetweenPoints($lat1, $lon1, $lat2, $lon2): int
{
    if ($lat1 == $lat2 && $lon1 == $lon2) {
        return 0; // The same point
    }
    $p1 = deg2rad($lat1);
    $p2 = deg2rad($lat2);
    $deltaPhi = deg2rad($lat2 - $lat1);
    $deltaLambda = deg2rad($lon2 - $lon1);
    $a = sin($deltaPhi / 2) * sin($deltaPhi / 2) +
        cos($p1) * cos($p2) * sin($deltaLambda / 2) * sin($deltaLambda / 2);
    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
    $earthRadius = 6371008; // Earth's radius in meters
    return (int)($earthRadius * $c);
}

function processData(array $rows): array
{
    $data = [];
    $delta_time = 60;
    $small_radius = 1000; // Radius in meters for proximity
    $big_radius = 5000;  // Radius in meters for distant proximity

    foreach ($rows as $value) {
        $timestamp = strtotime($value['event_timestamp']);

        if (isset($data[$value['calling_station_id']])) {
            $last = &$data[$value['calling_station_id']]['last'];
            if ($last['lat'] == $value['lat'] && $last['lon'] == $value['lon']) {
                $data[$value['calling_station_id']]['list'][$last['last_index']]['end'] = $timestamp + $delta_time;
            } else {
                $last['lat'] = $value['lat'];
                $last['lon'] = $value['lon'];
                $last['last_index']++;
                $data[$value['calling_station_id']]['list'][] = [
                    'lat' => $value['lat'],
                    'lon' => $value['lon'],
                    'start' => $timestamp - $delta_time,
                    'end' => $timestamp + $delta_time,
                ];
            }
        } else {
            $data[$value['calling_station_id']] = [
                'last' => [
                    'lat' => $value['lat'],
                    'lon' => $value['lon'],
                    'last_index' => 0
                ],
                'list' => [
                    [
                        'lat' => $value['lat'],
                        'lon' => $value['lon'],
                        'start' => $timestamp - $delta_time,
                        'end' => $timestamp + $delta_time,
                    ]
                ]
            ];
        }
    }

    // Post-process data to calculate distances and identify common points
    return identifyCommonPoints($data, $small_radius, $big_radius);
}


/// ***************************************************

$small_radius = 1000;
$big_radius = 5000;
$_data = [];
$_result = [];
$ldis = [];

function get_meters_between_points($latitude1, $longitude1, $latitude2, $longitude2)
{
    if (($latitude1 == $latitude2) && ($longitude1 == $longitude2)) {
        return 0;
    } // distance is zero because they're the same point
    $p1 = deg2rad($latitude1);
    $p2 = deg2rad($latitude2);
    $dp = deg2rad($latitude2 - $latitude1);
    $dl = deg2rad($longitude2 - $longitude1);
    $a = (sin($dp / 2) * sin($dp / 2)) + (cos($p1) * cos($p2) * sin($dl / 2) * sin($dl / 2));
    $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
    $r = 6371008; // Earth's average radius, in meters
    $d = $r * $c;
    return (int)$d; // distance, in meters
}

function countCommonPoints($primary, $secondary)
{
    global $_data, $_result, $ldis, $small_radius, $big_radius;
    if (!isset($ldis[$secondary])) {
        $ldis[$secondary] = [
            'last' => [
                'count' => 0,
                'lat' => -1,
                'lon' => -1
            ],
            'list' => []
        ];
    }

    if (!isset($_result[$secondary])) {
        $_result[$secondary] = [
            'count' => 0,
            'list' => []
        ];
    }

    foreach ($_data[$primary]['list'] as $p) {
        foreach ($_data[$secondary]['list'] as $s) {
            if (($p['start'] <= $s['start'] && $s['start'] <= $p['end'] || $p['start'] <= $s['end'] && $s['end'] <= $p['end'])
                && get_meters_between_points($p['lat'], $p['lon'], $s['lat'], $s['lon']) <= $small_radius
            ) {
                $_result[$secondary]['count']++;

                $_result[$secondary]['list'][] = [
                    'lat' => $s['lat'],
                    'lon' => $s['lon'],
                ];

                if ($ldis[$secondary]['last']['count'] == 0) {
                    $ldis[$secondary]['last']['lat'] = $s['lat'];
                    $ldis[$secondary]['last']['lon'] = $s['lon'];
                    $ldis[$secondary]['last']['count'] = 1;
                }

                if (get_meters_between_points($s['lat'], $s['lon'], $ldis[$secondary]['last']['lat'], $ldis[$secondary]['last']['lon']) >= $big_radius) {
                    $ldis[$secondary]['last']['lat'] = $s['lat'];
                    $ldis[$secondary]['last']['lon'] = $s['lon'];
                    $ldis[$secondary]['last']['count']++;
                    $ldis[$secondary]['list'][] = [
                        'lat' => $s['lat'],
                        'lon' => $s['lon'],
                    ];
                }
            }
        }
    }
}

function findSimilarNumberTrajectory($statement, $given)
{
    global $_data, $_result, $ldis;


    if (empty($statement) || !isset($statement)) {
        return [];
    }


//    $query = "WITH main AS (SELECT lat, lon, hash FROM cell_towers_dictionary as c WHERE c.hash IN (
//    SELECT hash FROM dist_radius_log as r WHERE r.calling_station_id='$given' AND r.event_timestamp between '$start_date' AND '$end_date'
//)),
//needed_towers AS (
//    SELECT distinct c.hash FROM cell_towers_dictionary as c, main as m WHERE
//    6371.0 * acos(cos(pi() * (90.0 - c.lat) / 180.0) * cos(pi() * (90.0 - m.lat) / 180.0) + sin(pi() * (90.0 - c.lat) / 180.0) * sin(pi() * (90.0 - m.lat) / 180.0) * cos(pi() * (c.lon - m.lon) / 180.0)) <= 1.0
//)
//SELECT calling_station_id, event_timestamp, lat, lon FROM dist_radius_log as r, cell_towers_dictionary as c WHERE r.event_timestamp between '$start_date' AND '$end_date' AND r.hash IN (SELECT hash FROM needed_towers) AND r.hash=c.hash;";
//
//    $statement = $db->select($query);

    $delat_time = 60;

    foreach ($statement as $value) {

        $timestamp = strtotime($value['event_timestamp']);

        if (isset($_data[$value['calling_station_id']])) {
            if ($_data[$value['calling_station_id']]['last']['lat'] == $value['lat'] && $_data[$value['calling_station_id']]['last']['lon'] == $value['lon']) {
                $_data[$value['calling_station_id']]['list'][$_data[$value['calling_station_id']]['last']['last_index']]['end'] = $timestamp + $delat_time;
            } else {
                $_data[$value['calling_station_id']]['last']['lat'] = $value['lat'];
                $_data[$value['calling_station_id']]['last']['lon'] = $value['lon'];
                $_data[$value['calling_station_id']]['last']['last_index']++;
                $_data[$value['calling_station_id']]['list'][] = [
                    'lat' => $value['lat'],
                    'lon' => $value['lon'],
                    'start' => $timestamp - $delat_time,
                    'end' => $timestamp + $delat_time,
                ];
            }
        } else {
            $_data[$value['calling_station_id']] = [
                'last' => [
                    'lat' => $value['lat'],
                    'lon' => $value['lon'],
                    'last_index' => 0
                ],
                'list' => [
                    [
                        'lat' => $value['lat'],
                        'lon' => $value['lon'],
                        'start' => $timestamp - $delat_time,
                        'end' => $timestamp + $delat_time,
                    ]
                ]
            ];
        }
        // var_dump($value);
    }

    if (empty($_data)) {
        return [];
    }

    foreach ($_data as $key => $value) {
        if ($key != $given) {
            $result_points[$key] = [];
            countCommonPoints($given, $key);
        }
    }

    $conclude = [];
    $max = 0;
    foreach ($ldis as $value) {
        if ($max < $value['last']['count']) {
            $max = $value['last']['count'];
        }
    }

    $mid = $max / 2;
    foreach ($ldis as $key => $value) {
        if ($mid <= $value['last']['count'] && $value['last']['count'] > 2) {
            $conclude[$key] = $value;
        }
    }

    // $ldis
    uasort($conclude, function ($a, $b) {
        if ($a['last']['count'] == $b['last']['count'])
            return 1;
        else
            return $b['last']['count'] - $a['last']['count'];
    });

    // array_reverse($ldis);
    array_reverse($conclude);

    $res = [];

    $res[$given] = [
        'points' => $_data[$given]['list']
    ];

    $top = 10;

    //$ldis
    foreach ($conclude as $key => $value) {
        $res[$key] = [
            'count' => $ldis[$key]['last']['count'],
            // 'short' => $_result[$key]['count'],
            'points' => $_data[$key]['list']
        ];
        if (--$top == 0) break;
    }

    return $res;
}

if (!function_exists('checkerTypeOfArgument')) {
    function checkerTypeOfArgument($input): string
    {
        $phonePattern = '/^994\d{9}$/';
        $phonePatternNoCode = '/\d{9}$/';
        // $platePattern = '/^\d{2}[A-Z]{2}\d{3}$/';
        $platePattern = '/^\d{2}-?[A-Za-z]{2}-?\d{3}$/i';
        $documentPattern = '/^(AA\d{7}|AZE\d{8})$/';
        $pinPattern = '/^[A-Za-z0-9]{7}$/';
        $namePattern = '/^[A-Za-zİiIıƏəÇçŞşĞğÜü]+(?:\s[A-Za-zİiIıƏəÇçŞşĞğÜü]+){0,2}(?:\s+\d{1,2})?$/';

        if (preg_match($phonePattern, $input)) {
            $type = 5;
        } elseif (preg_match($phonePatternNoCode, $input)) {
            $type = 5;
        } elseif (preg_match($platePattern, $input)) {
            $type = 3;
        } elseif (preg_match($namePattern, $input)) {
            $type = 1;
        } elseif (preg_match($documentPattern, $input)) {
            $type = 4;
        } elseif (preg_match($pinPattern, $input)) {
            $type = 2;
        } else {
            $type = 1;
        }
        return $type;
    }
}

function getSectionNameByArgument($input): string
{
    $sections = [
        1 => 'unknown',
        2 => 'pin',
        3 => 'car_number',
        4 => 'doc_number',
        5 => 'phone',
    ];

    $argument = checkerTypeOfArgument($input);

    return $sections[$argument];
}

function searchVehicleOwnerPin($vehicleNumber)
{
    try {
        $response_car = Http::withHeaders([
            'System-Id' => '9ae78b036a90487cbb5b04f1',
            'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
        ])
            ->timeout(10)
            ->get(config('endpoint.find-vehicle-by-pin') . strtoupper(trim($vehicleNumber)) . "/" . config('endpoint.find-vehicle-by-pin-pass'))
            ->json();
        if (isset($response_car['vehicle']) && $response_car['vehicle'] != null) {
            return $response_car;
        }
        return false;
    } catch (\Exception $exception) {
        return false;
    }

}


function findAntennasInPolygonCoverage($request): JsonResponse
{
    $coordinates = $request['coordinates'];
    $radius = $request['radius'];
    $sql = "
        -- Query 1: Find the candidate antenna(s) in which the signal can be reached from the given polygon
with cte_seed_data_0
    as (select '$coordinates' pgn_aswkt,
               ST_SetSRID(
                       ST_GeomFromEWKT('SRID=4326;$coordinates'),
                       4326
                   ) AS                                                                                                                                                                                                   user_polygon_area)
   , cte_seed_data_1 as (SELECT ST_AsGeoJSON(user_polygon_area) :: json -> 'coordinates' AS coordinates,
                                (st_dumppoints(user_polygon_area)).geom                  as ppoint,
                                st_astext((st_dumppoints(user_polygon_area)).geom),
                                user_polygon_area
                         FROM cte_seed_data_0)

   , cte_data_radius as (select ct.hash,
                                ct.lat,
                                ct.lon,
                                ct.beam,
                                ct.radius,
                                sd.user_polygon_area,
                                sd.ppoint                                                           as corner_point,
                                ct.polygon                                                          as tower_polygon,
                                ct.location                                                         as center_antenna,
                                ST_AsText(ST_Project(ct.location, $radius, radians(-6.59 + beam - 60))) as upperPnt_antenna,
                                ST_AsText(ST_Project(ct.location, $radius, radians(-6.59 + beam + 60))) as lowerPnt_antenna

                         from cte_seed_data_1 sd
                                  join public.cell_towers ct
                                       on st_distance(sd.ppoint::geography, ct.location::geography, False) <= $radius or
                                          st_within(ct.location, sd.user_polygon_area))
   , cte_output as (select hash,
                           --radius,
                           user_polygon_area,
                           st_astext(corner_point) as                  corner_point,
                           st_astext(tower_polygon)                    tower_polygon_wkt,
                           st_astext(center_antenna)                   center_antenna_wkt,
                           (center_antenna)                            center_antenna,
                           st_astext(upperPnt_antenna)                 upperPnt_antenna,
                           st_astext(lowerPnt_antenna)                 lowerPnt_antenna,
                           st_distance(ST_SetSRID(upperPnt_antenna::geometry, 4326)::geography,
                                       corner_point::geography, False) dist_a,
                           st_distance(ST_SetSRID(lowerPnt_antenna::geometry, 4326)::geography,
                                       corner_point::geography, False) dist_b
                    from cte_data_radius)
--
   , cte_groupped as (select
--      distinct center_antenna_wkt
                          distinct hash,
--                           center_antenna,
--                           tower_polygon_wkt,
--                           upperPnt_antenna,
--                           lowerPnt_antenna,
--                           center_antenna_wkt,
                          user_polygon_area
                      from cte_output
                      where st_within(center_antenna, user_polygon_area)
                         or ((dist_a + dist_b) <= 2 * $radius
                          and dist_a <= ($radius) * sqrt(3)
                          and dist_b <= ($radius) * sqrt(3)))

select hash,
       st_astext((ST_DumpPoints(ST_GeneratePoints(user_polygon_area, 1))).geom) AS point_wkt
from cte_groupped";
    $result = DB::connection("pgsql_postgis")->select($sql);
    $implodedHashes = implodeHashes($result);
    return getSignalReceivedByAntennasWithinTimeRange($implodedHashes, $result, $request);
}

function implodeHashes($antennas): JsonResponse|string
{
    $hashes = [];
    foreach ($antennas as $item) $hashes[] = $item->hash;
    if (empty($hashes)) {
        $response = [
            "status" => "Error",
            "message" => "Array is empty",
        ];
        return response()->json($response);
    }
    $uniqueAntennaHashesInt = array_unique($hashes);
    return implode(", ", $uniqueAntennaHashesInt);
}

// Elde olunan antennalardan(array of antenna hash) verilen zaman intervalinda hansi antennalardan hansi nomreler signal aldigini mueyyenleshdirmek ucun,
// clickhouse-a sorgu gondermek lazimdir. Netice olaraq: mobile nomre(calling_station_id), antenna_hash, event_timestamps(an array of datetime)
function getSignalReceivedByAntennasWithinTimeRange($hashes, $coverageResult, $coverageRequest): JsonResponse
{
    $timestamp = now()->format('YmdHisv');
    $perPage = $coverageRequest['per_page'];
    $page = $coverageRequest['page'];
    $offset = $coverageRequest['offset'];
    $url = $coverageRequest['url'];
    $fromDate = $coverageRequest['from_date'];
    $toDate = $coverageRequest['to_date'];
    $temp_table_sql = "
        CREATE TABLE IF NOT EXISTS radius_events.polygon_signal_receiver_$timestamp (
            hash Int64,
            start DATETIME('Asia/Baku'),
            end DATETIME('Asia/Baku')
        ) ENGINE = Memory();
    ";

    try {
        $temp_table_result = DB::connection('clickhouse_radius')->statement($temp_table_sql);
        if ($temp_table_result) {
            $values = [];
            $hashesString = explode(", ", $hashes);
            foreach ($hashesString as $hash) {
                $values[] = "($hash, '$fromDate', '$toDate')";
            }
            $valuesString = implode(', ', $values);
            $insert_sql = "
                INSERT INTO radius_events.polygon_signal_receiver_$timestamp (hash, start, end) VALUES
                $valuesString;
            ";

            $insertResult = DB::connection('clickhouse_radius')->insert($insert_sql);

        }
    } catch (\Exception $e) {
        return response()->json(['message' => $e->getMessage()]);
    }
    $sql = "with cte_filtered_radius_logs as (
            select hash, calling_station_id, groupArray(event_timestamp) event_timestamps
            from radius_events.dist_radius_log
            WHERE event_timestamp >= '$fromDate' AND event_timestamp <= '$toDate'
            group by hash, calling_station_id),

            cte_target_antennas as (
            select distinct hash from radius_events.polygon_signal_receiver_$timestamp)

            select logss.hash as antenna_hash, logss.calling_station_id, logss.event_timestamps
            from cte_target_antennas target
            join cte_filtered_radius_logs logss on logss.hash = target.hash

            LIMIT $perPage OFFSET $offset;";

    $results = DB::connection('clickhouse_radius')->select($sql);

    $mergedResults = [];
    foreach ($coverageResult as $coverage) {
        $antennaHash = $coverage->hash;
        $filteredResults = array_filter($results, function ($result) use ($antennaHash) {
            return $result['antenna_hash'] == $antennaHash;
        });
        foreach ($filteredResults as $result) {
            $mergedResults[] = [
                'antenna_hash' => $antennaHash,
                'point_wkt' => $coverage->point_wkt,
                'calling_station_id' => $result['calling_station_id'],
                'event_timestamps' => $result['event_timestamps']
            ];
        }
    }
    $mergedResults = extractCoordinates($mergedResults);
    $countSQL = "
    with cte_filtered_radius_logs as (
select hash, calling_station_id, groupArray(event_timestamp) event_timestamps
from radius_events.dist_radius_log
WHERE event_timestamp >= '$fromDate' AND event_timestamp <= '$toDate'
group by hash, calling_station_id
),

    cte_target_antennas as (
            select distinct hash from radius_events.polygon_signal_receiver_$timestamp)

SELECT COUNT(*) as total
from cte_filtered_radius_logs logss
join cte_target_antennas target on logss.hash = target.hash
    ";
    $totalCountResult = DB::connection('clickhouse_radius')->select($countSQL);
    $drop_table_sql = "DROP TABLE IF EXISTS radius_events.polygon_signal_receiver_$timestamp;";
    $drop_table_result = DB::connection('clickhouse_radius')->statement($drop_table_sql);
    $totalCount = (int)$totalCountResult[0]['total'] ?? 1;
    $lastPage = ceil($totalCount / $perPage);
    $url = url($url);
    $esService = new ESService();
    $phones = collectPhones($mergedResults);
    $params = request('search_params');
    $filteredData = $esService->filterPolygonPhones($phones, $params);
    $mapper = [];
    collect($filteredData)->map(function ($item) use (&$mapper) {
        $mapper[$item['phone']] = $item;
        return $mapper;
    });
    collect($mergedResults)->map(function (&$item) use ($mapper) {
        foreach ($mapper as $k => $v) {
            if ((int)$item['calling_station_id'] == (int)$k) {
                $item['calling_station_data'] = $v;
            }
//              else{
//                  $item['calling_station_data'] = [];
//              }
        }
        return $item;
    });
    $response = [
        "status" => "Success",
        "data" => [
            "current_page" => $page,
            "data" => $mergedResults,
            "first_page_url" => $url . "?page=1",
            "from" => $offset + 1,
            "last_page" => $lastPage,
            "last_page_url" => $url . "?page=" . $lastPage,
            "links" => generatePaginationLinks($page, $lastPage, $url),
            "next_page_url" => $page < $lastPage ? $url . "?page=" . ($page + 1) : null,
            "prev_page_url" => $page > 1 ? $url . "?page=" . ($page - 1) : null,
            "per_page" => $perPage,
            "to" => min($totalCount, ($page - 1) * $perPage + $perPage),
            "total" => $totalCount,
        ],
        "code" => 200,
        "message" => "response ok",
    ];
    return response()->json($response);
}

function extractCoordinates($response)
{
    foreach ($response as &$polygon) {
        $coordinatesString = $polygon['point_wkt'];
        $coordinatesString = str_replace('POINT(', '', $coordinatesString);
        $coordinatesString = str_replace(')', '', $coordinatesString);
        $coordinatesArray = explode(',', $coordinatesString);
        $coordinates = [];
        foreach ($coordinatesArray as $coordinate) {
            $coordinate = explode(' ', $coordinate);
            $coordinates[] = [
                'lat' => (float)$coordinate[1],
                'lng' => (float)$coordinate[0]
            ];
        }
        $polygon['event_timestamps'] = array_map(function ($timestamp) {
            return ['date' => $timestamp];
        }, $polygon['event_timestamps']);
        $polygon['point_wkt'] = $coordinates;
    }
    return $response;
}

function collectPhones($response)
{
    $calling_station_ids = [];
    foreach ($response as $phone) {
        $calling_station_ids[] = $phone['calling_station_id'];
    }
    return $calling_station_ids;
}

if (!function_exists('exportData')) {
    function exportData($data = [], $type_input = "XLSX"): Response|BinaryFileResponse|JsonResponse
    {

        if (!in_array($type_input, ['XLSX', 'HTML', 'DOMPDF'])) {
            return response()->json(['message' => 'Please , use correct type.'], 422);
        }

        $type = [
            'XLSX' => 'xlsx',
            'HTML' => 'html',
            'DOMPDF' => 'pdf'
        ];

        $type_type = [
            'XLSX' => Excel::XLSX,
            'HTML' => Excel::HTML,
            'DOMPDF' => Excel::MPDF
        ];


        if ($data instanceof Collection) {
            $data = $data->toArray();
            $head_tags = array_keys($data[0]);
        } else {
            $head_tags = $data->first()->getAttributes();
            $head_tags = array_keys($head_tags);
            $head_tags = collect($head_tags)->map(function ($item) {
                return ucfirst(str_ireplace('_', ' ', $item));
            })->toArray();
        }


        return (new Export($head_tags))
            ->setQuery(function () use ($data) {
                return $data;
            })
            ->download(
                time() . '.' . $type[$type_input],
                $type_type[$type_input]
            );
    }
}

//if (!function_exists('findSimilarity')) {
//    function findSimilarity($number, $start, $end, $responseCount = 20, $smallRadius = 1.0, $bigRadius = 4.0, $deltaTime = 100)
//    {
//        $client = new Client();
//        $baseURL = config('services.loc_base_url');
//        try {
//            $response = $client->request('GET', $baseURL . 'similarity', [
//                'headers' => [
//                    'Content-Type' => 'application/json',
//                ],
//                'json' => [
//                    'number' => $number,
//                    'start' => $start,
//                    'end' => $end,
//                    'response_count' => $responseCount,
//                    'small_radius' => $smallRadius,
//                    'big_radius' => $bigRadius,
//                    'delta_time' => $deltaTime,
//                ],
//            ]);
//            return json_decode($response->getBody(), true);
//
//        } catch (RequestException $e) {
//            return [
//                'error' => 'Request failed',
//                'message' => $e->getMessage(),
//                'code' => $e->getCode()
//            ];
//        } catch (\Exception $e) {
//            return [
//                'error' => 'An error occurred while processing your request.',
//                'message' => $e->getMessage()
//            ];
//        }
//    }
//}

function iteratePagination($initialUrl)
{
    $client = new Client();
    $url = $initialUrl;

    do {
        // HTTP sorğu göndərin
        $response = $client->request('GET', $url);
        $body = $response->getBody();
        $data = json_decode($body, true);

        // Burada məlumatları işləyin
        print_r($data); // Nümunə olaraq məlumatları çap edin

        // Pagination məlumatlarını alın
        $pagination = $data['pagination'];

        // Növbəti səhifənin URL-ni alın
        $url = $pagination['next_page_url'];

    } while ($url !== null);
}

function getSocialHubPhotoUrl($path): string
{
    return url(config('servers.s3_bucket_server') . '/all-pics/' . $path);
}

function formatPhoneNumber($number)
{
    if (str_starts_with($number, '994')) {
        return substr($number, 3);
    }
    return $number;
}


function sendRequestLogToAudit($body, $section = "request", $setImage = false, $importantWorkName = true, $auth = true)
{
    try {

        if (php_sapi_name() === 'cli') {
            return false;
        }

        $validSections = ['request', 'audit'];
        if (!in_array($section, $validSections)) {
            http_response_code(500);
            header('Content-Type: application/json; charset=utf-8');
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
            header('Content-Type: application/json; charset=utf-8');

            $response = [
                'error' => true,
                'type' => 'undefined section',
                'message' => $section. ' - Log bölməsi tapılmadı',
            ];

            echo json_encode($response, JSON_UNESCAPED_UNICODE);
            die();
//            return 500;
        }
        if ($section == "audit" && $importantWorkName) {
            $hasWorkName = 1;
            $response = [];

            $workData = getUserWorkData(\auth('api')->id());
            if ($workData == null) {
//            if (!request()->has('work_name')) {
                $hasWorkName = 0;
                $response = [
                    'error' => true,
                    'type' => 'work_name',
                    'message' => 'Zəhmət olmasa işin adını qeyd edin.',
                ];
            }

            if (!$hasWorkName) {
                http_response_code(421);
                header('Content-Type: application/json; charset=utf-8');
                header('Access-Control-Allow-Origin: *');
                header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
                header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
                header('Content-Type: application/json; charset=utf-8');

                echo json_encode($response, JSON_UNESCAPED_UNICODE);
                die();
            }else{
                $body['work_name'] = $workData['work_name'] ?? "unknown";
                $body['work_description'] = $workData['work_description'] ?? "unknown";
            }
        }

        $url = config('servers.audit_log_url') . "/api/" . $section . "-log/create";

        if ($auth)
        {
            $userInstance = UserInstance::getInstance()->getUser();

            $body["user_name"] = [
                'id' => $userInstance->id,
                'name' => $userInstance->name,
                'surname' => $userInstance->surname,
            ];
        }

        if (isset($body['input']) && $body['input'] != "") {
            $body['input_type'] = getSectionNameByArgument($body['input']);
        }

        $body['ip'] = IP() ?? '';
        $body['params'] = request()->all();

        if (isset($body['params']['password'])) {
            unset($body['params']['password']);
        }


        if ($setImage) {
            $body['params']['photo'] = $body['photo'];
        }

        return Http::withHeaders([
            'Authorization' => config('servers.audit_log_token'),
        ])->post($url, $body)->json();
    } catch (Exception $exception) {

    }

}

function getUserWorkData($userId)
{
    $cacheKey = 'user_work_' . $userId;

    if (Cache::has($cacheKey)) {
        return Cache::get($cacheKey);
    }

    return null;
}

function removeWorkCache($userId): bool
{
    $cacheKey = 'user_work_' . $userId;

    if (Cache::has($cacheKey)) {
        Cache::forget($cacheKey);
        return true;
    }

    return false;
}


function createSecurityKeyForProfile($userId, $uniqueKey)
{
    $key = 'secret_' . $userId . '_' . $uniqueKey;

    if (Cache::has($key)) {
        return Cache::get($key);
    }
    $secretKey = Str::random(40);
    Cache::put($key, $secretKey, now()->addMinutes(2));
}

function checkIfSecretKeyExists($uniqueKey): bool
{
    if (Auth::check()) {
        $key = 'secret_' . Auth::id() . '_' . $uniqueKey;
        return true;
        return Cache::has($key);
    }
    return false;
}

function processFinChunks($csvFilePath = [], $chunkSize = 1000): bool
{
    if (!file_exists($csvFilePath) || !is_readable($csvFilePath)) {
        return false;
    }

    if (($handle = fopen($csvFilePath, 'r')) !== false) {
        $currentChunk = [];
        while (($data = fgetcsv($handle, 1000, ",")) !== false) {
            if (isset($data[1])) {
                $currentChunk[] = $data[1];
            }
            if (count($currentChunk) == $chunkSize) {
                echo "Yeni Chunk:\n";
                print_r($currentChunk);
                echo "\n";
                $currentChunk = [];
            }
        }
        if (!empty($currentChunk)) {
            echo "Son Chunk:\n";
            print_r($currentChunk);
            echo "\n";
        }

        fclose($handle);
    }

    return true;
}

function getFinChunks($csvFilePath = [], $chunkSize = 1000): bool|array
{
    $finChunks = [];
    $currentChunk = [];

    if (!file_exists($csvFilePath) || !is_readable($csvFilePath)) {
        return false;
    }
    if (($handle = fopen($csvFilePath, 'r')) !== false) {
        while (($data = fgetcsv($handle, 1000, ",")) !== false) {
            if (isset($data[1])) {
                $currentChunk[] = $data[1];
            }
            if (count($currentChunk) == $chunkSize) {
                $finChunks[] = $currentChunk;
                $currentChunk = [];
            }
        }
        if (!empty($currentChunk)) {
            $finChunks[] = $currentChunk;
        }
        fclose($handle);
    }

    return $finChunks;
}

function IP()
{
    if (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_X_FORWARDED'])) {
        $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
    } elseif (isset($_SERVER['HTTP_X_CLUSTER_CLIENT_IP'])) {
        $ipaddress = $_SERVER['HTTP_X_CLUSTER_CLIENT_IP'];
    } elseif (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
    } elseif (isset($_SERVER['HTTP_FORWARDED'])) {
        $ipaddress = $_SERVER['HTTP_FORWARDED'];
    } elseif (isset($_SERVER['REMOTE_ADDR'])) {
        $ipaddress = $_SERVER['REMOTE_ADDR'];
    } else {
        $ipaddress = '127.0.0.1';
    }
    return $ipaddress;
}

function personImagePath($document_serial_number, $pin): string
{
    $path = $document_serial_number != '' ? 'imgs_old' : 'imgs_new';

    $imagePath = $path . "/" . $pin . ".png";

    return config('app.url'). "/api/v1/get-file/show-user-image/" . $imagePath;
}


function getDefaultUserImage(): string
{
    return env('APP_URL') . '/static/media/fbimg.478826c7f397c8c3c8521df389da74c0.svg';
}

function setPermissions()
{
    $user = auth('api')->user();
    $user->setRelation('permissions', $user->getAllPermissions());
}

function checkUserPermission($permissionName)
{
    if (\auth()->check()) {
        setPermissions();
        return auth()->user()->hasPermissionTo($permissionName);
    }
    return false;
}

function generateULID(): string
{
    $time = microtime(true) * 1000;
    $timeHex = substr(str_pad(dechex($time), 10, '0', STR_PAD_LEFT), 0, 10);
    $randomBytes = random_bytes(8);
    $randomHex = bin2hex($randomBytes);
    return strtoupper($timeHex . $randomHex);
}

function az_upper($string): string
{
    $lowerToUpper = [
        'i' => 'İ',
        'ü' => 'Ü',
        'ö' => 'Ö',
        'ə' => 'Ə',
        'ç' => 'Ç',
        'ş' => 'Ş',
        'ğ' => 'Ğ',
        'ı' => 'I',
        'a' => 'A',
        'b' => 'B',
        'c' => 'C',
        'd' => 'D',
        'e' => 'E',
        'f' => 'F',
        'g' => 'G',
        'h' => 'H',
        'x' => 'X',
        'j' => 'J',
        'k' => 'K',
        'l' => 'L',
        'm' => 'M',
        'n' => 'N',
        'o' => 'O',
        'p' => 'P',
        'q' => 'Q',
        'r' => 'R',
        's' => 'S',
        't' => 'T',
        'u' => 'U',
        'v' => 'V',
        'y' => 'Y',
        'z' => 'Z'
    ];
    return strtr(mb_strtolower($string, 'UTF-8'), $lowerToUpper);
}

function convertPolygonToWKT(array $polygon): string
{
    return implode(', ', array_map(fn($point) => "{$point['lng']} {$point['lat']}", $polygon));
}

function formatCarNumber($carNumber): string
{
    $cleanedNumber = str_replace('-', '', $carNumber);
    return strtoupper($cleanedNumber);
}


if (!function_exists('getDomainWithSchema')){
    function getDomainWithSchema(string $url): string
    {
        $parsedUrl = parse_url($url);
        return $parsedUrl['scheme'] . "://" . $parsedUrl['host'];
    }
}


function foreignImagePath($id): string
{
    return env('APP_URL') . "/api/v1/get-file/show-foreign-image/" . $id;
}

function countInVisitsByCountry($data): array
{
     return collect($data['data']['result'] ?? [])
        ->filter(function ($entry) {
            return isset($entry['crossingDirection']) && $entry['crossingDirection'] === 'IN';
        })
        ->map(function ($entry) {
            return $entry['crossingCountry']['nameShortEn'] ?? null;
        })
        ->filter()
        ->countBy()
        ->map(function ($count, $country) {
            return [
                'country' => $country,
                'visits' => $count
            ];
        })
        ->values()
        ->toArray();
}
