<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DevKeyController;

Route::group(['prefix' => 'search/car', 'middleware' => 'dev.key.verify'], static function () {
    Route::get('travel', [DevKeyController::class, 'getVehicleEntered'])->name('dev.key.vehicle-entered');
    Route::get('parking', [DevKeyController::class, 'getAzParking']);
    Route::get('car-travel', [DevKeyController::class, 'getCarVehicleEntered']);

});
