<?php


use App\Events\DiagnosingHealth;
use App\Models\User;

//use App\Services\EHDIS\CrossingBorderInfoService;
//use App\Services\EHDIS\DriverLicenseService;
//use App\Services\EHDIS\GetMobilNumber;
//use App\Services\EHDIS\PersonPhoneService;
//use App\Services\EHDIS\VoenListService;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/


Route::get('travel', function () {

    return Http::withHeaders([
        'Authorization' => 'Bearer eyJhbGciOiJIUzUxMiJ9.********************************************************************************.G8D4wbBLrrB4gldT2QPfEoTUmhuoxurcDEj61izAojEs-7XcrKfWr7ykfs3c-RuB-LUlo4RmnOKmVM9kLriehg',
        'System-Id' => '9ae78b036a90487cbb5b04f1',
        'Structure-Id' => 'f9e0d6de41ce4699857e7d46',
    ])
        ->get(config('servers.vehicle_second_host') . '/api/vehicle-number-recognition?vehicleNumber=77BD187&dateFrom=2024-12-20&dateTo=2024-12-23')
        ->json();
});

Route::get('handled-image/{image?}', function ($image = null) {

    $image = $image ?? '*********.jpg';

    $response = Http::get('http://127.0.0.1:8085/' . $image);
    if ($response->successful()) {
        header('Content-Type: ' . $response->header('Content-Type'));
        echo $response->body();
    } else {
        echo "Failed to load image.";
    }

});


Route::get('/', function () {
    return 'Welcome to Brain, Please be Relax..';
});

Route::get('/phpinfo', function () {
    phpinfo();
});

Route::get('/clientip', function () {
    $ipAddress = request()->ip();
    return response()->json(['client_ip' => $ipAddress]);
});

Route::get('/test-oracle-job', function () {
    try {
        $data = DB::connection('oracle_job')
            ->table('MV_EMAS_DTX_REPORT')
            ->selectRaw('DISTINCT "MOBILE_PHONE", "name", "SURNAME", "PATRONYMIC", "PIN", "DOC_NUMBER", "BIRTH_DATE", "ADDRESS_PIN"')
            ->first();
        dd($data);
    } catch (\Exception $e) {
        dd([
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
    }
});


Route::get('/test', function () {
//    return ImeiListService::run('5WD0A30');


    $fin = '2LGVA1T';
    $soapUrl = "http://**********:8080/EHDISService1.asmx";
    $soapAction = "http://tempuri.org/GetDriverLicenseByPin";

    $xml_post_string = '<?xml version="1.0" encoding="utf-8"?>' .
        '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">' .
        '<soap:Body>' .
        '<GetDriverLicenseByPin xmlns="http://tempuri.org/">' .
        '<fin>' . htmlspecialchars($fin) . '</fin>' .
        '</GetDriverLicenseByPin>' .
        '</soap:Body>' .
        '</soap:Envelope>';

    $headers = array(
        "Content-Type: text/xml; charset=utf-8",
        "Content-Length: " . strlen($xml_post_string),
        "SOAPAction: " . $soapAction
    );

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $soapUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $xml_post_string);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        echo 'Curl error: ' . curl_error($ch);
    } else {
        echo $response;
    }
    curl_close($ch);


});


Route::get('health-check', function () {

    if (request()->has('key') && request()->get('key') == 'DF534534FDGDFGJLK') {
        try {
            $bool = (bool)User::query()->count();
            return response()->json([
                'status' => $bool ? 'success' : 'error',
                'message' => $bool ? 'Server is up and running' : 'Server is down'
            ], $bool ? 200 : 500);
        } catch (\Exception $exception) {
            return $exception->getMessage();
        }
    } else {
        return 'Invalid Key';
    }

});


Route::get('dev', function () {
    $alertHistory = 2;
    return response()->json($alertHistory);
});

Route::get('up', function () {
    $exception = null;

    try {
        Event::dispatch(new DiagnosingHealth);
    } catch (\Throwable $e) {
        if (app()->hasDebugModeEnabled()) {
            throw $e;
        }

        report($e);

        $exception = $e->getMessage();
    }

    if (request()->isJson()){
        return response()->json([
            'status' => $exception ? 'error' : 'success',
            'message' => $exception ? 'Server is down' : 'Server is up and running',
        ], $exception ? 500 : 200);
    }else{
        return response(View::file(__DIR__ . '/../resources/views/health-up.blade.php', [
            'exception' => $exception,
        ]), status: $exception ? 500 : 200);
    }


});
