APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:5G9y2FFMmJPqUEXabk1Ktej7af8VAKDb+lYOoKHArcE=
APP_DEBUG=true
APP_URL=http://api.beein.loc
APP_SCHEME=https
APP_INSTALL=true

LOG_SAVER=mongo

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

CLICKHOUSE_HOST=***********
CLICKHOUSE_PORT=8123
CLICKHOUSE_DATABASE=packet_app
CLICKHOUSE_USERNAME=calls_user
CLICKHOUSE_PASSWORD=HLCskyOW5rMUPndYiS4r
CLICKHOUSE_TIMEOUT_CONNECT=2
CLICKHOUSE_TIMEOUT_QUERY=2
# only if you use https connection
CLICKHOUSE_HTTPS=false

CLICKHOUSE_RADIUS_HOST=***********
CLICKHOUSE_RADIUS_PORT=8123
CLICKHOUSE_RADIUS_DATABASE=radius_events
CLICKHOUSE_RADIUS_USERNAME=
CLICKHOUSE_RADIUS_PASSWORD=
CLICKHOUSE_RADIUS_TIMEOUT_CONNECT=20
CLICKHOUSE_RADIUS_TIMEOUT_QUERY=200
# only if you use https connection
CLICKHOUSE_RADIUS_HTTPS=false

CLICKHOUSE_HOST=***********
CLICKHOUSE_PORT=18123
CLICKHOUSE_DATABASE=packet_app
CLICKHOUSE_USERNAME=root
CLICKHOUSE_PASSWORD=root@333
CLICKHOUSE_TIMEOUT_CONNECT=2
CLICKHOUSE_TIMEOUT_QUERY=2
CLICKHOUSE_HTTPS=false

CLICKHOUSE_RADIUS_HOST=***********
CLICKHOUSE_RADIUS_PORT=18123
CLICKHOUSE_RADIUS_DATABASE=packet_app
CLICKHOUSE_RADIUS_USERNAME=root
CLICKHOUSE_RADIUS_PASSWORD=root@333
CLICKHOUSE_RADIUS_TIMEOUT_CONNECT=20
CLICKHOUSE_RADIUS_TIMEOUT_QUERY=200
# only if you use https connection
CLICKHOUSE_RADIUS_HTTPS=false

CLICKHOUSE_SAVE_HOST=***********
CLICKHOUSE_SAVE_PORT=18123
CLICKHOUSE_SAVE_DATABASE=search_history
CLICKHOUSE_SAVE_USERNAME=root
CLICKHOUSE_SAVE_PASSWORD=root@333
CLICKHOUSE_SAVE_TIMEOUT_CONNECT=20
CLICKHOUSE_SAVE_TIMEOUT_QUERY=200
# only if you use https connection
CLICKHOUSE_SAVE_HTTPS=false

DB_CONNECTION=pgsql
DB_HOST=***********
DB_PORT=5432
DB_DATABASE=beein
DB_USERNAME=beuser
DB_PASSWORD="Rw77#Kf3df"

MONGO_DB_HOST=***********
MONGO_DB_PORT=27017
MONGO_DB_DATABASE=beein
MONGO_DB_USERNAME=beuser
MONGO_DB_PASSWORD="U2b!5#kRa9"
MONGO_DB_URL=mongodb://***********:27017

ORACLE_DB_HOST=************
ORACLE_DB_PORT=1521
ORACLE_DB_DATABASE=DBATEAM
ORACLE_DB_USERNAME=dbateam
ORACLE_DB_PASSWORD="dbateam"
ORACLE_DB_SERVICENAME=orcl

ELASTICSEARCH_HOST=*********** ### Survelliance
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_SCHEME=http
ELASTICSEARCH_USER=elastic
ELASTICSEARCH_PASS="4d4riskx5PpCrPEH0YbU"

ELASTICSEARCH_HOST_2=*********** ### Advantage Search
ELASTICSEARCH_PORT_2=9200
ELASTICSEARCH_SCHEME_2=http
ELASTICSEARCH_USER_2=elastic
ELASTICSEARCH_PASS_2="4d4riskx5PpCrPEH0YbU"

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=rabbitmq
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

RABBITMQ_HOST=***********
RABBITMQ_PORT=5672
RABBITMQ_USER=dev
RABBITMQ_PASSWORD='oB*55lWY755o'
RABBITMQ_QUEUE=fin_unique_faces
RABBITMQ_VHOST=/

REDIS_HOST=beein-redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=PSFBSAZRDEBLDEJEKBMHALOINJFCAIPEIGMGFFDNP
AWS_SECRET_ACCESS_KEY=F95356C684f80259+a184628C62533a105c2CKPP
AWS_BUCKET=api
AWS_ENDPOINT=http://pure.beein.loc
AWS_DEFAULT_REGION=us-west-1
AWS_FILE_URL="http://***********:8080/"

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

JWT_SECRET=XrusricTvej9NvvTUUtZubSQE29bBLkMDgcaNYcgkc6vOuSdG4cs4VMxjScJDSNG
JWT_TTL=31104000
JWT_REFRESH_TTL=31104000
JWT_BLACKLIST_ENABLED=false
JWT_BLACKLIST_GRACE_PERIOD=30

L5_SWAGGER_CONST_HOST=http://***********/api/v1

BLACKLIST_SIMILARITY=0.65
BLACKLIST_MAX_COUNT=3

NEBULA_HOST=http://***********:5001
NEBULA_PATH=***********

NGRAPH_CLIENT=***********
NGRAPH_PORT=9669
NGRAPH_USERNAME=app_beein
NGRAPH_PASSWORD=T7gPa9eY

S3_BEEIN_LOC=http://s3.beein.loc
BACKEND_SERVER=http://***********:88
FRONTEND_SERVER=http://***********:8580

MILVUS_HOST=***********
IAMAS_HOST=http://***********:90
#DTX_WEBSERVICE=http://**********:8081
DTX_WEBSERVICE=http://localhost:8081
IAMAS_PERSON=http://**********:5099
VEHICLE_HOST=http://***********:8185
VEHICLE_SECOND_HOST=http://***********:8191

WORK_HOST=http://***********
PERSON_HOST=http://***********:5000
STUDENT_HOST=http://***********:8480

EHDIS_HOST=http://**********:8080
GPU_API_V1=http://***********:8000
GPU_API_V2=http://***********:8000/v2
GPU_API_PREDICT=http://***********:8123/predict
#GPU_API_PREDICT_FILE=http://***********:8123/predict_file #PROD
GPU_API_PREDICT_FILE=http://***********:8123/predict_file #DEV

GPU_API_SINGLE=http://***********:8000/

SIMILAR_IMAGE_HOST=http://***********/

ENIGMA_RABBITMQ_ACTIVE=true
ENIGMA_RABBITMQ_HOST=************
ENIGMA_RABBITMQ_PORT=15672
ENIGMA_RABBITMQ_USER=api
ENIGMA_RABBITMQ_PASSWORD=d322f4Ddg65dSw3fTsxF42f
ENIGMA_RABBITMQ_VHOST='/'

SOCIAL_CALLS_ENABLED=true

AUDIT_LOG_URL=http://api.beein.loc:8082
#AUDIT_LOG_URL=http://***********:8080 //prod

CORS_ALLOW_ORIGIN='http://beein.loc,http://beein.az,http://beein-v1.test'
CORS_ALLOW_REFERER='http://beein.loc,https://home.beein.az,http://***********:81/,http://beein-v1.test'
