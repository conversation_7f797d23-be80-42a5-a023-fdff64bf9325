<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Swagger UI with CDN</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css">
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin: 0;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div id="swagger-ui">
        <div style="padding: 20px; text-align: center; color: #666;">
            <h3>Loading CDN Swagger UI...</h3>
            <p>Testing if SwaggerUIBundle works with CDN versions.</p>
        </div>
    </div>

    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
    <script>
        function testCDNSwaggerUI() {
            console.log('CDN Swagger UI loading...');
            console.log('SwaggerUIBundle available:', typeof SwaggerUIBundle !== 'undefined');
            console.log('SwaggerUIStandalonePreset available:', typeof SwaggerUIStandalonePreset !== 'undefined');
            
            var swaggerContainer = document.getElementById('swagger-ui');
            if (!swaggerContainer) {
                console.error('Swagger UI container not found');
                return;
            }
            
            if (typeof SwaggerUIBundle === 'undefined') {
                console.error('SwaggerUIBundle is not loaded');
                swaggerContainer.innerHTML = '<div style="padding: 20px; color: red;">SwaggerUIBundle is not loaded.</div>';
                return;
            }
            
            try {
                console.log('Initializing CDN SwaggerUIBundle...');
                const ui = SwaggerUIBundle({
                    dom_id: '#swagger-ui',
                    url: "http://127.0.0.1:8000/docs?api-docs.json",
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIStandalonePreset
                    ],
                    plugins: [
                        SwaggerUIBundle.plugins.DownloadUrl
                    ],
                    layout: "StandaloneLayout",
                    deepLinking: true,
                    filter: true,
                    persistAuthorization: false,
                    onComplete: function() {
                        console.log('CDN Swagger UI loaded successfully');
                    },
                    onFailure: function(error) {
                        console.error('CDN Swagger UI failed to load:', error);
                    }
                });
                
                window.ui = ui;
                console.log('CDN Swagger UI initialized successfully');
                
            } catch (error) {
                console.error('Error initializing CDN Swagger UI:', error);
                swaggerContainer.innerHTML = '<div style="padding: 20px; color: red;">Error loading CDN Swagger UI: ' + error.message + '</div>';
            }
        }

        // Try with retry logic
        function tryCDNTest(attempt = 1) {
            if (typeof SwaggerUIBundle !== 'undefined') {
                testCDNSwaggerUI();
            } else if (attempt < 5) {
                console.log('Retrying CDN test... attempt', attempt);
                setTimeout(() => tryCDNTest(attempt + 1), 200);
            } else {
                console.error('CDN Test failed - SwaggerUIBundle not available');
                document.getElementById('swagger-ui').innerHTML = '<div style="padding: 20px; color: red;">CDN SwaggerUIBundle not available after 5 attempts</div>';
            }
        }

        window.onload = () => setTimeout(tryCDNTest, 100);
    </script>
</body>
</html>
