{"proxyList": [{"regNumber": "7909", "regDate": "2023-06-30T00:00:00", "strFullName": "Saatlı r. NO", "strCode": "803", "startDate": "2023-06-30T00:00:00", "expireDate": "2031-12-16T00:00:00", "cancelDate": null, "statusId": 0, "statusNote": "<PERSON><PERSON><PERSON><PERSON><PERSON> edilib", "typeId": 15, "insertId": 100047015592, "tpNumber": "BD274977", "vehRegNumber": "77BD187", "vehMarka": "KIA OPTIMA MİNİK", "toDlCode": "BC024619", "ownerCitizen": {"id": 0, "note": "", "name": null, "surname": null, "patronymic": null, "birthDate": null, "birthPlace": null, "regionId": null, "regAddress": null, "livePlace": null, "gender": {"id": 0, "name": "", "note": ""}, "phone": null, "email": null, "type": {"id": 0, "name": "", "note": ""}, "idSeries": null, "idNumber": null, "pin": null, "passportNo": null, "fullName": "null null null", "bloodGroup": null, "taxNumber": null, "cityzenship": 0, "image": null}, "fromCitizen": {"id": 0, "note": "", "name": "RAMİL", "surname": "HÜSEYNOV", "patronymic": "HAFİZ OĞLU", "birthDate": null, "birthPlace": null, "regionId": null, "regAddress": null, "livePlace": null, "gender": {"id": 0, "name": "", "note": ""}, "phone": null, "email": null, "type": {"id": 0, "name": "", "note": ""}, "idSeries": null, "idNumber": null, "pin": null, "passportNo": null, "fullName": "HÜSEYNOV RAMİL HAFİZ OĞLU", "bloodGroup": null, "taxNumber": null, "cityzenship": 0, "image": null}, "toCitizen": {"id": 0, "note": "", "name": "ELŞƏN", "surname": "ƏLİYEV", "patronymic": "RAMİZ OĞLU", "birthDate": null, "birthPlace": null, "regionId": null, "regAddress": "SUMQAYIT, MİK-13, , EV 50 , MƏN 7", "livePlace": "SUMQAYIT, MİK-13, , EV 50 , MƏN 7", "gender": {"id": 0, "name": "", "note": ""}, "phone": "(055)904-03-13", "email": null, "type": {"id": 0, "name": "", "note": ""}, "idSeries": null, "idNumber": null, "pin": null, "passportNo": null, "fullName": "ƏLİYEV ELŞƏN RAMİZ OĞLU", "bloodGroup": null, "taxNumber": null, "cityzenship": 0, "image": null}, "toDocType": 2, "attorneyTypeId": 0, "toRegRegionCode": null}], "messagingModel": {"code": 0, "message": "", "transactionNumber": "-*********"}}