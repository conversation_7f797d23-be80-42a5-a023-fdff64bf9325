<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Test Swagger UI</title>
    <link rel="stylesheet" type="text/css" href="http://127.0.0.1:8000/docs/asset/swagger-ui.css">
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin: 0;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div id="swagger-ui">
        <div style="padding: 20px; text-align: center; color: #666;">
            <h3>Loading Test Swagger UI...</h3>
            <p>Testing if SwaggerUIBundle works with our JSON.</p>
        </div>
    </div>

    <script src="http://127.0.0.1:8000/docs/asset/swagger-ui-bundle.js" onload="console.log('Bundle loaded')" onerror="console.error('Bundle failed')"></script>
    <script src="http://127.0.0.1:8000/docs/asset/swagger-ui-standalone-preset.js" onload="console.log('Preset loaded')" onerror="console.error('Preset failed')"></script>
    <script>
        function testSwaggerUI() {
            console.log('Test Swagger UI loading...');
            console.log('SwaggerUIBundle available:', typeof SwaggerUIBundle !== 'undefined');
            console.log('SwaggerUIStandalonePreset available:', typeof SwaggerUIStandalonePreset !== 'undefined');

            var swaggerContainer = document.getElementById('swagger-ui');
            if (!swaggerContainer) {
                console.error('Swagger UI container not found');
                return;
            }

            if (typeof SwaggerUIBundle === 'undefined') {
                console.error('SwaggerUIBundle is not loaded');
                swaggerContainer.innerHTML = '<div style="padding: 20px; color: red;">SwaggerUIBundle is not loaded.</div>';
                return;
            }

            try {
                console.log('Initializing SwaggerUIBundle...');
                const ui = SwaggerUIBundle({
                    dom_id: '#swagger-ui',
                    url: "http://127.0.0.1:8000/docs?api-docs.json",
                    presets: [
                        SwaggerUIBundle.presets.apis,
                        SwaggerUIStandalonePreset
                    ],
                    plugins: [
                        SwaggerUIBundle.plugins.DownloadUrl
                    ],
                    layout: "StandaloneLayout",
                    deepLinking: true,
                    filter: true,
                    persistAuthorization: false
                });

                window.ui = ui;
                console.log('Test Swagger UI initialized successfully');

            } catch (error) {
                console.error('Error initializing Test Swagger UI:', error);
                swaggerContainer.innerHTML = '<div style="padding: 20px; color: red;">Error loading Test Swagger UI: ' + error.message + '</div>';
            }
        }

        // Try with retry logic
        function tryTest(attempt = 1) {
            if (typeof SwaggerUIBundle !== 'undefined') {
                testSwaggerUI();
            } else if (attempt < 5) {
                console.log('Retrying test... attempt', attempt);
                setTimeout(() => tryTest(attempt + 1), 200);
            } else {
                console.error('Test failed - SwaggerUIBundle not available');
                document.getElementById('swagger-ui').innerHTML = '<div style="padding: 20px; color: red;">SwaggerUIBundle not available after 5 attempts</div>';
            }
        }

        window.onload = () => setTimeout(tryTest, 100);
    </script>
</body>
</html>
