FROM composer:2.8.5 as vendor

WORKDIR /app

COPY ./packages /app/packages
COPY composer.json composer.json

RUN composer install --ignore-platform-reqs --no-scripts



FROM php:8.1.9-fpm-buster

# Install necessary dependencies for PHP and Oracle
RUN apt-get update && apt-get install -y \
    libaio1 \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Download and install Oracle Instant Client
ADD https://download.oracle.com/otn_software/linux/instantclient/1926000/instantclient-basiclite-linux.x64-*********.0dbru.zip /tmp/instantclient.zip

ADD https://download.oracle.com/otn_software/linux/instantclient/1926000/instantclient-sdk-linux.x64-*********.0dbru.zip /tmp/instantclient-sdk.zip

RUN unzip /tmp/instantclient.zip -d /usr/local/ \
    && unzip /tmp/instantclient-sdk.zip -d /usr/local/ \
    && ln -s /usr/local/instantclient_19_26 /usr/local/instantclient \
    && echo '/usr/local/instantclient' > /etc/ld.so.conf.d/oracle-instantclient.conf \
    && ldconfig

# Install PHP Oracle extensions
RUN docker-php-ext-configure oci8 --with-oci8=instantclient,/usr/local/instantclient \
    && docker-php-ext-install oci8 \
    && docker-php-ext-enable oci8
RUN ldconfig

# RUN apt-get update
RUN apt-get install -y build-essential libssl-dev zlib1g-dev libpng-dev libjpeg-dev libfreetype6-dev
RUN apt-get install -y libzip-dev zip
RUN apt-get install -y libpq-dev  # Required for PostgreSQL support
RUN apt-get install -y nginx


# Install PHP extensions
RUN printf "\n" | pecl install mongodb && docker-php-ext-enable mongodb
RUN docker-php-ext-install pdo pdo_mysql pdo_pgsql gd
RUN docker-php-ext-install zip

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

WORKDIR /var/www/html

COPY --from=vendor app/vendor/ ./vendor/
COPY --from=vendor app/composer.lock ./composer.lock
COPY . .
COPY nginx/default.conf /etc/nginx/sites-enabled/default

RUN chmod -R gu+w storage
RUN chmod -R gu+w bootstrap/cache
RUN chmod  +x docker-entrypoint.sh

# Run commands
ENTRYPOINT ["/bin/bash", "./docker-entrypoint.sh"]
