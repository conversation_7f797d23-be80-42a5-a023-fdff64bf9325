<?php

namespace Tests\Integration;

use App\Models\Person;
use App\Models\User;
use App\Models\Blacklist;
use App\Models\Camera;
use App\Models\Object\Object_;
use App\Models\ObjectType;
use App\Models\CameraType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class DatabaseIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false;
    }

    public function testDatabaseConnectionsWork(): void
    {
        // Test PostgreSQL connection
        $this->assertTrue(DB::connection('pgsql')->getPdo() instanceof \PDO);
        
        // Test MongoDB connection if configured
        if (config('database.connections.mongodb')) {
            $this->assertTrue(DB::connection('mongodb')->getPdo() instanceof \PDO);
        }
    }

    public function testUserModelRelationships(): void
    {
        $user = User::factory()->create();
        
        // Test user can have roles
        $role = \Spatie\Permission\Models\Role::create(['name' => 'admin', 'guard_name' => 'api']);
        $user->assignRole($role);
        
        $this->assertTrue($user->hasRole('admin'));
        $this->assertCount(1, $user->roles);
    }

    public function testPersonModelOperations(): void
    {
        // Test creating person
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'TEST123',
            'is_sync' => true
        ]);

        $this->assertInstanceOf(Person::class, $person);
        $this->assertDatabaseHas('people', ['pin' => 'TEST123']);

        // Test updating person
        $person->update(['name' => 'Jane']);
        $this->assertEquals('Jane', $person->fresh()->name);

        // Test deleting person
        $personId = $person->id;
        $person->delete();
        $this->assertDatabaseMissing('people', ['id' => $personId]);
    }

    public function testBlacklistModelOperations(): void
    {
        $blacklist = Blacklist::create([
            'name' => 'Criminal',
            'surname' => 'Person',
            'pin' => 'BL123',
            'status' => 1
        ]);

        $this->assertInstanceOf(Blacklist::class, $blacklist);
        $this->assertDatabaseHas('blacklists', ['pin' => 'BL123']);

        // Test status filtering
        $activeBlacklists = Blacklist::where('status', 1)->get();
        $this->assertCount(1, $activeBlacklists);
    }

    public function testCameraObjectRelationships(): void
    {
        // Create object type
        $objectType = ObjectType::create([
            'name' => 'Building',
            'description' => 'Test building'
        ]);

        // Create object
        $object = Object_::create([
            'object_type_id' => $objectType->id,
            'name' => 'Test Building',
            'address' => 'Test Address',
            'gps' => ['lat' => 40.4093, 'lng' => 49.8671],
            'active' => true
        ]);

        // Create camera type
        $cameraType = CameraType::create([
            'name' => 'IP Camera',
            'description' => 'Test camera'
        ]);

        // Create camera
        $camera = Camera::create([
            'camera_id' => 'CAM001',
            'name' => 'Test Camera',
            'object_id' => $object->id,
            'camera_type_id' => $cameraType->id,
            'active' => true
        ]);

        // Test relationships
        $this->assertEquals($object->id, $camera->object->id);
        $this->assertEquals($cameraType->id, $camera->type->id);
        $this->assertEquals($objectType->id, $object->type->id);
    }

    public function testComplexQueries(): void
    {
        // Create test data
        $user1 = User::factory()->create(['name' => 'John', 'status' => 1]);
        $user2 = User::factory()->create(['name' => 'Jane', 'status' => 0]);
        
        Person::factory()->create(['name' => 'John', 'surname' => 'Doe']);
        Person::factory()->create(['name' => 'Jane', 'surname' => 'Smith']);
        
        // Test complex query with joins
        $results = DB::table('users')
            ->join('people', 'users.name', '=', 'people.name')
            ->where('users.status', 1)
            ->select('users.name', 'people.surname')
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals('John', $results->first()->name);
        $this->assertEquals('Doe', $results->first()->surname);
    }

    public function testTransactionRollback(): void
    {
        $initialCount = Person::count();

        try {
            DB::transaction(function () {
                Person::create([
                    'name' => 'Test',
                    'surname' => 'Person',
                    'pin' => 'TRANS123',
                    'is_sync' => true
                ]);

                // Force an exception
                throw new \Exception('Test rollback');
            });
        } catch (\Exception $e) {
            // Expected exception
        }

        // Verify rollback worked
        $this->assertEquals($initialCount, Person::count());
        $this->assertDatabaseMissing('people', ['pin' => 'TRANS123']);
    }

    public function testTransactionCommit(): void
    {
        $initialCount = Person::count();

        DB::transaction(function () {
            Person::create([
                'name' => 'Test',
                'surname' => 'Person',
                'pin' => 'COMMIT123',
                'is_sync' => true
            ]);

            Blacklist::create([
                'name' => 'Test',
                'surname' => 'Criminal',
                'pin' => 'COMMIT456',
                'status' => 1
            ]);
        });

        // Verify both records were created
        $this->assertEquals($initialCount + 1, Person::count());
        $this->assertDatabaseHas('people', ['pin' => 'COMMIT123']);
        $this->assertDatabaseHas('blacklists', ['pin' => 'COMMIT456']);
    }

    public function testBulkOperations(): void
    {
        $persons = [];
        for ($i = 1; $i <= 100; $i++) {
            $persons[] = [
                'name' => "Person{$i}",
                'surname' => 'Bulk',
                'pin' => "BULK{$i}",
                'is_sync' => true,
                'created_at' => now(),
                'updated_at' => now()
            ];
        }

        // Test bulk insert
        Person::insert($persons);

        $this->assertEquals(100, Person::where('surname', 'Bulk')->count());

        // Test bulk update
        Person::where('surname', 'Bulk')->update(['surname' => 'Updated']);

        $this->assertEquals(100, Person::where('surname', 'Updated')->count());
        $this->assertEquals(0, Person::where('surname', 'Bulk')->count());

        // Test bulk delete
        Person::where('surname', 'Updated')->delete();

        $this->assertEquals(0, Person::where('surname', 'Updated')->count());
    }

    public function testIndexesAndPerformance(): void
    {
        // Create test data
        Person::factory()->count(1000)->create();

        // Test query performance with index on pin
        $startTime = microtime(true);
        $person = Person::where('pin', Person::first()->pin)->first();
        $endTime = microtime(true);

        $queryTime = $endTime - $startTime;
        
        // Query should be fast (less than 100ms)
        $this->assertLessThan(0.1, $queryTime);
        $this->assertNotNull($person);
    }

    public function testDatabaseConstraints(): void
    {
        // Test unique constraint on person pin
        Person::create([
            'name' => 'First',
            'surname' => 'Person',
            'pin' => 'UNIQUE123',
            'is_sync' => true
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);
        
        Person::create([
            'name' => 'Second',
            'surname' => 'Person',
            'pin' => 'UNIQUE123', // Duplicate pin
            'is_sync' => true
        ]);
    }

    public function testForeignKeyConstraints(): void
    {
        $objectType = ObjectType::create([
            'name' => 'Test Type',
            'description' => 'Test'
        ]);

        $object = Object_::create([
            'object_type_id' => $objectType->id,
            'name' => 'Test Object',
            'address' => 'Test Address',
            'active' => true
        ]);

        // Test that deleting object type with related objects fails
        $this->expectException(\Illuminate\Database\QueryException::class);
        $objectType->delete();
    }

    public function testSoftDeletes(): void
    {
        // Test if Object model uses soft deletes
        $objectType = ObjectType::create(['name' => 'Test', 'description' => 'Test']);
        $object = Object_::create([
            'object_type_id' => $objectType->id,
            'name' => 'Test Object',
            'address' => 'Test Address',
            'active' => true
        ]);

        $objectId = $object->id;
        $object->delete();

        // Should be soft deleted
        $this->assertDatabaseHas('objects', ['id' => $objectId]);
        $this->assertNotNull(Object_::withTrashed()->find($objectId)->deleted_at);
        $this->assertNull(Object_::find($objectId));
    }

    public function testDatabaseSeeding(): void
    {
        // Test that we can run seeders
        $this->artisan('db:seed', ['--class' => 'DatabaseSeeder']);
        
        // Verify some basic data exists
        $this->assertGreaterThan(0, User::count());
    }
}
