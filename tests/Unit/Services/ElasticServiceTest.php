<?php

namespace Tests\Unit\Services;

use App\Services\ElasticService;
use Elastic\Elasticsearch\Client;
use Elastic\Elasticsearch\Exception\ClientResponseException;
use Tests\TestCase;
use Mockery;

class ElasticServiceTest extends TestCase
{
    protected ElasticService $elasticService;
    protected $mockClient;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false;
        
        $this->mockClient = Mockery::mock(Client::class);
        $this->elasticService = new ElasticService();
        
        // Use reflection to inject the mock client
        $reflection = new \ReflectionClass($this->elasticService);
        $clientProperty = $reflection->getProperty('client');
        $clientProperty->setAccessible(true);
        $clientProperty->setValue($this->elasticService, $this->mockClient);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testIndexDocument(): void
    {
        $index = 'test_index';
        $body = ['name' => '<PERSON> Do<PERSON>', 'age' => 30];
        $expectedResponse = ['_id' => '123', 'result' => 'created'];

        $this->mockClient
            ->shouldReceive('index')
            ->with([
                'index' => strtolower($index),
                'body' => $body
            ])
            ->once()
            ->andReturn($expectedResponse);

        $result = $this->elasticService->index($index, $body);

        $this->assertEquals($expectedResponse, $result);
    }

    public function testSearchDocuments(): void
    {
        $index = 'test_index';
        $body = [
            'query' => [
                'match' => ['name' => 'John']
            ]
        ];
        $expectedResponse = [
            'hits' => [
                'total' => ['value' => 1],
                'hits' => [
                    ['_source' => ['name' => 'John Doe', 'age' => 30]]
                ]
            ]
        ];

        $this->mockClient
            ->shouldReceive('search')
            ->with([
                'index' => $index,
                'body' => $body
            ])
            ->once()
            ->andReturn($expectedResponse);

        $result = $this->elasticService->search($index, $body);

        $this->assertEquals($expectedResponse, $result);
    }

    public function testSearchReturnsEmptyArrayOnException(): void
    {
        $index = 'test_index';
        $body = ['query' => ['match_all' => []]];

        $this->mockClient
            ->shouldReceive('search')
            ->with([
                'index' => $index,
                'body' => $body
            ])
            ->once()
            ->andThrow(new ClientResponseException('Search failed'));

        $result = $this->elasticService->search($index, $body);

        $this->assertEquals([], $result);
    }

    public function testCreateDocument(): void
    {
        $index = 'test_index';
        $id = '123';
        $body = ['name' => 'John Doe', 'age' => 30];

        $this->mockClient
            ->shouldReceive('index')
            ->with([
                'body' => $body,
                'id' => $id,
                'index' => $index
            ])
            ->once()
            ->andReturn(['result' => 'created']);

        $result = $this->elasticService->create($index, $id, $body);

        $this->assertTrue($result);
    }

    public function testCreateDocumentReturnsFalseOnException(): void
    {
        $index = 'test_index';
        $id = '123';
        $body = ['name' => 'John Doe'];

        $this->mockClient
            ->shouldReceive('index')
            ->with([
                'body' => $body,
                'id' => $id,
                'index' => $index
            ])
            ->once()
            ->andThrow(new ClientResponseException('Create failed'));

        $result = $this->elasticService->create($index, $id, $body);

        $this->assertFalse($result);
    }

    public function testDeleteDocument(): void
    {
        $index = 'test_index';
        $id = '123';

        $this->mockClient
            ->shouldReceive('delete')
            ->with([
                'index' => $index,
                'id' => $id
            ])
            ->once()
            ->andReturn(['acknowledge' => 1]);

        $result = $this->elasticService->delete($index, $id);

        $this->assertTrue($result);
    }

    public function testDeleteDocumentReturnsFalseOnException(): void
    {
        $index = 'test_index';
        $id = '123';

        $this->mockClient
            ->shouldReceive('delete')
            ->with([
                'index' => $index,
                'id' => $id
            ])
            ->once()
            ->andThrow(new ClientResponseException('Delete failed'));

        $result = $this->elasticService->delete($index, $id);

        $this->assertFalse($result);
    }

    public function testSearchScrollId(): void
    {
        $index = 'test_index';
        $body = ['query' => ['match_all' => []]];
        $expectedResponse = [
            'aggregations' => ['total' => ['value' => 100]],
            'hits' => ['hits' => [['_source' => ['name' => 'John']]]],
            '_scroll_id' => 'scroll123'
        ];

        $this->mockClient
            ->shouldReceive('search')
            ->with([
                'scroll' => '10s',
                'index' => $index,
                'body' => $body
            ])
            ->once()
            ->andReturn($expectedResponse);

        $result = $this->elasticService->searchScrollId($index, $body);

        $this->assertEquals(100, $result['total']);
        $this->assertCount(1, $result['result']);
        $this->assertEquals('scroll123', $result['scrollId']);
    }

    public function testSearchScrollIdReturnsEmptyArrayOnException(): void
    {
        $index = 'test_index';
        $body = ['query' => ['match_all' => []]];

        $this->mockClient
            ->shouldReceive('search')
            ->with([
                'scroll' => '10s',
                'index' => $index,
                'body' => $body
            ])
            ->once()
            ->andThrow(new ClientResponseException('Search failed'));

        $result = $this->elasticService->searchScrollId($index, $body);

        $this->assertEquals([], $result);
    }

    public function testPaginateViaScrollApi(): void
    {
        $scrollId = 'scroll123';
        $expectedResponse = [
            'hits' => [
                'hits' => [
                    ['_source' => ['name' => 'John']],
                    ['_source' => ['name' => 'Jane']]
                ]
            ]
        ];

        $this->mockClient
            ->shouldReceive('scroll')
            ->with([
                'scroll_id' => $scrollId,
                'scroll' => '10s'
            ])
            ->once()
            ->andReturn($expectedResponse);

        $result = $this->elasticService->paginateViaScrollApi($scrollId);

        $this->assertCount(2, $result);
        $this->assertEquals('John', $result[0]['_source']['name']);
        $this->assertEquals('Jane', $result[1]['_source']['name']);
    }

    public function testPaginateViaScrollApiReturnsFalseOnException(): void
    {
        $scrollId = 'scroll123';

        $this->mockClient
            ->shouldReceive('scroll')
            ->with([
                'scroll_id' => $scrollId,
                'scroll' => '10s'
            ])
            ->once()
            ->andThrow(new ClientResponseException('Scroll failed'));

        $result = $this->elasticService->paginateViaScrollApi($scrollId);

        $this->assertFalse($result);
    }

    public function testSearchWithPagination(): void
    {
        $index = 'test_index';
        $body = ['query' => ['match_all' => []]];
        $from = 0;
        $size = 10;

        $expectedParams = [
            'index' => $index,
            'body' => array_merge($body, ['from' => $from, 'size' => $size])
        ];

        $expectedResponse = [
            'hits' => [
                'total' => ['value' => 50],
                'hits' => []
            ]
        ];

        $this->mockClient
            ->shouldReceive('search')
            ->with($expectedParams)
            ->once()
            ->andReturn($expectedResponse);

        // Assuming there's a searchWithPagination method
        if (method_exists($this->elasticService, 'searchWithPagination')) {
            $result = $this->elasticService->searchWithPagination($index, $body, $from, $size);
            $this->assertEquals($expectedResponse, $result);
        } else {
            $this->markTestSkipped('searchWithPagination method not implemented');
        }
    }

    public function testBulkOperations(): void
    {
        $params = [
            'body' => [
                ['index' => ['_index' => 'test_index', '_id' => '1']],
                ['name' => 'John Doe'],
                ['index' => ['_index' => 'test_index', '_id' => '2']],
                ['name' => 'Jane Smith']
            ]
        ];

        $expectedResponse = [
            'items' => [
                ['index' => ['result' => 'created']],
                ['index' => ['result' => 'created']]
            ]
        ];

        $this->mockClient
            ->shouldReceive('bulk')
            ->with($params)
            ->once()
            ->andReturn($expectedResponse);

        // Assuming there's a bulk method
        if (method_exists($this->elasticService, 'bulk')) {
            $result = $this->elasticService->bulk($params);
            $this->assertEquals($expectedResponse, $result);
        } else {
            $this->markTestSkipped('bulk method not implemented');
        }
    }
}
