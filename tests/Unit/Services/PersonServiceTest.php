<?php

namespace Tests\Unit\Services;

use App\Models\Person;
use App\Repository\PersonRepository;
use App\Services\PersonService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Tests\TestCase;
use Mockery;

class PersonServiceTest extends TestCase
{
    use RefreshDatabase;

    protected PersonService $personService;
    protected $mockPersonRepository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false;
        
        $this->mockPersonRepository = Mockery::mock(PersonRepository::class);
        $this->personService = new PersonService($this->mockPersonRepository);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testFindAllReturnsCollection(): void
    {
        $expectedCollection = collect([
            ['id' => 1, 'name' => '<PERSON>', 'surname' => '<PERSON><PERSON>'],
            ['id' => 2, 'name' => '<PERSON>', 'surname' => '<PERSON>'],
        ]);

        $this->mockPersonRepository
            ->shouldReceive('findAll')
            ->with(15)
            ->once()
            ->andReturn($expectedCollection);

        $result = $this->personService->findAll();

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
        $this->assertEquals($expectedCollection, $result);
    }

    public function testFindAllWithCustomLimit(): void
    {
        $limit = 25;
        $expectedCollection = collect([]);

        $this->mockPersonRepository
            ->shouldReceive('findAll')
            ->with($limit)
            ->once()
            ->andReturn($expectedCollection);

        $result = $this->personService->findAll($limit);

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertEquals($expectedCollection, $result);
    }

    public function testFindReturnsPersonModel(): void
    {
        $personId = 1;
        $expectedPerson = new Person([
            'id' => $personId,
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123'
        ]);

        $this->mockPersonRepository
            ->shouldReceive('find')
            ->with($personId)
            ->once()
            ->andReturn($expectedPerson);

        $result = $this->personService->find($personId);

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals($personId, $result->id);
        $this->assertEquals('John', $result->name);
    }

    public function testFindReturnsNullWhenNotFound(): void
    {
        $personId = 999;

        $this->mockPersonRepository
            ->shouldReceive('find')
            ->with($personId)
            ->once()
            ->andReturn(null);

        $result = $this->personService->find($personId);

        $this->assertNull($result);
    }

    public function testSaveCreatesPerson(): void
    {
        $personData = [
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'is_sync' => true,
        ];

        $expectedPerson = new Person($personData);
        $expectedPerson->id = 1;

        $this->mockPersonRepository
            ->shouldReceive('save')
            ->with($personData)
            ->once()
            ->andReturn($expectedPerson);

        $result = $this->personService->save($personData);

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals('John', $result->name);
        $this->assertEquals('Doe', $result->surname);
        $this->assertEquals('PIN123', $result->pin);
    }

    public function testUpdateModifiesPerson(): void
    {
        $personId = 1;
        $updateData = [
            'name' => 'Jane',
            'surname' => 'Smith',
        ];

        $expectedPerson = new Person([
            'id' => $personId,
            'name' => 'Jane',
            'surname' => 'Smith',
            'pin' => 'PIN123'
        ]);

        $this->mockPersonRepository
            ->shouldReceive('update')
            ->with($updateData, $personId)
            ->once()
            ->andReturn($expectedPerson);

        $result = $this->personService->update($updateData, $personId);

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals('Jane', $result->name);
        $this->assertEquals('Smith', $result->surname);
    }

    public function testFindOneByConditionReturnsPersonWhenFound(): void
    {
        $conditions = ['pin' => 'PIN123', 'is_sync' => true];
        $expectedPerson = new Person([
            'id' => 1,
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'is_sync' => true
        ]);

        $this->mockPersonRepository
            ->shouldReceive('findOneByCondition')
            ->with($conditions)
            ->once()
            ->andReturn($expectedPerson);

        $result = $this->personService->findOneByCondition($conditions);

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals('PIN123', $result->pin);
        $this->assertTrue($result->is_sync);
    }

    public function testFindOneByConditionReturnsNullWhenNotFound(): void
    {
        $conditions = ['pin' => 'NONEXISTENT'];

        $this->mockPersonRepository
            ->shouldReceive('findOneByCondition')
            ->with($conditions)
            ->once()
            ->andReturn(null);

        $result = $this->personService->findOneByCondition($conditions);

        $this->assertNull($result);
    }

    public function testGetListsByPinsReturnsCollection(): void
    {
        $pins = ['PIN123', 'PIN456'];
        $newSearch = false;
        $expectedCollection = collect([
            new Person(['pin' => 'PIN123', 'name' => 'John']),
            new Person(['pin' => 'PIN456', 'name' => 'Jane']),
        ]);

        $this->mockPersonRepository
            ->shouldReceive('getListsByPins')
            ->with($pins, $newSearch)
            ->once()
            ->andReturn($expectedCollection);

        $result = $this->personService->getListsByPins($pins, $newSearch);

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    public function testGetListsByPinsWithNewSearch(): void
    {
        $pins = ['PIN123'];
        $newSearch = true;
        $expectedCollection = collect([]);

        $this->mockPersonRepository
            ->shouldReceive('getListsByPins')
            ->with($pins, $newSearch)
            ->once()
            ->andReturn($expectedCollection);

        $result = $this->personService->getListsByPins($pins, $newSearch);

        $this->assertInstanceOf(Collection::class, $result);
    }

    public function testDeleteRemovesPerson(): void
    {
        $personId = 1;

        $this->mockPersonRepository
            ->shouldReceive('delete')
            ->with($personId)
            ->once()
            ->andReturn(1); // Number of affected rows

        $result = $this->personService->delete($personId);

        $this->assertInstanceOf(\Illuminate\Http\JsonResponse::class, $result);
        $this->assertEquals(200, $result->getStatusCode());
    }

    public function testGetModelReturnsPersonModel(): void
    {
        $expectedModel = new Person();

        $this->mockPersonRepository
            ->shouldReceive('getModel')
            ->once()
            ->andReturn($expectedModel);

        $result = $this->personService->getModel();

        $this->assertInstanceOf(Person::class, $result);
    }

    public function testPaginationReturnsLengthAwarePaginator(): void
    {
        $limit = 10;
        $mockPaginator = Mockery::mock(\Illuminate\Contracts\Pagination\LengthAwarePaginator::class);

        $this->mockPersonRepository
            ->shouldReceive('paginate')
            ->with($limit)
            ->once()
            ->andReturn($mockPaginator);

        $result = $this->personService->pagination($limit);

        $this->assertInstanceOf(\Illuminate\Contracts\Pagination\LengthAwarePaginator::class, $result);
    }

    public function testGetOptionsReturnsCollection(): void
    {
        $query = null;
        $value = 'name';
        $id = 'id';
        $expectedOptions = collect([
            ['id' => 1, 'name' => 'John Doe'],
            ['id' => 2, 'name' => 'Jane Smith'],
        ]);

        $this->mockPersonRepository
            ->shouldReceive('getOptions')
            ->with($id, $value, $query)
            ->once()
            ->andReturn($expectedOptions);

        $result = $this->personService->getOptions($query, $value, $id);

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertEquals($expectedOptions, $result);
    }
}
