<?php

namespace Tests\Unit\Middleware;

use App\Http\Middleware\LogHistoryMiddleware;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Tests\TestCase;
use Mockery;

class LogHistoryMiddlewareTest extends TestCase
{
    use RefreshDatabase;

    protected LogHistoryMiddleware $middleware;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false;
        $this->middleware = new LogHistoryMiddleware();
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testMiddlewarePassesRequestThrough(): void
    {
        $request = Request::create('/test', 'GET');
        $response = new Response('Test response');

        $next = function ($req) use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $this->assertEquals($response, $result);
    }

    public function testMiddlewareLogsRequestWithAuthenticatedUser(): void
    {
        $user = User::factory()->create();
        $this->actingAs($user, 'api');

        $request = Request::create('/api/v1/test', 'POST', ['param' => 'value']);
        $request->headers->set('User-Agent', 'Test Browser');
        $request->headers->set('X-Forwarded-For', '192.168.1.1');

        $response = new Response('Test response', 200);

        $next = function ($req) use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $this->assertEquals($response, $result);

        // Verify log entry was created (assuming there's a logs table)
        if (\Schema::hasTable('logs')) {
            $this->assertDatabaseHas('logs', [
                'user_id' => $user->id,
                'method' => 'POST',
                'url' => '/api/v1/test',
                'status_code' => 200,
            ]);
        }
    }

    public function testMiddlewareLogsRequestWithoutAuthenticatedUser(): void
    {
        $request = Request::create('/api/v1/public', 'GET');
        $request->headers->set('User-Agent', 'Test Browser');

        $response = new Response('Public response', 200);

        $next = function ($req) use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $this->assertEquals($response, $result);

        // Verify log entry was created without user_id
        if (\Schema::hasTable('logs')) {
            $this->assertDatabaseHas('logs', [
                'user_id' => null,
                'method' => 'GET',
                'url' => '/api/v1/public',
                'status_code' => 200,
            ]);
        }
    }

    public function testMiddlewareHandlesDifferentHttpMethods(): void
    {
        $methods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];

        foreach ($methods as $method) {
            $request = Request::create('/api/v1/test', $method);
            $response = new Response('Response', 200);

            $next = function ($req) use ($response) {
                return $response;
            };

            $result = $this->middleware->handle($request, $next);

            $this->assertEquals($response, $result);

            if (\Schema::hasTable('logs')) {
                $this->assertDatabaseHas('logs', [
                    'method' => $method,
                    'url' => '/api/v1/test',
                ]);
            }
        }
    }

    public function testMiddlewareLogsErrorResponses(): void
    {
        $request = Request::create('/api/v1/error', 'GET');
        $response = new Response('Error response', 500);

        $next = function ($req) use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $this->assertEquals($response, $result);

        if (\Schema::hasTable('logs')) {
            $this->assertDatabaseHas('logs', [
                'method' => 'GET',
                'url' => '/api/v1/error',
                'status_code' => 500,
            ]);
        }
    }

    public function testMiddlewareLogsClientInformation(): void
    {
        $request = Request::create('/api/v1/test', 'GET');
        $request->headers->set('User-Agent', 'Mozilla/5.0 Test Browser');
        $request->headers->set('X-Forwarded-For', '***********');
        $request->server->set('REMOTE_ADDR', '*************');

        $response = new Response('Test response', 200);

        $next = function ($req) use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $this->assertEquals($response, $result);

        if (\Schema::hasTable('logs')) {
            $this->assertDatabaseHas('logs', [
                'user_agent' => 'Mozilla/5.0 Test Browser',
                'ip_address' => '***********', // X-Forwarded-For takes precedence
            ]);
        }
    }

    public function testMiddlewareLogsRequestParameters(): void
    {
        $requestData = [
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'age' => 30
        ];

        $request = Request::create('/api/v1/users', 'POST', $requestData);
        $response = new Response('Created', 201);

        $next = function ($req) use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $this->assertEquals($response, $result);

        if (\Schema::hasTable('logs')) {
            $logEntry = \DB::table('logs')
                ->where('method', 'POST')
                ->where('url', '/api/v1/users')
                ->first();

            if ($logEntry && isset($logEntry->request_data)) {
                $loggedData = json_decode($logEntry->request_data, true);
                $this->assertEquals('John Doe', $loggedData['name']);
                $this->assertEquals('<EMAIL>', $loggedData['email']);
            }
        }
    }

    public function testMiddlewareFilterssensitiveData(): void
    {
        $requestData = [
            'username' => 'testuser',
            'password' => 'secret123',
            'password_confirmation' => 'secret123',
            'token' => 'sensitive_token',
            'api_key' => 'secret_key'
        ];

        $request = Request::create('/api/v1/auth/login', 'POST', $requestData);
        $response = new Response('Login successful', 200);

        $next = function ($req) use ($response) {
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $this->assertEquals($response, $result);

        if (\Schema::hasTable('logs')) {
            $logEntry = \DB::table('logs')
                ->where('method', 'POST')
                ->where('url', '/api/v1/auth/login')
                ->first();

            if ($logEntry && isset($logEntry->request_data)) {
                $loggedData = json_decode($logEntry->request_data, true);
                
                // Sensitive fields should be filtered out or masked
                $this->assertEquals('testuser', $loggedData['username']);
                $this->assertArrayNotHasKey('password', $loggedData);
                $this->assertArrayNotHasKey('password_confirmation', $loggedData);
                $this->assertArrayNotHasKey('token', $loggedData);
                $this->assertArrayNotHasKey('api_key', $loggedData);
            }
        }
    }

    public function testMiddlewareHandlesExceptionInNext(): void
    {
        $request = Request::create('/api/v1/test', 'GET');

        $next = function ($req) {
            throw new \Exception('Test exception');
        };

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Test exception');

        $this->middleware->handle($request, $next);

        // Even if an exception occurs, the request should still be logged
        if (\Schema::hasTable('logs')) {
            $this->assertDatabaseHas('logs', [
                'method' => 'GET',
                'url' => '/api/v1/test',
            ]);
        }
    }

    public function testMiddlewareLogsExecutionTime(): void
    {
        $request = Request::create('/api/v1/slow', 'GET');
        $response = new Response('Slow response', 200);

        $next = function ($req) use ($response) {
            // Simulate slow operation
            usleep(100000); // 100ms
            return $response;
        };

        $result = $this->middleware->handle($request, $next);

        $this->assertEquals($response, $result);

        if (\Schema::hasTable('logs')) {
            $logEntry = \DB::table('logs')
                ->where('method', 'GET')
                ->where('url', '/api/v1/slow')
                ->first();

            if ($logEntry && isset($logEntry->execution_time)) {
                $this->assertGreaterThan(0.1, $logEntry->execution_time); // At least 100ms
            }
        }
    }

    public function testMiddlewareSkipsLoggingForSpecificRoutes(): void
    {
        // Test routes that should be excluded from logging (like health checks)
        $excludedRoutes = [
            '/health',
            '/api/health',
            '/status'
        ];

        foreach ($excludedRoutes as $route) {
            $request = Request::create($route, 'GET');
            $response = new Response('OK', 200);

            $next = function ($req) use ($response) {
                return $response;
            };

            $result = $this->middleware->handle($request, $next);

            $this->assertEquals($response, $result);

            // These routes should not be logged (implementation dependent)
            if (\Schema::hasTable('logs')) {
                $logCount = \DB::table('logs')
                    ->where('url', $route)
                    ->count();

                // This assertion depends on the actual implementation
                // If the middleware is configured to skip these routes
                // $this->assertEquals(0, $logCount);
            }
        }
    }
}
