<?php

namespace Tests\Unit\Models;

use App\Models\User;
use App\Models\Role;
use App\Models\Permission;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Illuminate\Support\Facades\Hash;

class UserTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false; // Don't auto-authenticate for unit tests
    }

    public function testUserCanBeCreated(): void
    {
        $userData = [
            'name' => 'Test',
            'surname' => 'User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'entity_name' => 'Test Entity',
            'phone_number' => '994501234567',
            'status' => 1,
            'is_system' => false,
        ];

        $user = User::create($userData);

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('Test', $user->name);
        $this->assertEquals('User', $user->surname);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('Test Entity', $user->entity_name);
        $this->assertEquals('994501234567', $user->phone_number);
        $this->assertEquals(1, $user->status);
        $this->assertFalse($user->is_system);
    }

    public function testUserPasswordIsHashed(): void
    {
        $user = User::factory()->create([
            'password' => Hash::make('password123')
        ]);

        $this->assertTrue(Hash::check('password123', $user->password));
        $this->assertNotEquals('password123', $user->password);
    }

    public function testUserHasRoles(): void
    {
        $user = User::factory()->create();
        $role = Role::create(['name' => 'admin', 'guard_name' => 'api']);

        $user->assignRole($role);

        $this->assertTrue($user->hasRole('admin'));
        $this->assertCount(1, $user->roles);
    }

    public function testUserHasPermissions(): void
    {
        $user = User::factory()->create();
        $permission = Permission::create(['name' => 'view-users', 'guard_name' => 'api']);

        $user->givePermissionTo($permission);

        $this->assertTrue($user->hasPermissionTo('view-users'));
        $this->assertCount(1, $user->permissions);
    }

    public function testUserCanHaveMultipleRoles(): void
    {
        $user = User::factory()->create();
        $adminRole = Role::create(['name' => 'admin', 'guard_name' => 'api']);
        $userRole = Role::create(['name' => 'user', 'guard_name' => 'api']);

        $user->assignRole([$adminRole, $userRole]);

        $this->assertTrue($user->hasRole('admin'));
        $this->assertTrue($user->hasRole('user'));
        $this->assertCount(2, $user->roles);
    }

    public function testUserFillableAttributes(): void
    {
        $fillableAttributes = [
            'name',
            'surname',
            'email',
            'password',
            'entity_name',
            'phone_number',
            'status',
            'is_system',
        ];

        $user = new User();
        $this->assertEquals($fillableAttributes, $user->getFillable());
    }

    public function testUserHiddenAttributes(): void
    {
        $user = User::factory()->create();
        $hiddenAttributes = ['password', 'remember_token'];

        foreach ($hiddenAttributes as $attribute) {
            $this->assertContains($attribute, $user->getHidden());
        }
    }

    public function testUserCastsAttributes(): void
    {
        $user = new User();
        $casts = $user->getCasts();

        $this->assertArrayHasKey('email_verified_at', $casts);
        $this->assertEquals('datetime', $casts['email_verified_at']);
    }

    public function testUserGuardName(): void
    {
        $user = new User();
        $this->assertEquals('api', $user->guard_name);
    }

    public function testUserConnection(): void
    {
        $user = new User();
        $this->assertEquals('pgsql', $user->getConnectionName());
    }

    public function testUserJWTIdentifier(): void
    {
        $user = User::factory()->create();
        $this->assertEquals($user->id, $user->getJWTIdentifier());
    }

    public function testUserJWTCustomClaims(): void
    {
        $user = User::factory()->create();
        $this->assertIsArray($user->getJWTCustomClaims());
    }

    public function testUserStatusScope(): void
    {
        User::factory()->create(['status' => 1]);
        User::factory()->create(['status' => 0]);

        $activeUsers = User::where('status', 1)->get();
        $inactiveUsers = User::where('status', 0)->get();

        $this->assertCount(1, $activeUsers);
        $this->assertCount(1, $inactiveUsers);
    }

    public function testUserSystemScope(): void
    {
        User::factory()->create(['is_system' => true]);
        User::factory()->create(['is_system' => false]);

        $systemUsers = User::where('is_system', true)->get();
        $regularUsers = User::where('is_system', false)->get();

        $this->assertCount(1, $systemUsers);
        $this->assertCount(1, $regularUsers);
    }

    public function testUserEmailUniqueness(): void
    {
        User::factory()->create(['email' => '<EMAIL>']);

        $this->expectException(\Illuminate\Database\QueryException::class);
        User::factory()->create(['email' => '<EMAIL>']);
    }

    public function testUserFullNameAccessor(): void
    {
        $user = User::factory()->create([
            'name' => 'John',
            'surname' => 'Doe'
        ]);

        // Assuming there's a full_name accessor
        if (method_exists($user, 'getFullNameAttribute')) {
            $this->assertEquals('John Doe', $user->full_name);
        }
    }
}
