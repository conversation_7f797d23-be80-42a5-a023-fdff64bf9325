<?php

namespace Tests\Unit\Models;

use App\Models\Person;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PersonTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false;
    }

    public function testPersonCanBeCreated(): void
    {
        $personData = [
            'name' => '<PERSON>',
            'surname' => '<PERSON><PERSON>',
            'father_name' => '<PERSON>',
            'pin' => '1234567',
            'doc_type' => 'passport',
            'doc_serial_number' => 'AA123456',
            'doc_number' => '*********',
            'is_sync' => true,
        ];

        $person = Person::create($personData);

        $this->assertInstanceOf(Person::class, $person);
        $this->assertEquals('John', $person->name);
        $this->assertEquals('Doe', $person->surname);
        $this->assertEquals('<PERSON>', $person->father_name);
        $this->assertEquals('1234567', $person->pin);
        $this->assertEquals('passport', $person->doc_type);
        $this->assertEquals('AA123456', $person->doc_serial_number);
        $this->assertEquals('*********', $person->doc_number);
        $this->assertTrue($person->is_sync);
    }

    public function testPersonConnection(): void
    {
        $person = new Person();
        $this->assertEquals('pgsql', $person->getConnectionName());
    }

    public function testPersonTableName(): void
    {
        $person = new Person();
        $this->assertEquals('people', $person->getTable());
    }

    public function testPersonGuardedAttributes(): void
    {
        $person = new Person();
        $this->assertContains('id', $person->getGuarded());
    }

    public function testPersonPinUniqueness(): void
    {
        Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'UNIQUE123',
            'is_sync' => true,
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);
        Person::create([
            'name' => 'Jane',
            'surname' => 'Smith',
            'pin' => 'UNIQUE123',
            'is_sync' => true,
        ]);
    }

    public function testPersonSyncScope(): void
    {
        Person::create(['name' => 'John', 'surname' => 'Doe', 'pin' => 'PIN1', 'is_sync' => true]);
        Person::create(['name' => 'Jane', 'surname' => 'Smith', 'pin' => 'PIN2', 'is_sync' => false]);

        $syncedPersons = Person::where('is_sync', true)->get();
        $unsyncedPersons = Person::where('is_sync', false)->get();

        $this->assertCount(1, $syncedPersons);
        $this->assertCount(1, $unsyncedPersons);
    }

    public function testPersonByPin(): void
    {
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'TESTPIN123',
            'is_sync' => true,
        ]);

        $foundPerson = Person::where('pin', 'TESTPIN123')->first();

        $this->assertNotNull($foundPerson);
        $this->assertEquals($person->id, $foundPerson->id);
        $this->assertEquals('TESTPIN123', $foundPerson->pin);
    }

    public function testPersonDataAttribute(): void
    {
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'data' => ['additional' => 'info'],
            'is_sync' => true,
        ]);

        $this->assertIsArray($person->data);
        $this->assertEquals(['additional' => 'info'], $person->data);
    }

    public function testPersonTimestamps(): void
    {
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'is_sync' => true,
        ]);

        $this->assertNotNull($person->created_at);
        $this->assertNotNull($person->updated_at);
    }

    public function testPersonFullNameAccessor(): void
    {
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'father_name' => 'Michael',
            'pin' => 'PIN123',
            'is_sync' => true,
        ]);

        // Test if there's a full name accessor
        if (method_exists($person, 'getFullNameAttribute')) {
            $this->assertEquals('John Doe', $person->full_name);
        }
    }

    public function testPersonDocumentInfo(): void
    {
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'doc_type' => 'passport',
            'doc_serial_number' => 'AA123456',
            'doc_number' => '*********',
            'is_sync' => true,
        ]);

        $this->assertEquals('passport', $person->doc_type);
        $this->assertEquals('AA123456', $person->doc_serial_number);
        $this->assertEquals('*********', $person->doc_number);
    }

    public function testPersonCanBeUpdated(): void
    {
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'is_sync' => false,
        ]);

        $person->update([
            'name' => 'Jane',
            'is_sync' => true,
        ]);

        $this->assertEquals('Jane', $person->name);
        $this->assertEquals('Doe', $person->surname); // Should remain unchanged
        $this->assertTrue($person->is_sync);
    }

    public function testPersonCanBeDeleted(): void
    {
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'is_sync' => true,
        ]);

        $personId = $person->id;
        $person->delete();

        $this->assertNull(Person::find($personId));
    }

    public function testPersonPinCaseInsensitive(): void
    {
        $person = Person::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'testpin123',
            'is_sync' => true,
        ]);

        // Test finding with different case
        $foundPerson = Person::where('pin', 'TESTPIN123')->first();
        
        // This test depends on database collation settings
        // If case-insensitive, should find the person
        if ($foundPerson) {
            $this->assertEquals($person->id, $foundPerson->id);
        }
    }
}
