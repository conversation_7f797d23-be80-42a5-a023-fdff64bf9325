<?php

namespace Tests\Unit\Models;

use App\Models\Blacklist;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class BlacklistTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false;
    }

    public function testBlacklistCanBeCreated(): void
    {
        $blacklistData = [
            'name' => '<PERSON>',
            'surname' => '<PERSON><PERSON>',
            'father_name' => '<PERSON>',
            'birthdate' => '1990-01-01',
            'document_number' => 'AA123456',
            'pin' => '1234567',
            'photo' => 'path/to/photo.jpg',
            'note' => 'Test note',
            'gender' => 'male',
            'status' => 1,
        ];

        $blacklist = Blacklist::create($blacklistData);

        $this->assertInstanceOf(Blacklist::class, $blacklist);
        $this->assertEquals('John', $blacklist->name);
        $this->assertEquals('<PERSON><PERSON>', $blacklist->surname);
        $this->assertEquals('<PERSON>', $blacklist->father_name);
        $this->assertEquals('1990-01-01', $blacklist->birthdate->format('Y-m-d'));
        $this->assertEquals('AA123456', $blacklist->document_number);
        $this->assertEquals('1234567', $blacklist->pin);
        $this->assertEquals('path/to/photo.jpg', $blacklist->photo);
        $this->assertEquals('Test note', $blacklist->note);
        $this->assertEquals('male', $blacklist->gender);
        $this->assertEquals(1, $blacklist->status);
    }

    public function testBlacklistConnection(): void
    {
        $blacklist = new Blacklist();
        $this->assertEquals('pgsql', $blacklist->getConnectionName());
    }

    public function testBlacklistTableName(): void
    {
        $blacklist = new Blacklist();
        $this->assertEquals('blacklists', $blacklist->getTable());
    }

    public function testBlacklistFillableAttributes(): void
    {
        $fillableAttributes = [
            'name',
            'surname',
            'father_name',
            'birthdate',
            'document_number',
            'pin',
            'photo',
            'note',
            'gender',
            'status',
        ];

        $blacklist = new Blacklist();
        $this->assertEquals($fillableAttributes, $blacklist->getFillable());
    }

    public function testBlacklistGuardedAttributes(): void
    {
        $blacklist = new Blacklist();
        $this->assertContains('id', $blacklist->getGuarded());
    }

    public function testBlacklistDateCasting(): void
    {
        $blacklist = Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'birthdate' => '1990-01-01',
            'pin' => '1234567',
            'status' => 1,
        ]);

        $this->assertInstanceOf(Carbon::class, $blacklist->birthdate);
        $this->assertEquals('1990-01-01', $blacklist->birthdate->format('Y-m-d'));
    }

    public function testBlacklistByStatus(): void
    {
        Blacklist::create(['name' => 'John', 'surname' => 'Doe', 'pin' => 'PIN1', 'status' => 1]);
        Blacklist::create(['name' => 'Jane', 'surname' => 'Smith', 'pin' => 'PIN2', 'status' => 0]);

        $activeBlacklists = Blacklist::where('status', 1)->get();
        $inactiveBlacklists = Blacklist::where('status', 0)->get();

        $this->assertCount(1, $activeBlacklists);
        $this->assertCount(1, $inactiveBlacklists);
    }

    public function testBlacklistByGender(): void
    {
        Blacklist::create(['name' => 'John', 'surname' => 'Doe', 'pin' => 'PIN1', 'gender' => 'male', 'status' => 1]);
        Blacklist::create(['name' => 'Jane', 'surname' => 'Smith', 'pin' => 'PIN2', 'gender' => 'female', 'status' => 1]);

        $maleBlacklists = Blacklist::where('gender', 'male')->get();
        $femaleBlacklists = Blacklist::where('gender', 'female')->get();

        $this->assertCount(1, $maleBlacklists);
        $this->assertCount(1, $femaleBlacklists);
    }

    public function testBlacklistPinUniqueness(): void
    {
        Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'UNIQUE123',
            'status' => 1,
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);
        Blacklist::create([
            'name' => 'Jane',
            'surname' => 'Smith',
            'pin' => 'UNIQUE123',
            'status' => 1,
        ]);
    }

    public function testBlacklistDocumentNumberUniqueness(): void
    {
        Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN1',
            'document_number' => 'UNIQUE123',
            'status' => 1,
        ]);

        $this->expectException(\Illuminate\Database\QueryException::class);
        Blacklist::create([
            'name' => 'Jane',
            'surname' => 'Smith',
            'pin' => 'PIN2',
            'document_number' => 'UNIQUE123',
            'status' => 1,
        ]);
    }

    public function testBlacklistTimestamps(): void
    {
        $blacklist = Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'status' => 1,
        ]);

        $this->assertNotNull($blacklist->created_at);
        $this->assertNotNull($blacklist->updated_at);
    }

    public function testBlacklistCanBeUpdated(): void
    {
        $blacklist = Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'status' => 0,
        ]);

        $blacklist->update([
            'name' => 'Jane',
            'status' => 1,
        ]);

        $this->assertEquals('Jane', $blacklist->name);
        $this->assertEquals('Doe', $blacklist->surname); // Should remain unchanged
        $this->assertEquals(1, $blacklist->status);
    }

    public function testBlacklistCanBeDeleted(): void
    {
        $blacklist = Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'status' => 1,
        ]);

        $blacklistId = $blacklist->id;
        $blacklist->delete();

        $this->assertNull(Blacklist::find($blacklistId));
    }

    public function testBlacklistPhotoPath(): void
    {
        $blacklist = Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'photo' => 'photos/john_doe.jpg',
            'status' => 1,
        ]);

        $this->assertEquals('photos/john_doe.jpg', $blacklist->photo);
    }

    public function testBlacklistNoteField(): void
    {
        $note = 'This person is on the blacklist for security reasons.';
        
        $blacklist = Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'note' => $note,
            'status' => 1,
        ]);

        $this->assertEquals($note, $blacklist->note);
    }

    public function testBlacklistBirthdateValidation(): void
    {
        $blacklist = Blacklist::create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN123',
            'birthdate' => '1990-12-25',
            'status' => 1,
        ]);

        $this->assertEquals('1990-12-25', $blacklist->birthdate->format('Y-m-d'));
        $this->assertEquals(25, $blacklist->birthdate->day);
        $this->assertEquals(12, $blacklist->birthdate->month);
        $this->assertEquals(1990, $blacklist->birthdate->year);
    }
}
