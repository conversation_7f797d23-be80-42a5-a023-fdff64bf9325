<?php

namespace Tests\Unit\Repository;

use App\Models\Person;
use App\Repository\AbstractRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AbstractRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected AbstractRepository $repository;
    protected Model $model;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false;
        
        $this->model = new Person();
        $this->repository = new class($this->model) extends AbstractRepository {};
    }

    public function testGetModel(): void
    {
        $model = $this->repository->getModel();
        
        $this->assertInstanceOf(Person::class, $model);
        $this->assertEquals($this->model, $model);
    }

    public function testSetModel(): void
    {
        $newModel = new Person(['name' => 'Test']);
        
        $this->repository->setModel($newModel);
        
        $this->assertEquals($newModel, $this->repository->getModel());
    }

    public function testSetParams(): void
    {
        $params = ['key' => 'value', 'another' => 'param'];
        
        $result = $this->repository->setParams($params);
        
        $this->assertInstanceOf(AbstractRepository::class, $result);
        $this->assertEquals($this->repository, $result);
    }

    public function testFindFirst(): void
    {
        // Create test data
        Person::factory()->create(['name' => 'First']);
        Person::factory()->create(['name' => 'Second']);

        $result = $this->repository->findFirst();

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals('First', $result->name);
    }

    public function testFindFirstReturnsNullWhenEmpty(): void
    {
        $result = $this->repository->findFirst();

        $this->assertNull($result);
    }

    public function testFindAll(): void
    {
        // Create test data
        Person::factory()->count(5)->create();

        $result = $this->repository->findAll(3);

        $this->assertCount(3, $result);
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
    }

    public function testFindAllWithDefaultLimit(): void
    {
        Person::factory()->count(20)->create();

        $result = $this->repository->findAll();

        $this->assertCount(15, $result); // Default limit is 15
    }

    public function testPaginate(): void
    {
        Person::factory()->count(25)->create();

        $result = $this->repository->paginate(10);

        $this->assertInstanceOf(\Illuminate\Contracts\Pagination\LengthAwarePaginator::class, $result);
        $this->assertEquals(10, $result->perPage());
        $this->assertEquals(25, $result->total());
        $this->assertCount(10, $result->items());
    }

    public function testFind(): void
    {
        $person = Person::factory()->create(['name' => 'Test Person']);

        $result = $this->repository->find($person->id);

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals($person->id, $result->id);
        $this->assertEquals('Test Person', $result->name);
    }

    public function testFindThrowsExceptionWhenNotFound(): void
    {
        $this->expectException(\Illuminate\Database\Eloquent\ModelNotFoundException::class);

        $this->repository->find(999999);
    }

    public function testFindOneByCondition(): void
    {
        Person::factory()->create(['name' => 'John', 'surname' => 'Doe', 'pin' => 'PIN123']);
        Person::factory()->create(['name' => 'Jane', 'surname' => 'Smith', 'pin' => 'PIN456']);

        $result = $this->repository->findOneByCondition(['pin' => 'PIN123']);

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals('John', $result->name);
        $this->assertEquals('PIN123', $result->pin);
    }

    public function testFindOneByConditionReturnsNullWhenNotFound(): void
    {
        $result = $this->repository->findOneByCondition(['pin' => 'NONEXISTENT']);

        $this->assertNull($result);
    }

    public function testSave(): void
    {
        $params = [
            'name' => 'New Person',
            'surname' => 'Test',
            'pin' => 'SAVE123',
            'is_sync' => true,
        ];

        $result = $this->repository->save($params);

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals('New Person', $result->name);
        $this->assertEquals('Test', $result->surname);
        $this->assertEquals('SAVE123', $result->pin);
        $this->assertTrue($result->is_sync);

        // Verify it was saved to database
        $this->assertDatabaseHas('people', [
            'name' => 'New Person',
            'surname' => 'Test',
            'pin' => 'SAVE123'
        ]);
    }

    public function testUpdate(): void
    {
        $person = Person::factory()->create([
            'name' => 'Original',
            'surname' => 'Name',
            'pin' => 'UPDATE123'
        ]);

        $updateParams = [
            'name' => 'Updated',
            'surname' => 'Person',
        ];

        $result = $this->repository->update($updateParams, $person->id);

        $this->assertInstanceOf(Person::class, $result);
        $this->assertEquals('Updated', $result->name);
        $this->assertEquals('Person', $result->surname);
        $this->assertEquals('UPDATE123', $result->pin); // Should remain unchanged

        // Verify it was updated in database
        $this->assertDatabaseHas('people', [
            'id' => $person->id,
            'name' => 'Updated',
            'surname' => 'Person',
            'pin' => 'UPDATE123'
        ]);
    }

    public function testDelete(): void
    {
        $person = Person::factory()->create();
        $personId = $person->id;

        $result = $this->repository->delete($personId);

        $this->assertEquals(1, $result); // Number of affected rows

        // Verify it was deleted from database
        $this->assertDatabaseMissing('people', [
            'id' => $personId
        ]);
    }

    public function testDeleteNonExistentRecord(): void
    {
        $result = $this->repository->delete(999999);

        $this->assertEquals(0, $result); // No rows affected
    }

    public function testGetOptions(): void
    {
        Person::factory()->create(['name' => 'Alice', 'id' => 1]);
        Person::factory()->create(['name' => 'Bob', 'id' => 2]);
        Person::factory()->create(['name' => 'Charlie', 'id' => 3]);

        $result = $this->repository->getOptions('id', 'name', null);

        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
        $this->assertCount(3, $result);

        $firstItem = $result->first();
        $this->assertArrayHasKey('id', $firstItem);
        $this->assertArrayHasKey('name', $firstItem);
        $this->assertEquals('Alice', $firstItem['name']);
    }

    public function testGetOptionsWithCustomQuery(): void
    {
        Person::factory()->create(['name' => 'Alice']);
        Person::factory()->create(['name' => 'Bob']);
        Person::factory()->create(['name' => 'Charlie']);

        $customQuery = Person::where('name', 'like', 'A%');
        $result = $this->repository->getOptions('id', 'name', $customQuery);

        $this->assertCount(1, $result);
        $this->assertEquals('Alice', $result->first()['name']);
    }

    public function testExistsById(): void
    {
        $person = Person::factory()->create();

        $exists = $this->repository->existsById($person->id);
        $notExists = $this->repository->existsById(999999);

        $this->assertTrue($exists);
        $this->assertFalse($notExists);
    }

    public function testBuildQuery(): void
    {
        $query = $this->repository->buildQuery();

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Builder::class, $query);
        $this->assertEquals('people', $query->getModel()->getTable());
    }

    public function testRepositoryChaining(): void
    {
        Person::factory()->count(5)->create();

        $result = $this->repository
            ->setParams(['test' => 'value'])
            ->findAll(3);

        $this->assertCount(3, $result);
    }

    public function testFindAllReturnsEmptyCollectionWhenNoData(): void
    {
        $result = $this->repository->findAll();

        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $result);
        $this->assertCount(0, $result);
        $this->assertTrue($result->isEmpty());
    }

    public function testPaginateReturnsEmptyPaginatorWhenNoData(): void
    {
        $result = $this->repository->paginate(10);

        $this->assertInstanceOf(\Illuminate\Contracts\Pagination\LengthAwarePaginator::class, $result);
        $this->assertEquals(0, $result->total());
        $this->assertCount(0, $result->items());
    }
}
