<?php

namespace Tests\Unit\Traits;

use App\Traits\ApiResponsible;
use Illuminate\Http\JsonResponse;
use Tests\TestCase;

class ApiResponsibleTest extends TestCase
{
    protected $traitObject;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false;
        
        // Create an anonymous class that uses the trait
        $this->traitObject = new class {
            use ApiResponsible;
        };
    }

    public function testSuccessResponse(): void
    {
        $response = $this->traitObject->successResponse(200);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testSuccessResponseWithData(): void
    {
        $data = ['name' => '<PERSON> Doe', 'email' => '<EMAIL>'];
        $response = $this->traitObject->successResponse(200, 'User created', $data);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
        $this->assertEquals('User created', $responseData['message']);
        $this->assertEquals($data, $responseData['data']);
    }

    public function testSuccessResponseWithCustomMessage(): void
    {
        $message = 'Operation completed successfully';
        $response = $this->traitObject->successResponse(201, $message);

        $responseData = $response->getData(true);
        $this->assertEquals($message, $responseData['message']);
        $this->assertEquals(201, $responseData['status']);
    }

    public function testErrorResponse(): void
    {
        $response = $this->traitObject->errorResponse(400, 'Bad request');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(400, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals(400, $responseData['status']);
        $this->assertEquals('Bad request', $responseData['message']);
    }

    public function testErrorResponseWithErrors(): void
    {
        $errors = [
            'name' => ['The name field is required.'],
            'email' => ['The email field is required.', 'The email must be valid.']
        ];
        
        $response = $this->traitObject->errorResponse(422, 'Validation failed', $errors);

        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals(422, $responseData['status']);
        $this->assertEquals('Validation failed', $responseData['message']);
        $this->assertEquals($errors, $responseData['errors']);
    }

    public function testNotFoundResponse(): void
    {
        $response = $this->traitObject->notFoundResponse('Resource not found');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(404, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals(404, $responseData['status']);
        $this->assertEquals('Resource not found', $responseData['message']);
    }

    public function testNotFoundResponseWithDefaultMessage(): void
    {
        $response = $this->traitObject->notFoundResponse();

        $responseData = $response->getData(true);
        $this->assertEquals('Not Found', $responseData['message']);
    }

    public function testUnauthorizedResponse(): void
    {
        $response = $this->traitObject->unauthorizedResponse('Access denied');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(401, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals(401, $responseData['status']);
        $this->assertEquals('Access denied', $responseData['message']);
    }

    public function testUnauthorizedResponseWithDefaultMessage(): void
    {
        $response = $this->traitObject->unauthorizedResponse();

        $responseData = $response->getData(true);
        $this->assertEquals('Unauthorized', $responseData['message']);
    }

    public function testForbiddenResponse(): void
    {
        $response = $this->traitObject->forbiddenResponse('Forbidden access');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(403, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals(403, $responseData['status']);
        $this->assertEquals('Forbidden access', $responseData['message']);
    }

    public function testValidationErrorResponse(): void
    {
        $errors = [
            'email' => ['The email field is required.'],
            'password' => ['The password must be at least 8 characters.']
        ];
        
        $response = $this->traitObject->validationErrorResponse($errors);

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(422, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals(422, $responseData['status']);
        $this->assertEquals('Validation Error', $responseData['message']);
        $this->assertEquals($errors, $responseData['errors']);
    }

    public function testInternalServerErrorResponse(): void
    {
        $response = $this->traitObject->internalServerErrorResponse('Server error occurred');

        $this->assertInstanceOf(JsonResponse::class, $response);
        $this->assertEquals(500, $response->getStatusCode());
        
        $responseData = $response->getData(true);
        $this->assertFalse($responseData['success']);
        $this->assertEquals(500, $responseData['status']);
        $this->assertEquals('Server error occurred', $responseData['message']);
    }

    public function testResponseWithPagination(): void
    {
        $data = ['item1', 'item2', 'item3'];
        $pagination = [
            'current_page' => 1,
            'per_page' => 10,
            'total' => 3,
            'last_page' => 1
        ];
        
        $response = $this->traitObject->successResponse(200, 'Success', $data, $pagination);

        $responseData = $response->getData(true);
        $this->assertTrue($responseData['success']);
        $this->assertEquals($data, $responseData['data']);
        $this->assertEquals($pagination, $responseData['pagination']);
    }

    public function testResponseWithMeta(): void
    {
        $data = ['result' => 'test'];
        $meta = [
            'execution_time' => 0.5,
            'memory_usage' => '2MB',
            'api_version' => '1.0'
        ];
        
        $response = $this->traitObject->successResponse(200, 'Success', $data, null, $meta);

        $responseData = $response->getData(true);
        $this->assertEquals($meta, $responseData['meta']);
    }

    public function testResponseStructureConsistency(): void
    {
        $successResponse = $this->traitObject->successResponse(200, 'Success');
        $errorResponse = $this->traitObject->errorResponse(400, 'Error');

        $successData = $successResponse->getData(true);
        $errorData = $errorResponse->getData(true);

        // Both should have these keys
        $requiredKeys = ['success', 'status', 'message'];
        
        foreach ($requiredKeys as $key) {
            $this->assertArrayHasKey($key, $successData);
            $this->assertArrayHasKey($key, $errorData);
        }

        // Success should be true for success response, false for error
        $this->assertTrue($successData['success']);
        $this->assertFalse($errorData['success']);
    }

    public function testResponseWithEmptyData(): void
    {
        $response = $this->traitObject->successResponse(200, 'Success', []);

        $responseData = $response->getData(true);
        $this->assertEquals([], $responseData['data']);
    }

    public function testResponseWithNullData(): void
    {
        $response = $this->traitObject->successResponse(200, 'Success', null);

        $responseData = $response->getData(true);
        $this->assertNull($responseData['data']);
    }

    public function testResponseWithComplexData(): void
    {
        $complexData = [
            'user' => [
                'id' => 1,
                'name' => 'John Doe',
                'roles' => ['admin', 'user'],
                'profile' => [
                    'avatar' => 'avatar.jpg',
                    'settings' => [
                        'theme' => 'dark',
                        'notifications' => true
                    ]
                ]
            ],
            'permissions' => ['read', 'write', 'delete']
        ];

        $response = $this->traitObject->successResponse(200, 'Success', $complexData);

        $responseData = $response->getData(true);
        $this->assertEquals($complexData, $responseData['data']);
        $this->assertEquals('John Doe', $responseData['data']['user']['name']);
        $this->assertContains('admin', $responseData['data']['user']['roles']);
    }

    public function testResponseHeaders(): void
    {
        $response = $this->traitObject->successResponse(200, 'Success');

        $this->assertEquals('application/json', $response->headers->get('Content-Type'));
    }

    public function testDifferentStatusCodes(): void
    {
        $statusCodes = [200, 201, 400, 401, 403, 404, 422, 500];

        foreach ($statusCodes as $code) {
            if ($code < 400) {
                $response = $this->traitObject->successResponse($code, 'Message');
            } else {
                $response = $this->traitObject->errorResponse($code, 'Error message');
            }

            $this->assertEquals($code, $response->getStatusCode());
            
            $responseData = $response->getData(true);
            $this->assertEquals($code, $responseData['status']);
        }
    }
}
