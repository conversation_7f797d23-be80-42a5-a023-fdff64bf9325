<?php

namespace Tests\Traits;

trait ResponseAssertions
{

    //api-nin isleyib islemediyini yoxlayir
    /**
     * Assert that the response contains a status key and it equals 200.
     *
     * @param array $response
     * @return void
     */
    protected function ehdisAssertSuccessfulStatus(array $response): void
    {
        $this->assertArrayHasKey('status', $response, 'Response does not contain status key.');
        $this->assertEquals(200, $response['status'], 'The status is not 200, stopping the test.');
    }

    public function assertResponseHasKeys($response, array $expectedKeys): void
    {
        $this->assertTrue(is_array($response) || is_object($response), 'Response must be an array or an object.');

        $responseArray = is_object($response) ? (array) $response : $response;

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $responseArray, "Response does not have the expected key: $key");
        }
    }

    protected function getTestPin($pin = ""): string
    {
        return strtoupper(env('TEST_PIN'.$pin));
    }

    protected function getTestCarNumber(): string
    {
        return strtoupper(env('TEST_CAR_NUMBER'));
    }

    protected function getTestPassportNumber(): string
    {
        return strtoupper(env('TEST_PASSPORT_NUMBER'));
    }


}
