<?php

namespace Tests\Feature\Profile;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class TelegramProfileTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_telegram_profile(): void
    {
        $pin = $this->getTestPin();

        $response = $this->get(self::V1_PREFIX.'/person/'.$pin.'/telegram-profile');
        $response->assertOk();
        $this->assertAuthenticated('api');

        $response->assertStatus(200);
    }
}
