<?php

namespace Tests\Feature\Profile;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class UserGalleryTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_user_gallery(): void
    {
        $pin = $this->getTestPin();

        $response = $this->get( $URI = self::V1_PREFIX.'/person/'.$pin.'/gallery');

        $response->assertOk();
        $this->assertAuthenticated('api');

        $response->assertStatus(200);
    }
}
