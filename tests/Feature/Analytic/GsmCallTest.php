<?php

namespace Tests\Feature\Analytic;

use Tests\TestCase;

class GsmCallTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testGsmCall(): void
    {
        $filters = [
            'rules' => []
        ];

        $response = $this->post(self::V1_PREFIX.'/calls/gsm-all-call', ['filters' => $filters]);
        $response->assertOk();
        $this->assertAuthenticated('api');

        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'called_imei',
                    'called_imsi',
                    'called_msisdn',
                    'called_start_cell_id',
                    'called_start_lac',
                    'called_start_lut',
                    'called_start_type',
                    'called_trace_id',
                    'caller_imei',
                    'caller_imsi',
                    'caller_msisdn',
                    'caller_start_cell_id',
                    'caller_start_lac',
                    'caller_start_lut',
                    'caller_start_type',
                    'caller_trace_id',
                    'conf_call_id',
                    'duration',
                    'end_reason',
                    'end_time',
                    'has_sound',
                    'sms_body',
                    'start_time',
                    'sub_system_id',
                    'type'
                ]
            ]
        ]);

        $responseData = $response->json('data');

        foreach ($responseData as $row) {
            $this->assertIsString($row['caller_imei']);
            $this->assertIsString($row['caller_imsi']);
            $this->assertIsString($row['caller_msisdn']);
            $this->assertIsInt((int)$row['sub_system_id']);
            $this->assertIsBool($row['has_sound']);
            $this->assertIsString($row['end_time']);
        }
    }
}


