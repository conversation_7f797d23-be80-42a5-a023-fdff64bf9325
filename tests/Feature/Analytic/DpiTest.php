<?php

namespace Tests\Feature\Analytic;

use Tests\TestCase;

class DpiTest extends TestCase
{
    /**
     * Test DPI data retrieval between two phone numbers.
     */
    public function testDetailBetweenNumbers(): void
    {
        $data = [
            'from' => "2024-04-26 09:52:11",
            'to' => "2024-04-26 09:52:13",
            'phoneFirst' => "994502560816",
            'phoneSecond' => "994502810415",
        ];

        $this->assertAuthenticated('api');

        $response = $this->postJson(self::V1_PREFIX . '/dpi-data/detail-between-numbers', $data);
        $response->assertOk();

        $responseArray = $response->json();
        $this->assertIsArray($responseArray);

        if (empty($responseArray)) {
            $this->markTestSkipped('No DPI data available for the given parameters');
            return;
        }

        foreach ($responseArray as $index => $row) {
            $this->assertArrayHasKey('lat', $row, "Row {$index} missing 'lat' key");
            $this->assertArrayHasKey('lon', $row, "Row {$index} missing 'lon' key");
            $this->assertArrayHasKey('number', $row, "Row {$index} missing 'number' key");
            $this->assertArrayHasKey('type', $row, "Row {$index} missing 'type' key");

            // Validate coordinates
            $lat = (float)$row['lat'];
            $lon = (float)$row['lon'];
            $this->assertGreaterThanOrEqual(-90, $lat);
            $this->assertLessThanOrEqual(90, $lat);
            $this->assertGreaterThanOrEqual(-180, $lon);
            $this->assertLessThanOrEqual(180, $lon);

            // Validate phone number format
            $this->assertMatchesRegularExpression('/^994\d{9}$/', $row['number']);

            // Validate type
            $this->assertContains($row['type'], ['receiver', 'caller']);

            // Ensure number matches input
            $this->assertContains($row['number'], [$data['phoneFirst'], $data['phoneSecond']]);
        }
    }

    /**
     * Test unauthenticated request.
     */
    public function testDetailBetweenNumbersUnauthenticated(): void
    {
        // This will be called BEFORE setUp(), so authentication won't happen
        $this->shouldAuthenticate = false;

        $data = [
            'from' => "2024-04-26 09:52:11",
            'to' => "2024-04-26 09:52:13",
            'phoneFirst' => "994502560816",
            'phoneSecond' => "994502810415",
        ];

        // Make request without any authentication
        $response = $this->postJson(self::V1_PREFIX . '/dpi-data/detail-between-numbers', $data);

        // Should get 401 Unauthorized
        $response->assertStatus(401);
    }

    /**
     * Alternative: Create a fresh application instance without auth.
     */
    public function testDetailBetweenNumbersUnauthenticatedFresh(): void
    {
        // Create a completely fresh application instance
        $this->refreshApplication();

        $data = [
            'from' => "2024-04-26 09:52:11",
            'to' => "2024-04-26 09:52:13",
            'phoneFirst' => "994502560816",
            'phoneSecond' => "994502810415",
        ];

        $response = $this->postJson(self::V1_PREFIX . '/dpi-data/detail-between-numbers', $data);

        // Check what we actually get
        if ($response->status() !== 401) {
            $this->fail(
                "Expected 401 but got {$response->status()}. " .
                "Response: " . $response->getContent()
            );
        }

        $response->assertStatus(401);
    }



    /**
     * Test with invalid request data.
     */
    public function testDetailBetweenNumbersValidation(): void
    {
        $invalidData = [
            'from' => "invalid-date",
            'to' => "2024-04-26 09:52:13",
            'phoneFirst' => "invalid-phone",
            'phoneSecond' => "994502810415",
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/dpi-data/detail-between-numbers', $invalidData);
        $response->assertStatus(422);

        // Assert validation errors structure
        $response->assertJsonStructure([
            'message',
            'errors' => [
                'from',
                'phoneFirst'
            ]
        ]);
    }



    /**
     * Validate phone number format and matching.
     */
    private function assertValidPhoneNumber(mixed $number, array $inputData, int $index): void
    {
        $this->assertIsString($number, "Row {$index} number should be a string");

        // Validate Azerbaijan phone format
        $this->assertMatchesRegularExpression('/^994\d{9}$/', $number,
            "Row {$index} number should be in Azerbaijan format (994XXXXXXXXX)");

        // Ensure the number matches one of the input phones
        $this->assertContains($number, [$inputData['phoneFirst'], $inputData['phoneSecond']],
            "Row {$index} number should match one of the input phone numbers");
    }
}
