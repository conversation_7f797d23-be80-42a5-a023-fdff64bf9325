<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CameraTypeControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = true;
    }

    public function testGetCameraTypesRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/cameraTypes');
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'status' => 401,
            'message' => 'Unauthorized'
        ]);
    }

    public function testGetCameraTypesReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameraTypes');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'description',
                    'category',
                    'resolution',
                    'features',
                    'manufacturer',
                    'model',
                    'is_active',
                    'created_at',
                    'updated_at'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetCameraTypesWithPaginationParameters(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameraTypes?page=1&per_page=10');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
    }

    public function testCreateCameraTypeRequiresAuthentication(): void
    {
        $cameraTypeData = [
            'name' => 'Test Security Camera',
            'category' => 'surveillance'
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        
        $response->assertStatus(401);
    }

    public function testCreateCameraTypeWithValidData(): void
    {
        $cameraTypeData = [
            'name' => 'High-Resolution Security Camera',
            'description' => 'Advanced security surveillance camera',
            'category' => 'surveillance',
            'resolution' => '4K',
            'features' => ['night_vision', 'motion_detection', 'ptz', 'ai_detection'],
            'manufacturer' => 'Hikvision',
            'model' => 'DS-2CD2143G0-I',
            'is_active' => true
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'name',
                'category',
                'created_at'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(201, $responseData['status']);
        $this->assertEquals($cameraTypeData['name'], $responseData['data']['name']);
    }

    public function testCreateCameraTypeValidationRequiresName(): void
    {
        $cameraTypeData = [
            'category' => 'surveillance'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    public function testCreateCameraTypeValidationRequiresCategory(): void
    {
        $cameraTypeData = [
            'name' => 'Test Camera'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['category']);
    }

    public function testCreateCameraTypeValidationNameLength(): void
    {
        $cameraTypeData = [
            'name' => 'A', // Too short
            'category' => 'surveillance'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    public function testGetCameraTypeByIdRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/cameraTypes/1');
        
        $response->assertStatus(401);
    }

    public function testGetCameraTypeByIdReturnsSuccessfulResponse(): void
    {
        // First create a camera type
        $cameraTypeData = [
            'name' => 'Test Camera Type',
            'description' => 'Test camera type description',
            'category' => 'surveillance',
            'resolution' => '1920x1080',
            'features' => ['night_vision', 'motion_detection']
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        $cameraTypeId = $createResponse->json('data.id');

        $response = $this->getAuthenticated(self::V1_PREFIX . "/cameraTypes/{$cameraTypeId}");
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'name',
                'description',
                'category',
                'resolution',
                'features',
                'manufacturer',
                'model',
                'is_active',
                'cameras_count',
                'created_at',
                'updated_at'
            ]
        ]);
    }

    public function testGetCameraTypeByIdNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameraTypes/99999');
        
        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'status' => 404,
            'message' => 'Camera type not found'
        ]);
    }

    public function testUpdateCameraTypeRequiresAuthentication(): void
    {
        $updateData = [
            'name' => 'Updated Camera Name'
        ];

        $response = $this->unauthenticatedRequest('PUT', self::V1_PREFIX . '/cameraTypes/1', $updateData);
        
        $response->assertStatus(401);
    }

    public function testUpdateCameraTypeWithValidData(): void
    {
        // First create a camera type
        $cameraTypeData = [
            'name' => 'Original Camera',
            'description' => 'Original description',
            'category' => 'surveillance'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        $cameraTypeId = $createResponse->json('data.id');

        $updateData = [
            'name' => 'Updated Camera Name',
            'description' => 'Updated description',
            'resolution' => '4K',
            'features' => ['night_vision', 'motion_detection', 'ptz', 'ai_detection'],
            'is_active' => false
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . "/cameraTypes/{$cameraTypeId}", $updateData);
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'name',
                'updated_at'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertEquals($updateData['name'], $responseData['data']['name']);
    }

    public function testUpdateCameraTypeNotFound(): void
    {
        $updateData = [
            'name' => 'Updated Name'
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . '/cameraTypes/99999', $updateData);
        
        $response->assertStatus(404);
    }

    public function testDeleteCameraTypeRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('DELETE', self::V1_PREFIX . '/cameraTypes/1');
        
        $response->assertStatus(401);
    }

    public function testDeleteCameraTypeSuccessfully(): void
    {
        // First create a camera type
        $cameraTypeData = [
            'name' => 'Camera to Delete',
            'description' => 'This camera type will be deleted',
            'category' => 'surveillance'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        $cameraTypeId = $createResponse->json('data.id');

        $response = $this->deleteAuthenticated(self::V1_PREFIX . "/cameraTypes/{$cameraTypeId}");
        
        $response->assertOk();
        $response->assertJson([
            'success' => true,
            'status' => 200,
            'message' => 'Camera type deleted successfully'
        ]);
    }

    public function testDeleteCameraTypeNotFound(): void
    {
        $response = $this->deleteAuthenticated(self::V1_PREFIX . '/cameraTypes/99999');
        
        $response->assertStatus(404);
    }

    public function testGetAllCameraTypesRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/cameraTypes/list/all');
        
        $response->assertStatus(401);
    }

    public function testGetAllCameraTypesReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameraTypes/list/all');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'category',
                    'is_active'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testCameraTypeDataStructureValidation(): void
    {
        // Create a camera type first
        $cameraTypeData = [
            'name' => 'Test Camera Type',
            'description' => 'Test description',
            'category' => 'surveillance',
            'resolution' => '1920x1080',
            'features' => ['night_vision', 'motion_detection'],
            'manufacturer' => 'Hikvision',
            'model' => 'DS-2CD2143G0-I',
            'is_active' => true
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        $cameraTypeId = $createResponse->json('data.id');

        $response = $this->getAuthenticated(self::V1_PREFIX . "/cameraTypes/{$cameraTypeId}");
        
        $response->assertOk();
        $data = $response->json('data');
        
        // Validate required fields
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('name', $data);
        $this->assertArrayHasKey('category', $data);
        $this->assertArrayHasKey('is_active', $data);
        
        // Validate data types
        $this->assertIsInt($data['id']);
        $this->assertIsString($data['name']);
        $this->assertIsString($data['category']);
        $this->assertIsBool($data['is_active']);
        
        // Validate features array
        if (isset($data['features'])) {
            $this->assertIsArray($data['features']);
            foreach ($data['features'] as $feature) {
                $this->assertIsString($feature);
            }
        }
        
        // Validate cameras_count if present
        if (isset($data['cameras_count'])) {
            $this->assertIsInt($data['cameras_count']);
            $this->assertGreaterThanOrEqual(0, $data['cameras_count']);
        }
        
        // Validate timestamps
        $this->assertArrayHasKey('created_at', $data);
        $this->assertArrayHasKey('updated_at', $data);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/', $data['created_at']);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/', $data['updated_at']);
    }

    public function testCameraTypeFeaturesValidation(): void
    {
        $cameraTypeData = [
            'name' => 'Feature Test Camera',
            'category' => 'surveillance',
            'features' => ['night_vision', 'motion_detection', 'ptz', 'ai_detection', 'audio_recording']
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        
        $response->assertStatus(201);
        $responseData = $response->json();
        
        // Verify features are stored correctly
        $cameraTypeId = $responseData['data']['id'];
        $getResponse = $this->getAuthenticated(self::V1_PREFIX . "/cameraTypes/{$cameraTypeId}");
        
        $getResponse->assertOk();
        $data = $getResponse->json('data');
        
        if (isset($data['features'])) {
            $this->assertIsArray($data['features']);
            $this->assertContains('night_vision', $data['features']);
            $this->assertContains('motion_detection', $data['features']);
            $this->assertContains('ptz', $data['features']);
        }
    }

    public function testCameraTypeResolutionValidation(): void
    {
        $testResolutions = ['720p', '1080p', '4K', '8K', '1920x1080', '3840x2160'];
        
        foreach ($testResolutions as $resolution) {
            $cameraTypeData = [
                'name' => "Camera with {$resolution}",
                'category' => 'surveillance',
                'resolution' => $resolution
            ];

            $response = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
            
            $response->assertStatus(201);
            $responseData = $response->json();
            $this->assertEquals($cameraTypeData['name'], $responseData['data']['name']);
        }
    }

    public function testCameraTypeCategoryValidation(): void
    {
        $validCategories = ['surveillance', 'security', 'monitoring', 'traffic', 'indoor', 'outdoor'];
        
        foreach ($validCategories as $category) {
            $cameraTypeData = [
                'name' => "Camera for {$category}",
                'category' => $category
            ];

            $response = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
            
            // Should either accept the category or return validation error
            $this->assertContains($response->status(), [201, 422]);
            
            if ($response->status() === 201) {
                $responseData = $response->json();
                $this->assertEquals($category, $responseData['data']['category']);
            }
        }
    }

    public function testCameraTypeManufacturerAndModelValidation(): void
    {
        $cameraTypeData = [
            'name' => 'Branded Camera',
            'category' => 'surveillance',
            'manufacturer' => 'Hikvision',
            'model' => 'DS-2CD2143G0-I'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameraTypes', $cameraTypeData);
        
        $response->assertStatus(201);
        $cameraTypeId = $response->json('data.id');
        
        $getResponse = $this->getAuthenticated(self::V1_PREFIX . "/cameraTypes/{$cameraTypeId}");
        $data = $getResponse->json('data');
        
        if (isset($data['manufacturer'])) {
            $this->assertEquals('Hikvision', $data['manufacturer']);
        }
        
        if (isset($data['model'])) {
            $this->assertEquals('DS-2CD2143G0-I', $data['model']);
        }
    }
}
