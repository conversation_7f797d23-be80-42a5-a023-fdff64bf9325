<?php

namespace Tests\Feature\Api;

use App\Models\Person;
use App\Models\Blacklist;
use App\Models\Camera;
use App\Models\ScannedFaces;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class SearchControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testSearchPersonsByName(): void
    {
        // Create test persons
        Person::factory()->create(['name' => 'John', 'surname' => 'Doe', 'pin' => 'PIN001']);
        Person::factory()->create(['name' => '<PERSON>', 'surname' => 'Smith', 'pin' => 'PIN002']);
        Person::factory()->create(['name' => 'Bob', 'surname' => '<PERSON>', 'pin' => 'PIN003']);

        $searchData = [
            'name' => 'John'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/persons', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'surname',
                    'pin'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData); // John Doe and Bob Johnson (contains 'John')
    }

    public function testSearchPersonsByPin(): void
    {
        Person::factory()->create(['name' => 'John', 'surname' => 'Doe', 'pin' => 'ABC123']);
        Person::factory()->create(['name' => 'Jane', 'surname' => 'Smith', 'pin' => 'DEF456']);

        $searchData = [
            'pin' => 'ABC123'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/persons', $searchData);

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('ABC123', $responseData[0]['pin']);
    }

    public function testSearchPersonsByMultipleCriteria(): void
    {
        Person::factory()->create(['name' => 'John', 'surname' => 'Doe', 'pin' => 'PIN001', 'father_name' => 'Michael']);
        Person::factory()->create(['name' => 'John', 'surname' => 'Smith', 'pin' => 'PIN002', 'father_name' => 'Robert']);

        $searchData = [
            'name' => 'John',
            'surname' => 'Doe'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/persons', $searchData);

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Doe', $responseData[0]['surname']);
    }

    public function testFaceSearchWithImage(): void
    {
        Storage::fake('public');
        
        $image = UploadedFile::fake()->image('face.jpg', 300, 300);

        $searchData = [
            'image' => $image,
            'similarity' => 0.8,
            'max_results' => 10
        ];

        // Mock the face recognition service response
        $this->mockFaceRecognitionService();

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/face', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'similarity_threshold',
                'results' => [
                    '*' => [
                        'person_id',
                        'similarity_score',
                        'image_path'
                    ]
                ]
            ]
        ]);
    }

    public function testFaceSearchValidationErrors(): void
    {
        $invalidData = [
            'similarity' => 1.5, // Invalid similarity (should be 0-1)
            'max_results' => 1001 // Too many results
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/face', $invalidData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['image', 'similarity', 'max_results']);
    }

    public function testAdvancedPersonSearch(): void
    {
        Person::factory()->create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'PIN001',
            'doc_number' => 'DOC123',
            'birthdate' => '1990-01-01'
        ]);

        $searchData = [
            'name' => 'John',
            'surname' => 'Doe',
            'birthdate_from' => '1989-01-01',
            'birthdate_to' => '1991-01-01',
            'document_number' => 'DOC123'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/persons/advanced', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'surname',
                    'pin',
                    'doc_number',
                    'birthdate'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('DOC123', $responseData[0]['doc_number']);
    }

    public function testSearchBlacklistPersons(): void
    {
        Blacklist::factory()->create(['name' => 'Criminal', 'surname' => 'Person', 'pin' => 'BL001', 'status' => 1]);
        Blacklist::factory()->create(['name' => 'Another', 'surname' => 'Criminal', 'pin' => 'BL002', 'status' => 1]);

        $searchData = [
            'name' => 'Criminal'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/blacklist', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'surname',
                    'pin',
                    'status'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData);
    }

    public function testSearchWithPagination(): void
    {
        Person::factory()->count(25)->create(['name' => 'Test']);

        $searchData = [
            'name' => 'Test',
            'page' => 2,
            'per_page' => 10
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/persons', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'links',
            'meta' => [
                'current_page',
                'per_page',
                'total'
            ]
        ]);

        $responseData = $response->json();
        $this->assertEquals(2, $responseData['meta']['current_page']);
        $this->assertEquals(10, $responseData['meta']['per_page']);
        $this->assertCount(10, $responseData['data']);
    }

    public function testSearchByPhoneNumber(): void
    {
        // Assuming there's a phone search functionality
        $searchData = [
            'phone' => '994501234567'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/phone', $searchData);

        // This test depends on the actual implementation
        if ($response->status() === 200) {
            $response->assertJsonStructure([
                'data' => [
                    'phone_number',
                    'owner_info',
                    'call_history'
                ]
            ]);
        } else {
            $response->assertStatus(404);
        }
    }

    public function testSearchByVehicleNumber(): void
    {
        $searchData = [
            'vehicle_number' => '10-AA-123'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/vehicle', $searchData);

        // This test depends on the actual implementation
        if ($response->status() === 200) {
            $response->assertJsonStructure([
                'data' => [
                    'vehicle_number',
                    'owner_info',
                    'movement_history'
                ]
            ]);
        } else {
            $response->assertStatus(404);
        }
    }

    public function testGlobalSearch(): void
    {
        // Create test data across different models
        Person::factory()->create(['name' => 'Global', 'surname' => 'Test', 'pin' => 'GT001']);
        Blacklist::factory()->create(['name' => 'Global', 'surname' => 'Criminal', 'pin' => 'GT002']);

        $searchData = [
            'query' => 'Global',
            'types' => ['persons', 'blacklist']
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/global', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'persons' => [
                    '*' => ['id', 'name', 'surname', 'pin']
                ],
                'blacklist' => [
                    '*' => ['id', 'name', 'surname', 'pin']
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(1, $responseData['persons']);
        $this->assertCount(1, $responseData['blacklist']);
    }

    public function testSearchWithFilters(): void
    {
        Person::factory()->create(['name' => 'John', 'gender' => 'male', 'is_sync' => true]);
        Person::factory()->create(['name' => 'Jane', 'gender' => 'female', 'is_sync' => true]);
        Person::factory()->create(['name' => 'Bob', 'gender' => 'male', 'is_sync' => false]);

        $searchData = [
            'name' => '',
            'filters' => [
                'gender' => 'male',
                'is_sync' => true
            ]
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/persons', $searchData);

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('John', $responseData[0]['name']);
    }

    public function testSearchResultsSorting(): void
    {
        Person::factory()->create(['name' => 'Charlie', 'created_at' => now()->subDays(3)]);
        Person::factory()->create(['name' => 'Alice', 'created_at' => now()->subDays(1)]);
        Person::factory()->create(['name' => 'Bob', 'created_at' => now()->subDays(2)]);

        $searchData = [
            'name' => '',
            'sort_by' => 'name',
            'sort_order' => 'asc'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/persons', $searchData);

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertEquals('Alice', $responseData[0]['name']);
        $this->assertEquals('Bob', $responseData[1]['name']);
        $this->assertEquals('Charlie', $responseData[2]['name']);
    }

    public function testSearchWithEmptyResults(): void
    {
        $searchData = [
            'name' => 'NonExistentName'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/search/persons', $searchData);

        $response->assertOk();
        $response->assertJson([
            'data' => []
        ]);
    }

    public function testUnauthenticatedUserCannotSearch(): void
    {
        $this->shouldAuthenticate = false;

        $response = $this->postJson(self::V1_PREFIX . '/search/persons', ['name' => 'test']);

        $response->assertStatus(401);
    }

    private function mockFaceRecognitionService(): void
    {
        // Mock the face recognition service to return test data
        $this->mock(\App\Services\FaceRecognitionService::class, function ($mock) {
            $mock->shouldReceive('searchSimilarFaces')
                ->andReturn([
                    [
                        'person_id' => 1,
                        'similarity_score' => 0.95,
                        'image_path' => 'faces/person1.jpg'
                    ],
                    [
                        'person_id' => 2,
                        'similarity_score' => 0.87,
                        'image_path' => 'faces/person2.jpg'
                    ]
                ]);
        });
    }
}
