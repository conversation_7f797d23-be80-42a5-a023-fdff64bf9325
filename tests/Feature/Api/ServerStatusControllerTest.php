<?php

namespace Tests\Feature\Api;

use App\Services\ServerStatusService;
use Tests\TestCase;
use Mockery;

class ServerStatusControllerTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock ServerStatusService to avoid actual server checks
        $this->mockServerStatusService = Mockery::mock(ServerStatusService::class);
        $this->app->instance(ServerStatusService::class, $this->mockServerStatusService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testGetAllServerStatuses(): void
    {
        $mockStatuses = [
            [
                'name' => 'Backend Server',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'API',
                'host' => 'localhost'
            ],
            [
                'name' => 'Postgre Sql',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'DB',
                'host' => '*.*.*.28'
            ],
            [
                'name' => 'MongoDB',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'DB',
                'host' => '*.*.*.29'
            ]
        ];

        $this->mockServerStatusService
            ->shouldReceive('getALLStatuses')
            ->once()
            ->andReturn($mockStatuses);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'name',
                    'status',
                    'message',
                    'type',
                    'host'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(3, $responseData);
        $this->assertEquals('Backend Server', $responseData[0]['name']);
        $this->assertEquals(200, $responseData[0]['status']);
    }

    public function testGetPostgreSqlStatus(): void
    {
        $mockStatus = [
            'name' => 'Postgre Sql',
            'status' => 200,
            'message' => 'Connected',
            'type' => 'DB',
            'host' => '*.*.*.28'
        ];

        $this->mockServerStatusService
            ->shouldReceive('postgreSql')
            ->once()
            ->andReturn($mockStatus);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status/postgresql');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'name',
                'status',
                'message',
                'type',
                'host'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Postgre Sql', $responseData['name']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetMongoDbStatus(): void
    {
        $mockStatus = [
            'name' => 'MongoDB',
            'status' => 200,
            'message' => 'Connected',
            'type' => 'DB',
            'host' => '*.*.*.29'
        ];

        $this->mockServerStatusService
            ->shouldReceive('mongoDb')
            ->once()
            ->andReturn($mockStatus);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status/mongodb');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'name',
                'status',
                'message',
                'type',
                'host'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('MongoDB', $responseData['name']);
    }

    public function testGetBackendServerStatus(): void
    {
        $mockStatus = [
            'name' => 'Backend Server',
            'status' => 200,
            'message' => 'Connected',
            'type' => 'API',
            'host' => 'localhost'
        ];

        $this->mockServerStatusService
            ->shouldReceive('backendServer')
            ->once()
            ->andReturn($mockStatus);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status/backend');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'name',
                'status',
                'message',
                'type',
                'host'
            ]
        ]);
    }

    public function testServerStatusWithFailures(): void
    {
        $mockStatuses = [
            [
                'name' => 'Backend Server',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'API',
                'host' => 'localhost'
            ],
            [
                'name' => 'Postgre Sql',
                'status' => 500,
                'message' => 'Connection failed',
                'type' => 'DB',
                'host' => '*.*.*.28'
            ],
            [
                'name' => 'MongoDB',
                'status' => 404,
                'message' => 'Service not found',
                'type' => 'DB',
                'host' => '*.*.*.29'
            ]
        ];

        $this->mockServerStatusService
            ->shouldReceive('getALLStatuses')
            ->once()
            ->andReturn($mockStatuses);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status');

        $response->assertOk();
        $responseData = $response->json('data');
        
        // Check that we get all statuses including failed ones
        $this->assertCount(3, $responseData);
        
        // Find the failed PostgreSQL status
        $postgresStatus = collect($responseData)->firstWhere('name', 'Postgre Sql');
        $this->assertEquals(500, $postgresStatus['status']);
        $this->assertEquals('Connection failed', $postgresStatus['message']);
        
        // Find the failed MongoDB status
        $mongoStatus = collect($responseData)->firstWhere('name', 'MongoDB');
        $this->assertEquals(404, $mongoStatus['status']);
        $this->assertEquals('Service not found', $mongoStatus['message']);
    }

    public function testServerStatusResponseFormat(): void
    {
        $mockStatuses = [
            [
                'name' => 'Test Service',
                'status' => 200,
                'message' => 'OK',
                'type' => 'API',
                'host' => 'test.example.com'
            ]
        ];

        $this->mockServerStatusService
            ->shouldReceive('getALLStatuses')
            ->once()
            ->andReturn($mockStatuses);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'name',
                    'status',
                    'message',
                    'type',
                    'host'
                ]
            ]
        ]);

        // Verify response format
        $responseData = $response->json('data');
        $service = $responseData[0];
        
        $this->assertIsString($service['name']);
        $this->assertIsInt($service['status']);
        $this->assertIsString($service['message']);
        $this->assertIsString($service['type']);
        $this->assertIsString($service['host']);
    }

    public function testServerStatusWithTimeout(): void
    {
        $mockStatuses = [
            [
                'name' => 'Slow Service',
                'status' => 408,
                'message' => 'Request timeout',
                'type' => 'API',
                'host' => 'slow.example.com'
            ]
        ];

        $this->mockServerStatusService
            ->shouldReceive('getALLStatuses')
            ->once()
            ->andReturn($mockStatuses);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status');

        $response->assertOk();
        $responseData = $response->json('data');
        
        $this->assertEquals(408, $responseData[0]['status']);
        $this->assertEquals('Request timeout', $responseData[0]['message']);
    }

    public function testUnauthenticatedUserCannotAccessServerStatus(): void
    {
        $this->shouldAuthenticate = false;

        $response = $this->getJson(self::V1_PREFIX . '/server-status');

        $response->assertStatus(401);
    }

    public function testServerStatusHealthCheck(): void
    {
        $mockStatuses = [
            [
                'name' => 'Backend Server',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'API',
                'host' => 'localhost'
            ],
            [
                'name' => 'Postgre Sql',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'DB',
                'host' => '*.*.*.28'
            ]
        ];

        $this->mockServerStatusService
            ->shouldReceive('getALLStatuses')
            ->once()
            ->andReturn($mockStatuses);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status/health');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'overall_status',
                'healthy_services',
                'failed_services',
                'total_services'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('healthy', $responseData['overall_status']);
        $this->assertEquals(2, $responseData['healthy_services']);
        $this->assertEquals(0, $responseData['failed_services']);
        $this->assertEquals(2, $responseData['total_services']);
    }

    public function testServerStatusHealthCheckWithFailures(): void
    {
        $mockStatuses = [
            [
                'name' => 'Backend Server',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'API',
                'host' => 'localhost'
            ],
            [
                'name' => 'Postgre Sql',
                'status' => 500,
                'message' => 'Connection failed',
                'type' => 'DB',
                'host' => '*.*.*.28'
            ]
        ];

        $this->mockServerStatusService
            ->shouldReceive('getALLStatuses')
            ->once()
            ->andReturn($mockStatuses);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status/health');

        $response->assertOk();
        $responseData = $response->json('data');
        
        $this->assertEquals('degraded', $responseData['overall_status']);
        $this->assertEquals(1, $responseData['healthy_services']);
        $this->assertEquals(1, $responseData['failed_services']);
        $this->assertEquals(2, $responseData['total_services']);
    }

    public function testServerStatusMetrics(): void
    {
        $mockStatuses = [
            [
                'name' => 'Backend Server',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'API',
                'host' => 'localhost',
                'response_time' => 50
            ],
            [
                'name' => 'Postgre Sql',
                'status' => 200,
                'message' => 'Connected',
                'type' => 'DB',
                'host' => '*.*.*.28',
                'response_time' => 25
            ]
        ];

        $this->mockServerStatusService
            ->shouldReceive('getALLStatuses')
            ->once()
            ->andReturn($mockStatuses);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/server-status/metrics');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'average_response_time',
                'fastest_service',
                'slowest_service',
                'uptime_percentage'
            ]
        ]);
    }
}
