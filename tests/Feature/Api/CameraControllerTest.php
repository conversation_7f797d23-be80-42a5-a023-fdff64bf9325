<?php

namespace Tests\Feature\Api;

use App\Models\Camera;
use App\Models\CameraType;
use App\Models\Object\Object_;
use App\Models\Object\ObjectType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CameraControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create necessary related models
        $this->createTestData();
    }

    private function createTestData(): void
    {
        // Create object type
        $objectType = ObjectType::create([
            'name' => 'Building',
            'description' => 'Test building type'
        ]);

        // Create object
        $this->testObject = Object_::create([
            'object_type_id' => $objectType->id,
            'name' => 'Test Building',
            'address' => 'Test Address',
            'gps' => ['lat' => 40.4093, 'lng' => 49.8671],
            'active' => true
        ]);

        // Create camera type
        $this->testCameraType = CameraType::create([
            'name' => 'IP Camera',
            'description' => 'Test IP camera type'
        ]);
    }

    public function testGetCamerasList(): void
    {
        // Create test cameras
        Camera::factory()->count(3)->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameras');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'camera_id',
                    'name',
                    'object_id',
                    'camera_type_id',
                    'active',
                    'created_at',
                    'updated_at'
                ]
            ]
        ]);
    }

    public function testGetCamerasListWithPagination(): void
    {
        Camera::factory()->count(25)->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameras?page=1&per_page=10');

        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'links',
            'meta' => [
                'current_page',
                'per_page',
                'total'
            ]
        ]);

        $responseData = $response->json();
        $this->assertCount(10, $responseData['data']);
    }

    public function testGetCameraById(): void
    {
        $camera = Camera::factory()->create([
            'camera_id' => 'CAM001',
            'name' => 'Test Camera',
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'active' => true
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . "/cameras/{$camera->id}");

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'camera_id',
                'name',
                'object_id',
                'camera_type_id',
                'active',
                'type',
                'object'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('CAM001', $responseData['camera_id']);
        $this->assertEquals('Test Camera', $responseData['name']);
        $this->assertTrue($responseData['active']);
    }

    public function testGetCameraByIdNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameras/999999');

        $response->assertStatus(404);
    }

    public function testCreateCamera(): void
    {
        $cameraData = [
            'camera_id' => 'CAM002',
            'name' => 'New Camera',
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'ip_address' => '*************',
            'port' => 8080,
            'username' => 'admin',
            'password' => 'password123',
            'active' => true,
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameras', $cameraData);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'camera_id',
                'name',
                'object_id',
                'camera_type_id',
                'ip_address',
                'port',
                'active'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('CAM002', $responseData['camera_id']);
        $this->assertEquals('New Camera', $responseData['name']);

        // Verify camera was created in database
        $this->assertDatabaseHas('cameras', [
            'camera_id' => 'CAM002',
            'name' => 'New Camera',
            'object_id' => $this->testObject->id
        ]);
    }

    public function testCreateCameraValidationErrors(): void
    {
        $invalidData = [
            'name' => 'Camera without required fields',
            // Missing required camera_id, object_id, camera_type_id
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameras', $invalidData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['camera_id', 'object_id', 'camera_type_id']);
    }

    public function testUpdateCamera(): void
    {
        $camera = Camera::factory()->create([
            'camera_id' => 'CAM003',
            'name' => 'Original Camera',
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'active' => false
        ]);

        $updateData = [
            'name' => 'Updated Camera',
            'active' => true,
            'ip_address' => '*************',
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . "/cameras/{$camera->id}", $updateData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'camera_id',
                'name',
                'active',
                'ip_address'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Updated Camera', $responseData['name']);
        $this->assertTrue($responseData['active']);
        $this->assertEquals('*************', $responseData['ip_address']);

        // Verify camera was updated in database
        $this->assertDatabaseHas('cameras', [
            'id' => $camera->id,
            'name' => 'Updated Camera',
            'active' => true,
            'ip_address' => '*************'
        ]);
    }

    public function testUpdateCameraNotFound(): void
    {
        $updateData = [
            'name' => 'Updated Name',
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . '/cameras/999999', $updateData);

        $response->assertStatus(404);
    }

    public function testDeleteCamera(): void
    {
        $camera = Camera::factory()->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id
        ]);

        $response = $this->deleteAuthenticated(self::V1_PREFIX . "/cameras/{$camera->id}");

        $response->assertOk();

        // Verify camera was deleted from database
        $this->assertDatabaseMissing('cameras', [
            'id' => $camera->id
        ]);
    }

    public function testDeleteCameraNotFound(): void
    {
        $response = $this->deleteAuthenticated(self::V1_PREFIX . '/cameras/999999');

        $response->assertStatus(404);
    }

    public function testFilterCamerasByObjectId(): void
    {
        $anotherObject = Object_::factory()->create();

        Camera::factory()->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'name' => 'Camera 1'
        ]);

        Camera::factory()->create([
            'object_id' => $anotherObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'name' => 'Camera 2'
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . "/cameras?object_id={$this->testObject->id}");

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Camera 1', $responseData[0]['name']);
    }

    public function testFilterCamerasByActive(): void
    {
        Camera::factory()->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'active' => true,
            'name' => 'Active Camera'
        ]);

        Camera::factory()->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'active' => false,
            'name' => 'Inactive Camera'
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameras?active=1');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Active Camera', $responseData[0]['name']);
        $this->assertTrue($responseData[0]['active']);
    }

    public function testSearchCamerasByName(): void
    {
        Camera::factory()->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'name' => 'Front Door Camera'
        ]);

        Camera::factory()->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'name' => 'Back Door Camera'
        ]);

        Camera::factory()->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
            'name' => 'Parking Lot Camera'
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/cameras/search?name=Door');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(2, $responseData); // Front Door and Back Door cameras
    }

    public function testGetCameraWithRelations(): void
    {
        $camera = Camera::factory()->create([
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . "/cameras/{$camera->id}?include=type,object");

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'camera_id',
                'name',
                'type' => [
                    'id',
                    'name'
                ],
                'object' => [
                    'id',
                    'name',
                    'address'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals($this->testCameraType->name, $responseData['type']['name']);
        $this->assertEquals($this->testObject->name, $responseData['object']['name']);
    }

    public function testCreateCameraWithDuplicateCameraId(): void
    {
        Camera::factory()->create([
            'camera_id' => 'DUPLICATE001',
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id
        ]);

        $cameraData = [
            'camera_id' => 'DUPLICATE001',
            'name' => 'Duplicate Camera',
            'object_id' => $this->testObject->id,
            'camera_type_id' => $this->testCameraType->id,
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cameras', $cameraData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['camera_id']);
    }

    public function testUnauthenticatedUserCannotAccessCameras(): void
    {
        $this->shouldAuthenticate = false;

        $response = $this->getJson(self::V1_PREFIX . '/cameras');

        $response->assertStatus(401);
    }
}
