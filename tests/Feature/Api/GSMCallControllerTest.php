<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class GSMCallControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = true;
    }

    public function testGetGSMCallsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/gsm/calls');
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'status' => 401,
            'message' => 'Unauthorized'
        ]);
    }

    public function testGetGSMCallsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/calls');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'call_id',
                    'caller_number',
                    'called_number',
                    'call_type',
                    'start_time',
                    'end_time',
                    'duration',
                    'call_status',
                    'caller_cell_tower',
                    'called_cell_tower',
                    'caller_location' => [
                        'latitude',
                        'longitude',
                        'address'
                    ],
                    'called_location' => [
                        'latitude',
                        'longitude',
                        'address'
                    ],
                    'operator',
                    'imei',
                    'imsi',
                    'call_cost'
                ]
            ],
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetGSMCallsWithPaginationParameters(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/calls?page=1&per_page=25');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $meta = $response->json('meta');
        $this->assertEquals(1, $meta['current_page']);
        $this->assertEquals(25, $meta['per_page']);
    }

    public function testGetGSMCallsWithCallerNumberFilter(): void
    {
        $callerNumber = '994501234567';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/gsm/calls?caller_number={$callerNumber}");
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMCallsWithCalledNumberFilter(): void
    {
        $calledNumber = '994507654321';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/gsm/calls?called_number={$calledNumber}");
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMCallsWithDateRange(): void
    {
        $startDate = '2024-01-15T00:00:00Z';
        $endDate = '2024-01-15T23:59:59Z';
        
        $response = $this->getAuthenticated(
            self::V1_PREFIX . "/gsm/calls?start_date={$startDate}&end_date={$endDate}"
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMCallsWithCallTypeFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/calls?call_type=voice');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMCallsWithDurationFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/calls?duration_min=30');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMCallsWithCellTowerFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/calls?cell_tower_id=TOWER_BAK_001');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMCallsValidatesCallTypeEnum(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/calls?call_type=invalid_type');
        
        // Should either return 422 for validation error or filter out invalid types
        $this->assertContains($response->status(), [200, 422]);
    }

    public function testGetGSMCallByIdRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/gsm/calls/CDR123456789');
        
        $response->assertStatus(401);
    }

    public function testGetGSMCallByIdReturnsSuccessfulResponse(): void
    {
        // Using a mock call ID since we don't have actual data
        $callId = 'CDR123456789';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/gsm/calls/{$callId}");
        
        // This might return 404 if no data exists, which is acceptable for testing
        $this->assertContains($response->status(), [200, 404]);
        
        if ($response->status() === 200) {
            $response->assertJsonStructure([
                'success',
                'status',
                'message',
                'data' => [
                    'call_id',
                    'caller_number',
                    'called_number',
                    'call_type',
                    'start_time',
                    'end_time',
                    'duration',
                    'call_status',
                    'caller_cell_tower',
                    'called_cell_tower',
                    'caller_location' => [
                        'latitude',
                        'longitude',
                        'address',
                        'accuracy'
                    ],
                    'called_location' => [
                        'latitude',
                        'longitude',
                        'address',
                        'accuracy'
                    ],
                    'operator',
                    'imei',
                    'imsi',
                    'call_cost',
                    'call_quality' => [
                        'signal_strength',
                        'call_drops',
                        'audio_quality'
                    ],
                    'related_calls',
                    'metadata'
                ]
            ]);
        }
    }

    public function testGetGSMCallByIdNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/calls/NONEXISTENT_CALL_ID');
        
        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'status' => 404,
            'message' => 'Call record not found'
        ]);
    }

    public function testGetGSMAnalyticsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/gsm/analytics');
        
        $response->assertStatus(401);
    }

    public function testGetGSMAnalyticsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/analytics');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'analysis_period',
                'total_calls',
                'total_sms',
                'total_duration',
                'unique_contacts',
                'call_patterns' => [
                    'peak_hours',
                    'most_active_day',
                    'average_call_duration',
                    'call_frequency_score'
                ],
                'top_contacts' => [
                    '*' => [
                        'phone_number',
                        'call_count',
                        'sms_count',
                        'total_duration',
                        'relationship_strength'
                    ]
                ],
                'location_analysis' => [
                    'most_frequent_location',
                    'location_diversity',
                    'travel_patterns'
                ],
                'behavioral_indicators' => [
                    'communication_style',
                    'activity_level',
                    'anomaly_score',
                    'risk_indicators'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetGSMAnalyticsWithPhoneNumberFilter(): void
    {
        $phoneNumber = '994501234567';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/gsm/analytics?phone_number={$phoneNumber}");
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMAnalyticsWithDateRange(): void
    {
        $startDate = '2024-01-15';
        $endDate = '2024-01-15';
        
        $response = $this->getAuthenticated(
            self::V1_PREFIX . "/gsm/analytics?start_date={$startDate}&end_date={$endDate}"
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMAnalyticsWithAnalysisType(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/analytics?analysis_type=patterns');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetGSMAnalyticsValidatesAnalysisTypeEnum(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/analytics?analysis_type=invalid_type');
        
        // Should either return 422 for validation error or use default analysis type
        $this->assertContains($response->status(), [200, 422]);
    }

    public function testGSMCallDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/calls');
        
        $response->assertOk();
        $data = $response->json('data');
        
        if (is_array($data) && count($data) > 0) {
            $call = $data[0];
            
            // Validate required fields
            $this->assertArrayHasKey('call_id', $call);
            $this->assertArrayHasKey('caller_number', $call);
            $this->assertArrayHasKey('called_number', $call);
            $this->assertArrayHasKey('call_type', $call);
            
            // Validate phone number format (Azerbaijan format)
            if (isset($call['caller_number'])) {
                $this->assertMatchesRegularExpression('/^994\d{9}$/', $call['caller_number']);
            }
            
            // Validate call type enum
            if (isset($call['call_type'])) {
                $this->assertContains($call['call_type'], ['voice', 'sms', 'data', 'mms']);
            }
            
            // Validate call status enum
            if (isset($call['call_status'])) {
                $this->assertContains($call['call_status'], ['completed', 'busy', 'no_answer', 'failed']);
            }
            
            // Validate numeric fields
            if (isset($call['duration'])) {
                $this->assertIsInt($call['duration']);
                $this->assertGreaterThanOrEqual(0, $call['duration']);
            }
            
            if (isset($call['call_cost'])) {
                $this->assertIsFloat($call['call_cost']);
                $this->assertGreaterThanOrEqual(0, $call['call_cost']);
            }
            
            // Validate location structure
            if (isset($call['caller_location'])) {
                $this->assertArrayHasKey('latitude', $call['caller_location']);
                $this->assertArrayHasKey('longitude', $call['caller_location']);
                $this->assertArrayHasKey('address', $call['caller_location']);
                
                // Validate coordinates for Azerbaijan region
                $lat = $call['caller_location']['latitude'];
                $lng = $call['caller_location']['longitude'];
                $this->assertGreaterThanOrEqual(38, $lat);
                $this->assertLessThanOrEqual(42, $lat);
                $this->assertGreaterThanOrEqual(44, $lng);
                $this->assertLessThanOrEqual(51, $lng);
            }
        }
    }

    public function testGSMAnalyticsDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/gsm/analytics');
        
        $response->assertOk();
        $data = $response->json('data');
        
        // Validate numeric fields
        if (isset($data['total_calls'])) {
            $this->assertIsInt($data['total_calls']);
            $this->assertGreaterThanOrEqual(0, $data['total_calls']);
        }
        
        if (isset($data['total_duration'])) {
            $this->assertIsInt($data['total_duration']);
            $this->assertGreaterThanOrEqual(0, $data['total_duration']);
        }
        
        // Validate call patterns structure
        if (isset($data['call_patterns'])) {
            $patterns = $data['call_patterns'];
            
            if (isset($patterns['peak_hours'])) {
                $this->assertIsArray($patterns['peak_hours']);
                foreach ($patterns['peak_hours'] as $hour) {
                    $this->assertIsInt($hour);
                    $this->assertGreaterThanOrEqual(0, $hour);
                    $this->assertLessThanOrEqual(23, $hour);
                }
            }
            
            if (isset($patterns['call_frequency_score'])) {
                $this->assertIsFloat($patterns['call_frequency_score']);
                $this->assertGreaterThanOrEqual(1, $patterns['call_frequency_score']);
                $this->assertLessThanOrEqual(10, $patterns['call_frequency_score']);
            }
        }
        
        // Validate behavioral indicators
        if (isset($data['behavioral_indicators'])) {
            $indicators = $data['behavioral_indicators'];
            
            if (isset($indicators['activity_level'])) {
                $this->assertContains($indicators['activity_level'], ['low', 'medium', 'high', 'very_high']);
            }
            
            if (isset($indicators['anomaly_score'])) {
                $this->assertIsFloat($indicators['anomaly_score']);
                $this->assertGreaterThanOrEqual(0, $indicators['anomaly_score']);
                $this->assertLessThanOrEqual(10, $indicators['anomaly_score']);
            }
        }
        
        // Validate top contacts structure
        if (isset($data['top_contacts']) && is_array($data['top_contacts'])) {
            foreach ($data['top_contacts'] as $contact) {
                $this->assertArrayHasKey('phone_number', $contact);
                $this->assertArrayHasKey('call_count', $contact);
                $this->assertArrayHasKey('relationship_strength', $contact);
                
                if (isset($contact['relationship_strength'])) {
                    $this->assertIsFloat($contact['relationship_strength']);
                    $this->assertGreaterThanOrEqual(1, $contact['relationship_strength']);
                    $this->assertLessThanOrEqual(10, $contact['relationship_strength']);
                }
            }
        }
    }
}
