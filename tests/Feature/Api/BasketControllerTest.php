<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class BasketControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = true;
    }

    public function testGetBasketsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/baskets');
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'status' => 401,
            'message' => 'Unauthorized'
        ]);
    }

    public function testGetBasketsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/baskets');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'description',
                    'type',
                    'is_shared',
                    'items_count',
                    'created_at',
                    'updated_at'
                ]
            ],
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetBasketsWithPaginationParameters(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/baskets?page=1&per_page=10');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $meta = $response->json('meta');
        $this->assertEquals(1, $meta['current_page']);
        $this->assertEquals(10, $meta['per_page']);
    }

    public function testGetBasketsWithTypeFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/baskets?type=investigation');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testCreateBasketRequiresAuthentication(): void
    {
        $basketData = [
            'name' => 'Test Investigation Basket',
            'description' => 'Test basket for investigation',
            'type' => 'investigation'
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/baskets', $basketData);
        
        $response->assertStatus(401);
    }

    public function testCreateBasketWithValidData(): void
    {
        $basketData = [
            'name' => 'Test Investigation Basket',
            'description' => 'Test basket for investigation',
            'type' => 'investigation',
            'is_shared' => false,
            'tags' => ['urgent', 'security']
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'name',
                'description',
                'type',
                'is_shared',
                'created_at'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(201, $responseData['status']);
        $this->assertEquals($basketData['name'], $responseData['data']['name']);
    }

    public function testCreateBasketValidationRequiresName(): void
    {
        $basketData = [
            'description' => 'Test basket without name',
            'type' => 'investigation'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    public function testCreateBasketValidationRequiresType(): void
    {
        $basketData = [
            'name' => 'Test Basket',
            'description' => 'Test basket without type'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['type']);
    }

    public function testCreateBasketValidationInvalidType(): void
    {
        $basketData = [
            'name' => 'Test Basket',
            'description' => 'Test basket with invalid type',
            'type' => 'invalid_type'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['type']);
    }

    public function testGetBasketByIdRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/baskets/1');
        
        $response->assertStatus(401);
    }

    public function testGetBasketByIdReturnsSuccessfulResponse(): void
    {
        // First create a basket
        $basketData = [
            'name' => 'Test Basket',
            'description' => 'Test basket description',
            'type' => 'investigation'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        $basketId = $createResponse->json('data.id');

        $response = $this->getAuthenticated(self::V1_PREFIX . "/baskets/{$basketId}");
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'name',
                'description',
                'type',
                'is_shared',
                'tags',
                'items',
                'items_count',
                'created_at',
                'updated_at'
            ]
        ]);
    }

    public function testGetBasketByIdNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/baskets/99999');
        
        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'status' => 404,
            'message' => 'Basket not found'
        ]);
    }

    public function testUpdateBasketRequiresAuthentication(): void
    {
        $updateData = [
            'name' => 'Updated Basket Name'
        ];

        $response = $this->unauthenticatedRequest('PUT', self::V1_PREFIX . '/baskets/1', $updateData);
        
        $response->assertStatus(401);
    }

    public function testUpdateBasketWithValidData(): void
    {
        // First create a basket
        $basketData = [
            'name' => 'Original Basket',
            'description' => 'Original description',
            'type' => 'investigation'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        $basketId = $createResponse->json('data.id');

        $updateData = [
            'name' => 'Updated Basket Name',
            'description' => 'Updated description',
            'is_shared' => true
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . "/baskets/{$basketId}", $updateData);
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'name',
                'description',
                'is_shared',
                'updated_at'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertEquals($updateData['name'], $responseData['data']['name']);
        $this->assertEquals($updateData['description'], $responseData['data']['description']);
    }

    public function testUpdateBasketNotFound(): void
    {
        $updateData = [
            'name' => 'Updated Name'
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . '/baskets/99999', $updateData);
        
        $response->assertStatus(404);
    }

    public function testDeleteBasketRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('DELETE', self::V1_PREFIX . '/baskets/1');
        
        $response->assertStatus(401);
    }

    public function testDeleteBasketSuccessfully(): void
    {
        // First create a basket
        $basketData = [
            'name' => 'Basket to Delete',
            'description' => 'This basket will be deleted',
            'type' => 'investigation'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        $basketId = $createResponse->json('data.id');

        $response = $this->deleteAuthenticated(self::V1_PREFIX . "/baskets/{$basketId}");
        
        $response->assertOk();
        $response->assertJson([
            'success' => true,
            'status' => 200,
            'message' => 'Basket deleted successfully'
        ]);
    }

    public function testDeleteBasketNotFound(): void
    {
        $response = $this->deleteAuthenticated(self::V1_PREFIX . '/baskets/99999');
        
        $response->assertStatus(404);
    }

    public function testAddItemToBasketRequiresAuthentication(): void
    {
        $itemData = [
            'item_type' => 'person',
            'item_id' => '1234567'
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/baskets/1/items', $itemData);
        
        $response->assertStatus(401);
    }

    public function testAddItemToBasketWithValidData(): void
    {
        // First create a basket
        $basketData = [
            'name' => 'Test Basket',
            'description' => 'Test basket for items',
            'type' => 'investigation'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        $basketId = $createResponse->json('data.id');

        $itemData = [
            'item_type' => 'person',
            'item_id' => '1234567',
            'notes' => 'Suspicious person of interest'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . "/baskets/{$basketId}/items", $itemData);
        
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'item_type',
                'item_id',
                'notes',
                'added_at'
            ]
        ]);
    }

    public function testAddItemToBasketValidationRequiresItemType(): void
    {
        $basketData = [
            'name' => 'Test Basket',
            'type' => 'investigation'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        $basketId = $createResponse->json('data.id');

        $itemData = [
            'item_id' => '1234567'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . "/baskets/{$basketId}/items", $itemData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['item_type']);
    }

    public function testRemoveItemFromBasketRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('DELETE', self::V1_PREFIX . '/baskets/1/items/1');
        
        $response->assertStatus(401);
    }

    public function testRemoveItemFromBasketSuccessfully(): void
    {
        // First create a basket and add an item
        $basketData = [
            'name' => 'Test Basket',
            'type' => 'investigation'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        $basketId = $createResponse->json('data.id');

        $itemData = [
            'item_type' => 'person',
            'item_id' => '1234567'
        ];

        $addItemResponse = $this->postAuthenticated(self::V1_PREFIX . "/baskets/{$basketId}/items", $itemData);
        $itemId = $addItemResponse->json('data.id');

        $response = $this->deleteAuthenticated(self::V1_PREFIX . "/baskets/{$basketId}/items/{$itemId}");
        
        $response->assertOk();
        $response->assertJson([
            'success' => true,
            'status' => 200,
            'message' => 'Item removed from basket successfully'
        ]);
    }

    public function testRemoveItemFromBasketNotFound(): void
    {
        $basketData = [
            'name' => 'Test Basket',
            'type' => 'investigation'
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/baskets', $basketData);
        $basketId = $createResponse->json('data.id');

        $response = $this->deleteAuthenticated(self::V1_PREFIX . "/baskets/{$basketId}/items/99999");
        
        $response->assertStatus(404);
    }
}
