<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class DPIControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = true;
    }

    public function testGetDPIAnalyticsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/dpi/analytics');
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'status' => 401,
            'message' => 'Unauthorized'
        ]);
    }

    public function testGetDPIAnalyticsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/analytics');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'period',
                'total_packets',
                'total_bytes',
                'unique_sources',
                'unique_destinations',
                'protocol_breakdown' => [
                    '*' => [
                        'packets',
                        'bytes',
                        'percentage'
                    ]
                ],
                'top_sources' => [
                    '*' => [
                        'ip_address',
                        'packets',
                        'bytes',
                        'connections'
                    ]
                ],
                'timeline' => [
                    '*' => [
                        'timestamp',
                        'packets',
                        'bytes'
                    ]
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetDPIAnalyticsWithDateRange(): void
    {
        $startDate = '2024-01-15T00:00:00Z';
        $endDate = '2024-01-15T23:59:59Z';
        
        $response = $this->getAuthenticated(
            self::V1_PREFIX . "/dpi/analytics?start_date={$startDate}&end_date={$endDate}"
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetDPIAnalyticsWithProtocolFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/analytics?protocol=HTTPS');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetDPIAnalyticsWithSourceIPFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/analytics?source_ip=*************');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetDPIAnalyticsWithAggregation(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/analytics?aggregation=hour');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetDPIAnalyticsValidatesProtocolEnum(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/analytics?protocol=INVALID_PROTOCOL');
        
        // Should either return 422 for validation error or filter out invalid protocols
        $this->assertContains($response->status(), [200, 422]);
    }

    public function testGetTrafficFlowsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/dpi/traffic-flows');
        
        $response->assertStatus(401);
    }

    public function testGetTrafficFlowsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/traffic-flows');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'flow_id',
                    'source_ip',
                    'source_port',
                    'destination_ip',
                    'destination_port',
                    'protocol',
                    'start_time',
                    'duration',
                    'packets_sent',
                    'bytes_sent',
                    'bytes_received',
                    'application',
                    'risk_score'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetTrafficFlowsWithPagination(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/traffic-flows?page=1&per_page=25');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
    }

    public function testGetTrafficFlowsWithProtocolFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/traffic-flows?protocol=HTTPS');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetSecurityEventsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/dpi/security-events');
        
        $response->assertStatus(401);
    }

    public function testGetSecurityEventsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/security-events');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'event_id',
                    'event_type',
                    'severity',
                    'title',
                    'description',
                    'source_ip',
                    'destination_ip',
                    'protocol',
                    'timestamp',
                    'confidence',
                    'risk_score',
                    'status'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetSecurityEventsWithSeverityFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/security-events?severity=high');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetSecurityEventsWithEventTypeFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/security-events?event_type=malware');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetSecurityEventsWithDateRange(): void
    {
        $startDate = '2024-01-15T00:00:00Z';
        $endDate = '2024-01-15T23:59:59Z';
        
        $response = $this->getAuthenticated(
            self::V1_PREFIX . "/dpi/security-events?start_date={$startDate}&end_date={$endDate}"
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetSecurityEventsValidatesSeverityEnum(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/security-events?severity=invalid_severity');
        
        // Should either return 422 for validation error or filter out invalid severities
        $this->assertContains($response->status(), [200, 422]);
    }

    public function testGetSecurityEventsValidatesEventTypeEnum(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/security-events?event_type=invalid_type');
        
        // Should either return 422 for validation error or filter out invalid types
        $this->assertContains($response->status(), [200, 422]);
    }

    public function testGetBandwidthUsageRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/dpi/bandwidth-usage');
        
        $response->assertStatus(401);
    }

    public function testGetBandwidthUsageReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/bandwidth-usage');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'total_bandwidth',
                'peak_usage',
                'average_usage',
                'usage_by_protocol' => [
                    'HTTPS',
                    'HTTP',
                    'FTP',
                    'OTHER'
                ],
                'timeline' => [
                    '*' => [
                        'timestamp',
                        'bytes_per_second'
                    ]
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetBandwidthUsageWithPeriodFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/bandwidth-usage?period=day');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetBandwidthUsageValidatesPeriodEnum(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/bandwidth-usage?period=invalid_period');
        
        // Should either return 422 for validation error or use default period
        $this->assertContains($response->status(), [200, 422]);
    }

    public function testDPIAnalyticsDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/analytics');
        
        $response->assertOk();
        $data = $response->json('data');
        
        // Validate numeric fields
        $this->assertIsInt($data['total_packets']);
        $this->assertIsInt($data['total_bytes']);
        $this->assertIsInt($data['unique_sources']);
        $this->assertIsInt($data['unique_destinations']);
        
        // Validate protocol breakdown structure
        if (isset($data['protocol_breakdown'])) {
            foreach ($data['protocol_breakdown'] as $protocol => $stats) {
                $this->assertIsArray($stats);
                $this->assertArrayHasKey('packets', $stats);
                $this->assertArrayHasKey('bytes', $stats);
                $this->assertArrayHasKey('percentage', $stats);
                $this->assertIsNumeric($stats['percentage']);
            }
        }
        
        // Validate timeline structure
        if (isset($data['timeline']) && is_array($data['timeline'])) {
            foreach ($data['timeline'] as $timePoint) {
                $this->assertArrayHasKey('timestamp', $timePoint);
                $this->assertArrayHasKey('packets', $timePoint);
                $this->assertArrayHasKey('bytes', $timePoint);
            }
        }
    }

    public function testTrafficFlowsDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/traffic-flows');
        
        $response->assertOk();
        $data = $response->json('data');
        
        if (is_array($data) && count($data) > 0) {
            $flow = $data[0];
            
            // Validate required fields
            $this->assertArrayHasKey('flow_id', $flow);
            $this->assertArrayHasKey('source_ip', $flow);
            $this->assertArrayHasKey('destination_ip', $flow);
            $this->assertArrayHasKey('protocol', $flow);
            
            // Validate IP address format (basic check)
            if (isset($flow['source_ip'])) {
                $this->assertMatchesRegularExpression('/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/', $flow['source_ip']);
            }
            
            // Validate numeric fields
            if (isset($flow['source_port'])) {
                $this->assertIsInt($flow['source_port']);
                $this->assertGreaterThanOrEqual(1, $flow['source_port']);
                $this->assertLessThanOrEqual(65535, $flow['source_port']);
            }
            
            if (isset($flow['risk_score'])) {
                $this->assertIsInt($flow['risk_score']);
                $this->assertGreaterThanOrEqual(1, $flow['risk_score']);
                $this->assertLessThanOrEqual(10, $flow['risk_score']);
            }
        }
    }

    public function testSecurityEventsDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/dpi/security-events');
        
        $response->assertOk();
        $data = $response->json('data');
        
        if (is_array($data) && count($data) > 0) {
            $event = $data[0];
            
            // Validate required fields
            $this->assertArrayHasKey('event_id', $event);
            $this->assertArrayHasKey('event_type', $event);
            $this->assertArrayHasKey('severity', $event);
            $this->assertArrayHasKey('timestamp', $event);
            
            // Validate enum values
            if (isset($event['severity'])) {
                $this->assertContains($event['severity'], ['low', 'medium', 'high', 'critical']);
            }
            
            if (isset($event['status'])) {
                $this->assertContains($event['status'], ['new', 'investigating', 'resolved', 'false_positive']);
            }
            
            // Validate numeric fields
            if (isset($event['confidence'])) {
                $this->assertIsFloat($event['confidence']);
                $this->assertGreaterThanOrEqual(0, $event['confidence']);
                $this->assertLessThanOrEqual(1, $event['confidence']);
            }
            
            if (isset($event['risk_score'])) {
                $this->assertIsInt($event['risk_score']);
                $this->assertGreaterThanOrEqual(1, $event['risk_score']);
                $this->assertLessThanOrEqual(10, $event['risk_score']);
            }
        }
    }
}
