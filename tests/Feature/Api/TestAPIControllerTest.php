<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class TestAPIControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        // Note: Health check endpoint should not require authentication
        $this->shouldAuthenticate = false;
    }

    public function testHealthCheckReturnsSuccessfulResponse(): void
    {
        $response = $this->get(self::V1_PREFIX . '/test/health');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'api_version',
                'timestamp',
                'uptime',
                'environment',
                'services' => [
                    'database',
                    'redis',
                    'elasticsearch',
                    'storage'
                ],
                'performance' => [
                    'memory_usage',
                    'cpu_usage',
                    'disk_usage',
                    'response_time'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
        $this->assertEquals('API is healthy', $responseData['message']);
    }

    public function testHealthCheckDataStructureValidation(): void
    {
        $response = $this->get(self::V1_PREFIX . '/test/health');
        
        $response->assertOk();
        $data = $response->json('data');
        
        // Validate required fields
        $this->assertArrayHasKey('api_version', $data);
        $this->assertArrayHasKey('timestamp', $data);
        $this->assertArrayHasKey('environment', $data);
        
        // Validate timestamp format
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/', $data['timestamp']);
        
        // Validate uptime is numeric
        if (isset($data['uptime'])) {
            $this->assertIsInt($data['uptime']);
            $this->assertGreaterThanOrEqual(0, $data['uptime']);
        }
        
        // Validate services status
        if (isset($data['services'])) {
            $validStatuses = ['healthy', 'degraded', 'unhealthy'];
            foreach ($data['services'] as $service => $status) {
                $this->assertContains($status, $validStatuses);
            }
        }
        
        // Validate performance metrics
        if (isset($data['performance'])) {
            $performance = $data['performance'];
            
            if (isset($performance['response_time'])) {
                $this->assertIsFloat($performance['response_time']);
                $this->assertGreaterThanOrEqual(0, $performance['response_time']);
            }
        }
    }

    public function testHealthCheckWhenUnhealthy(): void
    {
        // This test would require mocking unhealthy services
        // For now, we'll just verify the endpoint exists and returns valid JSON
        $response = $this->get(self::V1_PREFIX . '/test/health');
        
        // Should return either 200 (healthy) or 503 (unhealthy)
        $this->assertContains($response->status(), [200, 503]);
        
        if ($response->status() === 503) {
            $response->assertJsonStructure([
                'success',
                'status',
                'message',
                'data' => [
                    'issues'
                ]
            ]);
            
            $responseData = $response->json();
            $this->assertFalse($responseData['success']);
            $this->assertEquals(503, $responseData['status']);
        }
    }

    public function testEchoTestRequiresAuthentication(): void
    {
        $this->shouldAuthenticate = true;
        
        $echoData = [
            'message' => 'Hello, Beein API!',
            'timestamp' => '2024-01-15T10:30:00Z'
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/test/echo', $echoData);
        
        $response->assertStatus(401);
    }

    public function testEchoTestReturnsSuccessfulResponse(): void
    {
        $this->shouldAuthenticate = true;
        
        $echoData = [
            'message' => 'Hello, Beein API!',
            'data' => [
                'test_field' => 'test_value',
                'number' => 123
            ],
            'timestamp' => '2024-01-15T10:30:00Z'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/test/echo', $echoData);
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'echoed_data',
                'request_info' => [
                    'method',
                    'url',
                    'user_agent',
                    'ip_address',
                    'content_type'
                ],
                'server_timestamp',
                'processing_time'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
        
        // Verify echoed data matches input
        $this->assertEquals($echoData, $responseData['data']['echoed_data']);
    }

    public function testEchoTestRequestInfoValidation(): void
    {
        $this->shouldAuthenticate = true;
        
        $echoData = ['message' => 'Test'];
        $response = $this->postAuthenticated(self::V1_PREFIX . '/test/echo', $echoData);
        
        $response->assertOk();
        $requestInfo = $response->json('data.request_info');
        
        $this->assertEquals('POST', $requestInfo['method']);
        $this->assertStringContains('/api/v1/test/echo', $requestInfo['url']);
        $this->assertEquals('application/json', $requestInfo['content_type']);
        $this->assertIsFloat($response->json('data.processing_time'));
    }

    public function testPerformanceTestRequiresAuthentication(): void
    {
        $this->shouldAuthenticate = true;
        
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/test/performance');
        
        $response->assertStatus(401);
    }

    public function testPerformanceTestReturnsSuccessfulResponse(): void
    {
        $this->shouldAuthenticate = true;
        
        $response = $this->getAuthenticated(self::V1_PREFIX . '/test/performance');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'test_config' => [
                    'iterations',
                    'delay',
                    'memory_test',
                    'database_test'
                ],
                'results' => [
                    'total_time',
                    'average_iteration_time',
                    'min_iteration_time',
                    'max_iteration_time',
                    'iterations_per_second',
                    'memory_usage' => [
                        'start_memory',
                        'end_memory',
                        'peak_memory',
                        'memory_increase'
                    ]
                ],
                'system_info' => [
                    'php_version',
                    'laravel_version',
                    'server_load',
                    'memory_limit'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testPerformanceTestWithParameters(): void
    {
        $this->shouldAuthenticate = true;
        
        $response = $this->getAuthenticated(
            self::V1_PREFIX . '/test/performance?iterations=50&delay=5&memory_test=true&database_test=true'
        );
        
        $response->assertOk();
        $testConfig = $response->json('data.test_config');
        
        $this->assertEquals(50, $testConfig['iterations']);
        $this->assertEquals(5, $testConfig['delay']);
        $this->assertTrue($testConfig['memory_test']);
        $this->assertTrue($testConfig['database_test']);
    }

    public function testPerformanceTestValidatesParameters(): void
    {
        $this->shouldAuthenticate = true;
        
        // Test with invalid iterations (too high)
        $response = $this->getAuthenticated(self::V1_PREFIX . '/test/performance?iterations=20000');
        
        $response->assertStatus(400);
        $response->assertJsonValidationErrors(['iterations']);
    }

    public function testPerformanceTestDataStructureValidation(): void
    {
        $this->shouldAuthenticate = true;
        
        $response = $this->getAuthenticated(self::V1_PREFIX . '/test/performance');
        
        $response->assertOk();
        $results = $response->json('data.results');
        
        // Validate timing metrics
        $this->assertIsFloat($results['total_time']);
        $this->assertIsFloat($results['average_iteration_time']);
        $this->assertIsFloat($results['iterations_per_second']);
        $this->assertGreaterThan(0, $results['total_time']);
        $this->assertGreaterThan(0, $results['iterations_per_second']);
        
        // Validate memory usage
        if (isset($results['memory_usage'])) {
            $memory = $results['memory_usage'];
            $this->assertIsInt($memory['start_memory']);
            $this->assertIsInt($memory['end_memory']);
            $this->assertIsInt($memory['peak_memory']);
            $this->assertGreaterThanOrEqual(0, $memory['memory_increase']);
        }
        
        // Validate system info
        $systemInfo = $response->json('data.system_info');
        $this->assertArrayHasKey('php_version', $systemInfo);
        $this->assertArrayHasKey('laravel_version', $systemInfo);
        $this->assertMatchesRegularExpression('/^\d+\.\d+/', $systemInfo['php_version']);
    }

    public function testErrorTestRequiresAuthentication(): void
    {
        $this->shouldAuthenticate = true;
        
        $errorData = [
            'error_type' => 'validation',
            'error_code' => 422
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/test/error', $errorData);
        
        $response->assertStatus(401);
    }

    public function testErrorTestValidationError(): void
    {
        $this->shouldAuthenticate = true;
        
        $errorData = [
            'error_type' => 'validation',
            'error_code' => 422,
            'error_message' => 'Custom validation error for testing'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/test/error', $errorData);
        
        $response->assertStatus(422);
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'errors',
            'simulation_info' => [
                'simulated',
                'error_type',
                'timestamp'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertFalse($responseData['success']);
        $this->assertEquals(422, $responseData['status']);
        $this->assertTrue($responseData['simulation_info']['simulated']);
    }

    public function testErrorTestServerError(): void
    {
        $this->shouldAuthenticate = true;
        
        $errorData = [
            'error_type' => 'server_error',
            'error_code' => 500,
            'error_message' => 'Custom server error for testing'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/test/error', $errorData);
        
        $response->assertStatus(500);
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'simulation_info' => [
                'simulated',
                'error_type',
                'timestamp'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertFalse($responseData['success']);
        $this->assertEquals(500, $responseData['status']);
    }

    public function testErrorTestRequiresErrorType(): void
    {
        $this->shouldAuthenticate = true;
        
        $errorData = [
            'error_code' => 422
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/test/error', $errorData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['error_type']);
    }

    public function testDatabaseTestRequiresAuthentication(): void
    {
        $this->shouldAuthenticate = true;
        
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/test/database');
        
        $response->assertStatus(401);
    }

    public function testDatabaseTestReturnsSuccessfulResponse(): void
    {
        $this->shouldAuthenticate = true;
        
        $response = $this->getAuthenticated(self::V1_PREFIX . '/test/database');
        
        // Should return either 200 (success) or 503 (database issues)
        $this->assertContains($response->status(), [200, 503]);
        
        if ($response->status() === 200) {
            $response->assertJsonStructure([
                'success',
                'status',
                'message',
                'data' => [
                    'test_type',
                    'database_info' => [
                        'driver',
                        'version',
                        'host',
                        'database'
                    ],
                    'connection_test' => [
                        'status',
                        'connection_time',
                        'ping_time'
                    ],
                    'performance_test' => [
                        'simple_query_time',
                        'complex_query_time',
                        'insert_test_time',
                        'update_test_time'
                    ],
                    'statistics' => [
                        'active_connections',
                        'max_connections',
                        'uptime'
                    ]
                ]
            ]);
            
            $responseData = $response->json();
            $this->assertTrue($responseData['success']);
            $this->assertEquals(200, $responseData['status']);
        }
    }

    public function testDatabaseTestWithTestType(): void
    {
        $this->shouldAuthenticate = true;
        
        $response = $this->getAuthenticated(self::V1_PREFIX . '/test/database?test_type=performance');
        
        $this->assertContains($response->status(), [200, 503]);
        
        if ($response->status() === 200) {
            $testType = $response->json('data.test_type');
            $this->assertEquals('performance', $testType);
        }
    }

    public function testDatabaseTestDataStructureValidation(): void
    {
        $this->shouldAuthenticate = true;
        
        $response = $this->getAuthenticated(self::V1_PREFIX . '/test/database');
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            
            // Validate connection test
            if (isset($data['connection_test'])) {
                $connectionTest = $data['connection_test'];
                $this->assertEquals('success', $connectionTest['status']);
                $this->assertIsFloat($connectionTest['connection_time']);
                $this->assertIsFloat($connectionTest['ping_time']);
                $this->assertGreaterThan(0, $connectionTest['connection_time']);
            }
            
            // Validate performance test
            if (isset($data['performance_test'])) {
                $performanceTest = $data['performance_test'];
                foreach (['simple_query_time', 'complex_query_time', 'insert_test_time', 'update_test_time'] as $metric) {
                    if (isset($performanceTest[$metric])) {
                        $this->assertIsFloat($performanceTest[$metric]);
                        $this->assertGreaterThan(0, $performanceTest[$metric]);
                    }
                }
            }
            
            // Validate statistics
            if (isset($data['statistics'])) {
                $stats = $data['statistics'];
                if (isset($stats['active_connections'])) {
                    $this->assertIsInt($stats['active_connections']);
                    $this->assertGreaterThanOrEqual(0, $stats['active_connections']);
                }
                if (isset($stats['uptime'])) {
                    $this->assertIsInt($stats['uptime']);
                    $this->assertGreaterThanOrEqual(0, $stats['uptime']);
                }
            }
        }
    }
}
