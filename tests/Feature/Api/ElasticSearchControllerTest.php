<?php

namespace Tests\Feature\Api;

use App\Services\ElasticService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class ElasticSearchControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock ElasticService to avoid actual Elasticsearch calls
        $this->mockElasticService = Mockery::mock(ElasticService::class);
        $this->app->instance(ElasticService::class, $this->mockElasticService);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    public function testSearchPersons(): void
    {
        $searchData = [
            'query' => 'John Doe',
            'size' => 10,
            'from' => 0
        ];

        $mockResponse = [
            'hits' => [
                'total' => ['value' => 1],
                'hits' => [
                    [
                        '_source' => [
                            'name' => 'John',
                            'surname' => '<PERSON><PERSON>',
                            'pin' => 'PIN123',
                            'birthdate' => '1990-01-01'
                        ]
                    ]
                ]
            ]
        ];

        $this->mockElasticService
            ->shouldReceive('search')
            ->with('persons', Mockery::type('array'))
            ->once()
            ->andReturn($mockResponse);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/persons', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'total',
                'results' => [
                    '*' => [
                        'name',
                        'surname',
                        'pin'
                    ]
                ]
            ]
        ]);

        $responseData = $response->json();
        $this->assertEquals(1, $responseData['data']['total']);
        $this->assertEquals('John', $responseData['data']['results'][0]['name']);
    }

    public function testSearchPersonsValidationErrors(): void
    {
        $invalidData = [
            'size' => 1001, // Exceeds maximum
            'from' => -1    // Negative value
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/persons', $invalidData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['size', 'from']);
    }

    public function testSearchBorderCrossings(): void
    {
        $searchData = [
            'pin' => 'PIN123',
            'date_from' => '2024-01-01',
            'date_to' => '2024-12-31'
        ];

        $mockResponse = [
            'hits' => [
                'total' => ['value' => 2],
                'hits' => [
                    [
                        '_source' => [
                            'pin' => 'PIN123',
                            'crossing_date' => '2024-06-15',
                            'border_point' => 'Airport',
                            'direction' => 'entry'
                        ]
                    ],
                    [
                        '_source' => [
                            'pin' => 'PIN123',
                            'crossing_date' => '2024-07-20',
                            'border_point' => 'Airport',
                            'direction' => 'exit'
                        ]
                    ]
                ]
            ]
        ];

        $this->mockElasticService
            ->shouldReceive('search')
            ->with('border_crossings', Mockery::type('array'))
            ->once()
            ->andReturn($mockResponse);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/border-crossings', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'total',
                'results' => [
                    '*' => [
                        'pin',
                        'crossing_date',
                        'border_point',
                        'direction'
                    ]
                ]
            ]
        ]);

        $responseData = $response->json();
        $this->assertEquals(2, $responseData['data']['total']);
        $this->assertEquals('entry', $responseData['data']['results'][0]['direction']);
        $this->assertEquals('exit', $responseData['data']['results'][1]['direction']);
    }

    public function testSearchVoens(): void
    {
        $searchData = [
            'voen' => '**********',
            'company_name' => 'Test Company'
        ];

        $mockResponse = [
            'hits' => [
                'total' => ['value' => 1],
                'hits' => [
                    [
                        '_source' => [
                            'voen' => '**********',
                            'company_name' => 'Test Company LLC',
                            'address' => 'Test Address',
                            'registration_date' => '2020-01-01'
                        ]
                    ]
                ]
            ]
        ];

        $this->mockElasticService
            ->shouldReceive('search')
            ->with('voens', Mockery::type('array'))
            ->once()
            ->andReturn($mockResponse);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/voens', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'total',
                'results' => [
                    '*' => [
                        'voen',
                        'company_name',
                        'address'
                    ]
                ]
            ]
        ]);

        $responseData = $response->json();
        $this->assertEquals('**********', $responseData['data']['results'][0]['voen']);
        $this->assertEquals('Test Company LLC', $responseData['data']['results'][0]['company_name']);
    }

    public function testSearchSocialMediaAccounts(): void
    {
        $searchData = [
            'username' => 'testuser',
            'platform' => 'instagram',
            'size' => 20
        ];

        $mockResponse = [
            'hits' => [
                'total' => ['value' => 3],
                'hits' => [
                    [
                        '_source' => [
                            'username' => 'testuser123',
                            'platform' => 'instagram',
                            'followers_count' => 1500,
                            'posts_count' => 250
                        ]
                    ]
                ]
            ]
        ];

        $this->mockElasticService
            ->shouldReceive('search')
            ->with('social_accounts', Mockery::type('array'))
            ->once()
            ->andReturn($mockResponse);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/social-accounts', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'total',
                'results' => [
                    '*' => [
                        'username',
                        'platform',
                        'followers_count',
                        'posts_count'
                    ]
                ]
            ]
        ]);
    }

    public function testAdvancedSearch(): void
    {
        $searchData = [
            'indices' => ['persons', 'border_crossings'],
            'query' => [
                'bool' => [
                    'must' => [
                        ['match' => ['name' => 'John']]
                    ]
                ]
            ],
            'size' => 50
        ];

        $mockResponse = [
            'hits' => [
                'total' => ['value' => 5],
                'hits' => [
                    [
                        '_index' => 'persons',
                        '_source' => [
                            'name' => 'John',
                            'surname' => 'Doe'
                        ]
                    ]
                ]
            ]
        ];

        $this->mockElasticService
            ->shouldReceive('search')
            ->with(Mockery::type('array'), Mockery::type('array'))
            ->once()
            ->andReturn($mockResponse);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/advanced-search', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'total',
                'results'
            ]
        ]);
    }

    public function testSearchWithAggregations(): void
    {
        $searchData = [
            'query' => 'test',
            'aggregations' => [
                'by_gender' => [
                    'terms' => ['field' => 'gender']
                ]
            ]
        ];

        $mockResponse = [
            'hits' => [
                'total' => ['value' => 100],
                'hits' => []
            ],
            'aggregations' => [
                'by_gender' => [
                    'buckets' => [
                        ['key' => 'male', 'doc_count' => 60],
                        ['key' => 'female', 'doc_count' => 40]
                    ]
                ]
            ]
        ];

        $this->mockElasticService
            ->shouldReceive('search')
            ->with('persons', Mockery::type('array'))
            ->once()
            ->andReturn($mockResponse);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/persons', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'total',
                'results',
                'aggregations' => [
                    'by_gender' => [
                        'buckets'
                    ]
                ]
            ]
        ]);

        $responseData = $response->json();
        $this->assertEquals(60, $responseData['data']['aggregations']['by_gender']['buckets'][0]['doc_count']);
    }

    public function testSearchWithScrolling(): void
    {
        $searchData = [
            'query' => 'test',
            'scroll' => true,
            'scroll_size' => 1000
        ];

        $mockResponse = [
            'total' => 5000,
            'result' => [],
            'scrollId' => 'scroll123'
        ];

        $this->mockElasticService
            ->shouldReceive('searchScrollId')
            ->with('persons', Mockery::type('array'))
            ->once()
            ->andReturn($mockResponse);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/persons', $searchData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'total',
                'scroll_id',
                'results'
            ]
        ]);

        $responseData = $response->json();
        $this->assertEquals('scroll123', $responseData['data']['scroll_id']);
        $this->assertEquals(5000, $responseData['data']['total']);
    }

    public function testContinueScrolling(): void
    {
        $scrollData = [
            'scroll_id' => 'scroll123'
        ];

        $mockResponse = [
            [
                '_source' => ['name' => 'John', 'surname' => 'Doe']
            ]
        ];

        $this->mockElasticService
            ->shouldReceive('paginateViaScrollApi')
            ->with('scroll123')
            ->once()
            ->andReturn($mockResponse);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/scroll', $scrollData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'results'
            ]
        ]);
    }

    public function testElasticsearchConnectionError(): void
    {
        $searchData = [
            'query' => 'test'
        ];

        $this->mockElasticService
            ->shouldReceive('search')
            ->with('persons', Mockery::type('array'))
            ->once()
            ->andReturn([]);

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/persons', $searchData);

        $response->assertOk();
        $response->assertJson([
            'data' => [
                'total' => 0,
                'results' => []
            ]
        ]);
    }

    public function testUnauthenticatedUserCannotAccessElasticSearch(): void
    {
        $this->shouldAuthenticate = false;

        $response = $this->postJson(self::V1_PREFIX . '/elastic/search/persons', ['query' => 'test']);

        $response->assertStatus(401);
    }

    public function testSearchWithInvalidIndex(): void
    {
        $searchData = [
            'query' => 'test'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/elastic/search/invalid-index', $searchData);

        $response->assertStatus(404);
    }
}
