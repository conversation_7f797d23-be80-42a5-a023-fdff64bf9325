<?php

namespace Tests\Feature\Api;

use App\Models\Blacklist;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class BlacklistControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testGetBlacklistsList(): void
    {
        // Create test blacklist entries
        Blacklist::factory()->count(3)->create();

        $response = $this->getAuthenticated(self::V1_PREFIX . '/blacklists');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'surname',
                    'pin',
                    'status',
                    'created_at',
                    'updated_at'
                ]
            ]
        ]);
    }

    public function testGetBlacklistsListWithPagination(): void
    {
        Blacklist::factory()->count(25)->create();

        $response = $this->getAuthenticated(self::V1_PREFIX . '/blacklists?page=1&per_page=10');

        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'links',
            'meta' => [
                'current_page',
                'per_page',
                'total'
            ]
        ]);

        $responseData = $response->json();
        $this->assertCount(10, $responseData['data']);
    }

    public function testGetBlacklistById(): void
    {
        $blacklist = Blacklist::factory()->create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'BL123',
            'status' => 1
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . "/blacklists/{$blacklist->id}");

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'surname',
                'pin',
                'status',
                'created_at',
                'updated_at'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('John', $responseData['name']);
        $this->assertEquals('Doe', $responseData['surname']);
        $this->assertEquals('BL123', $responseData['pin']);
    }

    public function testGetBlacklistByIdNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/blacklists/999999');

        $response->assertStatus(404);
    }

    public function testCreateBlacklist(): void
    {
        Storage::fake('public');

        $blacklistData = [
            'name' => 'Jane',
            'surname' => 'Smith',
            'father_name' => 'Robert',
            'pin' => 'BL456',
            'document_number' => 'AA123456',
            'birthdate' => '1990-01-01',
            'gender' => 'female',
            'note' => 'Test blacklist entry',
            'status' => 1,
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/blacklists', $blacklistData);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'surname',
                'father_name',
                'pin',
                'document_number',
                'birthdate',
                'gender',
                'note',
                'status'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Jane', $responseData['name']);
        $this->assertEquals('Smith', $responseData['surname']);
        $this->assertEquals('BL456', $responseData['pin']);

        // Verify blacklist was created in database
        $this->assertDatabaseHas('blacklists', [
            'name' => 'Jane',
            'surname' => 'Smith',
            'pin' => 'BL456'
        ]);
    }

    public function testCreateBlacklistWithPhoto(): void
    {
        Storage::fake('public');

        $photo = UploadedFile::fake()->image('blacklist.jpg');

        $blacklistData = [
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'BL789',
            'photo' => $photo,
            'status' => 1,
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/blacklists', $blacklistData);

        $response->assertStatus(201);

        $responseData = $response->json('data');
        $this->assertNotNull($responseData['photo']);

        // Verify file was stored
        Storage::disk('public')->assertExists($responseData['photo']);
    }

    public function testCreateBlacklistValidationErrors(): void
    {
        $invalidData = [
            'surname' => 'Smith',
            // Missing required name and pin
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/blacklists', $invalidData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name', 'pin']);
    }

    public function testUpdateBlacklist(): void
    {
        $blacklist = Blacklist::factory()->create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'BL123',
            'status' => 0
        ]);

        $updateData = [
            'name' => 'Johnny',
            'surname' => 'Updated',
            'status' => 1,
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . "/blacklists/{$blacklist->id}", $updateData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'surname',
                'pin',
                'status'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Johnny', $responseData['name']);
        $this->assertEquals('Updated', $responseData['surname']);
        $this->assertEquals(1, $responseData['status']);

        // Verify blacklist was updated in database
        $this->assertDatabaseHas('blacklists', [
            'id' => $blacklist->id,
            'name' => 'Johnny',
            'surname' => 'Updated',
            'status' => 1
        ]);
    }

    public function testUpdateBlacklistNotFound(): void
    {
        $updateData = [
            'name' => 'Updated Name',
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . '/blacklists/999999', $updateData);

        $response->assertStatus(404);
    }

    public function testDeleteBlacklist(): void
    {
        $blacklist = Blacklist::factory()->create();

        $response = $this->deleteAuthenticated(self::V1_PREFIX . "/blacklists/{$blacklist->id}");

        $response->assertOk();

        // Verify blacklist was deleted from database
        $this->assertDatabaseMissing('blacklists', [
            'id' => $blacklist->id
        ]);
    }

    public function testDeleteBlacklistNotFound(): void
    {
        $response = $this->deleteAuthenticated(self::V1_PREFIX . '/blacklists/999999');

        $response->assertStatus(404);
    }

    public function testSearchBlacklistsByName(): void
    {
        Blacklist::factory()->create(['name' => 'John', 'surname' => 'Doe']);
        Blacklist::factory()->create(['name' => 'Jane', 'surname' => 'Smith']);
        Blacklist::factory()->create(['name' => 'Bob', 'surname' => 'Johnson']);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/blacklists/search?name=John');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'surname',
                    'pin'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData); // John Doe and Bob Johnson
    }

    public function testSearchBlacklistsByPin(): void
    {
        Blacklist::factory()->create(['pin' => 'BL123', 'name' => 'John']);
        Blacklist::factory()->create(['pin' => 'BL456', 'name' => 'Jane']);
        Blacklist::factory()->create(['pin' => 'BL789', 'name' => 'Bob']);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/blacklists/search?pin=BL1');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData); // Only BL123
    }

    public function testFilterBlacklistsByStatus(): void
    {
        Blacklist::factory()->create(['status' => 1, 'name' => 'Active']);
        Blacklist::factory()->create(['status' => 0, 'name' => 'Inactive']);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/blacklists?status=1');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Active', $responseData[0]['name']);
    }

    public function testFilterBlacklistsByGender(): void
    {
        Blacklist::factory()->create(['gender' => 'male', 'name' => 'John']);
        Blacklist::factory()->create(['gender' => 'female', 'name' => 'Jane']);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/blacklists?gender=female');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Jane', $responseData[0]['name']);
    }

    public function testBlacklistsListSorting(): void
    {
        Blacklist::factory()->create(['name' => 'Charlie', 'created_at' => now()->subDays(2)]);
        Blacklist::factory()->create(['name' => 'Alice', 'created_at' => now()->subDays(1)]);
        Blacklist::factory()->create(['name' => 'Bob', 'created_at' => now()]);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/blacklists?sort=name&order=asc');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertEquals('Alice', $responseData[0]['name']);
        $this->assertEquals('Bob', $responseData[1]['name']);
        $this->assertEquals('Charlie', $responseData[2]['name']);
    }

    public function testCreateBlacklistWithDuplicatePin(): void
    {
        Blacklist::factory()->create(['pin' => 'DUPLICATE123']);

        $blacklistData = [
            'name' => 'Jane',
            'surname' => 'Smith',
            'pin' => 'DUPLICATE123',
            'status' => 1,
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/blacklists', $blacklistData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['pin']);
    }

    public function testUnauthenticatedUserCannotAccessBlacklists(): void
    {
        $this->shouldAuthenticate = false;

        $response = $this->getJson(self::V1_PREFIX . '/blacklists');

        $response->assertStatus(401);
    }
}
