<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PolygonControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = true;
    }

    public function testGetPolygonsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/polygons');
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'status' => 401,
            'message' => 'Unauthorized'
        ]);
    }

    public function testGetPolygonsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/polygons');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'description',
                    'type',
                    'color',
                    'opacity',
                    'is_active',
                    'area',
                    'perimeter',
                    'coordinates_count',
                    'created_at',
                    'updated_at'
                ]
            ],
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetPolygonsWithPaginationParameters(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/polygons?page=1&per_page=15');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $meta = $response->json('meta');
        $this->assertEquals(1, $meta['current_page']);
        $this->assertEquals(15, $meta['per_page']);
    }

    public function testGetPolygonsWithTypeFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/polygons?type=zone');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testCreatePolygonRequiresAuthentication(): void
    {
        $polygonData = [
            'name' => 'Test Zone',
            'type' => 'zone',
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3]
            ]
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/polygons', $polygonData);
        
        $response->assertStatus(401);
    }

    public function testCreatePolygonWithValidData(): void
    {
        $polygonData = [
            'name' => 'Baku City Center Zone',
            'description' => 'Central business district of Baku',
            'type' => 'zone',
            'color' => '#FF5722',
            'opacity' => 0.7,
            'is_active' => true,
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3],
                ['latitude' => 40.4070, 'longitude' => 49.8660, 'order' => 4]
            ],
            'metadata' => [
                'created_by' => 'admin',
                'purpose' => 'surveillance'
            ]
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        
        $response->assertStatus(201);
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'name',
                'type',
                'area',
                'perimeter',
                'coordinates_count',
                'created_at'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(201, $responseData['status']);
        $this->assertEquals($polygonData['name'], $responseData['data']['name']);
    }

    public function testCreatePolygonValidationRequiresName(): void
    {
        $polygonData = [
            'type' => 'zone',
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3]
            ]
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name']);
    }

    public function testCreatePolygonValidationRequiresType(): void
    {
        $polygonData = [
            'name' => 'Test Polygon',
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3]
            ]
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['type']);
    }

    public function testCreatePolygonValidationRequiresMinimumCoordinates(): void
    {
        $polygonData = [
            'name' => 'Test Polygon',
            'type' => 'zone',
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2]
            ]
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['coordinates']);
    }

    public function testCreatePolygonValidatesTypeEnum(): void
    {
        $polygonData = [
            'name' => 'Test Polygon',
            'type' => 'invalid_type',
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3]
            ]
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['type']);
    }

    public function testGetPolygonByIdRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/polygons/1');
        
        $response->assertStatus(401);
    }

    public function testGetPolygonByIdReturnsSuccessfulResponse(): void
    {
        // First create a polygon
        $polygonData = [
            'name' => 'Test Polygon',
            'description' => 'Test polygon description',
            'type' => 'zone',
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3],
                ['latitude' => 40.4070, 'longitude' => 49.8660, 'order' => 4]
            ]
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        $polygonId = $createResponse->json('data.id');

        $response = $this->getAuthenticated(self::V1_PREFIX . "/polygons/{$polygonId}");
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'id',
                'name',
                'description',
                'type',
                'color',
                'opacity',
                'is_active',
                'area',
                'perimeter',
                'coordinates',
                'center_point' => [
                    'latitude',
                    'longitude'
                ],
                'bounding_box' => [
                    'north',
                    'south',
                    'east',
                    'west'
                ],
                'metadata',
                'created_at',
                'updated_at'
            ]
        ]);
    }

    public function testGetPolygonByIdNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/polygons/99999');
        
        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'status' => 404,
            'message' => 'Polygon not found'
        ]);
    }

    public function testCartesianDistancePolygonRequiresAuthentication(): void
    {
        $analysisData = [
            'polygon_id' => 1,
            'start_date' => '2024-01-15T00:00:00Z',
            'end_date' => '2024-01-15T23:59:59Z'
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/cartesian-distance', $analysisData);
        
        $response->assertStatus(401);
    }

    public function testCartesianDistancePolygonWithValidData(): void
    {
        // First create a polygon
        $polygonData = [
            'name' => 'Analysis Zone',
            'type' => 'surveillance_area',
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3],
                ['latitude' => 40.4070, 'longitude' => 49.8660, 'order' => 4]
            ]
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        $polygonId = $createResponse->json('data.id');

        $analysisData = [
            'polygon_id' => $polygonId,
            'start_date' => '2024-01-15T00:00:00Z',
            'end_date' => '2024-01-15T23:59:59Z',
            'person_pins' => ['1234567', '7654321'],
            'vehicle_plates' => ['10AB123', '90XY456'],
            'analysis_type' => 'movement',
            'time_interval' => 3600
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cartesian-distance', $analysisData);
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'polygon_info' => [
                    'id',
                    'name',
                    'area'
                ],
                'analysis_period',
                'total_data_points',
                'unique_entities',
                'movement_analysis' => [
                    'total_distance_covered',
                    'average_speed',
                    'max_speed',
                    'entry_points',
                    'exit_points',
                    'dwell_time_average'
                ],
                'density_analysis' => [
                    'peak_density_time',
                    'peak_density_count',
                    'low_density_time',
                    'average_density'
                ],
                'hotspots'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testCartesianDistancePolygonValidationRequiresPolygonId(): void
    {
        $analysisData = [
            'start_date' => '2024-01-15T00:00:00Z',
            'end_date' => '2024-01-15T23:59:59Z'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cartesian-distance', $analysisData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['polygon_id']);
    }

    public function testCartesianDistancePolygonNotFound(): void
    {
        $analysisData = [
            'polygon_id' => 99999,
            'start_date' => '2024-01-15T00:00:00Z',
            'end_date' => '2024-01-15T23:59:59Z'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cartesian-distance', $analysisData);
        
        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'status' => 404,
            'message' => 'Polygon not found'
        ]);
    }

    public function testCartesianDistanceExportRequiresAuthentication(): void
    {
        $exportData = [
            'polygon_id' => 1,
            'format' => 'excel'
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/cartesian-distance-export', $exportData);
        
        $response->assertStatus(401);
    }

    public function testCartesianDistanceExportWithValidData(): void
    {
        // First create a polygon
        $polygonData = [
            'name' => 'Export Test Zone',
            'type' => 'zone',
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3],
                ['latitude' => 40.4070, 'longitude' => 49.8660, 'order' => 4]
            ]
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        $polygonId = $createResponse->json('data.id');

        $exportData = [
            'polygon_id' => $polygonId,
            'format' => 'excel',
            'start_date' => '2024-01-15T00:00:00Z',
            'end_date' => '2024-01-15T23:59:59Z',
            'include_charts' => true,
            'include_raw_data' => false
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cartesian-distance-export', $exportData);
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'export_id',
                'download_url',
                'file_size',
                'expires_at'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testCartesianDistanceExportValidationRequiresPolygonId(): void
    {
        $exportData = [
            'format' => 'excel'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cartesian-distance-export', $exportData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['polygon_id']);
    }

    public function testCartesianDistanceExportValidationRequiresFormat(): void
    {
        $exportData = [
            'polygon_id' => 1
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cartesian-distance-export', $exportData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['format']);
    }

    public function testCartesianDistanceExportValidatesFormatEnum(): void
    {
        $exportData = [
            'polygon_id' => 1,
            'format' => 'invalid_format'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/cartesian-distance-export', $exportData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['format']);
    }

    public function testPolygonDataStructureValidation(): void
    {
        // Create a polygon first
        $polygonData = [
            'name' => 'Data Structure Test Polygon',
            'description' => 'Test polygon for data validation',
            'type' => 'zone',
            'color' => '#FF5722',
            'opacity' => 0.7,
            'coordinates' => [
                ['latitude' => 40.4093, 'longitude' => 49.8671, 'order' => 1],
                ['latitude' => 40.4100, 'longitude' => 49.8680, 'order' => 2],
                ['latitude' => 40.4080, 'longitude' => 49.8690, 'order' => 3],
                ['latitude' => 40.4070, 'longitude' => 49.8660, 'order' => 4]
            ]
        ];

        $createResponse = $this->postAuthenticated(self::V1_PREFIX . '/polygons', $polygonData);
        $polygonId = $createResponse->json('data.id');

        $response = $this->getAuthenticated(self::V1_PREFIX . "/polygons/{$polygonId}");
        
        $response->assertOk();
        $data = $response->json('data');
        
        // Validate required fields
        $this->assertArrayHasKey('id', $data);
        $this->assertArrayHasKey('name', $data);
        $this->assertArrayHasKey('type', $data);
        $this->assertArrayHasKey('coordinates', $data);
        
        // Validate data types
        $this->assertIsInt($data['id']);
        $this->assertIsString($data['name']);
        $this->assertIsString($data['type']);
        $this->assertIsArray($data['coordinates']);
        
        // Validate type enum
        $this->assertContains($data['type'], ['zone', 'district', 'restricted_area', 'surveillance_area']);
        
        // Validate numeric fields
        if (isset($data['area'])) {
            $this->assertIsFloat($data['area']);
            $this->assertGreaterThan(0, $data['area']);
        }
        
        if (isset($data['perimeter'])) {
            $this->assertIsFloat($data['perimeter']);
            $this->assertGreaterThan(0, $data['perimeter']);
        }
        
        if (isset($data['opacity'])) {
            $this->assertIsFloat($data['opacity']);
            $this->assertGreaterThanOrEqual(0, $data['opacity']);
            $this->assertLessThanOrEqual(1, $data['opacity']);
        }
        
        // Validate coordinates structure
        $this->assertGreaterThanOrEqual(3, count($data['coordinates']));
        foreach ($data['coordinates'] as $coordinate) {
            $this->assertArrayHasKey('latitude', $coordinate);
            $this->assertArrayHasKey('longitude', $coordinate);
            $this->assertArrayHasKey('order', $coordinate);
            $this->assertIsFloat($coordinate['latitude']);
            $this->assertIsFloat($coordinate['longitude']);
            $this->assertIsInt($coordinate['order']);
            
            // Validate coordinate ranges for Azerbaijan
            $this->assertGreaterThanOrEqual(38, $coordinate['latitude']);
            $this->assertLessThanOrEqual(42, $coordinate['latitude']);
            $this->assertGreaterThanOrEqual(44, $coordinate['longitude']);
            $this->assertLessThanOrEqual(51, $coordinate['longitude']);
        }
        
        // Validate center point
        if (isset($data['center_point'])) {
            $center = $data['center_point'];
            $this->assertArrayHasKey('latitude', $center);
            $this->assertArrayHasKey('longitude', $center);
            $this->assertIsFloat($center['latitude']);
            $this->assertIsFloat($center['longitude']);
        }
        
        // Validate bounding box
        if (isset($data['bounding_box'])) {
            $bbox = $data['bounding_box'];
            $this->assertArrayHasKey('north', $bbox);
            $this->assertArrayHasKey('south', $bbox);
            $this->assertArrayHasKey('east', $bbox);
            $this->assertArrayHasKey('west', $bbox);
            $this->assertIsFloat($bbox['north']);
            $this->assertIsFloat($bbox['south']);
            $this->assertIsFloat($bbox['east']);
            $this->assertIsFloat($bbox['west']);
            
            // Validate bounding box logic
            $this->assertGreaterThan($bbox['south'], $bbox['north']);
            $this->assertGreaterThan($bbox['west'], $bbox['east']);
        }
    }
}
