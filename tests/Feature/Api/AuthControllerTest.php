<?php

namespace Tests\Feature\Api;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Tests\TestCase;

class AuthControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = false; // Don't auto-authenticate for auth tests
    }

    public function testUserCanLoginWithValidCredentials(): void
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $response = $this->postJson(self::V1_PREFIX . '/auth/login', $loginData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'access_token',
                'token_type',
                'expires_in',
                'user' => [
                    'id',
                    'name',
                    'surname',
                    'email',
                    'status',
                ]
            ]
        ]);

        $responseData = $response->json();
        $this->assertEquals('bearer', $responseData['data']['token_type']);
        $this->assertNotEmpty($responseData['data']['access_token']);
        $this->assertEquals($user->email, $responseData['data']['user']['email']);
    }

    public function testUserCannotLoginWithInvalidCredentials(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ];

        $response = $this->postJson(self::V1_PREFIX . '/auth/login', $loginData);

        $response->assertStatus(401);
        $response->assertJson([
            'message' => 'Unauthorized'
        ]);
    }

    public function testUserCannotLoginWithInactiveAccount(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 0, // Inactive account
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $response = $this->postJson(self::V1_PREFIX . '/auth/login', $loginData);

        $response->assertStatus(401);
    }

    public function testLoginValidationRequiresEmail(): void
    {
        $loginData = [
            'password' => 'password123',
        ];

        $response = $this->postJson(self::V1_PREFIX . '/auth/login', $loginData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['email']);
    }

    public function testLoginValidationRequiresPassword(): void
    {
        $loginData = [
            'email' => '<EMAIL>',
        ];

        $response = $this->postJson(self::V1_PREFIX . '/auth/login', $loginData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['password']);
    }

    public function testLoginValidationRequiresValidEmail(): void
    {
        $loginData = [
            'email' => 'invalid-email',
            'password' => 'password123',
        ];

        $response = $this->postJson(self::V1_PREFIX . '/auth/login', $loginData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['email']);
    }

    public function testUserCanLogout(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
        ]);

        // First login to get token
        $loginResponse = $this->postJson(self::V1_PREFIX . '/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $token = $loginResponse->json('data.access_token');

        // Then logout
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson(self::V1_PREFIX . '/auth/logout');

        $response->assertOk();
        $response->assertJson([
            'message' => 'Successfully logged out'
        ]);
    }

    public function testUserCanRefreshToken(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
        ]);

        // First login to get token
        $loginResponse = $this->postJson(self::V1_PREFIX . '/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ]);

        $token = $loginResponse->json('data.access_token');

        // Then refresh token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson(self::V1_PREFIX . '/auth/refresh');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'access_token',
                'token_type',
                'expires_in',
                'user'
            ]
        ]);

        $newToken = $response->json('data.access_token');
        $this->assertNotEquals($token, $newToken);
    }

    public function testUserCanGetProfile(): void
    {
        $user = User::factory()->create([
            'name' => 'John',
            'surname' => 'Doe',
            'email' => '<EMAIL>',
            'status' => 1,
        ]);

        // Login first
        $loginResponse = $this->postJson(self::V1_PREFIX . '/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password',
        ]);

        $token = $loginResponse->json('data.access_token');

        // Get profile
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson(self::V1_PREFIX . '/auth/me');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'surname',
                'email',
                'status',
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('John', $responseData['name']);
        $this->assertEquals('Doe', $responseData['surname']);
        $this->assertEquals('<EMAIL>', $responseData['email']);
    }

    public function testUnauthenticatedUserCannotAccessProfile(): void
    {
        $response = $this->getJson(self::V1_PREFIX . '/auth/me');

        $response->assertStatus(401);
    }

    public function testInvalidTokenCannotAccessProtectedRoutes(): void
    {
        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-token',
        ])->getJson(self::V1_PREFIX . '/auth/me');

        $response->assertStatus(401);
    }

    public function testExpiredTokenCannotAccessProtectedRoutes(): void
    {
        // This test would require mocking JWT to return an expired token
        // For now, we'll test with a malformed token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer expired.token.here',
        ])->getJson(self::V1_PREFIX . '/auth/me');

        $response->assertStatus(401);
    }

    public function testLoginRateLimiting(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ];

        // Attempt multiple failed logins
        for ($i = 0; $i < 6; $i++) {
            $response = $this->postJson(self::V1_PREFIX . '/auth/login', $loginData);
            
            if ($i < 5) {
                $response->assertStatus(401);
            } else {
                // After 5 attempts, should be rate limited
                $response->assertStatus(429);
            }
        }
    }

    public function testUserWithSystemFlagCanLogin(): void
    {
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'status' => 1,
            'is_system' => true,
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
        ];

        $response = $this->postJson(self::V1_PREFIX . '/auth/login', $loginData);

        $response->assertOk();
        $responseData = $response->json();
        $this->assertTrue($responseData['data']['user']['is_system']);
    }
}
