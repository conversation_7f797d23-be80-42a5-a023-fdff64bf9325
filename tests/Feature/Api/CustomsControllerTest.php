<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class CustomsControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = true;
    }

    public function testGetPersonCustomsRecordsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/person/1234567/customs');
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'status' => 401,
            'message' => 'Unauthorized'
        ]);
    }

    public function testGetPersonCustomsRecordsReturnsSuccessfulResponse(): void
    {
        $pin = '1234567';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/person/{$pin}/customs");
        
        // Should return either 200 (records found) or 404 (no records)
        $this->assertContains($response->status(), [200, 404]);
        
        if ($response->status() === 200) {
            $response->assertJsonStructure([
                'success',
                'status',
                'message',
                'data' => [
                    'person_pin',
                    'total_records',
                    'customs_records' => [
                        '*' => [
                            'declaration_id',
                            'declaration_date',
                            'customs_office',
                            'operation_type',
                            'transport_mode',
                            'origin_country',
                            'destination_country',
                            'total_value',
                            'currency',
                            'duty_paid',
                            'vat_paid',
                            'status',
                            'goods',
                            'inspection_required',
                            'risk_assessment',
                            'processing_time',
                            'officer_id',
                            'notes'
                        ]
                    ],
                    'summary' => [
                        'total_declarations',
                        'total_value',
                        'total_duties_paid',
                        'most_common_goods',
                        'most_used_office',
                        'average_processing_time'
                    ]
                ]
            ]);
            
            $responseData = $response->json();
            $this->assertTrue($responseData['success']);
            $this->assertEquals(200, $responseData['status']);
            $this->assertEquals($pin, $responseData['data']['person_pin']);
        }
    }

    public function testGetPersonCustomsRecordsWithDateRange(): void
    {
        $pin = '1234567';
        $startDate = '2024-01-01';
        $endDate = '2024-01-31';
        
        $response = $this->getAuthenticated(
            self::V1_PREFIX . "/person/{$pin}/customs?start_date={$startDate}&end_date={$endDate}"
        );
        
        $this->assertContains($response->status(), [200, 404]);
    }

    public function testGetPersonCustomsRecordsWithCustomsTypeFilter(): void
    {
        $pin = '1234567';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/person/{$pin}/customs?customs_type=import");
        
        $this->assertContains($response->status(), [200, 404]);
    }

    public function testGetPersonCustomsRecordsNotFound(): void
    {
        $pin = 'NONEXISTENT_PIN';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/person/{$pin}/customs");
        
        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'status' => 404,
            'message' => 'No customs records found for this person'
        ]);
    }

    public function testGetPersonCustomsRecordsInsufficientPermissions(): void
    {
        // This would require mocking a user with insufficient permissions
        $pin = '1234567';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/person/{$pin}/customs");
        
        // Should return either 200 (access granted) or 403 (insufficient permissions)
        $this->assertContains($response->status(), [200, 403, 404]);
        
        if ($response->status() === 403) {
            $response->assertJson([
                'success' => false,
                'status' => 403,
                'message' => 'Insufficient permissions to access customs data'
            ]);
        }
    }

    public function testGetCustomsDeclarationsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/customs/declarations');
        
        $response->assertStatus(401);
    }

    public function testGetCustomsDeclarationsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/customs/declarations');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'declaration_id',
                    'declaration_date',
                    'person_pin',
                    'person_name',
                    'customs_office',
                    'operation_type',
                    'transport_mode',
                    'origin_country',
                    'destination_country',
                    'total_value',
                    'currency',
                    'duty_paid',
                    'vat_paid',
                    'status',
                    'risk_assessment',
                    'processing_time',
                    'goods_count',
                    'created_at'
                ]
            ],
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetCustomsDeclarationsWithPaginationParameters(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/customs/declarations?page=1&per_page=50');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $meta = $response->json('meta');
        $this->assertEquals(1, $meta['current_page']);
        $this->assertEquals(50, $meta['per_page']);
    }

    public function testGetCustomsDeclarationsWithFilters(): void
    {
        $filters = [
            'declaration_id' => 'DECL123456789',
            'person_pin' => '1234567',
            'customs_office' => 'Baku International Airport',
            'operation_type' => 'import',
            'status' => 'cleared',
            'start_date' => '2024-01-01',
            'end_date' => '2024-01-31',
            'min_value' => '1000.00'
        ];
        
        $queryString = http_build_query($filters);
        $response = $this->getAuthenticated(self::V1_PREFIX . "/customs/declarations?{$queryString}");
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetCustomsAnalyticsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/customs/analytics');
        
        $response->assertStatus(401);
    }

    public function testGetCustomsAnalyticsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/customs/analytics');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'period',
                'total_declarations',
                'total_value',
                'total_duties_collected',
                'total_vat_collected',
                'by_operation_type' => [
                    'import' => [
                        'count',
                        'value',
                        'percentage'
                    ],
                    'export' => [
                        'count',
                        'value',
                        'percentage'
                    ],
                    'transit' => [
                        'count',
                        'value',
                        'percentage'
                    ],
                    'personal' => [
                        'count',
                        'value',
                        'percentage'
                    ]
                ],
                'by_transport_mode',
                'top_countries',
                'processing_efficiency' => [
                    'average_processing_time',
                    'inspection_rate',
                    'clearance_rate',
                    'detention_rate'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetCustomsAnalyticsWithPeriodFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/customs/analytics?period=month');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetCustomsAnalyticsWithDateRange(): void
    {
        $startDate = '2024-01-01';
        $endDate = '2024-01-31';
        
        $response = $this->getAuthenticated(
            self::V1_PREFIX . "/customs/analytics?start_date={$startDate}&end_date={$endDate}"
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetCustomsAnalyticsWithCustomsOfficeFilter(): void
    {
        $customsOffice = 'Baku International Airport';
        $response = $this->getAuthenticated(
            self::V1_PREFIX . "/customs/analytics?customs_office=" . urlencode($customsOffice)
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testCustomsRecordsDataStructureValidation(): void
    {
        $pin = '1234567';
        $response = $this->getAuthenticated(self::V1_PREFIX . "/person/{$pin}/customs");
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            
            // Validate person PIN
            $this->assertEquals($pin, $data['person_pin']);
            
            // Validate total records
            $this->assertArrayHasKey('total_records', $data);
            $this->assertIsInt($data['total_records']);
            $this->assertGreaterThanOrEqual(0, $data['total_records']);
            
            // Validate customs records structure
            if (isset($data['customs_records']) && is_array($data['customs_records'])) {
                foreach ($data['customs_records'] as $record) {
                    $this->assertArrayHasKey('declaration_id', $record);
                    $this->assertArrayHasKey('declaration_date', $record);
                    $this->assertArrayHasKey('operation_type', $record);
                    $this->assertArrayHasKey('status', $record);
                    
                    // Validate operation type enum
                    if (isset($record['operation_type'])) {
                        $this->assertContains($record['operation_type'], ['import', 'export', 'transit', 'personal']);
                    }
                    
                    // Validate transport mode enum
                    if (isset($record['transport_mode'])) {
                        $this->assertContains($record['transport_mode'], ['air', 'sea', 'land', 'rail']);
                    }
                    
                    // Validate status enum
                    if (isset($record['status'])) {
                        $this->assertContains($record['status'], ['cleared', 'pending', 'inspection', 'detained']);
                    }
                    
                    // Validate risk assessment enum
                    if (isset($record['risk_assessment'])) {
                        $this->assertContains($record['risk_assessment'], ['low', 'medium', 'high']);
                    }
                    
                    // Validate numeric fields
                    if (isset($record['total_value'])) {
                        $this->assertIsFloat($record['total_value']);
                        $this->assertGreaterThanOrEqual(0, $record['total_value']);
                    }
                    
                    if (isset($record['duty_paid'])) {
                        $this->assertIsFloat($record['duty_paid']);
                        $this->assertGreaterThanOrEqual(0, $record['duty_paid']);
                    }
                    
                    if (isset($record['processing_time'])) {
                        $this->assertIsInt($record['processing_time']);
                        $this->assertGreaterThanOrEqual(0, $record['processing_time']);
                    }
                    
                    // Validate goods structure
                    if (isset($record['goods']) && is_array($record['goods'])) {
                        foreach ($record['goods'] as $good) {
                            $this->assertArrayHasKey('description', $good);
                            $this->assertArrayHasKey('quantity', $good);
                            $this->assertArrayHasKey('unit_value', $good);
                            
                            if (isset($good['quantity'])) {
                                $this->assertIsInt($good['quantity']);
                                $this->assertGreaterThan(0, $good['quantity']);
                            }
                            
                            if (isset($good['unit_value'])) {
                                $this->assertIsFloat($good['unit_value']);
                                $this->assertGreaterThanOrEqual(0, $good['unit_value']);
                            }
                        }
                    }
                }
            }
            
            // Validate summary structure
            if (isset($data['summary'])) {
                $summary = $data['summary'];
                
                if (isset($summary['total_declarations'])) {
                    $this->assertIsInt($summary['total_declarations']);
                    $this->assertGreaterThanOrEqual(0, $summary['total_declarations']);
                }
                
                if (isset($summary['total_value'])) {
                    $this->assertIsFloat($summary['total_value']);
                    $this->assertGreaterThanOrEqual(0, $summary['total_value']);
                }
                
                if (isset($summary['average_processing_time'])) {
                    $this->assertIsInt($summary['average_processing_time']);
                    $this->assertGreaterThanOrEqual(0, $summary['average_processing_time']);
                }
            }
        }
    }

    public function testCustomsAnalyticsDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/customs/analytics');
        
        $response->assertOk();
        $data = $response->json('data');
        
        // Validate numeric totals
        if (isset($data['total_declarations'])) {
            $this->assertIsInt($data['total_declarations']);
            $this->assertGreaterThanOrEqual(0, $data['total_declarations']);
        }
        
        if (isset($data['total_value'])) {
            $this->assertIsFloat($data['total_value']);
            $this->assertGreaterThanOrEqual(0, $data['total_value']);
        }
        
        // Validate operation type breakdown
        if (isset($data['by_operation_type'])) {
            $operationTypes = $data['by_operation_type'];
            
            foreach (['import', 'export', 'transit', 'personal'] as $type) {
                if (isset($operationTypes[$type])) {
                    $typeData = $operationTypes[$type];
                    $this->assertArrayHasKey('count', $typeData);
                    $this->assertArrayHasKey('value', $typeData);
                    $this->assertArrayHasKey('percentage', $typeData);
                    
                    $this->assertIsInt($typeData['count']);
                    $this->assertIsFloat($typeData['value']);
                    $this->assertIsFloat($typeData['percentage']);
                    $this->assertGreaterThanOrEqual(0, $typeData['percentage']);
                    $this->assertLessThanOrEqual(100, $typeData['percentage']);
                }
            }
        }
        
        // Validate transport mode breakdown
        if (isset($data['by_transport_mode'])) {
            $transportModes = $data['by_transport_mode'];
            
            foreach (['air', 'sea', 'land', 'rail'] as $mode) {
                if (isset($transportModes[$mode])) {
                    $this->assertIsInt($transportModes[$mode]);
                    $this->assertGreaterThanOrEqual(0, $transportModes[$mode]);
                }
            }
        }
        
        // Validate top countries structure
        if (isset($data['top_countries']) && is_array($data['top_countries'])) {
            foreach ($data['top_countries'] as $country) {
                $this->assertArrayHasKey('country', $country);
                $this->assertArrayHasKey('declarations', $country);
                $this->assertArrayHasKey('value', $country);
                
                $this->assertIsString($country['country']);
                $this->assertIsInt($country['declarations']);
                $this->assertIsFloat($country['value']);
                $this->assertGreaterThanOrEqual(0, $country['declarations']);
                $this->assertGreaterThanOrEqual(0, $country['value']);
            }
        }
        
        // Validate processing efficiency
        if (isset($data['processing_efficiency'])) {
            $efficiency = $data['processing_efficiency'];
            
            if (isset($efficiency['average_processing_time'])) {
                $this->assertIsInt($efficiency['average_processing_time']);
                $this->assertGreaterThanOrEqual(0, $efficiency['average_processing_time']);
            }
            
            foreach (['inspection_rate', 'clearance_rate', 'detention_rate'] as $rate) {
                if (isset($efficiency[$rate])) {
                    $this->assertIsFloat($efficiency[$rate]);
                    $this->assertGreaterThanOrEqual(0, $efficiency[$rate]);
                    $this->assertLessThanOrEqual(100, $efficiency[$rate]);
                }
            }
        }
    }
}
