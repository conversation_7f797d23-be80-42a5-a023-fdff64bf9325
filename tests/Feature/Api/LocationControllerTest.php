<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class LocationControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = true;
    }

    public function testFindLocationRequiresAuthentication(): void
    {
        $locationData = [
            'latitude' => 40.4093,
            'longitude' => 49.8671
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/find-location', $locationData);
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'status' => 401,
            'message' => 'Unauthorized'
        ]);
    }

    public function testFindLocationWithCoordinates(): void
    {
        $locationData = [
            'latitude' => 40.4093,
            'longitude' => 49.8671,
            'radius' => 1000,
            'include_poi' => true,
            'include_traffic' => false
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/find-location', $locationData);
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'coordinates' => [
                    'latitude',
                    'longitude'
                ],
                'address' => [
                    'formatted_address',
                    'street',
                    'city',
                    'district',
                    'country',
                    'postal_code'
                ],
                'administrative_info' => [
                    'region',
                    'municipality',
                    'settlement'
                ],
                'nearby_poi',
                'accuracy',
                'elevation'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testFindLocationWithAddress(): void
    {
        $locationData = [
            'address' => 'Nizami Street, Baku',
            'include_poi' => true
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/find-location', $locationData);
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'coordinates',
                'address',
                'administrative_info'
            ]
        ]);
    }

    public function testFindLocationValidationRequiresCoordinatesOrAddress(): void
    {
        $locationData = [
            'radius' => 1000
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/find-location', $locationData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['latitude']);
    }

    public function testFindLocationValidatesCoordinateRange(): void
    {
        $locationData = [
            'latitude' => 91.0, // Invalid latitude (> 90)
            'longitude' => 49.8671
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/find-location', $locationData);
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['latitude']);
    }

    public function testFindLocationNotFound(): void
    {
        $locationData = [
            'latitude' => 0.0,
            'longitude' => 0.0
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/find-location', $locationData);
        
        // Should either return 200 with empty data or 404
        $this->assertContains($response->status(), [200, 404]);
        
        if ($response->status() === 404) {
            $response->assertJson([
                'success' => false,
                'status' => 404,
                'message' => 'Location not found'
            ]);
        }
    }

    public function testFindLocationDataStructureValidation(): void
    {
        $locationData = [
            'latitude' => 40.4093,
            'longitude' => 49.8671,
            'include_poi' => true
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/find-location', $locationData);
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            
            // Validate coordinates
            $this->assertArrayHasKey('coordinates', $data);
            $coordinates = $data['coordinates'];
            $this->assertIsFloat($coordinates['latitude']);
            $this->assertIsFloat($coordinates['longitude']);
            $this->assertGreaterThanOrEqual(-90, $coordinates['latitude']);
            $this->assertLessThanOrEqual(90, $coordinates['latitude']);
            $this->assertGreaterThanOrEqual(-180, $coordinates['longitude']);
            $this->assertLessThanOrEqual(180, $coordinates['longitude']);
            
            // Validate address structure
            if (isset($data['address'])) {
                $address = $data['address'];
                $this->assertArrayHasKey('formatted_address', $address);
                $this->assertArrayHasKey('city', $address);
                $this->assertArrayHasKey('country', $address);
            }
            
            // Validate POI structure
            if (isset($data['nearby_poi']) && is_array($data['nearby_poi'])) {
                foreach ($data['nearby_poi'] as $poi) {
                    $this->assertArrayHasKey('name', $poi);
                    $this->assertArrayHasKey('type', $poi);
                    $this->assertArrayHasKey('distance', $poi);
                    $this->assertArrayHasKey('coordinates', $poi);
                    $this->assertIsInt($poi['distance']);
                    $this->assertGreaterThanOrEqual(0, $poi['distance']);
                }
            }
            
            // Validate accuracy
            if (isset($data['accuracy'])) {
                $this->assertIsInt($data['accuracy']);
                $this->assertGreaterThanOrEqual(0, $data['accuracy']);
            }
        }
    }

    public function testFindSimilarityRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/find-similarity?reference_lat=40.4093&reference_lng=49.8671');
        
        $response->assertStatus(401);
    }

    public function testFindSimilarityWithValidParameters(): void
    {
        $response = $this->getAuthenticated(
            self::V1_PREFIX . '/find-similarity?reference_lat=40.4093&reference_lng=49.8671&similarity_type=geographic&radius=10&limit=20'
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'reference_location' => [
                    'latitude',
                    'longitude',
                    'address'
                ],
                'similarity_type',
                'total_found',
                'similar_locations' => [
                    '*' => [
                        'location_id',
                        'coordinates' => [
                            'latitude',
                            'longitude'
                        ],
                        'address',
                        'similarity_score',
                        'distance',
                        'characteristics',
                        'match_reasons'
                    ]
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testFindSimilarityValidationRequiresCoordinates(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/find-similarity');
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['reference_lat']);
    }

    public function testFindSimilarityValidatesSimilarityType(): void
    {
        $response = $this->getAuthenticated(
            self::V1_PREFIX . '/find-similarity?reference_lat=40.4093&reference_lng=49.8671&similarity_type=invalid_type'
        );
        
        // Should either return 422 for validation error or use default type
        $this->assertContains($response->status(), [200, 422]);
    }

    public function testFindSimilarityDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(
            self::V1_PREFIX . '/find-similarity?reference_lat=40.4093&reference_lng=49.8671&similarity_type=geographic'
        );
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            
            // Validate reference location
            $this->assertArrayHasKey('reference_location', $data);
            $refLocation = $data['reference_location'];
            $this->assertIsFloat($refLocation['latitude']);
            $this->assertIsFloat($refLocation['longitude']);
            
            // Validate similarity type
            $this->assertArrayHasKey('similarity_type', $data);
            $this->assertContains($data['similarity_type'], ['demographic', 'geographic', 'traffic_pattern', 'poi_density']);
            
            // Validate similar locations
            if (isset($data['similar_locations']) && is_array($data['similar_locations'])) {
                foreach ($data['similar_locations'] as $location) {
                    $this->assertArrayHasKey('similarity_score', $location);
                    $this->assertArrayHasKey('distance', $location);
                    $this->assertIsFloat($location['similarity_score']);
                    $this->assertGreaterThanOrEqual(0, $location['similarity_score']);
                    $this->assertLessThanOrEqual(1, $location['similarity_score']);
                    $this->assertIsFloat($location['distance']);
                    $this->assertGreaterThanOrEqual(0, $location['distance']);
                }
            }
        }
    }

    public function testFindMeetingPlacesRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/find-meeting-places?person_pins=1234567,7654321');
        
        $response->assertStatus(401);
    }

    public function testFindMeetingPlacesWithValidParameters(): void
    {
        $response = $this->getAuthenticated(
            self::V1_PREFIX . '/find-meeting-places?person_pins=1234567,7654321,9876543&start_date=2024-01-15&end_date=2024-01-15&min_duration=30&max_distance=100'
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'analysis_period',
                'persons_analyzed',
                'total_meetings',
                'meeting_places' => [
                    '*' => [
                        'meeting_id',
                        'location' => [
                            'latitude',
                            'longitude',
                            'address'
                        ],
                        'start_time',
                        'end_time',
                        'duration',
                        'participants',
                        'confidence',
                        'meeting_type',
                        'location_type',
                        'frequency_score'
                    ]
                ],
                'patterns' => [
                    'most_frequent_location',
                    'average_meeting_duration',
                    'preferred_time_slots',
                    'meeting_frequency'
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testFindMeetingPlacesValidationRequiresPersonPins(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/find-meeting-places');
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['person_pins']);
    }

    public function testFindMeetingPlacesValidationMinimumPersons(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/find-meeting-places?person_pins=1234567');
        
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['person_pins']);
    }

    public function testFindMeetingPlacesDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(
            self::V1_PREFIX . '/find-meeting-places?person_pins=1234567,7654321&start_date=2024-01-15&end_date=2024-01-15'
        );
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            
            // Validate persons analyzed
            $this->assertArrayHasKey('persons_analyzed', $data);
            $this->assertIsArray($data['persons_analyzed']);
            $this->assertGreaterThanOrEqual(2, count($data['persons_analyzed']));
            
            // Validate total meetings
            $this->assertArrayHasKey('total_meetings', $data);
            $this->assertIsInt($data['total_meetings']);
            $this->assertGreaterThanOrEqual(0, $data['total_meetings']);
            
            // Validate meeting places structure
            if (isset($data['meeting_places']) && is_array($data['meeting_places'])) {
                foreach ($data['meeting_places'] as $meeting) {
                    $this->assertArrayHasKey('meeting_id', $meeting);
                    $this->assertArrayHasKey('location', $meeting);
                    $this->assertArrayHasKey('duration', $meeting);
                    $this->assertArrayHasKey('participants', $meeting);
                    $this->assertArrayHasKey('confidence', $meeting);
                    
                    // Validate duration
                    $this->assertIsInt($meeting['duration']);
                    $this->assertGreaterThan(0, $meeting['duration']);
                    
                    // Validate confidence score
                    $this->assertIsFloat($meeting['confidence']);
                    $this->assertGreaterThanOrEqual(0, $meeting['confidence']);
                    $this->assertLessThanOrEqual(1, $meeting['confidence']);
                    
                    // Validate meeting type
                    if (isset($meeting['meeting_type'])) {
                        $this->assertContains($meeting['meeting_type'], ['coincidental', 'planned', 'frequent']);
                    }
                    
                    // Validate participants
                    $this->assertIsArray($meeting['participants']);
                    $this->assertGreaterThanOrEqual(2, count($meeting['participants']));
                    
                    // Validate location coordinates
                    $location = $meeting['location'];
                    $this->assertIsFloat($location['latitude']);
                    $this->assertIsFloat($location['longitude']);
                    $this->assertGreaterThanOrEqual(-90, $location['latitude']);
                    $this->assertLessThanOrEqual(90, $location['latitude']);
                    $this->assertGreaterThanOrEqual(-180, $location['longitude']);
                    $this->assertLessThanOrEqual(180, $location['longitude']);
                }
            }
            
            // Validate patterns structure
            if (isset($data['patterns'])) {
                $patterns = $data['patterns'];
                
                if (isset($patterns['average_meeting_duration'])) {
                    $this->assertIsInt($patterns['average_meeting_duration']);
                    $this->assertGreaterThan(0, $patterns['average_meeting_duration']);
                }
                
                if (isset($patterns['preferred_time_slots'])) {
                    $this->assertIsArray($patterns['preferred_time_slots']);
                }
            }
        }
    }

    public function testLocationServicesWithAzerbaijanCoordinates(): void
    {
        // Test with coordinates within Azerbaijan boundaries
        $azerbaijanLocations = [
            ['latitude' => 40.4093, 'longitude' => 49.8671], // Baku
            ['latitude' => 40.6833, 'longitude' => 46.3667], // Ganja
            ['latitude' => 39.2075, 'longitude' => 45.0049], // Nakhchivan
            ['latitude' => 41.1919, 'longitude' => 47.1706], // Sheki
        ];
        
        foreach ($azerbaijanLocations as $location) {
            $response = $this->postAuthenticated(self::V1_PREFIX . '/find-location', $location);
            
            // Should return either 200 (found) or 404 (not found), but not validation errors
            $this->assertContains($response->status(), [200, 404]);
            
            if ($response->status() === 200) {
                $data = $response->json('data');
                
                // Validate that returned coordinates are within Azerbaijan
                if (isset($data['coordinates'])) {
                    $coords = $data['coordinates'];
                    $this->assertGreaterThanOrEqual(38, $coords['latitude']);
                    $this->assertLessThanOrEqual(42, $coords['latitude']);
                    $this->assertGreaterThanOrEqual(44, $coords['longitude']);
                    $this->assertLessThanOrEqual(51, $coords['longitude']);
                }
                
                // Validate that country is Azerbaijan
                if (isset($data['address']['country'])) {
                    $this->assertEquals('Azerbaijan', $data['address']['country']);
                }
            }
        }
    }
}
