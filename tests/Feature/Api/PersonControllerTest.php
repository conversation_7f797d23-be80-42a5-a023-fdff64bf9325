<?php

namespace Tests\Feature\Api;

use App\Models\Person;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PersonControllerTest extends TestCase
{
    use RefreshDatabase;

    public function testGetPersonsList(): void
    {
        // Create test persons
        Person::factory()->count(3)->create();

        $response = $this->getAuthenticated(self::V1_PREFIX . '/persons');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'surname',
                    'pin',
                    'created_at',
                    'updated_at'
                ]
            ]
        ]);
    }

    public function testGetPersonsListWithPagination(): void
    {
        Person::factory()->count(25)->create();

        $response = $this->getAuthenticated(self::V1_PREFIX . '/persons?page=1&per_page=10');

        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'links',
            'meta' => [
                'current_page',
                'per_page',
                'total'
            ]
        ]);

        $responseData = $response->json();
        $this->assertCount(10, $responseData['data']);
        $this->assertEquals(1, $responseData['meta']['current_page']);
        $this->assertEquals(10, $responseData['meta']['per_page']);
    }

    public function testGetPersonById(): void
    {
        $person = Person::factory()->create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'TEST123'
        ]);

        $response = $this->getAuthenticated(self::V1_PREFIX . "/persons/{$person->id}");

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'surname',
                'pin',
                'created_at',
                'updated_at'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('John', $responseData['name']);
        $this->assertEquals('Doe', $responseData['surname']);
        $this->assertEquals('TEST123', $responseData['pin']);
    }

    public function testGetPersonByIdNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/persons/999999');

        $response->assertStatus(404);
    }

    public function testCreatePerson(): void
    {
        $personData = [
            'name' => 'Jane',
            'surname' => 'Smith',
            'father_name' => 'Robert',
            'pin' => 'NEW123',
            'doc_type' => 'passport',
            'doc_serial_number' => 'AA123456',
            'doc_number' => '*********',
            'is_sync' => true,
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/persons', $personData);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'surname',
                'father_name',
                'pin',
                'doc_type',
                'doc_serial_number',
                'doc_number',
                'is_sync'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Jane', $responseData['name']);
        $this->assertEquals('Smith', $responseData['surname']);
        $this->assertEquals('NEW123', $responseData['pin']);

        // Verify person was created in database
        $this->assertDatabaseHas('people', [
            'name' => 'Jane',
            'surname' => 'Smith',
            'pin' => 'NEW123'
        ]);
    }

    public function testCreatePersonValidationErrors(): void
    {
        $invalidData = [
            'surname' => 'Smith',
            // Missing required name and pin
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/persons', $invalidData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['name', 'pin']);
    }

    public function testUpdatePerson(): void
    {
        $person = Person::factory()->create([
            'name' => 'John',
            'surname' => 'Doe',
            'pin' => 'UPDATE123'
        ]);

        $updateData = [
            'name' => 'Johnny',
            'surname' => 'Updated',
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . "/persons/{$person->id}", $updateData);

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                'id',
                'name',
                'surname',
                'pin'
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertEquals('Johnny', $responseData['name']);
        $this->assertEquals('Updated', $responseData['surname']);
        $this->assertEquals('UPDATE123', $responseData['pin']); // Should remain unchanged

        // Verify person was updated in database
        $this->assertDatabaseHas('people', [
            'id' => $person->id,
            'name' => 'Johnny',
            'surname' => 'Updated',
            'pin' => 'UPDATE123'
        ]);
    }

    public function testUpdatePersonNotFound(): void
    {
        $updateData = [
            'name' => 'Updated Name',
        ];

        $response = $this->putAuthenticated(self::V1_PREFIX . '/persons/999999', $updateData);

        $response->assertStatus(404);
    }

    public function testDeletePerson(): void
    {
        $person = Person::factory()->create();

        $response = $this->deleteAuthenticated(self::V1_PREFIX . "/persons/{$person->id}");

        $response->assertOk();

        // Verify person was deleted from database
        $this->assertDatabaseMissing('people', [
            'id' => $person->id
        ]);
    }

    public function testDeletePersonNotFound(): void
    {
        $response = $this->deleteAuthenticated(self::V1_PREFIX . '/persons/999999');

        $response->assertStatus(404);
    }

    public function testSearchPersonsByName(): void
    {
        Person::factory()->create(['name' => 'John', 'surname' => 'Doe']);
        Person::factory()->create(['name' => 'Jane', 'surname' => 'Smith']);
        Person::factory()->create(['name' => 'Bob', 'surname' => 'Johnson']);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/persons/search?name=John');

        $response->assertOk();
        $response->assertJsonStructure([
            'data' => [
                '*' => [
                    'id',
                    'name',
                    'surname',
                    'pin'
                ]
            ]
        ]);

        $responseData = $response->json('data');
        $this->assertCount(2, $responseData); // John Doe and Bob Johnson
    }

    public function testSearchPersonsByPin(): void
    {
        Person::factory()->create(['pin' => 'ABC123', 'name' => 'John']);
        Person::factory()->create(['pin' => 'DEF456', 'name' => 'Jane']);
        Person::factory()->create(['pin' => 'ABC789', 'name' => 'Bob']);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/persons/search?pin=ABC');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(2, $responseData); // ABC123 and ABC789
    }

    public function testGetPersonJobByPin(): void
    {
        $person = Person::factory()->create(['pin' => 'JOB123']);

        $response = $this->getAuthenticated(self::V1_PREFIX . "/jobX/{$person->pin}");

        $response->assertOk();
        // Structure depends on the actual implementation
        $response->assertJsonStructure([
            'data'
        ]);
    }

    public function testGetPersonJobByPinNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/jobX/NONEXISTENT');

        $response->assertStatus(404);
    }

    public function testUnauthenticatedUserCannotAccessPersons(): void
    {
        $this->shouldAuthenticate = false;

        $response = $this->getJson(self::V1_PREFIX . '/persons');

        $response->assertStatus(401);
    }

    public function testPersonsListFilterBySync(): void
    {
        Person::factory()->create(['is_sync' => true, 'name' => 'Synced']);
        Person::factory()->create(['is_sync' => false, 'name' => 'Unsynced']);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/persons?is_sync=1');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertCount(1, $responseData);
        $this->assertEquals('Synced', $responseData[0]['name']);
    }

    public function testPersonsListSorting(): void
    {
        Person::factory()->create(['name' => 'Charlie', 'created_at' => now()->subDays(2)]);
        Person::factory()->create(['name' => 'Alice', 'created_at' => now()->subDays(1)]);
        Person::factory()->create(['name' => 'Bob', 'created_at' => now()]);

        $response = $this->getAuthenticated(self::V1_PREFIX . '/persons?sort=name&order=asc');

        $response->assertOk();
        $responseData = $response->json('data');
        $this->assertEquals('Alice', $responseData[0]['name']);
        $this->assertEquals('Bob', $responseData[1]['name']);
        $this->assertEquals('Charlie', $responseData[2]['name']);
    }

    public function testCreatePersonWithDuplicatePin(): void
    {
        Person::factory()->create(['pin' => 'DUPLICATE123']);

        $personData = [
            'name' => 'Jane',
            'surname' => 'Smith',
            'pin' => 'DUPLICATE123',
            'is_sync' => true,
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/persons', $personData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['pin']);
    }
}
