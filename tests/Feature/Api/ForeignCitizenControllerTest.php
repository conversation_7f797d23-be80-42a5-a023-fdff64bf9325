<?php

namespace Tests\Feature\Api;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ForeignCitizenControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        $this->shouldAuthenticate = true;
    }

    public function testGetForeignCitizensRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/foreign-citizens');
        
        $response->assertStatus(401);
        $response->assertJson([
            'success' => false,
            'status' => 401,
            'message' => 'Unauthorized'
        ]);
    }

    public function testGetForeignCitizensReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/foreign-citizens');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'passport_number',
                    'full_name',
                    'nationality',
                    'date_of_birth',
                    'gender',
                    'visa_number',
                    'visa_type',
                    'entry_date',
                    'exit_date',
                    'visa_expiry',
                    'entry_point',
                    'entry_purpose',
                    'accommodation_address',
                    'sponsor_info',
                    'current_status',
                    'days_in_country',
                    'created_at'
                ]
            ],
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetForeignCitizensWithPaginationParameters(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/foreign-citizens?page=1&per_page=50');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'data',
            'meta' => [
                'current_page',
                'total',
                'per_page'
            ]
        ]);
        
        $meta = $response->json('meta');
        $this->assertEquals(1, $meta['current_page']);
        $this->assertEquals(50, $meta['per_page']);
    }

    public function testGetForeignCitizensWithFilters(): void
    {
        $filters = [
            'nationality' => 'Turkish',
            'visa_type' => 'business',
            'entry_purpose' => 'business',
            'entry_date_from' => '2024-01-01',
            'entry_date_to' => '2024-01-31',
            'status' => 'in_country'
        ];
        
        $queryString = http_build_query($filters);
        $response = $this->getAuthenticated(self::V1_PREFIX . "/foreign-citizens?{$queryString}");
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetForeignCitizensInsufficientPermissions(): void
    {
        // This would require mocking a user with insufficient permissions
        $response = $this->getAuthenticated(self::V1_PREFIX . '/foreign-citizens');
        
        // Should return either 200 (access granted) or 403 (insufficient permissions)
        $this->assertContains($response->status(), [200, 403]);
        
        if ($response->status() === 403) {
            $response->assertJson([
                'success' => false,
                'status' => 403,
                'message' => 'Insufficient permissions to access foreign citizen data'
            ]);
        }
    }

    public function testGetForeignCitizenByIdRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/foreign-citizens/1');
        
        $response->assertStatus(401);
    }

    public function testGetForeignCitizenByIdReturnsSuccessfulResponse(): void
    {
        // Using a mock ID since we don't have actual data
        $foreignCitizenId = 1;
        $response = $this->getAuthenticated(self::V1_PREFIX . "/foreign-citizens/{$foreignCitizenId}");
        
        // Should return either 200 (found) or 404 (not found)
        $this->assertContains($response->status(), [200, 404]);
        
        if ($response->status() === 200) {
            $response->assertJsonStructure([
                'success',
                'status',
                'message',
                'data' => [
                    'id',
                    'passport_number',
                    'full_name',
                    'nationality',
                    'date_of_birth',
                    'place_of_birth',
                    'gender',
                    'passport_issue_date',
                    'passport_expiry_date',
                    'passport_issuing_authority',
                    'visa_number',
                    'visa_type',
                    'visa_issue_date',
                    'visa_expiry',
                    'visa_issuing_office',
                    'entry_date',
                    'exit_date',
                    'entry_point',
                    'exit_point',
                    'entry_purpose',
                    'accommodation_address',
                    'accommodation_type',
                    'sponsor_info',
                    'contact_info',
                    'current_status',
                    'days_in_country',
                    'allowed_stay_days',
                    'remaining_days',
                    'entry_exit_history',
                    'risk_assessment',
                    'created_at',
                    'updated_at'
                ]
            ]);
        }
    }

    public function testGetForeignCitizenByIdNotFound(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/foreign-citizens/99999');
        
        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'status' => 404,
            'message' => 'Foreign citizen not found'
        ]);
    }

    public function testSearchForeignCitizensRequiresAuthentication(): void
    {
        $searchData = [
            'passport_number' => 'A12345678',
            'full_name' => 'John Smith'
        ];

        $response = $this->unauthenticatedRequest('POST', self::V1_PREFIX . '/foreign-citizens/search', $searchData);
        
        $response->assertStatus(401);
    }

    public function testSearchForeignCitizensWithValidCriteria(): void
    {
        $searchData = [
            'passport_number' => 'A12345678',
            'full_name' => 'John Smith',
            'nationality' => 'Turkish',
            'visa_number' => 'V123456789',
            'date_of_birth' => '1985-03-15',
            'entry_date_from' => '2024-01-01',
            'entry_date_to' => '2024-01-31',
            'visa_type' => 'business',
            'current_status' => 'in_country',
            'sponsor_name' => 'Beein Technologies',
            'accommodation_address' => 'Baku',
            'page' => 1,
            'per_page' => 20
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/foreign-citizens/search', $searchData);
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'total_found',
                'search_time',
                'results' => [
                    '*' => [
                        'id',
                        'passport_number',
                        'full_name',
                        'nationality',
                        'visa_type',
                        'entry_date',
                        'current_status',
                        'relevance_score'
                    ]
                ]
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testSearchForeignCitizensEmptyResults(): void
    {
        $searchData = [
            'passport_number' => 'NONEXISTENT_PASSPORT',
            'full_name' => 'Nonexistent Person'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/foreign-citizens/search', $searchData);
        
        $response->assertOk();
        $data = $response->json('data');
        $this->assertEquals(0, $data['total_found']);
        $this->assertIsArray($data['results']);
        $this->assertEmpty($data['results']);
    }

    public function testGetForeignCitizenAnalyticsRequiresAuthentication(): void
    {
        $response = $this->unauthenticatedRequest('GET', self::V1_PREFIX . '/foreign-citizens/analytics');
        
        $response->assertStatus(401);
    }

    public function testGetForeignCitizenAnalyticsReturnsSuccessfulResponse(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/foreign-citizens/analytics');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data' => [
                'period',
                'total_entries',
                'total_exits',
                'currently_in_country',
                'overstayed_visas',
                'by_nationality',
                'by_visa_type' => [
                    'tourist',
                    'business',
                    'work',
                    'student',
                    'transit'
                ],
                'by_entry_point',
                'average_stay_duration',
                'monthly_trends'
            ]
        ]);
        
        $responseData = $response->json();
        $this->assertTrue($responseData['success']);
        $this->assertEquals(200, $responseData['status']);
    }

    public function testGetForeignCitizenAnalyticsWithPeriodFilter(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/foreign-citizens/analytics?period=month');
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testGetForeignCitizenAnalyticsWithDateRange(): void
    {
        $startDate = '2024-01-01';
        $endDate = '2024-01-31';
        
        $response = $this->getAuthenticated(
            self::V1_PREFIX . "/foreign-citizens/analytics?start_date={$startDate}&end_date={$endDate}"
        );
        
        $response->assertOk();
        $response->assertJsonStructure([
            'success',
            'status',
            'message',
            'data'
        ]);
    }

    public function testForeignCitizenDataStructureValidation(): void
    {
        $foreignCitizenId = 1;
        $response = $this->getAuthenticated(self::V1_PREFIX . "/foreign-citizens/{$foreignCitizenId}");
        
        if ($response->status() === 200) {
            $data = $response->json('data');
            
            // Validate required fields
            $this->assertArrayHasKey('id', $data);
            $this->assertArrayHasKey('passport_number', $data);
            $this->assertArrayHasKey('full_name', $data);
            $this->assertArrayHasKey('nationality', $data);
            $this->assertArrayHasKey('current_status', $data);
            
            // Validate data types
            $this->assertIsInt($data['id']);
            $this->assertIsString($data['passport_number']);
            $this->assertIsString($data['full_name']);
            $this->assertIsString($data['nationality']);
            
            // Validate gender enum
            if (isset($data['gender'])) {
                $this->assertContains($data['gender'], ['male', 'female']);
            }
            
            // Validate visa type enum
            if (isset($data['visa_type'])) {
                $this->assertContains($data['visa_type'], ['tourist', 'business', 'work', 'student', 'transit', 'diplomatic']);
            }
            
            // Validate current status enum
            if (isset($data['current_status'])) {
                $this->assertContains($data['current_status'], ['in_country', 'departed', 'overstayed', 'deported']);
            }
            
            // Validate numeric fields
            if (isset($data['days_in_country'])) {
                $this->assertIsInt($data['days_in_country']);
                $this->assertGreaterThanOrEqual(0, $data['days_in_country']);
            }
            
            if (isset($data['allowed_stay_days'])) {
                $this->assertIsInt($data['allowed_stay_days']);
                $this->assertGreaterThan(0, $data['allowed_stay_days']);
            }
            
            if (isset($data['remaining_days'])) {
                $this->assertIsInt($data['remaining_days']);
            }
            
            // Validate sponsor info structure
            if (isset($data['sponsor_info']) && is_array($data['sponsor_info'])) {
                $sponsor = $data['sponsor_info'];
                if (isset($sponsor['name'])) {
                    $this->assertIsString($sponsor['name']);
                }
                if (isset($sponsor['voen'])) {
                    $this->assertIsString($sponsor['voen']);
                }
                if (isset($sponsor['phone'])) {
                    $this->assertIsString($sponsor['phone']);
                }
            }
            
            // Validate contact info structure
            if (isset($data['contact_info']) && is_array($data['contact_info'])) {
                $contact = $data['contact_info'];
                if (isset($contact['phone'])) {
                    $this->assertIsString($contact['phone']);
                }
                if (isset($contact['email'])) {
                    $this->assertIsString($contact['email']);
                    $this->assertStringContains('@', $contact['email']);
                }
            }
            
            // Validate entry/exit history structure
            if (isset($data['entry_exit_history']) && is_array($data['entry_exit_history'])) {
                foreach ($data['entry_exit_history'] as $history) {
                    $this->assertArrayHasKey('entry_date', $history);
                    $this->assertArrayHasKey('entry_point', $history);
                    $this->assertArrayHasKey('purpose', $history);
                    
                    if (isset($history['duration_days'])) {
                        $this->assertIsInt($history['duration_days']);
                        $this->assertGreaterThanOrEqual(0, $history['duration_days']);
                    }
                }
            }
            
            // Validate risk assessment structure
            if (isset($data['risk_assessment']) && is_array($data['risk_assessment'])) {
                $risk = $data['risk_assessment'];
                
                if (isset($risk['risk_level'])) {
                    $this->assertContains($risk['risk_level'], ['low', 'medium', 'high']);
                }
                
                if (isset($risk['risk_factors'])) {
                    $this->assertIsArray($risk['risk_factors']);
                }
                
                if (isset($risk['watch_list_status'])) {
                    $this->assertIsBool($risk['watch_list_status']);
                }
            }
            
            // Validate date formats
            foreach (['date_of_birth', 'entry_date', 'exit_date', 'visa_expiry'] as $dateField) {
                if (isset($data[$dateField]) && $data[$dateField] !== null) {
                    $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}/', $data[$dateField]);
                }
            }
        }
    }

    public function testForeignCitizenAnalyticsDataStructureValidation(): void
    {
        $response = $this->getAuthenticated(self::V1_PREFIX . '/foreign-citizens/analytics');
        
        $response->assertOk();
        $data = $response->json('data');
        
        // Validate numeric totals
        if (isset($data['total_entries'])) {
            $this->assertIsInt($data['total_entries']);
            $this->assertGreaterThanOrEqual(0, $data['total_entries']);
        }
        
        if (isset($data['total_exits'])) {
            $this->assertIsInt($data['total_exits']);
            $this->assertGreaterThanOrEqual(0, $data['total_exits']);
        }
        
        if (isset($data['currently_in_country'])) {
            $this->assertIsInt($data['currently_in_country']);
            $this->assertGreaterThanOrEqual(0, $data['currently_in_country']);
        }
        
        if (isset($data['overstayed_visas'])) {
            $this->assertIsInt($data['overstayed_visas']);
            $this->assertGreaterThanOrEqual(0, $data['overstayed_visas']);
        }
        
        // Validate nationality breakdown
        if (isset($data['by_nationality']) && is_array($data['by_nationality'])) {
            foreach ($data['by_nationality'] as $nationality) {
                $this->assertArrayHasKey('nationality', $nationality);
                $this->assertArrayHasKey('count', $nationality);
                $this->assertArrayHasKey('percentage', $nationality);
                
                $this->assertIsString($nationality['nationality']);
                $this->assertIsInt($nationality['count']);
                $this->assertIsFloat($nationality['percentage']);
                $this->assertGreaterThanOrEqual(0, $nationality['percentage']);
                $this->assertLessThanOrEqual(100, $nationality['percentage']);
            }
        }
        
        // Validate visa type breakdown
        if (isset($data['by_visa_type'])) {
            $visaTypes = $data['by_visa_type'];
            
            foreach (['tourist', 'business', 'work', 'student', 'transit'] as $type) {
                if (isset($visaTypes[$type])) {
                    $this->assertIsInt($visaTypes[$type]);
                    $this->assertGreaterThanOrEqual(0, $visaTypes[$type]);
                }
            }
        }
        
        // Validate entry point breakdown
        if (isset($data['by_entry_point']) && is_array($data['by_entry_point'])) {
            foreach ($data['by_entry_point'] as $entryPoint) {
                $this->assertArrayHasKey('entry_point', $entryPoint);
                $this->assertArrayHasKey('count', $entryPoint);
                $this->assertArrayHasKey('percentage', $entryPoint);
                
                $this->assertIsString($entryPoint['entry_point']);
                $this->assertIsInt($entryPoint['count']);
                $this->assertIsFloat($entryPoint['percentage']);
            }
        }
        
        // Validate average stay duration
        if (isset($data['average_stay_duration'])) {
            $this->assertIsFloat($data['average_stay_duration']);
            $this->assertGreaterThanOrEqual(0, $data['average_stay_duration']);
        }
        
        // Validate monthly trends
        if (isset($data['monthly_trends']) && is_array($data['monthly_trends'])) {
            foreach ($data['monthly_trends'] as $trend) {
                $this->assertArrayHasKey('month', $trend);
                $this->assertArrayHasKey('entries', $trend);
                $this->assertArrayHasKey('exits', $trend);
                
                $this->assertMatchesRegularExpression('/^\d{4}-\d{2}$/', $trend['month']);
                $this->assertIsInt($trend['entries']);
                $this->assertIsInt($trend['exits']);
                $this->assertGreaterThanOrEqual(0, $trend['entries']);
                $this->assertGreaterThanOrEqual(0, $trend['exits']);
            }
        }
    }

    public function testSearchForeignCitizensDataStructureValidation(): void
    {
        $searchData = [
            'nationality' => 'Turkish',
            'visa_type' => 'business'
        ];

        $response = $this->postAuthenticated(self::V1_PREFIX . '/foreign-citizens/search', $searchData);
        
        $response->assertOk();
        $data = $response->json('data');
        
        // Validate search metadata
        $this->assertArrayHasKey('total_found', $data);
        $this->assertArrayHasKey('search_time', $data);
        $this->assertArrayHasKey('results', $data);
        
        $this->assertIsInt($data['total_found']);
        $this->assertIsFloat($data['search_time']);
        $this->assertIsArray($data['results']);
        $this->assertGreaterThanOrEqual(0, $data['total_found']);
        $this->assertGreaterThan(0, $data['search_time']);
        
        // Validate search results structure
        foreach ($data['results'] as $result) {
            $this->assertArrayHasKey('id', $result);
            $this->assertArrayHasKey('passport_number', $result);
            $this->assertArrayHasKey('full_name', $result);
            $this->assertArrayHasKey('nationality', $result);
            $this->assertArrayHasKey('relevance_score', $result);
            
            $this->assertIsInt($result['id']);
            $this->assertIsString($result['passport_number']);
            $this->assertIsString($result['full_name']);
            $this->assertIsString($result['nationality']);
            $this->assertIsFloat($result['relevance_score']);
            $this->assertGreaterThanOrEqual(0, $result['relevance_score']);
            $this->assertLessThanOrEqual(1, $result['relevance_score']);
        }
    }
}
