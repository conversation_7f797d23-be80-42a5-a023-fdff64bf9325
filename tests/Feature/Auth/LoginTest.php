<?php

namespace Tests\Feature\Auth;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class LoginTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testUserCanLogin(): void
    {
        $response = $this->postJson(self::V1_PREFIX.'/auth/login', [
            'email' => env('TEST_USERNAME'),
            'password' => env('TEST_PASSWORD'),
        ]);

        $response->assertStatus(200);
    }
}
