<?php

namespace Tests\Feature\Ehdis;

use App\Services\EHDIS\PersonMilitaryInfoService;
use JetBrains\PhpStorm\NoReturn;
use Tests\TestCase;

class EhdisPersonMilitaryServiceTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    #[NoReturn]
    public function test_ehdis_person_military_service_run(): void
    {
        $pin = $this->getTestPin();

        $response = PersonMilitaryInfoService::run($pin);

        $this->ehdisAssertSuccessfulStatus($response);

        $responseData = $response['data'];

        $this->assertResponseHasKeys($response, ['status', 'message']);

        $this->assertArrayHasKey('faultCode', $responseData['response'], 'Response does not contain faultCode key.');
        $this->assertNotEquals("0", $responseData['response']['faultCode'], $responseData['response']['faultString'] ?? "Error");

        $infoList = $responseData['response'];

        $file = 'tests/Feature/MockJson/person_military.json'; // Hedef dosya

        $expectedKeys = $this->getKeysFromFile($file);

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $infoList, "Key $key is missing in the data.");
        }
    }

}
