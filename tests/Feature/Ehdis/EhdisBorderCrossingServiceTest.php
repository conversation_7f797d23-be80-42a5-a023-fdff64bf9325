<?php

namespace Tests\Feature\Ehdis;

use App\Services\EHDIS\CrossingBorderInfoService;
use Tests\TestCase;

class EhdisBorderCrossingServiceTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_ehdis_border_crossing_service_run(): void
    {
        $pin = $this->getTestPassportNumber();

        $response = CrossingBorderInfoService::run($pin);

        $this->ehdisAssertSuccessfulStatus($response);

        $responseData = $response['data'];

        $this->assertResponseHasKeys($response, ['status', 'message']);

        $this->assertArrayHasKey('faultCode', $responseData['response'], 'Response does not contain faultCode key.');
        $this->assertNotEquals(0, $responseData['response']['faultCode'], $responseData['response']['faultString'] ?? "Error");

        $infoList = $responseData['response']['response'];

        $this->assertArrayHasKey('Completed', $infoList, 'Response does not contain Completed key.');

        if (isset($infoList['ErrorMessage'])) {
            $this->assertStringNotContainsString(
                'ServiceError',
                $infoList['Completed'],
                $infoList['ErrorMessage'] ?: 'The Completed field contains ServiceError.'
            );
        }
    }


}
