<?php

namespace Tests\Feature\Ehdis;

use App\Services\EHDIS\PersonMilitaryInfoService;
use App\Services\EHDIS\TQDKInfoService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class EhdisTqdkInfoServiceTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function test_ehdis_tqdk_info_service_run(): void
    {
        $pin = $this->getTestPin();

        $response = TQDKInfoService::run($pin);

        $this->ehdisAssertSuccessfulStatus($response);

        $responseData = $response['data'];

        $this->assertResponseHasKeys($response, ['status', 'message']);

        $this->assertArrayHasKey('faultCode', $responseData['response'], 'Response does not contain faultCode key.');
        $this->assertNotEquals("0", $responseData['response']['faultCode'], $responseData['response']['faultString'] ?? "Error");

        $infoList = $responseData['response'];

        $expectedKeys = $this->getArrayKeys($infoList);

        foreach ($expectedKeys as $key) {
            $this->assertArrayHasKey($key, $infoList, "Key $key is missing in the data.");
        }
    }
}
