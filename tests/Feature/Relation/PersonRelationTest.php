<?php

namespace Tests\Feature\Relation;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class PersonRelationTest extends TestCase
{
    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testPersonSingleRelation(): void
    {


        $params = [
            'value' => env('TEST_PIN'),
            'to' => '',
            'relation_level' => 1,
            'search_type' => 'basic',
            'filter_type' => 'pin',
            'types' => [
                'Classmate',
                'Facebook',
                'Family',
                'Relative',
                'Neighbor',
                'University',
                'Workmate',
                'Resident',
                'cdr_call_count'
            ],
            'page_number' => 1,
            'per_page' => 30
        ];

        $url = self::V1_PREFIX . '/relation/get-person-subgraph-new?' . http_build_query($params);

        $response = $this->getJson($url);
        $response->assertOk();

        $responseData = $response->json();
        $this->assertIsArray($responseData);

        $response->assertOk();
        $this->assertAuthenticated('api');

        $response->assertStatus(200);
    }


    /**
     * A basic feature test example.
     *
     * @return void
     */
    public function testBetweenTwoPersonRelation(): void
    {

        $response = $this->getJson(self::V1_PREFIX.'/relation/between-two-person-v2?from='.env('TEST_PIN').'
        &to='.env('TEST_PIN2').'
        &search_type=basic&filter_type=pin&types[]=Classmate&types[]=Facebook&types[]=Family&types[]=Relative
        &types[]=Neighbor&types[]=University&types[]=Workmate&types[]=Resident&types[]=cdr_call_count
        &page_number=1&per_page=30');
        $response->assertOk();
        $this->assertAuthenticated('api');

        $response->assertStatus(200);
    }
}
