<?php

namespace Tests\Feature;

use App\Models\Object\Object_;
use App\Models\Object\ObjectType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Symfony\Component\HttpFoundation\Response;
use Tests\ActingAsTrait;
use Tests\TestCase;

class ObjectTest extends TestCase
{
    use ActingAsTrait, WithFaker;
    /**
     * A basic feature test example.
     *
     * @return void
     */

    public function testIndex()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/objects', ['page' => 1]);

        $response->assertOk();
    }

    public function testLists()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/objects/list/all');

        $response->assertOk();
    }

    public function testSearch()
    {
        $user = User::first();
        $object = Object_::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/objects/search/list?q=' . $object->name);

        $response->assertOk();
    }

    public function testStore()
    {
        $user = User::first();
        $fakerName = $this->faker->name;

        $response = $this
            ->actingAs($user, 'api')
            ->post('api/v1/objects', [
                'name' => $fakerName,
                'object_type_id' => ObjectType::first()->id,
                'address' => $this->faker->address,
            ]);

        Object_::query()
            ->where(['name' => $fakerName])
            ->forceDelete();

        $response->assertCreated();
    }

    public function testUpdate()
    {
        $user = User::first();
        $object = Object_::first();

        $this->actingAs($user, 'api')
            ->putJson('api/v1/objects/' . $object->id, [
                'name' => $this->faker->name,
                'object_type_id' => ObjectType::first()->id,
                'address' => $object->address,
            ])
            ->assertOk();
    }

    public function testDelete()
    {
        $user = User::first();

        $created = Object_::query()
            ->create([
                'name' => $this->faker->name,
                'address' => $this->faker->address,
                'object_type_id' => ObjectType::first()->id,
            ]);

        $response = $this
            ->actingAs($user, 'api')
            ->delete('api/v1/objects/' . $created->id);

        $response->assertOk();
    }
}
