<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Symfony\Component\HttpFoundation\Response;
use Tests\ActingAsTrait;
use Tests\TestCase;

class UserTest extends TestCase
{
    use ActingAsTrait, WithFaker;
    /**
     * A basic feature test example.
     *
     * @return void
     */

    public function testMe()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/me');

        $response->assertOk();
    }

    public function testIndex()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/users', ['page' => 1]);

        $response->assertOk();
    }

    public function testShow()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/users/' . $user->id);

        $response->assertOk();
    }

    public function testStore()
    {
        $user = User::first();
        $fakerEmail = $this->faker->email;

        $response = $this
            ->actingAs($user, 'api')
            ->post('api/v1/users', [
                'email' => $fakerEmail,
                'name' => $this->faker->name,
                'surname' => $this->faker->lastName,
                'password' => $this->faker->password,
                'roles' => [1]
            ]);

        User::query()
            ->where(['email' => $fakerEmail])
            ->forceDelete();

        $response->assertCreated();
    }

    public function testUpdate()
    {
        $user = User::first();

        $this->actingAs($user, 'api')
            ->putJson('api/v1/users/' . $user->id, [
                'name' => $user->name,
                'surname' => $user->surname,
                'email' => $user->email,
                'roles' => [1]
            ])
            ->assertOk();
    }

    public function testDelete()
    {
        $user = User::first();
        $fakerEmail = $this->faker->email;

        $created = User::query()
            ->create([
                'email' => $fakerEmail,
                'name' => $this->faker->name,
                'surname' => $this->faker->lastName,
                'password' => $this->faker->password,
            ]);

        $response = $this
            ->actingAs($user, 'api')
            ->delete('api/v1/users/' . $created->id);

        $response->assertStatus(Response::HTTP_NO_CONTENT);
    }
}
