<?php

namespace Tests\Feature;

use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\ActingAsTrait;
use Tests\TestCase;

class SurveillanceTest extends TestCase
{
    use ActingAsTrait;
    /**
     * A basic feature test example.
     *
     * @return void
     */

    protected string $sessionName = 'ea27060e-da12-4728-9d39-116f502c8ffe';

    public function testSearch()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->post('api/v1/surveillance/search', [
                'similarity' => 0.1,
                'max_count' => 5,
                'camera_ids' => [],
                'date_from' => Carbon::now()->subDays(2)->format('d.m.Y H:i'),
                'date_to' => Carbon::now()->format('d.m.Y H:i'),
                'photo' => file_get_contents(storage_path('app/photo-binary.txt'))
            ]);

        $response->assertOk();
    }

    public function testShow()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/surveillance/show/' . $this->sessionName);

        $response->assertOk();
    }
}
