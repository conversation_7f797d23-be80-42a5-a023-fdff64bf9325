<?php

namespace Tests\Feature;

use App\Models\Object\ObjectType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Symfony\Component\HttpFoundation\Response;
use Tests\ActingAsTrait;
use Tests\TestCase;

class ObjectTypeTest extends TestCase
{
    use ActingAsTrait, WithFaker;
    /**
     * A basic feature test example.
     *
     * @return void
     */

    public function testIndex()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/objectTypes', ['page' => 1]);

        $response->assertOk();
    }

    public function testLists()
    {
        $user = User::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/objectTypes/list/all');

        $response->assertOk();
    }

    public function testSearch()
    {
        $user = User::first();
        $objectType = ObjectType::first();

        $response = $this
            ->actingAs($user, 'api')
            ->get('api/v1/objectTypes/search/list?q=' . $objectType->name);

        $response->assertOk();
    }

    public function testStore()
    {
        $user = User::first();
        $fakerName = $this->faker->name;

        $response = $this
            ->actingAs($user, 'api')
            ->post('api/v1/objectTypes', [
                'name' => $fakerName,
            ]);

        ObjectType::query()
            ->where(['name' => $fakerName])
            ->forceDelete();

        $response->assertCreated();
    }

    public function testUpdate()
    {
        $user = User::first();
        $objectType = ObjectType::first();

        $this->actingAs($user, 'api')
            ->putJson('api/v1/objectTypes/' . $objectType->id, [
                'name' => $this->faker->name,
            ])
            ->assertOk();
    }

    public function testDelete()
    {
        $user = User::first();

        $created = ObjectType::query()
            ->create([
                'name' => $this->faker->name,
            ]);

        $response = $this
            ->actingAs($user, 'api')
            ->delete('api/v1/objectTypes/' . $created->id);

        $response->assertOk();
    }
}
