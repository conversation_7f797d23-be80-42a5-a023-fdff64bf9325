FROM composer:2.8.5 as vendor

WORKDIR /app

COPY ./packages /app/packages
RUN ls -la ./packages
COPY composer.json composer.json
#COPY composer.lock composer.lock

RUN composer install --ignore-platform-reqs --no-scripts -vvv



FROM php:8.1.9-fpm-buster

RUN apt-get update
RUN apt-get install -y build-essential libssl-dev zlib1g-dev libpng-dev libjpeg-dev libfreetype6-dev
RUN apt-get install -y libzip-dev zip
RUN apt-get install -y libpq-dev  # Required for PostgreSQL support
RUN apt-get install -y nginx


# Install PHP extensions
RUN printf "\n" | pecl install mongodb && docker-php-ext-enable mongodb
RUN docker-php-ext-install pdo pdo_mysql pdo_pgsql gd
RUN docker-php-ext-install zip

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

WORKDIR /var/www/html

COPY --from=vendor app/vendor/ ./vendor/
COPY --from=vendor app/composer.lock ./composer.lock
COPY . .
COPY nginx/default.conf /etc/nginx/sites-enabled/default

RUN chmod -R gu+w storage
RUN chmod -R gu+w bootstrap/cache
RUN chmod  +x docker-entrypoint.sh

# Run commands
ENTRYPOINT ["/bin/bash", "./docker-entrypoint.sh"]
