# Comprehensive Swagger/OpenAPI Documentation for Beein API

## Overview

This document provides a complete guide to the Swagger/OpenAPI documentation implementation for the Beein API project. The documentation covers all API endpoints with comprehensive schemas, authentication, and interactive testing capabilities.

## 📋 Table of Contents

1. [Implementation Overview](#implementation-overview)
2. [API Documentation Structure](#api-documentation-structure)
3. [Authentication & Security](#authentication--security)
4. [Endpoint Categories](#endpoint-categories)
5. [Schema Definitions](#schema-definitions)
6. [Usage Instructions](#usage-instructions)
7. [Testing & Validation](#testing--validation)
8. [Maintenance & Updates](#maintenance--updates)

## 🚀 Implementation Overview

### What's Been Implemented

- **Complete API Documentation**: All major endpoints documented with OpenAPI 3.0 specifications
- **Interactive Swagger UI**: Accessible web interface for testing APIs
- **Comprehensive Schemas**: Detailed request/response models with validation rules
- **Authentication Integration**: JWT Bearer token authentication setup
- **Organized Structure**: Logical grouping by functionality with consistent naming
- **Validation & Testing**: Automated tools for documentation validation

### Key Features

- ✅ **Authentication Endpoints**: Login, logout, refresh, profile management
- ✅ **Person Management**: CRUD operations with advanced search
- ✅ **Blacklist Operations**: Complete blacklist management system
- ✅ **Camera Management**: Camera monitoring and configuration
- ✅ **Search Functionality**: Face search, global search, advanced filters
- ✅ **Elasticsearch Integration**: Advanced search with aggregations
- ✅ **Server Monitoring**: Health checks and status monitoring
- ✅ **Error Handling**: Comprehensive error response documentation

## 📁 API Documentation Structure

### File Organization

```
app/Swagger/
├── Controller.php              # Main API info and security schemes
├── AuthController.php          # Authentication endpoints
├── PersonController.php        # Person management endpoints
├── BlacklistController.php     # Blacklist management endpoints
├── CameraController.php        # Camera management endpoints
├── SearchController.php        # Search functionality endpoints
├── ElasticSearchController.php # Elasticsearch operations
└── ServerStatusController.php  # Server monitoring endpoints
```

### Configuration Files

- **`config/l5-swagger.php`**: L5-Swagger package configuration
- **`generate-swagger-docs.sh`**: Documentation generation and validation script

## 🔐 Authentication & Security

### Security Scheme

The API uses JWT Bearer token authentication:

```yaml
securitySchemes:
  bearerAuth:
    type: http
    scheme: bearer
    bearerFormat: JWT
    description: "JWT Authorization header using the Bearer scheme"
```

### Authentication Flow

1. **Login**: `POST /api/v1/auth/login`
   - Accepts email/password credentials
   - Returns JWT access token with user information

2. **Token Usage**: Include in Authorization header
   ```
   Authorization: Bearer {your-jwt-token}
   ```

3. **Token Refresh**: `POST /api/v1/auth/refresh`
   - Extends token validity without re-authentication

4. **Logout**: `POST /api/v1/auth/logout`
   - Invalidates the current token

## 📊 Endpoint Categories

### 1. Authentication (`/api/v1/auth/*`)
- **Login**: User authentication with credentials
- **Mobile Login**: Mobile-specific authentication
- **Logout**: Token invalidation
- **Refresh**: Token renewal
- **Profile**: Current user information

### 2. Person Management (`/api/v1/persons/*`)
- **List Persons**: Paginated person listing with filters
- **Get Person**: Individual person details
- **Create Person**: New person registration
- **Update Person**: Person information updates
- **Delete Person**: Person removal
- **Job Information**: Employment data by PIN

### 3. Blacklist Management (`/api/v1/blacklist/*`)
- **List Blacklist**: Paginated blacklist entries
- **Get Entry**: Individual blacklist details
- **Create Entry**: New blacklist addition with photo upload
- **Update Entry**: Blacklist information updates
- **Delete Entry**: Blacklist removal

### 4. Camera Management (`/api/v1/cameras/*`)
- **List Cameras**: Camera inventory with filtering
- **Get Camera**: Individual camera details
- **Create Camera**: New camera registration
- **Update Camera**: Camera configuration updates
- **Delete Camera**: Camera removal

### 5. Search Operations (`/api/v1/search/*`)
- **Person Search**: Multi-criteria person search
- **Blacklist Search**: Blacklist entry search
- **Face Search**: Image-based facial recognition
- **Global Search**: Cross-entity search
- **Advanced Search**: Complex filtering and sorting
- **Search Suggestions**: Auto-complete functionality

### 6. Elasticsearch (`/api/v1/elastic/*`)
- **Person Search**: Elasticsearch-powered person search
- **Border Crossings**: Immigration record search
- **VOEN Search**: Company registration search
- **Social Accounts**: Social media profile search
- **Advanced Query**: Custom Elasticsearch DSL
- **Scroll Results**: Large dataset pagination

### 7. Server Status (`/api/v1/server-status/*`)
- **All Statuses**: Complete system health overview
- **Database Status**: PostgreSQL and MongoDB health
- **Backend Status**: API server health
- **Elasticsearch Status**: Search cluster health
- **Redis Status**: Cache server health
- **System Health**: Overall health metrics
- **Performance Metrics**: System performance data

## 🏗️ Schema Definitions

### Core Entities

#### User Schema
```yaml
User:
  type: object
  properties:
    id: integer
    name: string
    surname: string
    email: string (email format)
    entity_name: string
    phone_number: string
    status: integer (1=active, 0=inactive)
    is_system: boolean
    created_at: string (date-time)
    updated_at: string (date-time)
```

#### Person Schema
```yaml
Person:
  type: object
  properties:
    id: integer
    name: string
    surname: string
    father_name: string
    pin: string
    doc_type: string
    doc_serial_number: string
    doc_number: string
    is_sync: boolean
    data: object
    created_at: string (date-time)
    updated_at: string (date-time)
```

#### Blacklist Schema
```yaml
Blacklist:
  type: object
  properties:
    id: integer
    name: string
    surname: string
    father_name: string
    birthdate: string (date)
    document_number: string
    pin: string
    photo: string
    note: string
    gender: string (enum: male, female)
    status: integer (1=active, 0=inactive)
    created_at: string (date-time)
    updated_at: string (date-time)
```

### Response Patterns

#### Success Response
```yaml
SuccessResponse:
  type: object
  properties:
    success: boolean (true)
    status: integer (200-299)
    message: string
    data: object
```

#### Error Response
```yaml
ErrorResponse:
  type: object
  properties:
    success: boolean (false)
    status: integer (400-599)
    message: string
    errors: object (validation errors)
```

#### Paginated Response
```yaml
PaginatedResponse:
  type: object
  properties:
    success: boolean
    status: integer
    message: string
    data: array
    links: object (pagination links)
    meta: object (pagination metadata)
```

## 🎯 Usage Instructions

### Accessing Swagger UI

1. **Start the Laravel server**:
   ```bash
   php artisan serve
   ```

2. **Access Swagger UI**:
   ```
   http://localhost:8000/api/documentation
   ```

3. **View JSON Documentation**:
   ```
   http://localhost:8000/docs/api-docs.json
   ```

### Authentication in Swagger UI

1. **Login via API**:
   - Use the `/api/v1/auth/login` endpoint
   - Copy the `access_token` from the response

2. **Authorize in Swagger UI**:
   - Click the "Authorize" button (🔒)
   - Enter: `Bearer {your-access-token}`
   - Click "Authorize"

3. **Test Protected Endpoints**:
   - All secured endpoints will now include the authorization header

### Testing API Endpoints

1. **Select an endpoint** from the Swagger UI
2. **Click "Try it out"**
3. **Fill in required parameters**
4. **Click "Execute"**
5. **Review the response**

## 🧪 Testing & Validation

### Generate Documentation

Use the provided script to generate and validate documentation:

```bash
# Generate and validate everything
./generate-swagger-docs.sh all

# Only generate documentation
./generate-swagger-docs.sh generate

# Only validate annotations
./generate-swagger-docs.sh validate

# Test Swagger UI accessibility
./generate-swagger-docs.sh test

# Generate summary report
./generate-swagger-docs.sh summary
```

### Manual Validation

1. **Check JSON validity**:
   ```bash
   php artisan l5-swagger:generate
   cat storage/api-docs/api-docs.json | jq .
   ```

2. **Validate OpenAPI specification**:
   - Use online validators like [Swagger Editor](https://editor.swagger.io/)
   - Upload the generated JSON file

3. **Test endpoints**:
   - Use Swagger UI for interactive testing
   - Use tools like Postman or curl for automated testing

## 🔧 Maintenance & Updates

### Adding New Endpoints

1. **Create Swagger annotations** in the appropriate controller file
2. **Define request/response schemas** if needed
3. **Regenerate documentation**:
   ```bash
   php artisan l5-swagger:generate
   ```

### Updating Existing Documentation

1. **Modify annotations** in the relevant Swagger controller file
2. **Update schemas** if data structures changed
3. **Regenerate and validate**:
   ```bash
   ./generate-swagger-docs.sh all
   ```

### Best Practices

1. **Consistent Naming**: Use consistent naming conventions for operations and schemas
2. **Detailed Descriptions**: Provide clear descriptions for all endpoints and parameters
3. **Example Values**: Include realistic example values in schemas
4. **Error Documentation**: Document all possible error responses
5. **Version Control**: Keep documentation in sync with API changes
6. **Regular Validation**: Run validation scripts regularly during development

### Configuration Updates

#### Environment Variables

Set these in your `.env` file:

```env
L5_SWAGGER_CONST_HOST=http://localhost:8000
L5_SWAGGER_GENERATE_ALWAYS=true
L5_SWAGGER_UI_DOC_EXPANSION=none
L5_SWAGGER_UI_FILTERS=true
```

#### Production Settings

For production environments:

```env
L5_SWAGGER_GENERATE_ALWAYS=false
L5_SWAGGER_CONST_HOST=https://api.beein.az
```

## 📈 Benefits

### For Developers
- **Clear API Contract**: Understand exactly what each endpoint expects and returns
- **Interactive Testing**: Test APIs directly from the browser
- **Code Generation**: Generate client SDKs from the OpenAPI specification
- **Validation**: Ensure requests/responses match the documented schema

### For Frontend Teams
- **Self-Service**: Access complete API documentation without asking backend team
- **Real-Time Testing**: Test API changes immediately
- **Integration Planning**: Plan frontend integration based on documented schemas

### For QA Teams
- **Test Planning**: Use documentation to plan comprehensive test cases
- **Automated Testing**: Generate test cases from OpenAPI specifications
- **Regression Testing**: Ensure API changes don't break existing functionality

### For DevOps/Monitoring
- **Health Checks**: Monitor API health using documented endpoints
- **Performance Testing**: Use documented endpoints for load testing
- **Documentation Deployment**: Deploy documentation alongside API updates

## 🚀 Next Steps

1. **Review Generated Documentation**: Check all endpoints are properly documented
2. **Test Authentication Flow**: Verify JWT authentication works in Swagger UI
3. **Validate All Endpoints**: Test each endpoint category thoroughly
4. **Set Up CI/CD Integration**: Automate documentation generation in deployment pipeline
5. **Train Team Members**: Ensure all team members know how to use and maintain the documentation

This comprehensive Swagger implementation provides a solid foundation for API documentation, testing, and maintenance. The interactive documentation will significantly improve developer experience and API adoption.
