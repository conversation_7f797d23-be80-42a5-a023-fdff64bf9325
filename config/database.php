<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'pgsql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'url' => env('DATABASE_URL'),
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
            'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        ],

        'mysql' => [
            'driver' => 'mysql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => true,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsql_taxes' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE_TAXES', 'taxes'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsql_social' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST_SOCIAL', '127.0.0.1'),
            'port' => env('DB_PORT_SOCIAL', '5432'),
            'database' => env('DB_DATABASE_SOCIAL', 'forge'),
            'username' => env('DB_USERNAME_SOCIAL', 'forge'),
            'password' => env('DB_PASSWORD_SOCIAL', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],
        'pgsql_postgis' => [
            'driver' => 'pgsql',
            'url' => env('DATABASE_URL'),
            'host' => env('POSTGIS_HOST', '127.0.0.1'),
            'port' => env('POSTGIS_PORT', '5432'),
            'database' => env('POSTGIS_DATABASE', 'forge'),
            'username' => env('POSTGIS_USERNAME', 'forge'),
            'password' => env('POSTGIS_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'url' => env('DATABASE_URL'),
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

        'mongodb' => [
            'driver' => 'mongodb',
            'host' => env('MONGO_DB_HOST', '127.0.0.1'),
            'port' => env('MONGO_DB_PORT', 27017),
            'database' => env('MONGO_DB_DATABASE', 'forge'),
            'username' => env('MONGO_DB_USERNAME', 'forge'),
            'password' => env('MONGO_DB_PASSWORD', ''),
            'options' => [
                'database' => env('MONGO_DB_DATABASE')
            ],
        ],

        'mongodb-log' => [
            'driver' => 'mongodb',
            'host' => env('MONGO_DB_HOST_LOG', '127.0.0.1'),
            'port' => env('MONGO_DB_PORT_LOG', 27017),
            'database' => env('MONGO_DB_DATABASE_LOG', 'forge'),
            'username' => env('MONGO_DB_USERNAME_LOG', 'forge'),
            'password' => env('MONGO_DB_PASSWORD_LOG', ''),
            'options' => [
                'database' => env('MONGO_DB_DATABASE_LOG')
            ],
        ],

        'clickhouse' => [
            'driver' => 'clickhouse',
            'host' => env('CLICKHOUSE_HOST'),
            'port' => env('CLICKHOUSE_PORT','8123'),
            'database' => env('CLICKHOUSE_DATABASE','default'),
            'username' => env('CLICKHOUSE_USERNAME','default'),
            'password' => env('CLICKHOUSE_PASSWORD',''),
            'timeout_connect' => env('CLICKHOUSE_TIMEOUT_CONNECT',2),
            'timeout_query' => env('CLICKHOUSE_TIMEOUT_QUERY',2),
            'https' => (bool)env('CLICKHOUSE_HTTPS', null),
            'retries' => env('CLICKHOUSE_RETRIES', 0),
            'settings' => [ // optional
                'max_partitions_per_insert_block' => 300,
            ],
        ],

        'clickhouse_radius' => [
            'driver' => 'clickhouse',
            'host' => env('CLICKHOUSE_RADIUS_HOST'),
            'port' => env('CLICKHOUSE_RADIUS_PORT','8123'),
            'database' => env('CLICKHOUSE_RADIUS_DATABASE','default'),
            'username' => env('CLICKHOUSE_RADIUS_USERNAME','default'),
            'password' => env('CLICKHOUSE_RADIUS_PASSWORD',''),
            'timeout_connect' => env('CLICKHOUSE_RADIUS_TIMEOUT_CONNECT',2),
            'timeout_query' => env('CLICKHOUSE_RADIUS_TIMEOUT_QUERY',2),
            'https' => (bool)env('CLICKHOUSE_RADIUS_HTTPS', null),
            'retries' => env('CLICKHOUSE_RADIUS_RETRIES', 0),
            'readonly' => false,
            'settings' => [ // optional
                'max_partitions_per_insert_block' => 300,
                'max_query_size' => 100000000
            ],
        ],
        'clickhouse_save' => [
            'driver' => 'clickhouse',
            'host' => env('CLICKHOUSE_SAVE_HOST'),
            'port' => env('CLICKHOUSE_SAVE_PORT','8123'),
            'database' => env('CLICKHOUSE_SAVE_DATABASE','default'),
            'username' => env('CLICKHOUSE_SAVE_USERNAME','default'),
            'password' => env('CLICKHOUSE_SAVE_PASSWORD',''),
            'timeout_connect' => env('CLICKHOUSE_SAVE_TIMEOUT_CONNECT',2),
            'timeout_query' => env('CLICKHOUSE_SAVE_TIMEOUT_QUERY',2),
            'https' => (bool)env('CLICKHOUSE_SAVE_HTTPS', null),
            'retries' => env('CLICKHOUSE_SAVE_RETRIES', 0),
            'settings' => [ // optional
                'max_partitions_per_insert_block' => 300,
            ],
        ],

        'elasticsearch' => [
            'driver' => 'elasticsearch',
            'hosts' => [
                [
                    'host' => env('ELASTICSEARCH_HOST', 'localhost'),
                    'port' => env('ELASTICSEARCH_PORT', 9200),
                    'scheme' => env('ELASTICSEARCH_SCHEME', 'http'),
                    'user' => env('ELASTICSEARCH_USER'),
                    'pass' => env('ELASTICSEARCH_PASS'),
                ],
            ],
            'hosts_2' => [
                [
                    'host' => env('ELASTICSEARCH_HOST_2', 'localhost'),
                    'port' => env('ELASTICSEARCH_PORT_2', 9200),
                    'scheme' => env('ELASTICSEARCH_SCHEME_2', 'http'),
                    'user' => env('ELASTICSEARCH_USER_2'),
                    'pass' => env('ELASTICSEARCH_PASS_2'),
                ],
            ],
        ],
        'saved_results' => [
            'driver' => env('SAVED_RESULTS_CONNECTION', 'pgsql'),
            'host' => env('SAVED_RESULTS_HOST', '127.0.0.1'),
            'port' => env('SAVED_RESULTS_PORT', '5432'),
            'database' => env('SAVED_RESULTS_DATABASE', 'forge'),
            'username' => env('SAVED_RESULTS_USERNAME', 'forge'),
            'password' => env('SAVED_RESULTS_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'disable',
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => env('REDIS_CLIENT', 'phpredis'),

        'options' => [
            'cluster' => env('REDIS_CLUSTER', 'redis'),
            'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
        ],

        'default' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_DB', '0'),
        ],

        'cache' => [
            'url' => env('REDIS_URL'),
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', '6379'),
            'database' => env('REDIS_CACHE_DB', '1'),
        ],

    ],

];
