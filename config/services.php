<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'x_api_key' => env('X_API_KEY', 'x-api-key'),
    'loc_base_url' => env('LOC_BASE_URL', 'http://localhost:8000'),
    'rust_base_url' => env('RUST_BASE_URL'),
    'drill_query_url' => env('DRILL_QUERY_URL'),
    'rust_api_key'=> env('RUST_API_KEY'),
    'dev' => [
        'key' => env('DEV_KEY_WITHOUT_JWT','b7f910fa-c9dd-4d39-8797-83c778e2f76f'),
    ],
    'camera' => [
        'screen_shot_url' => env('SCREEN_SHOT_URL'),
        'surveillance_url' => env('CAMERA_SURVEILLANCE_URL'),
    ],
    'drill' => [
        'username' => env('DRILL_USERNAME'),
        'password' => env('DRILL_PASSWORD'),
    ],
    'user_header_key' => env('USER_HEADER_KEY', 'x-user-id'),
    'default_auth_middleware' => env('DEFAULT_AUTH_MIDDLEWARE','jwt.verify'),
    'mock' => [
        'is_mock_etibarname'=>env('IS_MOCK_ETIBARNAME'),
        'is_mock_fines'=>env('IS_MOCK_FINES'),
        'is_mock_driver_license'=>env('IS_MOCK_DRIVER_LICENSE'),
    ]
];
