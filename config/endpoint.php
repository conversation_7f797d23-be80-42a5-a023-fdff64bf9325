<?php

return [
    'student' => env('STUDENT_HOST', 'http://***********:8480').'/student.php',
    'find-related-fins-from-facebook' => config('servers.person_host').'/find-related-fins/',
    'find-related-fins-s3' => env('FRONTEND_SERVER', 'http://***********:8580').'/bot-pics/',
    'find-person-by-home' => env('IAMAS_PERSON', 'http://**********:5099').'/api/home',
    'find-person-by-mobile' => env('IAMAS_PERSON', 'http://**********:5099').'/api/mobile',
    'find-vehicle-by-pin' => env('VEHICLE_HOST', 'http://***********:8185').'/v1/eIntegration/out/StateSecurityServiceWS/getVehicleForSSS/',
    'find-vehicle-by-pin-pass' => '123123',
    'social_calls_enabled' => env('SOCIAL_CALLS_ENABLED',false),
    'aws_file_url' => env('AWS_FILE_URL', 'http://***********:8080/'),
];
