<?php

return array(

    /*
    |--------------------------------------------------------------------------
    | Custom Elasticsearch Client Configuration
    |--------------------------------------------------------------------------
    |
    | This array will be passed to the Elasticsearch client.
    | See configuration options here:
    |
    | http://www.elasticsearch.org/guide/en/elasticsearch/client/php-api/current/_configuration.html
    */

    'config' => [
        'hosts'     => [
            [
                'host' => env('ELASTIC_HOST', '***********'),
                'port' => env('ELASTIC_PORT', '9200'),
                'scheme' => env('ELASTIC_SCHEMA', 'https'),
                'user' => env('ELASTIC_USER', 'elastic'),
                'pass' => env('ELASTIC_PASS', '4d4riskx5PpCrPEH0YbU')
            ],
        ],
        'retries'   => 1,
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Index Name
    |--------------------------------------------------------------------------
    |
    | This is the index name that Elasticated will use for all
    | Elasticated models.
    */

    'default_index' => 'articles',

);
