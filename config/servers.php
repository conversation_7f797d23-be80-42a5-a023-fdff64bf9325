<?php


return [

    'sync_api' => env('SYNC_API',false),

    'nebula' => env('NEBULA_HOST', 'http://***********:5001'),

    'nebula_path' => env('NEBULA_PATH', '***********'),

    'milvus' => env('MILVUS_HOST', '***********'),

    'ngraph' => env('NGRAPH_CLIENT', '***********'),

    'ngraph_port' => env('NGRAPH_PORT', '9669'),
    'ngraph_username' => env('NGRAPH_USERNAME'),
    'ngraph_password' => env('NGRAPH_PASSWORD'),

    's3_beein' => env('S3_BEEIN_LOC', 'http://s3.beein.loc'),

    'frontend' => env('FRONTEND_SERVER', 'http://***********:8580'),

    'backend' => env('BACKEND_SERVER', 'http://***********:88'),

    'iamas_host' => env('IAMAS_HOST', 'http://***********:90'),
    'dtx_webservice' => env('IAMAS_WEBSERVICE', 'http://**********:8081'),
    'dyp_webservice' => env('DYP_WEBSERVICE', 'http://**********:7099'),
    'iamas_person' => env('IAMAS_PERSON', 'http://**********:5099'),

    'vehicle_host' => env('VEHICLE_HOST', 'http://***********:8185'),
    'vehicle_second_host' => env('VEHICLE_SECOND_HOST', 'http://***********:8191'),

    'work_host' => env('WORK_HOST', 'http://***********'),
    'person_host' => env('PERSON_HOST', 'http://***********:5000'),
    'student_host' => env('STUDENT_HOST', 'http://***********:8480'),

    'ehdis' => env('EHDIS_HOST', 'http://**********:8080'),
//    'ehdis' => 'http://localhost:8080',

    'gpu_api_v1' => env('GPU_API_V1', 'http://***********:8000/'),
    'gpu_api_v2' => env('GPU_API_V2', 'http://***********:8000/v2'),
    'gpu_api_v3' => env('GPU_API_V3', ''),
    'gpu_api_predict' => env('GPU_API_PREDICT', 'http://***********:8123/predict'),
    'gpu_api_predict_file' => env('GPU_API_PREDICT_FILE', 'http://***********:8123/predict_file'),

    'gpu_api_single' => env('GPU_API_SINGLE', 'http://***********:8000'),
    'similar_image_host' => env('SIMILAR_IMAGE_HOST', 'http://***********/'),

    'camera_archive_url' => env('CAMERA_ARCHIVE_URL', 'http://***********'),

    's3_bucket_server' => env('S3_BUCKET_SERVER', 'http://***********:8580'),

    'audit_log_url' => env('AUDIT_LOG_URL', 'http://api.beein.loc:8082'),
    'audit_log_token' => env('AUDIT_LOG_TOKEN', 'aW4iLCJJc3N1ZXIiOiJJc3N1ZXIiLCJVc2VybmFtImV4cCI6MTcy'),



    'social_face_embed' => env('SOCIAL_FACE_EMBEDS', '***********:5000/search'),
    'social_image_embed' => env('SOCIAL_IMAGE_EMBEDS', '***********:5001/search'),

    'gsm_audio_bucket' => env('GSM_AUDIO_BUCKET', ''),

];
