PHPUnit 9.6.23 by <PERSON> and contributors.

[4mDatabase Integration (Tests\Integration\DatabaseIntegration)[0m
 [33m✘[0m Database connections work
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m User model relationships
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Person model operations
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Blacklist model operations
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Camera object relationships
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Complex queries
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Transaction rollback
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Transaction commit
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Bulk operations
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Indexes and performance
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Database constraints
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Foreign key constraints
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Soft deletes
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

 [33m✘[0m Database seeding
   [33m┐[0m
   [33m├[0m [43;30mPDOException: SQLSTATE[08006] [7] connection to server at "127.0.0.1", port 5432 failed: Connection refused[0m
   [33m├[0m [43;30m	Is the server running on that host and accepting TCP/IP connections?                                      [0m
   [33m│[0m
   [33m╵[0m [2m/[22mUsers[2m/[22mraw[2m/[22mHerd[2m/[22mapi-v-1-service[2m/[22mvendor[2m/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mDatabase[2m/[22mConnectors[2m/[22mConnector.php[2m:[22m[34m70[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php[22m[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mPostgresConnector.php[2m:[22m[34m32[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Connectors/[22mConnectionFactory.php[2m:[22m[34m184[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConnection.php[2m:[22m[34m1064[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConcerns[2m/[22mManagesTransactions.php[2m:[22m[34m139[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m115[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Concerns/ManagesTransactions.php[22m[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m402[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m202[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m167[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/Migrations/Migrator.php[22m[2m:[22m[34m112[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m84[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mMigrations[2m/[22mMigrator.php[2m:[22m[34m606[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Database/[22mConsole[2m/[22mMigrations[2m/[22mMigrateCommand.php[2m:[22m[34m77[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mContainer[2m/[22mBoundMethod.php[2m:[22m[34m36[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mUtil.php[2m:[22m[34m40[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mBoundMethod.php[2m:[22m[34m93[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php[22m[2m:[22m[34m35[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/[22mContainer.php[2m:[22m[34m653[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mConsole[2m/[22mCommand.php[2m:[22m[34m136[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mCommand[2m/[22mCommand.php[2m:[22m[34m298[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mCommand.php[2m:[22m[34m120[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22msymfony[2m/[22mconsole[2m/[22mApplication.php[2m:[22m[34m1040[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m301[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/symfony/console/Application.php[22m[2m:[22m[34m171[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/[22mlaravel[2m/[22mframework[2m/[22msrc[2m/[22mIlluminate[2m/[22mConsole[2m/[22mApplication.php[2m:[22m[34m94[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Console/Application.php[22m[2m:[22m[34m185[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mConsole[2m/[22mKernel.php[2m:[22m[34m263[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mTesting[2m/[22mPendingCommand.php[2m:[22m[34m260[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Testing/PendingCommand.php[22m[2m:[22m[34m413[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/[22mFoundation[2m/[22mTesting[2m/[22mConcerns[2m/[22mInteractsWithConsole.php[2m:[22m[34m66[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mRefreshDatabase.php[2m:[22m[34m45[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/RefreshDatabase.php[22m[2m:[22m[34m20[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/[22mTestCase.php[2m:[22m[34m122[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/TestCase.php[22m[2m:[22m[34m91[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/[22mtests[2m/[22mTestCase.php[2m:[22m[34m23[0m
   [33m╵[0m [2m/Users/<USER>/Herd/api-v-1-service/tests/[22mIntegration[2m/[22mDatabaseIntegrationTest.php[2m:[22m[34m22[0m
   [33m┴[0m

Time: 00:02.130, Memory: 58.50 MB


[37;41mERRORS![0m
[37;41mTests: 14[0m[37;41m, Assertions: 0[0m[37;41m, Errors: 14[0m[37;41m.[0m

Generating code coverage report in HTML format ... 
Fatal error: Cannot declare class App\Http\Controllers\Api\PhoneRealUsersController, because the name is already in use in /Users/<USER>/Herd/api-v-1-service/app/Services/PhoneRealUsersController.php on line 19

Call Stack:
    0.0007     670816   1. {main}() /Users/<USER>/Herd/api-v-1-service/vendor/bin/phpunit:0
    0.0008     682728   2. include('/Users/<USER>/Herd/api-v-1-service/vendor/phpunit/phpunit/phpunit') /Users/<USER>/Herd/api-v-1-service/vendor/bin/phpunit:122
    0.0125    5998360   3. PHPUnit\TextUI\Command::main($exit = ???) /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/phpunit/phpunit:107
    0.0125    5998472   4. PHPUnit\TextUI\Command->run($argv = [0 => 'vendor/bin/phpunit', 1 => 'tests/Integration', 2 => '--testdox', 3 => '--colors=always', 4 => '--coverage-html=coverage/integration'], $exit = TRUE) /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/phpunit/src/TextUI/Command.php:99
    0.0219    9154376   5. PHPUnit\TextUI\TestRunner->run($suite = class PHPUnit\Framework\TestSuite { protected $backupGlobals = NULL; protected $backupStaticAttributes = NULL; protected $runTestInSeparateProcess = FALSE; protected $name = '/Users/<USER>/Herd/api-v-1-service/tests/Integration'; protected $groups = ['default' => [...]]; protected $tests = [0 => class PHPUnit\Framework\TestSuite { ... }]; protected $numTests = 14; protected $testCase = FALSE; protected $foundClasses = [1 => 'Tests\\TestCase', 2 => 'Illuminate\\Foundation\\Testing\\TestCase', 3 => 'PHPUnit\\Framework\\TestCase', 4 => 'PHPUnit\\Framework\\Assert', 5 => 'class@anonymous\000/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Testing/Concerns/MakesHttpRequests.php:146$22']; protected $providedTests = NULL; protected $requiredTests = NULL; private $beStrictAboutChangesToGlobalState = NULL; private $iteratorFilter = NULL; private $declaredClassesPointer = 463; private $warnings = [] }, $arguments = ['extensions' => [], 'listGroups' => FALSE, 'listSuites' => FALSE, 'listTests' => FALSE, 'listTestsXml' => FALSE, 'loader' => NULL, 'useDefaultConfiguration' => TRUE, 'loadedExtensions' => [], 'unavailableExtensions' => [], 'notLoadedExtensions' => [], 'colors' => 'always', 'coverageHtml' => 'coverage/integration', 'printer' => 'PHPUnit\\Util\\TestDox\\CliTestDoxPrinter', 'testSuffixes' => [0 => 'Test.php', 1 => '.phpt'], 'configuration' => '/Users/<USER>/Herd/api-v-1-service/phpunit.xml', 'configurationObject' => class PHPUnit\TextUI\XmlConfiguration\Configuration { private $filename = '/Users/<USER>/Herd/api-v-1-service/phpunit.xml'; private $validationResult = class PHPUnit\Util\Xml\ValidationResult { ... }; private $extensions = class PHPUnit\TextUI\XmlConfiguration\ExtensionCollection { ... }; private $codeCoverage = class PHPUnit\TextUI\XmlConfiguration\CodeCoverage\CodeCoverage { ... }; private $groups = class PHPUnit\TextUI\XmlConfiguration\Groups { ... }; private $testdoxGroups = class PHPUnit\TextUI\XmlConfiguration\Groups { ... }; private $listeners = class PHPUnit\TextUI\XmlConfiguration\ExtensionCollection { ... }; private $logging = class PHPUnit\TextUI\XmlConfiguration\Logging\Logging { ... }; private $php = class PHPUnit\TextUI\XmlConfiguration\Php { ... }; private $phpunit = class PHPUnit\TextUI\XmlConfiguration\PHPUnit { ... }; private $testSuite = class PHPUnit\TextUI\XmlConfiguration\TestSuiteCollection { ... } }, 'stderr' => FALSE, 'columns' => 80], $warnings = [], $exit = TRUE) /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/phpunit/src/TextUI/Command.php:146
    2.1570   48835024   6. SebastianBergmann\CodeCoverage\Report\Html\Facade->process($coverage = class SebastianBergmann\CodeCoverage\CodeCoverage { private $driver = class SebastianBergmann\CodeCoverage\Driver\Xdebug3Driver { private ${SebastianBergmann\CodeCoverage\Driver\Driver}collectBranchAndPathCoverage = FALSE; private ${SebastianBergmann\CodeCoverage\Driver\Driver}detectDeadCode = TRUE }; private $filter = class SebastianBergmann\CodeCoverage\Filter { private $files = [...]; private $isFileCache = [...] }; private $wizard = class SebastianBergmann\CodeUnitReverseLookup\Wizard { private $lookupTable = [...]; private $processedClasses = [...]; private $processedFunctions = [...] }; private $checkForUnintentionallyCoveredCode = FALSE; private $includeUncoveredFiles = TRUE; private $processUncoveredFiles = TRUE; private $ignoreDeprecatedCode = FALSE; private $currentId = NULL; private $data = class SebastianBergmann\CodeCoverage\ProcessedCodeCoverageData { private $lineCoverage = [...]; private $functionCoverage = [...] }; private $useAnnotationsForIgnoringCode = TRUE; private $tests = []; private $parentClassesExcludedFromUnintentionallyCoveredCodeCheck = [0 => 'SebastianBergmann\\Comparator\\Comparator']; private $analyser = class SebastianBergmann\CodeCoverage\StaticAnalysis\ParsingFileAnalyser { private $classes = [...]; private $traits = [...]; private $functions = [...]; private $linesOfCode = [...]; private $ignoredLines = [...]; private $executableLines = [...]; private $useAnnotationsForIgnoringCode = TRUE; private $ignoreDeprecatedCode = FALSE }; private $cacheDirectory = NULL; private $cachedReport = NULL }, $target = 'coverage/integration') /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/phpunit/src/TextUI/TestRunner.php:736
    2.1574   48837736   7. SebastianBergmann\CodeCoverage\CodeCoverage->getReport() /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/php-code-coverage/src/Report/Html/Facade.php:61
    2.1580   48858976   8. SebastianBergmann\CodeCoverage\Node\Builder->build($coverage = class SebastianBergmann\CodeCoverage\CodeCoverage { private $driver = class SebastianBergmann\CodeCoverage\Driver\Xdebug3Driver { private ${SebastianBergmann\CodeCoverage\Driver\Driver}collectBranchAndPathCoverage = FALSE; private ${SebastianBergmann\CodeCoverage\Driver\Driver}detectDeadCode = TRUE }; private $filter = class SebastianBergmann\CodeCoverage\Filter { private $files = [...]; private $isFileCache = [...] }; private $wizard = class SebastianBergmann\CodeUnitReverseLookup\Wizard { private $lookupTable = [...]; private $processedClasses = [...]; private $processedFunctions = [...] }; private $checkForUnintentionallyCoveredCode = FALSE; private $includeUncoveredFiles = TRUE; private $processUncoveredFiles = TRUE; private $ignoreDeprecatedCode = FALSE; private $currentId = NULL; private $data = class SebastianBergmann\CodeCoverage\ProcessedCodeCoverageData { private $lineCoverage = [...]; private $functionCoverage = [...] }; private $useAnnotationsForIgnoringCode = TRUE; private $tests = []; private $parentClassesExcludedFromUnintentionallyCoveredCodeCheck = [0 => 'SebastianBergmann\\Comparator\\Comparator']; private $analyser = class SebastianBergmann\CodeCoverage\StaticAnalysis\ParsingFileAnalyser { private $classes = [...]; private $traits = [...]; private $functions = [...]; private $linesOfCode = [...]; private $ignoredLines = [...]; private $executableLines = [...]; private $useAnnotationsForIgnoringCode = TRUE; private $ignoreDeprecatedCode = FALSE }; private $cacheDirectory = NULL; private $cachedReport = NULL }) /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/php-code-coverage/src/CodeCoverage.php:136
    2.1580   48858976   9. SebastianBergmann\CodeCoverage\CodeCoverage->getData($raw = ???) /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/php-code-coverage/src/Node/Builder.php:44
    2.1580   48858976  10. SebastianBergmann\CodeCoverage\CodeCoverage->processUncoveredFilesFromFilter() /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/php-code-coverage/src/CodeCoverage.php:176
    2.2955   60726384  11. include_once('/Users/<USER>/Herd/api-v-1-service/app/Services/PhoneRealUsersController.php') /Users/<USER>/Herd/api-v-1-service/vendor/phpunit/php-code-coverage/src/CodeCoverage.php:578


Fatal error: Uncaught Illuminate\Contracts\Container\BindingResolutionException: Target [Illuminate\Contracts\Debug\ExceptionHandler] is not instantiable. in /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/Container.php on line 1089

Illuminate\Contracts\Container\BindingResolutionException: Target [Illuminate\Contracts\Debug\ExceptionHandler] is not instantiable. in /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/Container.php on line 1089

Call Stack:
    2.2977   60652336   1. Illuminate\Foundation\Bootstrap\HandleExceptions->handleException($e = class Illuminate\Contracts\Container\BindingResolutionException { protected $message = 'Target [Illuminate\\Contracts\\Debug\\ExceptionHandler] is not instantiable.'; private string ${Exception}string = ''; protected $code = 0; protected string $file = '/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/Container.php'; protected int $line = 1089; private array ${Exception}trace = [0 => [...], 1 => [...], 2 => [...], 3 => [...], 4 => [...], 5 => [...], 6 => [...], 7 => [...], 8 => [...], 9 => [...]]; private ?Throwable ${Exception}previous = NULL }) /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php:0
    2.2979   60632160   2. Illuminate\Foundation\Bootstrap\HandleExceptions->renderForConsole($e = class Illuminate\Contracts\Container\BindingResolutionException { protected $message = 'Target [Illuminate\\Contracts\\Debug\\ExceptionHandler] is not instantiable.'; private string ${Exception}string = ''; protected $code = 0; protected string $file = '/Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/Container.php'; protected int $line = 1089; private array ${Exception}trace = [0 => [...], 1 => [...], 2 => [...], 3 => [...], 4 => [...], 5 => [...], 6 => [...], 7 => [...]]; private ?Throwable ${Exception}previous = NULL }) /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php:171
    2.2979   60632160   3. Illuminate\Foundation\Bootstrap\HandleExceptions->getExceptionHandler() /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php:185
    2.2979   60632160   4. Illuminate\Foundation\Application->make($abstract = 'Illuminate\\Contracts\\Debug\\ExceptionHandler', $parameters = ???) /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php:254
    2.2979   60632160   5. Illuminate\Container\Container->make($abstract = 'Illuminate\\Contracts\\Debug\\ExceptionHandler', $parameters = []) /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Application.php:838
    2.2979   60632160   6. Illuminate\Foundation\Application->resolve($abstract = 'Illuminate\\Contracts\\Debug\\ExceptionHandler', $parameters = [], $raiseEvents = ???) /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/Container.php:694
    2.2979   60632160   7. Illuminate\Container\Container->resolve($abstract = 'Illuminate\\Contracts\\Debug\\ExceptionHandler', $parameters = [], $raiseEvents = TRUE) /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Foundation/Application.php:853
    2.2979   60632320   8. Illuminate\Container\Container->build($concrete = 'Illuminate\\Contracts\\Debug\\ExceptionHandler') /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/Container.php:758
    2.2979   60632416   9. Illuminate\Container\Container->notInstantiable($concrete = 'Illuminate\\Contracts\\Debug\\ExceptionHandler') /Users/<USER>/Herd/api-v-1-service/vendor/laravel/framework/src/Illuminate/Container/Container.php:886

