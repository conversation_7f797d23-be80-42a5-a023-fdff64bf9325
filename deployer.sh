#!/bin/bash
#chmod +x deployer.sh
# sudo ./deployer.sh -path=/path/to/your/laravel/project -dest=beein-v1-master
usage() {
  echo "Usage: sudo $0 -path=/path/to/your/laravel/project -dest=beein-v1-master"
  exit 1
}
if [ "$(id -u)" -ne 0 ]; then
  echo "This script must be run with sudo."
  usage
fi
PROJECT_PATH=""
DESTINATION=""
while [[ $# -gt 0 ]]; do
  case $1 in
    -path=*)
      PROJECT_PATH="${1#*=}"
      shift
      ;;
    -dest=*)
      DESTINATION="${1#*=}"
      shift
      ;;
    *)
      echo "Unknown option: $1"
      usage
      ;;
  esac
done
if [ -z "$PROJECT_PATH" ] || [ -z "$DESTINATION" ]; then
  echo "Error: -path and -dest arguments are required."
  usage
fi
cd "$PROJECT_PATH" || { echo "Directory not found: $PROJECT_PATH"; exit 1; }
SOURCE_API="api"
LAST_BACKUP=$(ls -1d "${PROJECT_PATH}/api.back.*" 2>/dev/null | grep -Eo '[0-9]+' | sort -n | tail -n 1)
if [ -z "$LAST_BACKUP" ]; then
  NEW_BACKUP_NUMBER=1
else
  NEW_BACKUP_NUMBER=$((LAST_BACKUP + 1))
fi
BACKUP_API="${PROJECT_PATH}/api.back.${NEW_BACKUP_NUMBER}"
cp "${PROJECT_PATH}/${SOURCE_API}/.env" "${PROJECT_PATH}/${DESTINATION}/.env"
cp "${PROJECT_PATH}/${SOURCE_API}/composer.lock" "${PROJECT_PATH}/${DESTINATION}/composer.lock"
cp -R "${PROJECT_PATH}/${SOURCE_API}/storage" "${PROJECT_PATH}/${DESTINATION}/storage"
cp -R "${PROJECT_PATH}/${SOURCE_API}/vendor" "${PROJECT_PATH}/${DESTINATION}/vendor"
chown -R beein:beein "${PROJECT_PATH}/${DESTINATION}"
chown -R www-data:www-data "${PROJECT_PATH}/${DESTINATION}/storage"
chown -R www-data:www-data "${PROJECT_PATH}/${DESTINATION}/bootstrap/cache"
mv "${PROJECT_PATH}/${SOURCE_API}" "${BACKUP_API}"
mv "${PROJECT_PATH}/${DESTINATION}" "${PROJECT_PATH}/${SOURCE_API}"
cd "${PROJECT_PATH}/${SOURCE_API}" || { echo "Directory not found: ${PROJECT_PATH}/${SOURCE_API}"; exit 1; }
php artisan storage:link
chmod -R 777 storage
echo "Deployment completed successfully."
echo "Backup created: $BACKUP_API"
