#!/bin/bash

# Comprehensive Swagger Documentation Generator for Beein API
# This script generates and validates the Swagger/OpenAPI documentation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check if <PERSON><PERSON> is installed
    if [ ! -f "artisan" ]; then
        print_error "<PERSON>vel artisan command not found. Please run this script from the Laravel project root."
        exit 1
    fi
    
    # Check if L5-Swagger is installed
    if ! php artisan list | grep -q "l5-swagger"; then
        print_error "L5-Swagger package not found. Please install it first:"
        echo "composer require darkaonline/l5-swagger"
        exit 1
    fi
    
    # Check if Swagger directory exists
    if [ ! -d "app/Swagger" ]; then
        print_error "Swagger directory not found at app/Swagger"
        exit 1
    fi
    
    print_success "Prerequisites check completed"
}

# Validate Swagger annotations
validate_annotations() {
    print_header "Validating Swagger Annotations"
    
    # Count annotation files
    annotation_files=$(find app/Swagger -name "*.php" | wc -l)
    print_info "Found $annotation_files Swagger annotation files"
    
    # List annotation files
    echo "Annotation files:"
    find app/Swagger -name "*.php" -exec basename {} \; | sort
    
    # Check for syntax errors in PHP files
    for file in app/Swagger/*.php; do
        if [ -f "$file" ]; then
            if php -l "$file" > /dev/null 2>&1; then
                print_success "$(basename "$file") - Syntax OK"
            else
                print_error "$(basename "$file") - Syntax Error"
                php -l "$file"
                exit 1
            fi
        fi
    done
}

# Generate Swagger documentation
generate_documentation() {
    print_header "Generating Swagger Documentation"
    
    # Clear cache first
    print_info "Clearing application cache..."
    php artisan config:clear > /dev/null 2>&1 || true
    php artisan cache:clear > /dev/null 2>&1 || true
    
    # Generate Swagger documentation
    print_info "Generating Swagger JSON..."
    if php artisan l5-swagger:generate; then
        print_success "Swagger documentation generated successfully"
    else
        print_error "Failed to generate Swagger documentation"
        exit 1
    fi
    
    # Check if documentation files were created
    if [ -f "storage/api-docs/api-docs.json" ]; then
        print_success "api-docs.json created successfully"
        
        # Get file size
        file_size=$(stat -f%z "storage/api-docs/api-docs.json" 2>/dev/null || stat -c%s "storage/api-docs/api-docs.json" 2>/dev/null || echo "unknown")
        print_info "Documentation file size: $file_size bytes"
    else
        print_error "api-docs.json was not created"
        exit 1
    fi
}

# Validate generated JSON
validate_json() {
    print_header "Validating Generated JSON"
    
    json_file="storage/api-docs/api-docs.json"
    
    if [ -f "$json_file" ]; then
        # Check if JSON is valid
        if python3 -m json.tool "$json_file" > /dev/null 2>&1; then
            print_success "Generated JSON is valid"
        elif python -m json.tool "$json_file" > /dev/null 2>&1; then
            print_success "Generated JSON is valid"
        elif php -r "json_decode(file_get_contents('$json_file')); echo json_last_error() === JSON_ERROR_NONE ? 'Valid' : 'Invalid';" | grep -q "Valid"; then
            print_success "Generated JSON is valid"
        else
            print_error "Generated JSON is invalid"
            exit 1
        fi
        
        # Extract basic information from JSON
        if command -v jq > /dev/null 2>&1; then
            print_info "API Information:"
            jq -r '.info.title' "$json_file" 2>/dev/null && echo ""
            jq -r '.info.version' "$json_file" 2>/dev/null && echo ""
            
            # Count endpoints
            endpoint_count=$(jq '[.paths | to_entries[] | .value | to_entries[] | select(.key != "parameters")] | length' "$json_file" 2>/dev/null || echo "unknown")
            print_info "Total API endpoints: $endpoint_count"
            
            # Count schemas
            schema_count=$(jq '.components.schemas | length' "$json_file" 2>/dev/null || echo "unknown")
            print_info "Total schemas: $schema_count"
            
            # List tags
            print_info "API Tags:"
            jq -r '.tags[]?.name' "$json_file" 2>/dev/null | sort || echo "No tags found"
        else
            print_warning "jq not installed. Skipping detailed JSON analysis."
        fi
    else
        print_error "JSON file not found"
        exit 1
    fi
}

# Test Swagger UI accessibility
test_swagger_ui() {
    print_header "Testing Swagger UI Accessibility"
    
    # Start Laravel development server in background
    print_info "Starting Laravel development server..."
    php artisan serve --host=127.0.0.1 --port=8000 > /dev/null 2>&1 &
    SERVER_PID=$!
    
    # Wait for server to start
    sleep 3
    
    # Test if server is running
    if curl -s http://127.0.0.1:8000 > /dev/null; then
        print_success "Laravel server is running"
        
        # Test Swagger UI endpoint
        if curl -s http://127.0.0.1:8000/api/documentation > /dev/null; then
            print_success "Swagger UI is accessible at http://127.0.0.1:8000/api/documentation"
        else
            print_error "Swagger UI is not accessible"
        fi
        
        # Test API docs JSON endpoint
        if curl -s http://127.0.0.1:8000/docs/api-docs.json > /dev/null; then
            print_success "API docs JSON is accessible at http://127.0.0.1:8000/docs/api-docs.json"
        else
            print_error "API docs JSON is not accessible"
        fi
    else
        print_error "Laravel server failed to start"
    fi
    
    # Stop the server
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID > /dev/null 2>&1 || true
        print_info "Development server stopped"
    fi
}

# Generate documentation summary
generate_summary() {
    print_header "Documentation Summary"
    
    json_file="storage/api-docs/api-docs.json"
    
    if [ -f "$json_file" ] && command -v jq > /dev/null 2>&1; then
        echo "# Beein API Documentation Summary"
        echo ""
        echo "**Generated:** $(date)"
        echo "**API Version:** $(jq -r '.info.version' "$json_file")"
        echo "**API Title:** $(jq -r '.info.title' "$json_file")"
        echo ""
        
        echo "## Endpoints by Tag"
        echo ""
        
        # Group endpoints by tag
        jq -r '.paths | to_entries[] | .key as $path | .value | to_entries[] | select(.key != "parameters") | .key as $method | .value | "\(.tags[0] // "Untagged")|\($method | ascii_upcase)|\($path)"' "$json_file" | sort | while IFS='|' read -r tag method path; do
            echo "- **$tag**: $method $path"
        done
        
        echo ""
        echo "## Available Tags"
        echo ""
        jq -r '.tags[]?.name' "$json_file" | sort | while read -r tag; do
            echo "- $tag"
        done
        
        echo ""
        echo "## Security Schemes"
        echo ""
        jq -r '.components.securitySchemes | to_entries[] | "- \(.key): \(.value.type)"' "$json_file" 2>/dev/null || echo "- No security schemes defined"
        
        echo ""
        echo "## Access URLs"
        echo ""
        echo "- **Swagger UI**: http://localhost:8000/api/documentation"
        echo "- **JSON Documentation**: http://localhost:8000/docs/api-docs.json"
        echo "- **YAML Documentation**: http://localhost:8000/docs/api-docs.yaml (if enabled)"
        
    else
        print_warning "Cannot generate detailed summary. JSON file not found or jq not installed."
    fi
}

# Main execution
main() {
    local command=${1:-"all"}
    
    case $command in
        "validate")
            check_prerequisites
            validate_annotations
            ;;
        "generate")
            check_prerequisites
            generate_documentation
            validate_json
            ;;
        "test")
            check_prerequisites
            test_swagger_ui
            ;;
        "summary")
            generate_summary
            ;;
        "all")
            check_prerequisites
            validate_annotations
            generate_documentation
            validate_json
            test_swagger_ui
            generate_summary
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  all         Run all steps (default)"
            echo "  validate    Validate Swagger annotations"
            echo "  generate    Generate Swagger documentation"
            echo "  test        Test Swagger UI accessibility"
            echo "  summary     Generate documentation summary"
            echo "  help        Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 all"
            echo "  $0 generate"
            echo "  $0 test"
            ;;
        *)
            print_error "Unknown command: $command"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
