{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1.6", "ext-curl": "*", "ext-gd": "*", "ext-json": "*", "ext-mongo": "*", "ext-mongodb": "*", "ext-simplexml": "*", "ext-fileinfo": "*", "aws/aws-sdk-php": "^3.232", "darkaonline/l5-swagger": "^8.3", "doctrine/dbal": "^3.4", "dompdf/dompdf": "~0.7", "elasticsearch/elasticsearch": "~7.17", "fruitcake/laravel-cors": "^2.0", "glushkovds/phpclickhouse-laravel": "^1.19", "guzzlehttp/guzzle": "^7.0.1", "jackiedo/dotenv-editor": "^2.0", "jenssegers/mongodb": "3.8.x", "laravel/framework": "^8.75", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "laudis/neo4j-php-client": "^2.8", "league/flysystem-aws-s3-v3": "^1.0", "maatwebsite/excel": "^3.1", "mpdf/mpdf": "^8.0", "php-amqplib/php-amqplib": "^3.2", "predis/predis": "^2.2", "ramil/nebula-php": "@dev", "spatie/array-to-xml": "^3.1", "spatie/laravel-permission": "^5.5", "tecnickcom/tcpdf": "^6.4", "tymon/jwt-auth": "^1.0", "vladimir-yuldashev/laravel-queue-rabbitmq": "*", "yajra/laravel-oci8": "^8"}, "repositories": {"ramil/nebula-php": {"type": "path", "url": "./packages/nebula-php-main", "minimum-stability": "dev", "prefer-stable": true, "options": {"symlink": true}}}, "require-dev": {"phpunit/phpunit": "^9.5.10", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "fakerphp/faker": "^1.9.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["helpers/helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"start": ["kill -9 $(lsof -ti:8000,8001) && php artisan serve"], "post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "health-check": ["@php artisan db:monitor --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}