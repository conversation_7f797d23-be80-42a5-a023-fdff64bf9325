<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */


    'accepted'             => ' :attribute sahəsi mütləq qəbul edilməlidir.',
    'active_url'           => ' :attribute url düzgün olmalıdır.',
    'after'                => ' :attribute sahəsi  :date tarixindən az olmamalıdır.',
    'after_or_equal'       => ' :attribute sahəsi must be a date after or equal to :date.',
    'alpha'                => ' :attribute sahəsi yalnız hərflərdən ibarət ola bilər.',
    'alpha_dash'           => ' :attribute sahəsi yalnız hərflərdən, rəqəmlərdən ibarət ola bilər.',
    'alpha_num'            => ' :attribute sahəsi yalnız rəqəm və ya hərflərdən ibarət ola bilər.',
    'array'                => 'The :attribute sahəsi  array olmalıdır.',
    'before'               => 'The :attribute sahəsi  :date əvvəlki vaxt olmalıdır.',
    'before_or_equal'      => 'The :attribute sahəsi  :date əvvəl və ya  bərabər olmalı.',
    'between'              => [
        'numeric' => ':attribute sahəsi minimum :min maksimum :max.',
        'file'    => ':attribute sahəsi minimum :min maksimum :max kilobayt.',
        'string'  => ':attribute sahəsi minimum :min maksimum :max simvol.',
        'array'   => ':attribute sahəsi minimum :min maksimum :max .',
    ],
    'boolean'              => ':attribute sahəsi  düz və ya səhv ola bilər.',
    'confirmed'            => ' :attribute sahəsi uyğun deyil.',
    'date'                 => ' :attribute sahəsi vaxt düzgün seçilməlidir.',
    'date_format'          => ':attribute sahəsi formatı :format uğun deyil.',
    'different'            => ':attribute sahəsi və :other fərqli olmalıdır.',
    'digits'               => ':attribute sahəsi  :digits olmalıdır.',
    'digits_between'       => ':attribute sahəsi minimum :min maksimum :max rəqəm.',
    'dimensions'           => ':attribute sahəsi düzgün ölçüdə deyil.',
    'distinct'             => ':attribute sahəsi təkrarlanır.',
    'email'                => ':attribute sahəsi email düzgün yazılmalıdır',
    'exists'               => 'Seçilmiş :attribute sahəsi səhvdir.',
    'file'                 => ' :attribute sahəsi fayl olmalıdır.',
    'filled'               => ' :attribute sahəsi vacibdir.',
    'image'                => ':attribute sahəsi şəkil tipi olmalıdır.',
    'in'                   => 'Seçilmiş :attribute sahəsi is səhvd.',
    'in_array'             => ' :attribute sahəsi mövcud deyil :other.',
    'integer'              => ' :attribute sahəsi  integer olmalıdır.',
    'ip'                   => ':attribute sahəsi  düzgün ip adresi olmalıdır.',
    'ipv4'                 => ':attribute sahəsi düzgün IPv4 adresi olmalıdır.',
    'ipv6'                 => ':attribute sahəsi düzgün IPv6 adresi.',
    'json'                 => ' :attribute sahəsi düzgün JSON string olmalıdır.',
    'max'                  => [
        'numeric' => ':attribute sahəsi :max dan böyük olmamalıdır.',
        'file'    => ':attribute sahəsi :max kilobaytdan böyük olmamalıdır.',
        'string'  => ':attribute sahəsi :max simvoldan çox olmamalıdır.',
        'array'   => ':attribute sahəsi :max saydan çox olmamalıdır.',
    ],
    'mimes'                => ' :attribute sahəsi faylın tipi  type: :values olmalıdır.',
    'mimetypes'            => ' :attribute sahəsi fayl tipi  type: :values olmalıdır.',
    'min'                  => [
        'numeric' => ':attribute sahəsi ən azı :min olmalıdır.',
        'file'    => ':attribute sahəsi ən azı :min kilobayt olmalıdır.',
        'string'  => ':attribute sahəsi ən azı :min simvol olmalıdır.',
        'array'   => ':attribute sahəsi ən azı  :min sayda olmalıdır.',
    ],
    'not_in'               => 'Seçilmiş :attribute sahəsi səhvdir.',
    'numeric'              => ':attribute sahəsi rəqəm olmalıdır.',
    'present'              => ':attribute sahəsi bölməsi indi olmalıdır.',
    'regex'                => ':attribute sahəsi formatı səhvdir.',
    'required'             => ':attribute sahəsi vacibdir.',
    'required_if'          => ':attribute sahəsi sahə vacibdir :other :value.',
    'required_unless'      => ':attribute sahəsi sahə vacibdir :other  :values.',
    'required_with'        => ':attribute sahəsi sahə vacibdir :values .',
    'required_with_all'    => ':attribute sahəsi sahə vacibdir :values .',
    'required_without'     => ':attribute sahəsi sahə vacibdir :values .',
    'required_without_all' => ':attribute sahəsi sahə vacibdir :values .',
    'same'                 => ':attribute sahəsi :other eyni olmalıdır .',
    'size'                 => [
        'numeric' => ' :attribute sahəsi  :size ibarət olmalıdır.',
        'file'    => ' :attribute sahəsi həcmi :size kilobayt olmalıdır.',
        'string'  => ' :attribute sahəsi simovol sayı :size olmalıdır.',
        'array'   => ' :attribute sahəsi sayı  :size  olmalıdır.',
    ],
    'string'               => ':attribute sahəsi yalnız tekst tipi ola bilər.',
    'timezone'             => ':attribute sahəsi düzgün vaxt zonası seçilməlidir .',
    'unique'               => ':attribute sahəsi artıq bazada mövcuddur.',
    'uploaded'             => ':attribute sahəsi yükləmədə səhv.',
    'url'                  => ':attribute sahəsi format səhvdir.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention “attribute.rule” to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap attribute place-holders
    | with something more reader friendly such as E-Mail Address instead
    | of "email". This simply helps us make messages a little cleaner.
    |
    */

    'attributes' => [
        'pin' => 'FİN',
        'document_number' => 'Vəsiqə Nömrəsi'
    ],

];
