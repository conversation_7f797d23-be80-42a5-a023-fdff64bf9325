<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>{{config('l5-swagger.documentations.'.$documentation.'.api.title')}}</title>
    <link rel="stylesheet" type="text/css" href="{{ l5_swagger_asset($documentation, 'swagger-ui.css') }}">
    <link rel="icon" type="image/png" href="{{ l5_swagger_asset($documentation, 'favicon-32x32.png') }}" sizes="32x32"/>
    <link rel="icon" type="image/png" href="{{ l5_swagger_asset($documentation, 'favicon-16x16.png') }}" sizes="16x16"/>
    <style>
    html
    {
        box-sizing: border-box;
        overflow: -moz-scrollbars-vertical;
        overflow-y: scroll;
    }
    *,
    *:before,
    *:after
    {
        box-sizing: inherit;
    }

    body {
      margin:0;
      background: #fafafa;
    }
    </style>
</head>

<body>
<div id="swagger-ui">
    <div style="padding: 20px; text-align: center; color: #666;">
        <h3>Loading Swagger UI...</h3>
        <p>If this message persists, please check the browser console for errors.</p>
    </div>
</div>

<script src="{{ l5_swagger_asset($documentation, 'swagger-ui-bundle.js') }}"></script>
<script src="{{ l5_swagger_asset($documentation, 'swagger-ui-standalone-preset.js') }}"></script>
<script>
    function initializeSwaggerUI() {
        console.log('Swagger UI loading...');
        console.log('SwaggerUIBundle available:', typeof SwaggerUIBundle !== 'undefined');
        console.log('SwaggerUIStandalonePreset available:', typeof SwaggerUIStandalonePreset !== 'undefined');

        var swaggerContainer = document.getElementById('swagger-ui');
        if (!swaggerContainer) {
            console.error('Swagger UI container not found');
            return;
        }

        if (typeof SwaggerUIBundle === 'undefined') {
            console.error('SwaggerUIBundle is not loaded');
            swaggerContainer.innerHTML = '<div style="padding: 20px; color: red;">SwaggerUIBundle is not loaded. Please check if the JavaScript files are accessible.</div>';
            return;
        }

        try {
            // Build a system
            const ui = SwaggerUIBundle({
            dom_id: '#swagger-ui',
            url: "{!! $urlToDocs !!}",
            operationsSorter: {!! isset($operationsSorter) ? '"' . $operationsSorter . '"' : 'null' !!},
            configUrl: {!! isset($configUrl) ? '"' . $configUrl . '"' : 'null' !!},
            validatorUrl: {!! isset($validatorUrl) ? '"' . $validatorUrl . '"' : 'null' !!},
            oauth2RedirectUrl: "{{ route('l5-swagger.'.$documentation.'.oauth2_callback', [], $useAbsolutePath) }}",

            requestInterceptor: function(request) {
                request.headers['X-CSRF-TOKEN'] = '';
                return request;
            },

            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIStandalonePreset
            ],

            plugins: [
                SwaggerUIBundle.plugins.DownloadUrl
            ],

            layout: "StandaloneLayout",
            docExpansion : "{!! config('l5-swagger.defaults.ui.display.doc_expansion', 'none') !!}",
            deepLinking: true,
            filter: {!! config('l5-swagger.defaults.ui.display.filter') ? 'true' : 'false' !!},
            persistAuthorization: {!! config('l5-swagger.defaults.ui.authorization.persist_authorization') ? 'true' : 'false' !!},
            onComplete: function() {
                console.log('Swagger UI loaded successfully');
            },
            onFailure: function(error) {
                console.error('Swagger UI failed to load:', error);
                swaggerContainer.innerHTML = '<div style="padding: 20px; color: red;">Failed to load API documentation: ' + error + '</div>';
            }

            })

            window.ui = ui
            console.log('Swagger UI initialized successfully');

            @if(in_array('oauth2', array_column(config('l5-swagger.defaults.securityDefinitions.securitySchemes'), 'type')))
            ui.initOAuth({
                usePkceWithAuthorizationCodeGrant: {!! (bool)config('l5-swagger.defaults.ui.authorization.oauth2.use_pkce_with_authorization_code_grant') ? 'true' : 'false' !!}
            })
            @endif
        } catch (error) {
            console.error('Error initializing Swagger UI:', error);
            swaggerContainer.innerHTML = '<div style="padding: 20px; color: red;">Error loading Swagger UI: ' + error.message + '</div>';
        }
    }

    // Try multiple initialization strategies
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeSwaggerUI);
    } else if (document.readyState === 'interactive' || document.readyState === 'complete') {
        initializeSwaggerUI();
    } else {
        window.onload = initializeSwaggerUI;
    }
</script>
</body>
</html>
