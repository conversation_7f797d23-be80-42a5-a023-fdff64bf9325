#!/bin/bash

# Comprehensive Test Runner Script for Beein API
# This script runs all tests with proper configuration and reporting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PHP_BINARY=${PHP_BINARY:-php}
PHPUNIT_BINARY="vendor/bin/phpunit"
TEST_ENV="testing"
COVERAGE_DIR="coverage"
REPORTS_DIR="test-reports"

# Functions
print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    if [ ! -f "$PHPUNIT_BINARY" ]; then
        print_error "PHPUnit not found. Please run: composer install"
        exit 1
    fi
    
    if [ ! -f ".env.testing" ]; then
        print_warning ".env.testing not found. Using .env"
    fi
    
    print_success "Prerequisites check completed"
}

# Setup test environment
setup_test_environment() {
    print_header "Setting Up Test Environment"
    
    # Create necessary directories
    mkdir -p "$COVERAGE_DIR"
    mkdir -p "$REPORTS_DIR"
    
    # Set environment
    export APP_ENV="$TEST_ENV"
    
    # Clear caches
    $PHP_BINARY artisan config:clear --env="$TEST_ENV" > /dev/null 2>&1 || true
    $PHP_BINARY artisan cache:clear --env="$TEST_ENV" > /dev/null 2>&1 || true
    
    print_success "Test environment setup completed"
}

# Run specific test suite
run_test_suite() {
    local suite_name=$1
    local suite_path=$2
    local options=$3
    
    print_info "Running $suite_name tests..."
    
    if $PHP_BINARY $PHPUNIT_BINARY \
        --testsuite="$suite_name" \
        --testdox \
        --colors=always \
        $options \
        2>&1 | tee "$REPORTS_DIR/${suite_name,,}-results.txt"; then
        print_success "$suite_name tests passed"
        return 0
    else
        print_error "$suite_name tests failed"
        return 1
    fi
}

# Run unit tests
run_unit_tests() {
    print_header "Running Unit Tests"
    run_test_suite "Unit" "tests/Unit" "--coverage-html=$COVERAGE_DIR/unit"
}

# Run feature tests
run_feature_tests() {
    print_header "Running Feature Tests"
    run_test_suite "Feature" "tests/Feature" "--coverage-html=$COVERAGE_DIR/feature"
}

# Run integration tests
run_integration_tests() {
    print_header "Running Integration Tests"
    if [ -d "tests/Integration" ]; then
        $PHP_BINARY $PHPUNIT_BINARY \
            tests/Integration \
            --testdox \
            --colors=always \
            --coverage-html="$COVERAGE_DIR/integration" \
            2>&1 | tee "$REPORTS_DIR/integration-results.txt"
    else
        print_warning "No integration tests found"
    fi
}

# Run all tests with coverage
run_all_tests_with_coverage() {
    print_header "Running All Tests with Coverage"
    
    $PHP_BINARY $PHPUNIT_BINARY \
        --coverage-html="$COVERAGE_DIR/all" \
        --coverage-clover="$COVERAGE_DIR/clover.xml" \
        --log-junit="$REPORTS_DIR/junit.xml" \
        --testdox-html="$REPORTS_DIR/testdox.html" \
        --testdox \
        --colors=always \
        2>&1 | tee "$REPORTS_DIR/all-tests-results.txt"
}

# Run specific test file
run_specific_test() {
    local test_file=$1
    
    if [ -z "$test_file" ]; then
        print_error "Please specify a test file"
        exit 1
    fi
    
    print_header "Running Specific Test: $test_file"
    
    $PHP_BINARY $PHPUNIT_BINARY \
        "$test_file" \
        --testdox \
        --colors=always
}

# Run tests by filter
run_filtered_tests() {
    local filter=$1
    
    if [ -z "$filter" ]; then
        print_error "Please specify a filter"
        exit 1
    fi
    
    print_header "Running Filtered Tests: $filter"
    
    $PHP_BINARY $PHPUNIT_BINARY \
        --filter="$filter" \
        --testdox \
        --colors=always
}

# Generate test report
generate_report() {
    print_header "Generating Test Report"
    
    local report_file="$REPORTS_DIR/test-summary.md"
    
    cat > "$report_file" << EOF
# Test Execution Report

**Generated:** $(date)
**Environment:** $TEST_ENV

## Test Results Summary

EOF
    
    # Count test results
    if [ -f "$REPORTS_DIR/all-tests-results.txt" ]; then
        local total_tests=$(grep -o "Tests: [0-9]*" "$REPORTS_DIR/all-tests-results.txt" | head -1 | grep -o "[0-9]*")
        local assertions=$(grep -o "Assertions: [0-9]*" "$REPORTS_DIR/all-tests-results.txt" | head -1 | grep -o "[0-9]*")
        local failures=$(grep -o "Failures: [0-9]*" "$REPORTS_DIR/all-tests-results.txt" | head -1 | grep -o "[0-9]*" || echo "0")
        local errors=$(grep -o "Errors: [0-9]*" "$REPORTS_DIR/all-tests-results.txt" | head -1 | grep -o "[0-9]*" || echo "0")
        
        cat >> "$report_file" << EOF
- **Total Tests:** $total_tests
- **Assertions:** $assertions
- **Failures:** $failures
- **Errors:** $errors

## Coverage Reports

- [Full Coverage Report]($COVERAGE_DIR/all/index.html)
- [Unit Tests Coverage]($COVERAGE_DIR/unit/index.html)
- [Feature Tests Coverage]($COVERAGE_DIR/feature/index.html)

## Detailed Results

- [JUnit XML Report]($REPORTS_DIR/junit.xml)
- [TestDox HTML Report]($REPORTS_DIR/testdox.html)
- [Full Test Output]($REPORTS_DIR/all-tests-results.txt)

EOF
    fi
    
    print_success "Test report generated: $report_file"
}

# Clean up
cleanup() {
    print_header "Cleaning Up"
    
    # Clear test caches
    $PHP_BINARY artisan config:clear --env="$TEST_ENV" > /dev/null 2>&1 || true
    $PHP_BINARY artisan cache:clear --env="$TEST_ENV" > /dev/null 2>&1 || true
    
    print_success "Cleanup completed"
}

# Main execution
main() {
    local command=${1:-"all"}
    
    case $command in
        "unit")
            check_prerequisites
            setup_test_environment
            run_unit_tests
            cleanup
            ;;
        "feature")
            check_prerequisites
            setup_test_environment
            run_feature_tests
            cleanup
            ;;
        "integration")
            check_prerequisites
            setup_test_environment
            run_integration_tests
            cleanup
            ;;
        "coverage")
            check_prerequisites
            setup_test_environment
            run_all_tests_with_coverage
            generate_report
            cleanup
            ;;
        "file")
            check_prerequisites
            setup_test_environment
            run_specific_test "$2"
            cleanup
            ;;
        "filter")
            check_prerequisites
            setup_test_environment
            run_filtered_tests "$2"
            cleanup
            ;;
        "all")
            check_prerequisites
            setup_test_environment
            
            local failed=0
            
            run_unit_tests || failed=1
            run_feature_tests || failed=1
            run_integration_tests || failed=1
            
            if [ $failed -eq 0 ]; then
                print_success "All test suites passed!"
            else
                print_error "Some test suites failed!"
            fi
            
            generate_report
            cleanup
            
            exit $failed
            ;;
        "help"|"-h"|"--help")
            echo "Usage: $0 [command] [options]"
            echo ""
            echo "Commands:"
            echo "  all         Run all test suites (default)"
            echo "  unit        Run unit tests only"
            echo "  feature     Run feature tests only"
            echo "  integration Run integration tests only"
            echo "  coverage    Run all tests with coverage report"
            echo "  file <path> Run specific test file"
            echo "  filter <pattern> Run tests matching filter"
            echo "  help        Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0 unit"
            echo "  $0 file tests/Unit/Models/UserTest.php"
            echo "  $0 filter UserTest"
            ;;
        *)
            print_error "Unknown command: $command"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Execute main function with all arguments
main "$@"
