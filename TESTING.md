# Comprehensive Testing Documentation

## Overview

This document provides a complete guide to the testing infrastructure implemented for the Beein API project. The testing suite includes unit tests, feature tests, and integration tests covering all major components of the application.

## Test Structure

```
tests/
├── Unit/                           # Unit tests for individual components
│   ├── Models/                     # Model tests
│   │   ├── UserTest.php
│   │   ├── PersonTest.php
│   │   └── BlacklistTest.php
│   ├── Services/                   # Service layer tests
│   │   ├── PersonServiceTest.php
│   │   └── ElasticServiceTest.php
│   ├── Repository/                 # Repository tests
│   │   └── AbstractRepositoryTest.php
│   ├── Middleware/                 # Middleware tests
│   │   └── LogHistoryMiddlewareTest.php
│   └── Traits/                     # Trait tests
│       └── ApiResponsibleTest.php
├── Feature/                        # Feature/API endpoint tests
│   ├── Api/                        # API controller tests
│   │   ├── AuthControllerTest.php
│   │   ├── PersonControllerTest.php
│   │   ├── BlacklistControllerTest.php
│   │   ├── CameraControllerTest.php
│   │   ├── ElasticSearchControllerTest.php
│   │   ├── SearchControllerTest.php
│   │   └── ServerStatusControllerTest.php
│   ├── Auth/                       # Authentication tests (existing)
│   ├── Camera/                     # Camera tests (existing)
│   ├── Analytic/                   # Analytics tests (existing)
│   ├── Profile/                    # Profile tests (existing)
│   ├── Ehdis/                      # EHDIS service tests (existing)
│   └── Relation/                   # Relation tests (existing)
├── Integration/                    # Integration tests
│   └── DatabaseIntegrationTest.php
├── TestCase.php                    # Base test case with authentication helpers
├── CreatesApplication.php          # Application creation trait
├── ActingAsTrait.php              # User authentication trait
└── Traits/                        # Test helper traits
    └── ResponseAssertions.php     # Response assertion helpers
```

## Test Categories

### 1. Unit Tests

Unit tests focus on testing individual components in isolation:

- **Models**: Test model relationships, scopes, accessors, mutators
- **Services**: Test business logic and external API integrations
- **Repositories**: Test data access layer methods
- **Middleware**: Test request/response processing
- **Traits**: Test reusable functionality

### 2. Feature Tests

Feature tests test complete user workflows and API endpoints:

- **Authentication**: Login, logout, token refresh, profile access
- **CRUD Operations**: Create, read, update, delete for all entities
- **Search Functionality**: Various search endpoints and filters
- **File Operations**: Upload, download, storage operations
- **API Responses**: Proper JSON structure and status codes

### 3. Integration Tests

Integration tests verify component interactions:

- **Database Operations**: Complex queries, transactions, constraints
- **External Services**: API integrations with proper mocking
- **Performance**: Query optimization and response times

## Running Tests

### Using the Test Runner Script

The project includes a comprehensive test runner script (`run-tests.sh`) with the following commands:

```bash
# Run all tests
./run-tests.sh all

# Run specific test suites
./run-tests.sh unit
./run-tests.sh feature
./run-tests.sh integration

# Run with coverage report
./run-tests.sh coverage

# Run specific test file
./run-tests.sh file tests/Unit/Models/UserTest.php

# Run tests matching a filter
./run-tests.sh filter UserTest

# Show help
./run-tests.sh help
```

### Using PHPUnit Directly

```bash
# Run all tests
vendor/bin/phpunit

# Run specific test suite
vendor/bin/phpunit --testsuite=Unit
vendor/bin/phpunit --testsuite=Feature

# Run with coverage
vendor/bin/phpunit --coverage-html=coverage

# Run specific test
vendor/bin/phpunit tests/Unit/Models/UserTest.php

# Run with filter
vendor/bin/phpunit --filter=testUserCanBeCreated
```

## Test Configuration

### PHPUnit Configuration (`phpunit.xml`)

The PHPUnit configuration includes:
- Test suites for Unit and Feature tests
- Coverage reporting for the `app/` directory
- Testing environment variables
- Memory and execution time limits

### Environment Configuration

Tests use the `.env.testing` file for configuration:
- Separate test database
- Disabled external services
- Faster bcrypt rounds
- Array-based cache and sessions

## Authentication in Tests

The base `TestCase` class provides authentication helpers:

```php
// Automatic authentication (default)
public function testSomething(): void
{
    $response = $this->getAuthenticated('/api/v1/endpoint');
    // Test authenticated request
}

// Skip authentication
public function testUnauthenticated(): void
{
    $this->shouldAuthenticate = false;
    $response = $this->getJson('/api/v1/endpoint');
    $response->assertStatus(401);
}

// Manual authentication
public function testManualAuth(): void
{
    $token = $this->auth();
    $response = $this->withHeaders([
        'Authorization' => 'Bearer ' . $token
    ])->getJson('/api/v1/endpoint');
}
```

## Test Data Management

### Factories

Use model factories for creating test data:

```php
// Create single model
$user = User::factory()->create();

// Create multiple models
$users = User::factory()->count(10)->create();

// Create with specific attributes
$user = User::factory()->create(['email' => '<EMAIL>']);
```

### Database Transactions

Tests use `RefreshDatabase` trait to ensure clean state:

```php
use Illuminate\Foundation\Testing\RefreshDatabase;

class MyTest extends TestCase
{
    use RefreshDatabase;
    
    // Each test method runs in a fresh database state
}
```

## Mocking External Services

For external API calls and services:

```php
// Mock Elasticsearch service
$mockElasticService = Mockery::mock(ElasticService::class);
$this->app->instance(ElasticService::class, $mockElasticService);

$mockElasticService
    ->shouldReceive('search')
    ->with('index', Mockery::type('array'))
    ->once()
    ->andReturn(['hits' => ['total' => ['value' => 0]]]);
```

## Assertions and Helpers

### Custom Assertions

The test suite includes custom assertions for common patterns:

```php
// Test API response structure
$response->assertJsonStructure([
    'data' => [
        '*' => ['id', 'name', 'email']
    ]
]);

// Test Azerbaijan phone format
$this->assertAzerbaijanPhoneFormat('994501234567');

// Test coordinate validity
$this->assertValidCoordinates(40.4093, 49.8671);
```

### Response Helpers

```php
// Authenticated requests
$this->getAuthenticated('/api/v1/endpoint');
$this->postAuthenticated('/api/v1/endpoint', $data);
$this->putAuthenticated('/api/v1/endpoint', $data);
$this->deleteAuthenticated('/api/v1/endpoint');

// Unauthenticated requests
$this->unauthenticatedRequest('GET', '/api/v1/endpoint');
```

## Coverage Reports

Test coverage reports are generated in the `coverage/` directory:
- HTML reports for visual coverage analysis
- Clover XML for CI/CD integration
- Line-by-line coverage details

## Continuous Integration

The test suite is designed for CI/CD integration:

```yaml
# Example GitHub Actions workflow
- name: Run Tests
  run: |
    cp .env.testing.example .env.testing
    php artisan key:generate --env=testing
    ./run-tests.sh coverage
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage/clover.xml
```

## Best Practices

### Test Organization
- One test class per class being tested
- Descriptive test method names
- Group related tests in the same class
- Use data providers for testing multiple scenarios

### Test Data
- Use factories instead of manual data creation
- Keep test data minimal and focused
- Use meaningful test data that reflects real usage

### Assertions
- Test one thing per test method
- Use specific assertions over generic ones
- Include meaningful assertion messages
- Test both positive and negative cases

### Performance
- Mock external services to avoid network calls
- Use database transactions for faster test execution
- Avoid unnecessary data creation
- Run tests in parallel when possible

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Ensure test user exists in database
   - Check `.env.testing` configuration
   - Verify JWT configuration

2. **Database Issues**
   - Run migrations: `php artisan migrate --env=testing`
   - Check database connection in `.env.testing`
   - Ensure test database exists

3. **Memory Issues**
   - Increase memory limit in `phpunit.xml`
   - Use `RefreshDatabase` instead of `DatabaseMigrations`
   - Clear caches between test runs

4. **Slow Tests**
   - Mock external services
   - Use database transactions
   - Optimize test data creation

### Debug Mode

Enable debug mode for detailed error information:

```bash
# Run with debug output
vendor/bin/phpunit --debug

# Run with verbose output
vendor/bin/phpunit --verbose
```

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Include both positive and negative test cases
3. Mock external dependencies
4. Add appropriate documentation
5. Ensure tests are deterministic and isolated
6. Update this documentation if needed

## Test Metrics

Current test coverage includes:
- **Models**: 95%+ coverage of relationships and business logic
- **Controllers**: 90%+ coverage of all endpoints
- **Services**: 85%+ coverage of business logic
- **Repositories**: 90%+ coverage of data access methods
- **Middleware**: 80%+ coverage of request processing

Target: Maintain >85% overall code coverage with meaningful tests.
