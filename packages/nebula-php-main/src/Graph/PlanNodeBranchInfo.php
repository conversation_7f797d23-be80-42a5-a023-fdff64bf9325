<?php
namespace Nebula\Graph;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class PlanNodeBranchInfo
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'is_do_branch',
            'isRequired' => true,
            'type' => TType::BOOL,
        ),
        2 => array(
            'var' => 'condition_node_id',
            'isRequired' => true,
            'type' => TType::I64,
        ),
    );

    /**
     * @var bool
     */
    public $is_do_branch = null;
    /**
     * @var int
     */
    public $condition_node_id = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['is_do_branch'])) {
                $this->is_do_branch = $vals['is_do_branch'];
            }
            if (isset($vals['condition_node_id'])) {
                $this->condition_node_id = $vals['condition_node_id'];
            }
        }
    }

    public function getName()
    {
        return 'PlanNodeBranchInfo';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->is_do_branch);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->condition_node_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('PlanNodeBranchInfo');
        if ($this->is_do_branch !== null) {
            $xfer += $output->writeFieldBegin('is_do_branch', TType::BOOL, 1);
            $xfer += $output->writeBool($this->is_do_branch);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->condition_node_id !== null) {
            $xfer += $output->writeFieldBegin('condition_node_id', TType::I64, 2);
            $xfer += $output->writeI64($this->condition_node_id);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
