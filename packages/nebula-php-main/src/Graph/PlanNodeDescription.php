<?php
namespace Nebula\Graph;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class PlanNodeDescription
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'name',
            'isRequired' => true,
            'type' => TType::STRING,
        ),
        2 => array(
            'var' => 'id',
            'isRequired' => true,
            'type' => TType::I64,
        ),
        3 => array(
            'var' => 'output_var',
            'isRequired' => true,
            'type' => TType::STRING,
        ),
        4 => array(
            'var' => 'description',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Graph\Pair',
                ),
        ),
        5 => array(
            'var' => 'profiles',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Graph\ProfilingStats',
                ),
        ),
        6 => array(
            'var' => 'branch_info',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Graph\PlanNodeBranchInfo',
        ),
        7 => array(
            'var' => 'dependencies',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::I64,
            'elem' => array(
                'type' => TType::I64,
                ),
        ),
    );

    /**
     * @var string
     */
    public $name = null;
    /**
     * @var int
     */
    public $id = null;
    /**
     * @var string
     */
    public $output_var = null;
    /**
     * @var \Nebula\Graph\Pair[]
     */
    public $description = null;
    /**
     * @var \Nebula\Graph\ProfilingStats[]
     */
    public $profiles = null;
    /**
     * @var \Nebula\Graph\PlanNodeBranchInfo
     */
    public $branch_info = null;
    /**
     * @var int[]
     */
    public $dependencies = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['name'])) {
                $this->name = $vals['name'];
            }
            if (isset($vals['id'])) {
                $this->id = $vals['id'];
            }
            if (isset($vals['output_var'])) {
                $this->output_var = $vals['output_var'];
            }
            if (isset($vals['description'])) {
                $this->description = $vals['description'];
            }
            if (isset($vals['profiles'])) {
                $this->profiles = $vals['profiles'];
            }
            if (isset($vals['branch_info'])) {
                $this->branch_info = $vals['branch_info'];
            }
            if (isset($vals['dependencies'])) {
                $this->dependencies = $vals['dependencies'];
            }
        }
    }

    public function getName()
    {
        return 'PlanNodeDescription';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->output_var);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::LST) {
                        $this->description = array();
                        $_size9 = 0;
                        $_etype12 = 0;
                        $xfer += $input->readListBegin($_etype12, $_size9);
                        for ($_i13 = 0; $_i13 < $_size9; ++$_i13) {
                            $elem14 = null;
                            $elem14 = new \Nebula\Graph\Pair();
                            $xfer += $elem14->read($input);
                            $this->description []= $elem14;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::LST) {
                        $this->profiles = array();
                        $_size15 = 0;
                        $_etype18 = 0;
                        $xfer += $input->readListBegin($_etype18, $_size15);
                        for ($_i19 = 0; $_i19 < $_size15; ++$_i19) {
                            $elem20 = null;
                            $elem20 = new \Nebula\Graph\ProfilingStats();
                            $xfer += $elem20->read($input);
                            $this->profiles []= $elem20;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRUCT) {
                        $this->branch_info = new \Nebula\Graph\PlanNodeBranchInfo();
                        $xfer += $this->branch_info->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::LST) {
                        $this->dependencies = array();
                        $_size21 = 0;
                        $_etype24 = 0;
                        $xfer += $input->readListBegin($_etype24, $_size21);
                        for ($_i25 = 0; $_i25 < $_size21; ++$_i25) {
                            $elem26 = null;
                            $xfer += $input->readI64($elem26);
                            $this->dependencies []= $elem26;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('PlanNodeDescription');
        if ($this->name !== null) {
            $xfer += $output->writeFieldBegin('name', TType::STRING, 1);
            $xfer += $output->writeString($this->name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->id !== null) {
            $xfer += $output->writeFieldBegin('id', TType::I64, 2);
            $xfer += $output->writeI64($this->id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->output_var !== null) {
            $xfer += $output->writeFieldBegin('output_var', TType::STRING, 3);
            $xfer += $output->writeString($this->output_var);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->description !== null) {
            if (!is_array($this->description)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('description', TType::LST, 4);
            $output->writeListBegin(TType::STRUCT, count($this->description));
            foreach ($this->description as $iter27) {
                $xfer += $iter27->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->profiles !== null) {
            if (!is_array($this->profiles)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('profiles', TType::LST, 5);
            $output->writeListBegin(TType::STRUCT, count($this->profiles));
            foreach ($this->profiles as $iter28) {
                $xfer += $iter28->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->branch_info !== null) {
            if (!is_object($this->branch_info)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('branch_info', TType::STRUCT, 6);
            $xfer += $this->branch_info->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->dependencies !== null) {
            if (!is_array($this->dependencies)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('dependencies', TType::LST, 7);
            $output->writeListBegin(TType::I64, count($this->dependencies));
            foreach ($this->dependencies as $iter29) {
                $xfer += $output->writeI64($iter29);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
