<?php
namespace Nebula\Graph;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class AuthResponse
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'error_code',
            'isRequired' => true,
            'type' => TType::I32,
            'class' => '\Nebula\Common\ErrorCode',
        ),
        2 => array(
            'var' => 'error_msg',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        3 => array(
            'var' => 'session_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        4 => array(
            'var' => 'time_zone_offset_seconds',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        5 => array(
            'var' => 'time_zone_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var int
     */
    public $error_code = null;
    /**
     * @var string
     */
    public $error_msg = null;
    /**
     * @var int
     */
    public $session_id = null;
    /**
     * @var int
     */
    public $time_zone_offset_seconds = null;
    /**
     * @var string
     */
    public $time_zone_name = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['error_code'])) {
                $this->error_code = $vals['error_code'];
            }
            if (isset($vals['error_msg'])) {
                $this->error_msg = $vals['error_msg'];
            }
            if (isset($vals['session_id'])) {
                $this->session_id = $vals['session_id'];
            }
            if (isset($vals['time_zone_offset_seconds'])) {
                $this->time_zone_offset_seconds = $vals['time_zone_offset_seconds'];
            }
            if (isset($vals['time_zone_name'])) {
                $this->time_zone_name = $vals['time_zone_name'];
            }
        }
    }

    public function getName()
    {
        return 'AuthResponse';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->error_code);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->error_msg);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->session_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->time_zone_offset_seconds);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->time_zone_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('AuthResponse');
        if ($this->error_code !== null) {
            $xfer += $output->writeFieldBegin('error_code', TType::I32, 1);
            $xfer += $output->writeI32($this->error_code);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->error_msg !== null) {
            $xfer += $output->writeFieldBegin('error_msg', TType::STRING, 2);
            $xfer += $output->writeString($this->error_msg);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->session_id !== null) {
            $xfer += $output->writeFieldBegin('session_id', TType::I64, 3);
            $xfer += $output->writeI64($this->session_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->time_zone_offset_seconds !== null) {
            $xfer += $output->writeFieldBegin('time_zone_offset_seconds', TType::I32, 4);
            $xfer += $output->writeI32($this->time_zone_offset_seconds);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->time_zone_name !== null) {
            $xfer += $output->writeFieldBegin('time_zone_name', TType::STRING, 5);
            $xfer += $output->writeString($this->time_zone_name);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
