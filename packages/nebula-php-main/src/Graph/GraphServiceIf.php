<?php
namespace Nebula\Graph;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

interface GraphServiceIf
{
    /**
     * @param string $username
     * @param string $password
     * @return \Nebula\Graph\AuthResponse
     */
    public function authenticate($username, $password);
    /**
     * @param int $sessionId
     */
    public function signout($sessionId);
    /**
     * @param int $sessionId
     * @param string $stmt
     * @return \Nebula\Graph\ExecutionResponse
     */
    public function execute($sessionId, $stmt);
    /**
     * @param int $sessionId
     * @param string $stmt
     * @return string
     */
    public function executeJson($sessionId, $stmt);
    /**
     * @param \Nebula\Graph\VerifyClientVersionReq $req
     * @return \Nebula\Graph\VerifyClientVersionResp
     */
    public function verifyClientVersion(\Nebula\Graph\VerifyClientVersionReq $req);
}
