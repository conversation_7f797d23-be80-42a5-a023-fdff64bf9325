<?php
namespace Nebula\Graph;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class PlanDescription
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'plan_node_descs',
            'isRequired' => true,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Graph\PlanNodeDescription',
                ),
        ),
        2 => array(
            'var' => 'node_index_map',
            'isRequired' => true,
            'type' => TType::MAP,
            'ktype' => TType::I64,
            'vtype' => TType::I64,
            'key' => array(
                'type' => TType::I64,
            ),
            'val' => array(
                'type' => TType::I64,
                ),
        ),
        3 => array(
            'var' => 'format',
            'isRequired' => true,
            'type' => TType::STRING,
        ),
        4 => array(
            'var' => 'optimize_time_in_us',
            'isRequired' => true,
            'type' => TType::I32,
        ),
    );

    /**
     * @var \Nebula\Graph\PlanNodeDescription[]
     */
    public $plan_node_descs = null;
    /**
     * @var array
     */
    public $node_index_map = null;
    /**
     * @var string
     */
    public $format = null;
    /**
     * @var int
     */
    public $optimize_time_in_us = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['plan_node_descs'])) {
                $this->plan_node_descs = $vals['plan_node_descs'];
            }
            if (isset($vals['node_index_map'])) {
                $this->node_index_map = $vals['node_index_map'];
            }
            if (isset($vals['format'])) {
                $this->format = $vals['format'];
            }
            if (isset($vals['optimize_time_in_us'])) {
                $this->optimize_time_in_us = $vals['optimize_time_in_us'];
            }
        }
    }

    public function getName()
    {
        return 'PlanDescription';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::LST) {
                        $this->plan_node_descs = array();
                        $_size30 = 0;
                        $_etype33 = 0;
                        $xfer += $input->readListBegin($_etype33, $_size30);
                        for ($_i34 = 0; $_i34 < $_size30; ++$_i34) {
                            $elem35 = null;
                            $elem35 = new \Nebula\Graph\PlanNodeDescription();
                            $xfer += $elem35->read($input);
                            $this->plan_node_descs []= $elem35;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::MAP) {
                        $this->node_index_map = array();
                        $_size36 = 0;
                        $_ktype37 = 0;
                        $_vtype38 = 0;
                        $xfer += $input->readMapBegin($_ktype37, $_vtype38, $_size36);
                        for ($_i40 = 0; $_i40 < $_size36; ++$_i40) {
                            $key41 = 0;
                            $val42 = 0;
                            $xfer += $input->readI64($key41);
                            $xfer += $input->readI64($val42);
                            $this->node_index_map[$key41] = $val42;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->format);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->optimize_time_in_us);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('PlanDescription');
        if ($this->plan_node_descs !== null) {
            if (!is_array($this->plan_node_descs)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('plan_node_descs', TType::LST, 1);
            $output->writeListBegin(TType::STRUCT, count($this->plan_node_descs));
            foreach ($this->plan_node_descs as $iter43) {
                $xfer += $iter43->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->node_index_map !== null) {
            if (!is_array($this->node_index_map)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('node_index_map', TType::MAP, 2);
            $output->writeMapBegin(TType::I64, TType::I64, count($this->node_index_map));
            foreach ($this->node_index_map as $kiter44 => $viter45) {
                $xfer += $output->writeI64($kiter44);
                $xfer += $output->writeI64($viter45);
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->format !== null) {
            $xfer += $output->writeFieldBegin('format', TType::STRING, 3);
            $xfer += $output->writeString($this->format);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->optimize_time_in_us !== null) {
            $xfer += $output->writeFieldBegin('optimize_time_in_us', TType::I32, 4);
            $xfer += $output->writeI32($this->optimize_time_in_us);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
