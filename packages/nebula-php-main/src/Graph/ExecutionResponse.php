<?php
namespace Nebula\Graph;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ExecutionResponse
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'error_code',
            'isRequired' => true,
            'type' => TType::I32,
            'class' => '\Nebula\Common\ErrorCode',
        ),
        2 => array(
            'var' => 'latency_in_us',
            'isRequired' => true,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'data',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\DataSet',
        ),
        4 => array(
            'var' => 'space_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        5 => array(
            'var' => 'error_msg',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        6 => array(
            'var' => 'plan_desc',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Graph\PlanDescription',
        ),
        7 => array(
            'var' => 'comment',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var int
     */
    public $error_code = null;
    /**
     * @var int
     */
    public $latency_in_us = null;
    /**
     * @var \Nebula\Common\DataSet
     */
    public $data = null;
    /**
     * @var string
     */
    public $space_name = null;
    /**
     * @var string
     */
    public $error_msg = null;
    /**
     * @var \Nebula\Graph\PlanDescription
     */
    public $plan_desc = null;
    /**
     * @var string
     */
    public $comment = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['error_code'])) {
                $this->error_code = $vals['error_code'];
            }
            if (isset($vals['latency_in_us'])) {
                $this->latency_in_us = $vals['latency_in_us'];
            }
            if (isset($vals['data'])) {
                $this->data = $vals['data'];
            }
            if (isset($vals['space_name'])) {
                $this->space_name = $vals['space_name'];
            }
            if (isset($vals['error_msg'])) {
                $this->error_msg = $vals['error_msg'];
            }
            if (isset($vals['plan_desc'])) {
                $this->plan_desc = $vals['plan_desc'];
            }
            if (isset($vals['comment'])) {
                $this->comment = $vals['comment'];
            }
        }
    }

    public function getName()
    {
        return 'ExecutionResponse';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->error_code);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->latency_in_us);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRUCT) {
                        $this->data = new \Nebula\Common\DataSet();
                        $xfer += $this->data->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->space_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->error_msg);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRUCT) {
                        $this->plan_desc = new \Nebula\Graph\PlanDescription();
                        $xfer += $this->plan_desc->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->comment);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ExecutionResponse');
        if ($this->error_code !== null) {
            $xfer += $output->writeFieldBegin('error_code', TType::I32, 1);
            $xfer += $output->writeI32($this->error_code);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->latency_in_us !== null) {
            $xfer += $output->writeFieldBegin('latency_in_us', TType::I32, 2);
            $xfer += $output->writeI32($this->latency_in_us);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->data !== null) {
            if (!is_object($this->data)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('data', TType::STRUCT, 3);
            $xfer += $this->data->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->space_name !== null) {
            $xfer += $output->writeFieldBegin('space_name', TType::STRING, 4);
            $xfer += $output->writeString($this->space_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->error_msg !== null) {
            $xfer += $output->writeFieldBegin('error_msg', TType::STRING, 5);
            $xfer += $output->writeString($this->error_msg);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->plan_desc !== null) {
            if (!is_object($this->plan_desc)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('plan_desc', TType::STRUCT, 6);
            $xfer += $this->plan_desc->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->comment !== null) {
            $xfer += $output->writeFieldBegin('comment', TType::STRING, 7);
            $xfer += $output->writeString($this->comment);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
