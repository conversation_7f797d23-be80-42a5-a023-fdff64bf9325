<?php
namespace Nebula\Graph;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ProfilingStats
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'rows',
            'isRequired' => true,
            'type' => TType::I64,
        ),
        2 => array(
            'var' => 'exec_duration_in_us',
            'isRequired' => true,
            'type' => TType::I64,
        ),
        3 => array(
            'var' => 'total_duration_in_us',
            'isRequired' => true,
            'type' => TType::I64,
        ),
        4 => array(
            'var' => 'other_stats',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::STRING,
            'vtype' => TType::STRING,
            'key' => array(
                'type' => TType::STRING,
            ),
            'val' => array(
                'type' => TType::STRING,
                ),
        ),
    );

    /**
     * @var int
     */
    public $rows = null;
    /**
     * @var int
     */
    public $exec_duration_in_us = null;
    /**
     * @var int
     */
    public $total_duration_in_us = null;
    /**
     * @var array
     */
    public $other_stats = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['rows'])) {
                $this->rows = $vals['rows'];
            }
            if (isset($vals['exec_duration_in_us'])) {
                $this->exec_duration_in_us = $vals['exec_duration_in_us'];
            }
            if (isset($vals['total_duration_in_us'])) {
                $this->total_duration_in_us = $vals['total_duration_in_us'];
            }
            if (isset($vals['other_stats'])) {
                $this->other_stats = $vals['other_stats'];
            }
        }
    }

    public function getName()
    {
        return 'ProfilingStats';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->rows);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->exec_duration_in_us);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->total_duration_in_us);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::MAP) {
                        $this->other_stats = array();
                        $_size0 = 0;
                        $_ktype1 = 0;
                        $_vtype2 = 0;
                        $xfer += $input->readMapBegin($_ktype1, $_vtype2, $_size0);
                        for ($_i4 = 0; $_i4 < $_size0; ++$_i4) {
                            $key5 = '';
                            $val6 = '';
                            $xfer += $input->readString($key5);
                            $xfer += $input->readString($val6);
                            $this->other_stats[$key5] = $val6;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ProfilingStats');
        if ($this->rows !== null) {
            $xfer += $output->writeFieldBegin('rows', TType::I64, 1);
            $xfer += $output->writeI64($this->rows);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->exec_duration_in_us !== null) {
            $xfer += $output->writeFieldBegin('exec_duration_in_us', TType::I64, 2);
            $xfer += $output->writeI64($this->exec_duration_in_us);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->total_duration_in_us !== null) {
            $xfer += $output->writeFieldBegin('total_duration_in_us', TType::I64, 3);
            $xfer += $output->writeI64($this->total_duration_in_us);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->other_stats !== null) {
            if (!is_array($this->other_stats)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('other_stats', TType::MAP, 4);
            $output->writeMapBegin(TType::STRING, TType::STRING, count($this->other_stats));
            foreach ($this->other_stats as $kiter7 => $viter8) {
                $xfer += $output->writeString($kiter7);
                $xfer += $output->writeString($viter8);
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
