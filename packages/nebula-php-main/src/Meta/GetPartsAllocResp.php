<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class GetPartsAllocResp
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'code',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Common\ErrorCode',
        ),
        2 => array(
            'var' => 'leader',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\HostAddr',
        ),
        3 => array(
            'var' => 'parts',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I32,
            'vtype' => TType::LST,
            'key' => array(
                'type' => TType::I32,
            ),
            'val' => array(
                'type' => TType::LST,
                'etype' => TType::STRUCT,
                'elem' => array(
                    'type' => TType::STRUCT,
                    'class' => '\Nebula\Common\HostAddr',
                    ),
                ),
        ),
        4 => array(
            'var' => 'terms',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I32,
            'vtype' => TType::I64,
            'key' => array(
                'type' => TType::I32,
            ),
            'val' => array(
                'type' => TType::I64,
                ),
        ),
    );

    /**
     * @var int
     */
    public $code = null;
    /**
     * @var \Nebula\Common\HostAddr
     */
    public $leader = null;
    /**
     * @var array
     */
    public $parts = null;
    /**
     * @var array
     */
    public $terms = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['code'])) {
                $this->code = $vals['code'];
            }
            if (isset($vals['leader'])) {
                $this->leader = $vals['leader'];
            }
            if (isset($vals['parts'])) {
                $this->parts = $vals['parts'];
            }
            if (isset($vals['terms'])) {
                $this->terms = $vals['terms'];
            }
        }
    }

    public function getName()
    {
        return 'GetPartsAllocResp';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->code);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRUCT) {
                        $this->leader = new \Nebula\Common\HostAddr();
                        $xfer += $this->leader->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::MAP) {
                        $this->parts = array();
                        $_size194 = 0;
                        $_ktype195 = 0;
                        $_vtype196 = 0;
                        $xfer += $input->readMapBegin($_ktype195, $_vtype196, $_size194);
                        for ($_i198 = 0; $_i198 < $_size194; ++$_i198) {
                            $key199 = 0;
                            $val200 = array();
                            $xfer += $input->readI32($key199);
                            $val200 = array();
                            $_size201 = 0;
                            $_etype204 = 0;
                            $xfer += $input->readListBegin($_etype204, $_size201);
                            for ($_i205 = 0; $_i205 < $_size201; ++$_i205) {
                                $elem206 = null;
                                $elem206 = new \Nebula\Common\HostAddr();
                                $xfer += $elem206->read($input);
                                $val200 []= $elem206;
                            }
                            $xfer += $input->readListEnd();
                            $this->parts[$key199] = $val200;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::MAP) {
                        $this->terms = array();
                        $_size207 = 0;
                        $_ktype208 = 0;
                        $_vtype209 = 0;
                        $xfer += $input->readMapBegin($_ktype208, $_vtype209, $_size207);
                        for ($_i211 = 0; $_i211 < $_size207; ++$_i211) {
                            $key212 = 0;
                            $val213 = 0;
                            $xfer += $input->readI32($key212);
                            $xfer += $input->readI64($val213);
                            $this->terms[$key212] = $val213;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('GetPartsAllocResp');
        if ($this->code !== null) {
            $xfer += $output->writeFieldBegin('code', TType::I32, 1);
            $xfer += $output->writeI32($this->code);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader !== null) {
            if (!is_object($this->leader)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('leader', TType::STRUCT, 2);
            $xfer += $this->leader->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->parts !== null) {
            if (!is_array($this->parts)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('parts', TType::MAP, 3);
            $output->writeMapBegin(TType::I32, TType::LST, count($this->parts));
            foreach ($this->parts as $kiter214 => $viter215) {
                $xfer += $output->writeI32($kiter214);
                $output->writeListBegin(TType::STRUCT, count($viter215));
                foreach ($viter215 as $iter216) {
                    $xfer += $iter216->write($output);
                }
                $output->writeListEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->terms !== null) {
            if (!is_array($this->terms)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('terms', TType::MAP, 4);
            $output->writeMapBegin(TType::I32, TType::I64, count($this->terms));
            foreach ($this->terms as $kiter217 => $viter218) {
                $xfer += $output->writeI32($kiter217);
                $xfer += $output->writeI64($viter218);
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
