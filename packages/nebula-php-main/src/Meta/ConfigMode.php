<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

final class ConfigMode
{
    const IMMUTABLE = 0;

    const REBOOT = 1;

    const MUTABLE = 2;

    const IGNORED = 3;

    static public $__names = array(
        0 => 'IMMUTABLE',
        1 => 'REBOOT',
        2 => 'MUTABLE',
        3 => 'IGNORED',
    );
}

