<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class HBReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'role',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Meta\HostRole',
        ),
        2 => array(
            'var' => 'host',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\HostAddr',
        ),
        3 => array(
            'var' => 'cluster_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        4 => array(
            'var' => 'leader_partIds',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I32,
            'vtype' => TType::LST,
            'key' => array(
                'type' => TType::I32,
            ),
            'val' => array(
                'type' => TType::LST,
                'etype' => TType::STRUCT,
                'elem' => array(
                    'type' => TType::STRUCT,
                    'class' => '\Nebula\Meta\LeaderInfo',
                    ),
                ),
        ),
        5 => array(
            'var' => 'git_info_sha',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        6 => array(
            'var' => 'version',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var int
     */
    public $role = null;
    /**
     * @var \Nebula\Common\HostAddr
     */
    public $host = null;
    /**
     * @var int
     */
    public $cluster_id = null;
    /**
     * @var array
     */
    public $leader_partIds = null;
    /**
     * @var string
     */
    public $git_info_sha = null;
    /**
     * @var string
     */
    public $version = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['role'])) {
                $this->role = $vals['role'];
            }
            if (isset($vals['host'])) {
                $this->host = $vals['host'];
            }
            if (isset($vals['cluster_id'])) {
                $this->cluster_id = $vals['cluster_id'];
            }
            if (isset($vals['leader_partIds'])) {
                $this->leader_partIds = $vals['leader_partIds'];
            }
            if (isset($vals['git_info_sha'])) {
                $this->git_info_sha = $vals['git_info_sha'];
            }
            if (isset($vals['version'])) {
                $this->version = $vals['version'];
            }
        }
    }

    public function getName()
    {
        return 'HBReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->role);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRUCT) {
                        $this->host = new \Nebula\Common\HostAddr();
                        $xfer += $this->host->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->cluster_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::MAP) {
                        $this->leader_partIds = array();
                        $_size247 = 0;
                        $_ktype248 = 0;
                        $_vtype249 = 0;
                        $xfer += $input->readMapBegin($_ktype248, $_vtype249, $_size247);
                        for ($_i251 = 0; $_i251 < $_size247; ++$_i251) {
                            $key252 = 0;
                            $val253 = array();
                            $xfer += $input->readI32($key252);
                            $val253 = array();
                            $_size254 = 0;
                            $_etype257 = 0;
                            $xfer += $input->readListBegin($_etype257, $_size254);
                            for ($_i258 = 0; $_i258 < $_size254; ++$_i258) {
                                $elem259 = null;
                                $elem259 = new \Nebula\Meta\LeaderInfo();
                                $xfer += $elem259->read($input);
                                $val253 []= $elem259;
                            }
                            $xfer += $input->readListEnd();
                            $this->leader_partIds[$key252] = $val253;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->git_info_sha);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->version);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('HBReq');
        if ($this->role !== null) {
            $xfer += $output->writeFieldBegin('role', TType::I32, 1);
            $xfer += $output->writeI32($this->role);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->host !== null) {
            if (!is_object($this->host)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('host', TType::STRUCT, 2);
            $xfer += $this->host->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->cluster_id !== null) {
            $xfer += $output->writeFieldBegin('cluster_id', TType::I64, 3);
            $xfer += $output->writeI64($this->cluster_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader_partIds !== null) {
            if (!is_array($this->leader_partIds)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('leader_partIds', TType::MAP, 4);
            $output->writeMapBegin(TType::I32, TType::LST, count($this->leader_partIds));
            foreach ($this->leader_partIds as $kiter260 => $viter261) {
                $xfer += $output->writeI32($kiter260);
                $output->writeListBegin(TType::STRUCT, count($viter261));
                foreach ($viter261 as $iter262) {
                    $xfer += $iter262->write($output);
                }
                $output->writeListEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->git_info_sha !== null) {
            $xfer += $output->writeFieldBegin('git_info_sha', TType::STRING, 5);
            $xfer += $output->writeString($this->git_info_sha);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->version !== null) {
            $xfer += $output->writeFieldBegin('version', TType::STRING, 6);
            $xfer += $output->writeString($this->version);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
