<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class RestoreMetaReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'files',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRING,
            'elem' => array(
                'type' => TType::STRING,
                ),
        ),
        2 => array(
            'var' => 'hosts',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Meta\HostPair',
                ),
        ),
    );

    /**
     * @var string[]
     */
    public $files = null;
    /**
     * @var \Nebula\Meta\HostPair[]
     */
    public $hosts = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['files'])) {
                $this->files = $vals['files'];
            }
            if (isset($vals['hosts'])) {
                $this->hosts = $vals['hosts'];
            }
        }
    }

    public function getName()
    {
        return 'RestoreMetaReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::LST) {
                        $this->files = array();
                        $_size463 = 0;
                        $_etype466 = 0;
                        $xfer += $input->readListBegin($_etype466, $_size463);
                        for ($_i467 = 0; $_i467 < $_size463; ++$_i467) {
                            $elem468 = null;
                            $xfer += $input->readString($elem468);
                            $this->files []= $elem468;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::LST) {
                        $this->hosts = array();
                        $_size469 = 0;
                        $_etype472 = 0;
                        $xfer += $input->readListBegin($_etype472, $_size469);
                        for ($_i473 = 0; $_i473 < $_size469; ++$_i473) {
                            $elem474 = null;
                            $elem474 = new \Nebula\Meta\HostPair();
                            $xfer += $elem474->read($input);
                            $this->hosts []= $elem474;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('RestoreMetaReq');
        if ($this->files !== null) {
            if (!is_array($this->files)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('files', TType::LST, 1);
            $output->writeListBegin(TType::STRING, count($this->files));
            foreach ($this->files as $iter475) {
                $xfer += $output->writeString($iter475);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->hosts !== null) {
            if (!is_array($this->hosts)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('hosts', TType::LST, 2);
            $output->writeListBegin(TType::STRUCT, count($this->hosts));
            foreach ($this->hosts as $iter476) {
                $xfer += $iter476->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
