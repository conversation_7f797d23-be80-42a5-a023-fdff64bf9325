<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class TagItem
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'tag_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'tag_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        3 => array(
            'var' => 'version',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        4 => array(
            'var' => 'schema',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Meta\Schema',
        ),
    );

    /**
     * @var int
     */
    public $tag_id = null;
    /**
     * @var string
     */
    public $tag_name = null;
    /**
     * @var int
     */
    public $version = null;
    /**
     * @var \Nebula\Meta\Schema
     */
    public $schema = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['tag_id'])) {
                $this->tag_id = $vals['tag_id'];
            }
            if (isset($vals['tag_name'])) {
                $this->tag_name = $vals['tag_name'];
            }
            if (isset($vals['version'])) {
                $this->version = $vals['version'];
            }
            if (isset($vals['schema'])) {
                $this->schema = $vals['schema'];
            }
        }
    }

    public function getName()
    {
        return 'TagItem';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->tag_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->tag_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->version);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::STRUCT) {
                        $this->schema = new \Nebula\Meta\Schema();
                        $xfer += $this->schema->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('TagItem');
        if ($this->tag_id !== null) {
            $xfer += $output->writeFieldBegin('tag_id', TType::I32, 1);
            $xfer += $output->writeI32($this->tag_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->tag_name !== null) {
            $xfer += $output->writeFieldBegin('tag_name', TType::STRING, 2);
            $xfer += $output->writeString($this->tag_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->version !== null) {
            $xfer += $output->writeFieldBegin('version', TType::I64, 3);
            $xfer += $output->writeI64($this->version);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->schema !== null) {
            if (!is_object($this->schema)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('schema', TType::STRUCT, 4);
            $xfer += $this->schema->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
