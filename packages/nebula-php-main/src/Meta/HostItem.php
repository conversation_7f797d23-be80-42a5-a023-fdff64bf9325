<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class HostItem
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'hostAddr',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\HostAddr',
        ),
        2 => array(
            'var' => 'status',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Meta\HostStatus',
        ),
        3 => array(
            'var' => 'leader_parts',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::STRING,
            'vtype' => TType::LST,
            'key' => array(
                'type' => TType::STRING,
            ),
            'val' => array(
                'type' => TType::LST,
                'etype' => TType::I32,
                'elem' => array(
                    'type' => TType::I32,
                    ),
                ),
        ),
        4 => array(
            'var' => 'all_parts',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::STRING,
            'vtype' => TType::LST,
            'key' => array(
                'type' => TType::STRING,
            ),
            'val' => array(
                'type' => TType::LST,
                'etype' => TType::I32,
                'elem' => array(
                    'type' => TType::I32,
                    ),
                ),
        ),
        5 => array(
            'var' => 'role',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Meta\HostRole',
        ),
        6 => array(
            'var' => 'git_info_sha',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        7 => array(
            'var' => 'zone_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        8 => array(
            'var' => 'version',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var \Nebula\Common\HostAddr
     */
    public $hostAddr = null;
    /**
     * @var int
     */
    public $status = null;
    /**
     * @var array
     */
    public $leader_parts = null;
    /**
     * @var array
     */
    public $all_parts = null;
    /**
     * @var int
     */
    public $role = null;
    /**
     * @var string
     */
    public $git_info_sha = null;
    /**
     * @var string
     */
    public $zone_name = null;
    /**
     * @var string
     */
    public $version = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['hostAddr'])) {
                $this->hostAddr = $vals['hostAddr'];
            }
            if (isset($vals['status'])) {
                $this->status = $vals['status'];
            }
            if (isset($vals['leader_parts'])) {
                $this->leader_parts = $vals['leader_parts'];
            }
            if (isset($vals['all_parts'])) {
                $this->all_parts = $vals['all_parts'];
            }
            if (isset($vals['role'])) {
                $this->role = $vals['role'];
            }
            if (isset($vals['git_info_sha'])) {
                $this->git_info_sha = $vals['git_info_sha'];
            }
            if (isset($vals['zone_name'])) {
                $this->zone_name = $vals['zone_name'];
            }
            if (isset($vals['version'])) {
                $this->version = $vals['version'];
            }
        }
    }

    public function getName()
    {
        return 'HostItem';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRUCT) {
                        $this->hostAddr = new \Nebula\Common\HostAddr();
                        $xfer += $this->hostAddr->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->status);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::MAP) {
                        $this->leader_parts = array();
                        $_size14 = 0;
                        $_ktype15 = 0;
                        $_vtype16 = 0;
                        $xfer += $input->readMapBegin($_ktype15, $_vtype16, $_size14);
                        for ($_i18 = 0; $_i18 < $_size14; ++$_i18) {
                            $key19 = '';
                            $val20 = array();
                            $xfer += $input->readString($key19);
                            $val20 = array();
                            $_size21 = 0;
                            $_etype24 = 0;
                            $xfer += $input->readListBegin($_etype24, $_size21);
                            for ($_i25 = 0; $_i25 < $_size21; ++$_i25) {
                                $elem26 = null;
                                $xfer += $input->readI32($elem26);
                                $val20 []= $elem26;
                            }
                            $xfer += $input->readListEnd();
                            $this->leader_parts[$key19] = $val20;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::MAP) {
                        $this->all_parts = array();
                        $_size27 = 0;
                        $_ktype28 = 0;
                        $_vtype29 = 0;
                        $xfer += $input->readMapBegin($_ktype28, $_vtype29, $_size27);
                        for ($_i31 = 0; $_i31 < $_size27; ++$_i31) {
                            $key32 = '';
                            $val33 = array();
                            $xfer += $input->readString($key32);
                            $val33 = array();
                            $_size34 = 0;
                            $_etype37 = 0;
                            $xfer += $input->readListBegin($_etype37, $_size34);
                            for ($_i38 = 0; $_i38 < $_size34; ++$_i38) {
                                $elem39 = null;
                                $xfer += $input->readI32($elem39);
                                $val33 []= $elem39;
                            }
                            $xfer += $input->readListEnd();
                            $this->all_parts[$key32] = $val33;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->role);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->git_info_sha);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->zone_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 8:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->version);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('HostItem');
        if ($this->hostAddr !== null) {
            if (!is_object($this->hostAddr)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('hostAddr', TType::STRUCT, 1);
            $xfer += $this->hostAddr->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->status !== null) {
            $xfer += $output->writeFieldBegin('status', TType::I32, 2);
            $xfer += $output->writeI32($this->status);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader_parts !== null) {
            if (!is_array($this->leader_parts)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('leader_parts', TType::MAP, 3);
            $output->writeMapBegin(TType::STRING, TType::LST, count($this->leader_parts));
            foreach ($this->leader_parts as $kiter40 => $viter41) {
                $xfer += $output->writeString($kiter40);
                $output->writeListBegin(TType::I32, count($viter41));
                foreach ($viter41 as $iter42) {
                    $xfer += $output->writeI32($iter42);
                }
                $output->writeListEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->all_parts !== null) {
            if (!is_array($this->all_parts)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('all_parts', TType::MAP, 4);
            $output->writeMapBegin(TType::STRING, TType::LST, count($this->all_parts));
            foreach ($this->all_parts as $kiter43 => $viter44) {
                $xfer += $output->writeString($kiter43);
                $output->writeListBegin(TType::I32, count($viter44));
                foreach ($viter44 as $iter45) {
                    $xfer += $output->writeI32($iter45);
                }
                $output->writeListEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->role !== null) {
            $xfer += $output->writeFieldBegin('role', TType::I32, 5);
            $xfer += $output->writeI32($this->role);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->git_info_sha !== null) {
            $xfer += $output->writeFieldBegin('git_info_sha', TType::STRING, 6);
            $xfer += $output->writeString($this->git_info_sha);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->zone_name !== null) {
            $xfer += $output->writeFieldBegin('zone_name', TType::STRING, 7);
            $xfer += $output->writeString($this->zone_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->version !== null) {
            $xfer += $output->writeFieldBegin('version', TType::STRING, 8);
            $xfer += $output->writeString($this->version);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
