<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class RegConfigReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'items',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Meta\ConfigItem',
                ),
        ),
    );

    /**
     * @var \Nebula\Meta\ConfigItem[]
     */
    public $items = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['items'])) {
                $this->items = $vals['items'];
            }
        }
    }

    public function getName()
    {
        return 'RegConfigReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::LST) {
                        $this->items = array();
                        $_size321 = 0;
                        $_etype324 = 0;
                        $xfer += $input->readListBegin($_etype324, $_size321);
                        for ($_i325 = 0; $_i325 < $_size321; ++$_i325) {
                            $elem326 = null;
                            $elem326 = new \Nebula\Meta\ConfigItem();
                            $xfer += $elem326->read($input);
                            $this->items []= $elem326;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('RegConfigReq');
        if ($this->items !== null) {
            if (!is_array($this->items)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('items', TType::LST, 1);
            $output->writeListBegin(TType::STRUCT, count($this->items));
            foreach ($this->items as $iter327) {
                $xfer += $iter327->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
