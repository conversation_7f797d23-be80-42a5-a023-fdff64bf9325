<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class HostPair
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'from_host',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\HostAddr',
        ),
        2 => array(
            'var' => 'to_host',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\HostAddr',
        ),
    );

    /**
     * @var \Nebula\Common\HostAddr
     */
    public $from_host = null;
    /**
     * @var \Nebula\Common\HostAddr
     */
    public $to_host = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['from_host'])) {
                $this->from_host = $vals['from_host'];
            }
            if (isset($vals['to_host'])) {
                $this->to_host = $vals['to_host'];
            }
        }
    }

    public function getName()
    {
        return 'HostPair';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRUCT) {
                        $this->from_host = new \Nebula\Common\HostAddr();
                        $xfer += $this->from_host->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRUCT) {
                        $this->to_host = new \Nebula\Common\HostAddr();
                        $xfer += $this->to_host->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('HostPair');
        if ($this->from_host !== null) {
            if (!is_object($this->from_host)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('from_host', TType::STRUCT, 1);
            $xfer += $this->from_host->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->to_host !== null) {
            if (!is_object($this->to_host)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('to_host', TType::STRUCT, 2);
            $xfer += $this->to_host->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
