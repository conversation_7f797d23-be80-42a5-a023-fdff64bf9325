<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class MultiGetReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'segment',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        2 => array(
            'var' => 'keys',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRING,
            'elem' => array(
                'type' => TType::STRING,
                ),
        ),
    );

    /**
     * @var string
     */
    public $segment = null;
    /**
     * @var string[]
     */
    public $keys = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['segment'])) {
                $this->segment = $vals['segment'];
            }
            if (isset($vals['keys'])) {
                $this->keys = $vals['keys'];
            }
        }
    }

    public function getName()
    {
        return 'MultiGetReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->segment);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::LST) {
                        $this->keys = array();
                        $_size226 = 0;
                        $_etype229 = 0;
                        $xfer += $input->readListBegin($_etype229, $_size226);
                        for ($_i230 = 0; $_i230 < $_size226; ++$_i230) {
                            $elem231 = null;
                            $xfer += $input->readString($elem231);
                            $this->keys []= $elem231;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('MultiGetReq');
        if ($this->segment !== null) {
            $xfer += $output->writeFieldBegin('segment', TType::STRING, 1);
            $xfer += $output->writeString($this->segment);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->keys !== null) {
            if (!is_array($this->keys)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('keys', TType::LST, 2);
            $output->writeListBegin(TType::STRING, count($this->keys));
            foreach ($this->keys as $iter232) {
                $xfer += $output->writeString($iter232);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
