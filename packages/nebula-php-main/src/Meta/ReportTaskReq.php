<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ReportTaskReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'code',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Common\ErrorCode',
        ),
        2 => array(
            'var' => 'job_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'task_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        4 => array(
            'var' => 'stats',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Meta\StatsItem',
        ),
    );

    /**
     * @var int
     */
    public $code = null;
    /**
     * @var int
     */
    public $job_id = null;
    /**
     * @var int
     */
    public $task_id = null;
    /**
     * @var \Nebula\Meta\StatsItem
     */
    public $stats = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['code'])) {
                $this->code = $vals['code'];
            }
            if (isset($vals['job_id'])) {
                $this->job_id = $vals['job_id'];
            }
            if (isset($vals['task_id'])) {
                $this->task_id = $vals['task_id'];
            }
            if (isset($vals['stats'])) {
                $this->stats = $vals['stats'];
            }
        }
    }

    public function getName()
    {
        return 'ReportTaskReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->code);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->job_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->task_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::STRUCT) {
                        $this->stats = new \Nebula\Meta\StatsItem();
                        $xfer += $this->stats->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ReportTaskReq');
        if ($this->code !== null) {
            $xfer += $output->writeFieldBegin('code', TType::I32, 1);
            $xfer += $output->writeI32($this->code);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->job_id !== null) {
            $xfer += $output->writeFieldBegin('job_id', TType::I32, 2);
            $xfer += $output->writeI32($this->job_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->task_id !== null) {
            $xfer += $output->writeFieldBegin('task_id', TType::I32, 3);
            $xfer += $output->writeI32($this->task_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->stats !== null) {
            if (!is_object($this->stats)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('stats', TType::STRUCT, 4);
            $xfer += $this->stats->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
