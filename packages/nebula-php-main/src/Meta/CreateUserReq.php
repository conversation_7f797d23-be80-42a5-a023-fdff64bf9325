<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class CreateUserReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'account',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        2 => array(
            'var' => 'encoded_pwd',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        3 => array(
            'var' => 'if_not_exists',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
    );

    /**
     * @var string
     */
    public $account = null;
    /**
     * @var string
     */
    public $encoded_pwd = null;
    /**
     * @var bool
     */
    public $if_not_exists = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['account'])) {
                $this->account = $vals['account'];
            }
            if (isset($vals['encoded_pwd'])) {
                $this->encoded_pwd = $vals['encoded_pwd'];
            }
            if (isset($vals['if_not_exists'])) {
                $this->if_not_exists = $vals['if_not_exists'];
            }
        }
    }

    public function getName()
    {
        return 'CreateUserReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->account);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->encoded_pwd);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->if_not_exists);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('CreateUserReq');
        if ($this->account !== null) {
            $xfer += $output->writeFieldBegin('account', TType::STRING, 1);
            $xfer += $output->writeString($this->account);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->encoded_pwd !== null) {
            $xfer += $output->writeFieldBegin('encoded_pwd', TType::STRING, 2);
            $xfer += $output->writeString($this->encoded_pwd);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->if_not_exists !== null) {
            $xfer += $output->writeFieldBegin('if_not_exists', TType::BOOL, 3);
            $xfer += $output->writeBool($this->if_not_exists);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
