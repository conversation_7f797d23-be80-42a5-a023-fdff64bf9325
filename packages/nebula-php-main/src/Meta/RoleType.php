<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

final class RoleType
{
    const GOD = 1;

    const ADMIN = 2;

    const DBA = 3;

    const USER = 4;

    const GUEST = 5;

    static public $__names = array(
        1 => 'GOD',
        2 => 'ADMIN',
        3 => 'DBA',
        4 => 'USER',
        5 => 'GUEST',
    );
}

