<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class IndexItem
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'index_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'index_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        3 => array(
            'var' => 'schema_id',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\SchemaID',
        ),
        4 => array(
            'var' => 'schema_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        5 => array(
            'var' => 'fields',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Meta\ColumnDef',
                ),
        ),
        6 => array(
            'var' => 'comment',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var int
     */
    public $index_id = null;
    /**
     * @var string
     */
    public $index_name = null;
    /**
     * @var \Nebula\Common\SchemaID
     */
    public $schema_id = null;
    /**
     * @var string
     */
    public $schema_name = null;
    /**
     * @var \Nebula\Meta\ColumnDef[]
     */
    public $fields = null;
    /**
     * @var string
     */
    public $comment = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['index_id'])) {
                $this->index_id = $vals['index_id'];
            }
            if (isset($vals['index_name'])) {
                $this->index_name = $vals['index_name'];
            }
            if (isset($vals['schema_id'])) {
                $this->schema_id = $vals['schema_id'];
            }
            if (isset($vals['schema_name'])) {
                $this->schema_name = $vals['schema_name'];
            }
            if (isset($vals['fields'])) {
                $this->fields = $vals['fields'];
            }
            if (isset($vals['comment'])) {
                $this->comment = $vals['comment'];
            }
        }
    }

    public function getName()
    {
        return 'IndexItem';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->index_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->index_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRUCT) {
                        $this->schema_id = new \Nebula\Common\SchemaID();
                        $xfer += $this->schema_id->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->schema_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::LST) {
                        $this->fields = array();
                        $_size7 = 0;
                        $_etype10 = 0;
                        $xfer += $input->readListBegin($_etype10, $_size7);
                        for ($_i11 = 0; $_i11 < $_size7; ++$_i11) {
                            $elem12 = null;
                            $elem12 = new \Nebula\Meta\ColumnDef();
                            $xfer += $elem12->read($input);
                            $this->fields []= $elem12;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->comment);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('IndexItem');
        if ($this->index_id !== null) {
            $xfer += $output->writeFieldBegin('index_id', TType::I32, 1);
            $xfer += $output->writeI32($this->index_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->index_name !== null) {
            $xfer += $output->writeFieldBegin('index_name', TType::STRING, 2);
            $xfer += $output->writeString($this->index_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->schema_id !== null) {
            if (!is_object($this->schema_id)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('schema_id', TType::STRUCT, 3);
            $xfer += $this->schema_id->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->schema_name !== null) {
            $xfer += $output->writeFieldBegin('schema_name', TType::STRING, 4);
            $xfer += $output->writeString($this->schema_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->fields !== null) {
            if (!is_array($this->fields)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('fields', TType::LST, 5);
            $output->writeListBegin(TType::STRUCT, count($this->fields));
            foreach ($this->fields as $iter13) {
                $xfer += $iter13->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->comment !== null) {
            $xfer += $output->writeFieldBegin('comment', TType::STRING, 6);
            $xfer += $output->writeString($this->comment);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
