<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ID
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'tag_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'edge_type',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        4 => array(
            'var' => 'index_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        5 => array(
            'var' => 'cluster_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
    );

    /**
     * @var int
     */
    public $space_id = null;
    /**
     * @var int
     */
    public $tag_id = null;
    /**
     * @var int
     */
    public $edge_type = null;
    /**
     * @var int
     */
    public $index_id = null;
    /**
     * @var int
     */
    public $cluster_id = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space_id'])) {
                $this->space_id = $vals['space_id'];
            }
            if (isset($vals['tag_id'])) {
                $this->tag_id = $vals['tag_id'];
            }
            if (isset($vals['edge_type'])) {
                $this->edge_type = $vals['edge_type'];
            }
            if (isset($vals['index_id'])) {
                $this->index_id = $vals['index_id'];
            }
            if (isset($vals['cluster_id'])) {
                $this->cluster_id = $vals['cluster_id'];
            }
        }
    }

    public function getName()
    {
        return 'ID';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->tag_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->edge_type);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->index_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->cluster_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ID');
        if ($this->space_id !== null) {
            $xfer += $output->writeFieldBegin('space_id', TType::I32, 1);
            $xfer += $output->writeI32($this->space_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->tag_id !== null) {
            $xfer += $output->writeFieldBegin('tag_id', TType::I32, 2);
            $xfer += $output->writeI32($this->tag_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->edge_type !== null) {
            $xfer += $output->writeFieldBegin('edge_type', TType::I32, 3);
            $xfer += $output->writeI32($this->edge_type);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->index_id !== null) {
            $xfer += $output->writeFieldBegin('index_id', TType::I32, 4);
            $xfer += $output->writeI32($this->index_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->cluster_id !== null) {
            $xfer += $output->writeFieldBegin('cluster_id', TType::I64, 5);
            $xfer += $output->writeI64($this->cluster_id);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
