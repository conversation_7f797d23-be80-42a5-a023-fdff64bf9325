<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class CreateFTIndexReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'fulltext_index_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        2 => array(
            'var' => 'index',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Meta\FTIndex',
        ),
    );

    /**
     * @var string
     */
    public $fulltext_index_name = null;
    /**
     * @var \Nebula\Meta\FTIndex
     */
    public $index = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['fulltext_index_name'])) {
                $this->fulltext_index_name = $vals['fulltext_index_name'];
            }
            if (isset($vals['index'])) {
                $this->index = $vals['index'];
            }
        }
    }

    public function getName()
    {
        return 'CreateFTIndexReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->fulltext_index_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRUCT) {
                        $this->index = new \Nebula\Meta\FTIndex();
                        $xfer += $this->index->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('CreateFTIndexReq');
        if ($this->fulltext_index_name !== null) {
            $xfer += $output->writeFieldBegin('fulltext_index_name', TType::STRING, 1);
            $xfer += $output->writeString($this->fulltext_index_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->index !== null) {
            if (!is_object($this->index)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('index', TType::STRUCT, 2);
            $xfer += $this->index->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
