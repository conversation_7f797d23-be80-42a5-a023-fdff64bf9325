<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class SpaceBackupInfo
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Meta\SpaceDesc',
        ),
        2 => array(
            'var' => 'info',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Meta\BackupInfo',
                ),
        ),
    );

    /**
     * @var \Nebula\Meta\SpaceDesc
     */
    public $space = null;
    /**
     * @var \Nebula\Meta\BackupInfo[]
     */
    public $info = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space'])) {
                $this->space = $vals['space'];
            }
            if (isset($vals['info'])) {
                $this->info = $vals['info'];
            }
        }
    }

    public function getName()
    {
        return 'SpaceBackupInfo';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRUCT) {
                        $this->space = new \Nebula\Meta\SpaceDesc();
                        $xfer += $this->space->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::LST) {
                        $this->info = array();
                        $_size433 = 0;
                        $_etype436 = 0;
                        $xfer += $input->readListBegin($_etype436, $_size433);
                        for ($_i437 = 0; $_i437 < $_size433; ++$_i437) {
                            $elem438 = null;
                            $elem438 = new \Nebula\Meta\BackupInfo();
                            $xfer += $elem438->read($input);
                            $this->info []= $elem438;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('SpaceBackupInfo');
        if ($this->space !== null) {
            if (!is_object($this->space)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('space', TType::STRUCT, 1);
            $xfer += $this->space->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->info !== null) {
            if (!is_array($this->info)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('info', TType::LST, 2);
            $output->writeListBegin(TType::STRUCT, count($this->info));
            foreach ($this->info as $iter439) {
                $xfer += $iter439->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
