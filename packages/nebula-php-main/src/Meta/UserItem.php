<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class UserItem
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'account',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        2 => array(
            'var' => 'is_lock',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        3 => array(
            'var' => 'max_queries_per_hour',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        4 => array(
            'var' => 'max_updates_per_hour',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        5 => array(
            'var' => 'max_connections_per_hour',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        6 => array(
            'var' => 'max_user_connections',
            'isRequired' => false,
            'type' => TType::I32,
        ),
    );

    /**
     * @var string
     */
    public $account = null;
    /**
     * @var bool
     */
    public $is_lock = null;
    /**
     * @var int
     */
    public $max_queries_per_hour = null;
    /**
     * @var int
     */
    public $max_updates_per_hour = null;
    /**
     * @var int
     */
    public $max_connections_per_hour = null;
    /**
     * @var int
     */
    public $max_user_connections = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['account'])) {
                $this->account = $vals['account'];
            }
            if (isset($vals['is_lock'])) {
                $this->is_lock = $vals['is_lock'];
            }
            if (isset($vals['max_queries_per_hour'])) {
                $this->max_queries_per_hour = $vals['max_queries_per_hour'];
            }
            if (isset($vals['max_updates_per_hour'])) {
                $this->max_updates_per_hour = $vals['max_updates_per_hour'];
            }
            if (isset($vals['max_connections_per_hour'])) {
                $this->max_connections_per_hour = $vals['max_connections_per_hour'];
            }
            if (isset($vals['max_user_connections'])) {
                $this->max_user_connections = $vals['max_user_connections'];
            }
        }
    }

    public function getName()
    {
        return 'UserItem';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->account);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->is_lock);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->max_queries_per_hour);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->max_updates_per_hour);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->max_connections_per_hour);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->max_user_connections);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('UserItem');
        if ($this->account !== null) {
            $xfer += $output->writeFieldBegin('account', TType::STRING, 1);
            $xfer += $output->writeString($this->account);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->is_lock !== null) {
            $xfer += $output->writeFieldBegin('is_lock', TType::BOOL, 2);
            $xfer += $output->writeBool($this->is_lock);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->max_queries_per_hour !== null) {
            $xfer += $output->writeFieldBegin('max_queries_per_hour', TType::I32, 3);
            $xfer += $output->writeI32($this->max_queries_per_hour);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->max_updates_per_hour !== null) {
            $xfer += $output->writeFieldBegin('max_updates_per_hour', TType::I32, 4);
            $xfer += $output->writeI32($this->max_updates_per_hour);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->max_connections_per_hour !== null) {
            $xfer += $output->writeFieldBegin('max_connections_per_hour', TType::I32, 5);
            $xfer += $output->writeI32($this->max_connections_per_hour);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->max_user_connections !== null) {
            $xfer += $output->writeFieldBegin('max_user_connections', TType::I32, 6);
            $xfer += $output->writeI32($this->max_user_connections);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
