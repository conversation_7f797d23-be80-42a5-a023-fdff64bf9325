<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

final class ConfigModule
{
    const UNKNOWN = 0;

    const ALL = 1;

    const GRAPH = 2;

    const META = 3;

    const STORAGE = 4;

    static public $__names = array(
        0 => 'UNKNOWN',
        1 => 'ALL',
        2 => 'GRAPH',
        3 => 'META',
        4 => 'STORAGE',
    );
}

