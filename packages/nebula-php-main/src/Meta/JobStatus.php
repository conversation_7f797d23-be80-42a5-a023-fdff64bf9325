<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

final class JobStatus
{
    const QUEUE = 1;

    const RUNNING = 2;

    const FINISHED = 3;

    const FAILED = 4;

    const STOPPED = 5;

    const INVALID = 255;

    static public $__names = array(
        1 => 'QUEUE',
        2 => 'RUNNING',
        3 => 'FINISHED',
        4 => 'FAILED',
        5 => 'STOPPED',
        255 => 'INVALID',
    );
}

