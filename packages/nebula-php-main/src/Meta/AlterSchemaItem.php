<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class AlterSchemaItem
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'op',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Meta\AlterSchemaOp',
        ),
        2 => array(
            'var' => 'schema',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Meta\Schema',
        ),
    );

    /**
     * @var int
     */
    public $op = null;
    /**
     * @var \Nebula\Meta\Schema
     */
    public $schema = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['op'])) {
                $this->op = $vals['op'];
            }
            if (isset($vals['schema'])) {
                $this->schema = $vals['schema'];
            }
        }
    }

    public function getName()
    {
        return 'AlterSchemaItem';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->op);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRUCT) {
                        $this->schema = new \Nebula\Meta\Schema();
                        $xfer += $this->schema->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('AlterSchemaItem');
        if ($this->op !== null) {
            $xfer += $output->writeFieldBegin('op', TType::I32, 1);
            $xfer += $output->writeI32($this->op);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->schema !== null) {
            if (!is_object($this->schema)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('schema', TType::STRUCT, 2);
            $xfer += $this->schema->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
