<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class UpdateSessionsReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'sessions',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Meta\Session',
                ),
        ),
    );

    /**
     * @var \Nebula\Meta\Session[]
     */
    public $sessions = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['sessions'])) {
                $this->sessions = $vals['sessions'];
            }
        }
    }

    public function getName()
    {
        return 'UpdateSessionsReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::LST) {
                        $this->sessions = array();
                        $_size525 = 0;
                        $_etype528 = 0;
                        $xfer += $input->readListBegin($_etype528, $_size525);
                        for ($_i529 = 0; $_i529 < $_size525; ++$_i529) {
                            $elem530 = null;
                            $elem530 = new \Nebula\Meta\Session();
                            $xfer += $elem530->read($input);
                            $this->sessions []= $elem530;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('UpdateSessionsReq');
        if ($this->sessions !== null) {
            if (!is_array($this->sessions)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('sessions', TType::LST, 1);
            $output->writeListBegin(TType::STRUCT, count($this->sessions));
            foreach ($this->sessions as $iter531) {
                $xfer += $iter531->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
