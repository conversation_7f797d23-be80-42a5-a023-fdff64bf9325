<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class KillQueryReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'kill_queries',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I64,
            'vtype' => TType::SET,
            'key' => array(
                'type' => TType::I64,
            ),
            'val' => array(
                'type' => TType::SET,
                'etype' => TType::I64,
                'elem' => array(
                    'type' => TType::I64,
                    ),
                ),
        ),
    );

    /**
     * @var array
     */
    public $kill_queries = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['kill_queries'])) {
                $this->kill_queries = $vals['kill_queries'];
            }
        }
    }

    public function getName()
    {
        return 'KillQueryReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::MAP) {
                        $this->kill_queries = array();
                        $_size557 = 0;
                        $_ktype558 = 0;
                        $_vtype559 = 0;
                        $xfer += $input->readMapBegin($_ktype558, $_vtype559, $_size557);
                        for ($_i561 = 0; $_i561 < $_size557; ++$_i561) {
                            $key562 = 0;
                            $val563 = array();
                            $xfer += $input->readI64($key562);
                            $val563 = array();
                            $_size564 = 0;
                            $_etype567 = 0;
                            $xfer += $input->readSetBegin($_etype567, $_size564);
                            for ($_i568 = 0; $_i568 < $_size564; ++$_i568) {
                                $elem569 = null;
                                $xfer += $input->readI64($elem569);
                                $val563[$elem569] = true;
                            }
                            $xfer += $input->readSetEnd();
                            $this->kill_queries[$key562] = $val563;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('KillQueryReq');
        if ($this->kill_queries !== null) {
            if (!is_array($this->kill_queries)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('kill_queries', TType::MAP, 1);
            $output->writeMapBegin(TType::I64, TType::SET, count($this->kill_queries));
            foreach ($this->kill_queries as $kiter570 => $viter571) {
                $xfer += $output->writeI64($kiter570);
                $output->writeSetBegin(TType::I64, count($viter571));
                foreach ($viter571 as $iter572 => $iter573) {
                    $xfer += $output->writeI64($iter572);
                }
                $output->writeSetEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
