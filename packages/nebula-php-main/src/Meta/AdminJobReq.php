<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class AdminJobReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'op',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Meta\AdminJobOp',
        ),
        2 => array(
            'var' => 'cmd',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Meta\AdminCmd',
        ),
        3 => array(
            'var' => 'paras',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRING,
            'elem' => array(
                'type' => TType::STRING,
                ),
        ),
    );

    /**
     * @var int
     */
    public $op = null;
    /**
     * @var int
     */
    public $cmd = null;
    /**
     * @var string[]
     */
    public $paras = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['op'])) {
                $this->op = $vals['op'];
            }
            if (isset($vals['cmd'])) {
                $this->cmd = $vals['cmd'];
            }
            if (isset($vals['paras'])) {
                $this->paras = $vals['paras'];
            }
        }
    }

    public function getName()
    {
        return 'AdminJobReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->op);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->cmd);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::LST) {
                        $this->paras = array();
                        $_size46 = 0;
                        $_etype49 = 0;
                        $xfer += $input->readListBegin($_etype49, $_size46);
                        for ($_i50 = 0; $_i50 < $_size46; ++$_i50) {
                            $elem51 = null;
                            $xfer += $input->readString($elem51);
                            $this->paras []= $elem51;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('AdminJobReq');
        if ($this->op !== null) {
            $xfer += $output->writeFieldBegin('op', TType::I32, 1);
            $xfer += $output->writeI32($this->op);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->cmd !== null) {
            $xfer += $output->writeFieldBegin('cmd', TType::I32, 2);
            $xfer += $output->writeI32($this->cmd);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->paras !== null) {
            if (!is_array($this->paras)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('paras', TType::LST, 3);
            $output->writeListBegin(TType::STRING, count($this->paras));
            foreach ($this->paras as $iter52) {
                $xfer += $output->writeString($iter52);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
