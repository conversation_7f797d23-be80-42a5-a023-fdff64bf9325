<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class StatsItem
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'tag_vertices',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::STRING,
            'vtype' => TType::I64,
            'key' => array(
                'type' => TType::STRING,
            ),
            'val' => array(
                'type' => TType::I64,
                ),
        ),
        2 => array(
            'var' => 'edges',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::STRING,
            'vtype' => TType::I64,
            'key' => array(
                'type' => TType::STRING,
            ),
            'val' => array(
                'type' => TType::I64,
                ),
        ),
        3 => array(
            'var' => 'space_vertices',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        4 => array(
            'var' => 'space_edges',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        5 => array(
            'var' => 'positive_part_correlativity',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I32,
            'vtype' => TType::LST,
            'key' => array(
                'type' => TType::I32,
            ),
            'val' => array(
                'type' => TType::LST,
                'etype' => TType::STRUCT,
                'elem' => array(
                    'type' => TType::STRUCT,
                    'class' => '\Nebula\Meta\Correlativity',
                    ),
                ),
        ),
        6 => array(
            'var' => 'negative_part_correlativity',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I32,
            'vtype' => TType::LST,
            'key' => array(
                'type' => TType::I32,
            ),
            'val' => array(
                'type' => TType::LST,
                'etype' => TType::STRUCT,
                'elem' => array(
                    'type' => TType::STRUCT,
                    'class' => '\Nebula\Meta\Correlativity',
                    ),
                ),
        ),
        7 => array(
            'var' => 'status',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Meta\JobStatus',
        ),
    );

    /**
     * @var array
     */
    public $tag_vertices = null;
    /**
     * @var array
     */
    public $edges = null;
    /**
     * @var int
     */
    public $space_vertices = null;
    /**
     * @var int
     */
    public $space_edges = null;
    /**
     * @var array
     */
    public $positive_part_correlativity = null;
    /**
     * @var array
     */
    public $negative_part_correlativity = null;
    /**
     * @var int
     */
    public $status = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['tag_vertices'])) {
                $this->tag_vertices = $vals['tag_vertices'];
            }
            if (isset($vals['edges'])) {
                $this->edges = $vals['edges'];
            }
            if (isset($vals['space_vertices'])) {
                $this->space_vertices = $vals['space_vertices'];
            }
            if (isset($vals['space_edges'])) {
                $this->space_edges = $vals['space_edges'];
            }
            if (isset($vals['positive_part_correlativity'])) {
                $this->positive_part_correlativity = $vals['positive_part_correlativity'];
            }
            if (isset($vals['negative_part_correlativity'])) {
                $this->negative_part_correlativity = $vals['negative_part_correlativity'];
            }
            if (isset($vals['status'])) {
                $this->status = $vals['status'];
            }
        }
    }

    public function getName()
    {
        return 'StatsItem';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::MAP) {
                        $this->tag_vertices = array();
                        $_size74 = 0;
                        $_ktype75 = 0;
                        $_vtype76 = 0;
                        $xfer += $input->readMapBegin($_ktype75, $_vtype76, $_size74);
                        for ($_i78 = 0; $_i78 < $_size74; ++$_i78) {
                            $key79 = '';
                            $val80 = 0;
                            $xfer += $input->readString($key79);
                            $xfer += $input->readI64($val80);
                            $this->tag_vertices[$key79] = $val80;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::MAP) {
                        $this->edges = array();
                        $_size81 = 0;
                        $_ktype82 = 0;
                        $_vtype83 = 0;
                        $xfer += $input->readMapBegin($_ktype82, $_vtype83, $_size81);
                        for ($_i85 = 0; $_i85 < $_size81; ++$_i85) {
                            $key86 = '';
                            $val87 = 0;
                            $xfer += $input->readString($key86);
                            $xfer += $input->readI64($val87);
                            $this->edges[$key86] = $val87;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->space_vertices);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->space_edges);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::MAP) {
                        $this->positive_part_correlativity = array();
                        $_size88 = 0;
                        $_ktype89 = 0;
                        $_vtype90 = 0;
                        $xfer += $input->readMapBegin($_ktype89, $_vtype90, $_size88);
                        for ($_i92 = 0; $_i92 < $_size88; ++$_i92) {
                            $key93 = 0;
                            $val94 = array();
                            $xfer += $input->readI32($key93);
                            $val94 = array();
                            $_size95 = 0;
                            $_etype98 = 0;
                            $xfer += $input->readListBegin($_etype98, $_size95);
                            for ($_i99 = 0; $_i99 < $_size95; ++$_i99) {
                                $elem100 = null;
                                $elem100 = new \Nebula\Meta\Correlativity();
                                $xfer += $elem100->read($input);
                                $val94 []= $elem100;
                            }
                            $xfer += $input->readListEnd();
                            $this->positive_part_correlativity[$key93] = $val94;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::MAP) {
                        $this->negative_part_correlativity = array();
                        $_size101 = 0;
                        $_ktype102 = 0;
                        $_vtype103 = 0;
                        $xfer += $input->readMapBegin($_ktype102, $_vtype103, $_size101);
                        for ($_i105 = 0; $_i105 < $_size101; ++$_i105) {
                            $key106 = 0;
                            $val107 = array();
                            $xfer += $input->readI32($key106);
                            $val107 = array();
                            $_size108 = 0;
                            $_etype111 = 0;
                            $xfer += $input->readListBegin($_etype111, $_size108);
                            for ($_i112 = 0; $_i112 < $_size108; ++$_i112) {
                                $elem113 = null;
                                $elem113 = new \Nebula\Meta\Correlativity();
                                $xfer += $elem113->read($input);
                                $val107 []= $elem113;
                            }
                            $xfer += $input->readListEnd();
                            $this->negative_part_correlativity[$key106] = $val107;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->status);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('StatsItem');
        if ($this->tag_vertices !== null) {
            if (!is_array($this->tag_vertices)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('tag_vertices', TType::MAP, 1);
            $output->writeMapBegin(TType::STRING, TType::I64, count($this->tag_vertices));
            foreach ($this->tag_vertices as $kiter114 => $viter115) {
                $xfer += $output->writeString($kiter114);
                $xfer += $output->writeI64($viter115);
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->edges !== null) {
            if (!is_array($this->edges)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('edges', TType::MAP, 2);
            $output->writeMapBegin(TType::STRING, TType::I64, count($this->edges));
            foreach ($this->edges as $kiter116 => $viter117) {
                $xfer += $output->writeString($kiter116);
                $xfer += $output->writeI64($viter117);
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->space_vertices !== null) {
            $xfer += $output->writeFieldBegin('space_vertices', TType::I64, 3);
            $xfer += $output->writeI64($this->space_vertices);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->space_edges !== null) {
            $xfer += $output->writeFieldBegin('space_edges', TType::I64, 4);
            $xfer += $output->writeI64($this->space_edges);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->positive_part_correlativity !== null) {
            if (!is_array($this->positive_part_correlativity)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('positive_part_correlativity', TType::MAP, 5);
            $output->writeMapBegin(TType::I32, TType::LST, count($this->positive_part_correlativity));
            foreach ($this->positive_part_correlativity as $kiter118 => $viter119) {
                $xfer += $output->writeI32($kiter118);
                $output->writeListBegin(TType::STRUCT, count($viter119));
                foreach ($viter119 as $iter120) {
                    $xfer += $iter120->write($output);
                }
                $output->writeListEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->negative_part_correlativity !== null) {
            if (!is_array($this->negative_part_correlativity)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('negative_part_correlativity', TType::MAP, 6);
            $output->writeMapBegin(TType::I32, TType::LST, count($this->negative_part_correlativity));
            foreach ($this->negative_part_correlativity as $kiter121 => $viter122) {
                $xfer += $output->writeI32($kiter121);
                $output->writeListBegin(TType::STRUCT, count($viter122));
                foreach ($viter122 as $iter123) {
                    $xfer += $iter123->write($output);
                }
                $output->writeListEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->status !== null) {
            $xfer += $output->writeFieldBegin('status', TType::I32, 7);
            $xfer += $output->writeI32($this->status);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
