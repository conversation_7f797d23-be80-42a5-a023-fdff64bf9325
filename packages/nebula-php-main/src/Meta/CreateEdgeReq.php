<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class CreateEdgeReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'edge_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        3 => array(
            'var' => 'schema',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Meta\Schema',
        ),
        4 => array(
            'var' => 'if_not_exists',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
    );

    /**
     * @var int
     */
    public $space_id = null;
    /**
     * @var string
     */
    public $edge_name = null;
    /**
     * @var \Nebula\Meta\Schema
     */
    public $schema = null;
    /**
     * @var bool
     */
    public $if_not_exists = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space_id'])) {
                $this->space_id = $vals['space_id'];
            }
            if (isset($vals['edge_name'])) {
                $this->edge_name = $vals['edge_name'];
            }
            if (isset($vals['schema'])) {
                $this->schema = $vals['schema'];
            }
            if (isset($vals['if_not_exists'])) {
                $this->if_not_exists = $vals['if_not_exists'];
            }
        }
    }

    public function getName()
    {
        return 'CreateEdgeReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->edge_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRUCT) {
                        $this->schema = new \Nebula\Meta\Schema();
                        $xfer += $this->schema->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->if_not_exists);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('CreateEdgeReq');
        if ($this->space_id !== null) {
            $xfer += $output->writeFieldBegin('space_id', TType::I32, 1);
            $xfer += $output->writeI32($this->space_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->edge_name !== null) {
            $xfer += $output->writeFieldBegin('edge_name', TType::STRING, 2);
            $xfer += $output->writeString($this->edge_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->schema !== null) {
            if (!is_object($this->schema)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('schema', TType::STRUCT, 3);
            $xfer += $this->schema->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->if_not_exists !== null) {
            $xfer += $output->writeFieldBegin('if_not_exists', TType::BOOL, 4);
            $xfer += $output->writeBool($this->if_not_exists);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
