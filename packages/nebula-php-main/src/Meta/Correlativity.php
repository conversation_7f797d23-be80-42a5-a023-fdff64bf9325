<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class Correlativity
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'part_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'proportion',
            'isRequired' => false,
            'type' => TType::DOUBLE,
        ),
    );

    /**
     * @var int
     */
    public $part_id = null;
    /**
     * @var double
     */
    public $proportion = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['part_id'])) {
                $this->part_id = $vals['part_id'];
            }
            if (isset($vals['proportion'])) {
                $this->proportion = $vals['proportion'];
            }
        }
    }

    public function getName()
    {
        return 'Correlativity';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->part_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::DOUBLE) {
                        $xfer += $input->readDouble($this->proportion);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('Correlativity');
        if ($this->part_id !== null) {
            $xfer += $output->writeFieldBegin('part_id', TType::I32, 1);
            $xfer += $output->writeI32($this->part_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->proportion !== null) {
            $xfer += $output->writeFieldBegin('proportion', TType::DOUBLE, 2);
            $xfer += $output->writeDouble($this->proportion);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
