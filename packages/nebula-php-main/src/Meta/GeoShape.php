<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

final class GeoShape
{
    const ANY = 0;

    const POINT = 1;

    const LINESTRING = 2;

    const POLYGON = 3;

    static public $__names = array(
        0 => 'ANY',
        1 => 'POINT',
        2 => 'LINESTRING',
        3 => 'POLYGON',
    );
}

