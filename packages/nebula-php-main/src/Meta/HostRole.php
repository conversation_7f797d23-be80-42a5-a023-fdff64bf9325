<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

final class HostRole
{
    const GRAPH = 0;

    const META = 1;

    const STORAGE = 2;

    const LISTENER = 3;

    const UNKNOWN = 4;

    static public $__names = array(
        0 => 'GRAPH',
        1 => 'META',
        2 => 'STORAGE',
        3 => 'LISTENER',
        4 => 'UNKNOWN',
    );
}

