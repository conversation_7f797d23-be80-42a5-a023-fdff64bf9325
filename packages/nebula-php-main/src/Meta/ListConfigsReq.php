<?php
namespace Nebula\Meta;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ListConfigsReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        2 => array(
            'var' => 'ConfigModule',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Meta\ConfigModule',
        ),
    );

    /**
     * @var string
     */
    public $space = null;
    /**
     * @var int
     */
    public $ConfigModule = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space'])) {
                $this->space = $vals['space'];
            }
            if (isset($vals['ConfigModule'])) {
                $this->ConfigModule = $vals['ConfigModule'];
            }
        }
    }

    public function getName()
    {
        return 'ListConfigsReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->space);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->ConfigModule);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ListConfigsReq');
        if ($this->space !== null) {
            $xfer += $output->writeFieldBegin('space', TType::STRING, 1);
            $xfer += $output->writeString($this->space);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->ConfigModule !== null) {
            $xfer += $output->writeFieldBegin('ConfigModule', TType::I32, 2);
            $xfer += $output->writeI32($this->ConfigModule);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
