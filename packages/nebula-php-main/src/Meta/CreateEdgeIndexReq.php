<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class CreateEdgeIndexReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'index_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        3 => array(
            'var' => 'edge_name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        4 => array(
            'var' => 'fields',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Meta\IndexFieldDef',
                ),
        ),
        5 => array(
            'var' => 'if_not_exists',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        6 => array(
            'var' => 'comment',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var int
     */
    public $space_id = null;
    /**
     * @var string
     */
    public $index_name = null;
    /**
     * @var string
     */
    public $edge_name = null;
    /**
     * @var \Nebula\Meta\IndexFieldDef[]
     */
    public $fields = null;
    /**
     * @var bool
     */
    public $if_not_exists = null;
    /**
     * @var string
     */
    public $comment = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space_id'])) {
                $this->space_id = $vals['space_id'];
            }
            if (isset($vals['index_name'])) {
                $this->index_name = $vals['index_name'];
            }
            if (isset($vals['edge_name'])) {
                $this->edge_name = $vals['edge_name'];
            }
            if (isset($vals['fields'])) {
                $this->fields = $vals['fields'];
            }
            if (isset($vals['if_not_exists'])) {
                $this->if_not_exists = $vals['if_not_exists'];
            }
            if (isset($vals['comment'])) {
                $this->comment = $vals['comment'];
            }
        }
    }

    public function getName()
    {
        return 'CreateEdgeIndexReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->index_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->edge_name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::LST) {
                        $this->fields = array();
                        $_size277 = 0;
                        $_etype280 = 0;
                        $xfer += $input->readListBegin($_etype280, $_size277);
                        for ($_i281 = 0; $_i281 < $_size277; ++$_i281) {
                            $elem282 = null;
                            $elem282 = new \Nebula\Meta\IndexFieldDef();
                            $xfer += $elem282->read($input);
                            $this->fields []= $elem282;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->if_not_exists);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->comment);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('CreateEdgeIndexReq');
        if ($this->space_id !== null) {
            $xfer += $output->writeFieldBegin('space_id', TType::I32, 1);
            $xfer += $output->writeI32($this->space_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->index_name !== null) {
            $xfer += $output->writeFieldBegin('index_name', TType::STRING, 2);
            $xfer += $output->writeString($this->index_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->edge_name !== null) {
            $xfer += $output->writeFieldBegin('edge_name', TType::STRING, 3);
            $xfer += $output->writeString($this->edge_name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->fields !== null) {
            if (!is_array($this->fields)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('fields', TType::LST, 4);
            $output->writeListBegin(TType::STRUCT, count($this->fields));
            foreach ($this->fields as $iter283) {
                $xfer += $iter283->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->if_not_exists !== null) {
            $xfer += $output->writeFieldBegin('if_not_exists', TType::BOOL, 5);
            $xfer += $output->writeBool($this->if_not_exists);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->comment !== null) {
            $xfer += $output->writeFieldBegin('comment', TType::STRING, 6);
            $xfer += $output->writeString($this->comment);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
