<?php
namespace Nebula\Meta;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class SchemaProp
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'ttl_duration',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        2 => array(
            'var' => 'ttl_col',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        3 => array(
            'var' => 'comment',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var int
     */
    public $ttl_duration = null;
    /**
     * @var string
     */
    public $ttl_col = null;
    /**
     * @var string
     */
    public $comment = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['ttl_duration'])) {
                $this->ttl_duration = $vals['ttl_duration'];
            }
            if (isset($vals['ttl_col'])) {
                $this->ttl_col = $vals['ttl_col'];
            }
            if (isset($vals['comment'])) {
                $this->comment = $vals['comment'];
            }
        }
    }

    public function getName()
    {
        return 'SchemaProp';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->ttl_duration);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->ttl_col);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->comment);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('SchemaProp');
        if ($this->ttl_duration !== null) {
            $xfer += $output->writeFieldBegin('ttl_duration', TType::I64, 1);
            $xfer += $output->writeI64($this->ttl_duration);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->ttl_col !== null) {
            $xfer += $output->writeFieldBegin('ttl_col', TType::STRING, 2);
            $xfer += $output->writeString($this->ttl_col);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->comment !== null) {
            $xfer += $output->writeFieldBegin('comment', TType::STRING, 3);
            $xfer += $output->writeString($this->comment);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
