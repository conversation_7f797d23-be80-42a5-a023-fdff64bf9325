<?php
namespace Nebula\Storage;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ScanVertexRequest
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'part_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'cursor',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        4 => array(
            'var' => 'return_columns',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Storage\VertexProp',
        ),
        5 => array(
            'var' => 'limit',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        6 => array(
            'var' => 'start_time',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        7 => array(
            'var' => 'end_time',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        8 => array(
            'var' => 'filter',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        9 => array(
            'var' => 'only_latest_version',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        10 => array(
            'var' => 'enable_read_from_follower',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        11 => array(
            'var' => 'common',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Storage\RequestCommon',
        ),
    );

    /**
     * @var int
     */
    public $space_id = null;
    /**
     * @var int
     */
    public $part_id = null;
    /**
     * @var string
     */
    public $cursor = null;
    /**
     * @var \Nebula\Storage\VertexProp
     */
    public $return_columns = null;
    /**
     * @var int
     */
    public $limit = null;
    /**
     * @var int
     */
    public $start_time = null;
    /**
     * @var int
     */
    public $end_time = null;
    /**
     * @var string
     */
    public $filter = null;
    /**
     * @var bool
     */
    public $only_latest_version = false;
    /**
     * @var bool
     */
    public $enable_read_from_follower = true;
    /**
     * @var \Nebula\Storage\RequestCommon
     */
    public $common = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space_id'])) {
                $this->space_id = $vals['space_id'];
            }
            if (isset($vals['part_id'])) {
                $this->part_id = $vals['part_id'];
            }
            if (isset($vals['cursor'])) {
                $this->cursor = $vals['cursor'];
            }
            if (isset($vals['return_columns'])) {
                $this->return_columns = $vals['return_columns'];
            }
            if (isset($vals['limit'])) {
                $this->limit = $vals['limit'];
            }
            if (isset($vals['start_time'])) {
                $this->start_time = $vals['start_time'];
            }
            if (isset($vals['end_time'])) {
                $this->end_time = $vals['end_time'];
            }
            if (isset($vals['filter'])) {
                $this->filter = $vals['filter'];
            }
            if (isset($vals['only_latest_version'])) {
                $this->only_latest_version = $vals['only_latest_version'];
            }
            if (isset($vals['enable_read_from_follower'])) {
                $this->enable_read_from_follower = $vals['enable_read_from_follower'];
            }
            if (isset($vals['common'])) {
                $this->common = $vals['common'];
            }
        }
    }

    public function getName()
    {
        return 'ScanVertexRequest';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->part_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->cursor);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::STRUCT) {
                        $this->return_columns = new \Nebula\Storage\VertexProp();
                        $xfer += $this->return_columns->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->limit);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->start_time);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->end_time);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 8:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->filter);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 9:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->only_latest_version);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 10:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->enable_read_from_follower);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 11:
                    if ($ftype == TType::STRUCT) {
                        $this->common = new \Nebula\Storage\RequestCommon();
                        $xfer += $this->common->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ScanVertexRequest');
        if ($this->space_id !== null) {
            $xfer += $output->writeFieldBegin('space_id', TType::I32, 1);
            $xfer += $output->writeI32($this->space_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->part_id !== null) {
            $xfer += $output->writeFieldBegin('part_id', TType::I32, 2);
            $xfer += $output->writeI32($this->part_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->cursor !== null) {
            $xfer += $output->writeFieldBegin('cursor', TType::STRING, 3);
            $xfer += $output->writeString($this->cursor);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->return_columns !== null) {
            if (!is_object($this->return_columns)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('return_columns', TType::STRUCT, 4);
            $xfer += $this->return_columns->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->limit !== null) {
            $xfer += $output->writeFieldBegin('limit', TType::I64, 5);
            $xfer += $output->writeI64($this->limit);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->start_time !== null) {
            $xfer += $output->writeFieldBegin('start_time', TType::I64, 6);
            $xfer += $output->writeI64($this->start_time);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->end_time !== null) {
            $xfer += $output->writeFieldBegin('end_time', TType::I64, 7);
            $xfer += $output->writeI64($this->end_time);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->filter !== null) {
            $xfer += $output->writeFieldBegin('filter', TType::STRING, 8);
            $xfer += $output->writeString($this->filter);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->only_latest_version !== null) {
            $xfer += $output->writeFieldBegin('only_latest_version', TType::BOOL, 9);
            $xfer += $output->writeBool($this->only_latest_version);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->enable_read_from_follower !== null) {
            $xfer += $output->writeFieldBegin('enable_read_from_follower', TType::BOOL, 10);
            $xfer += $output->writeBool($this->enable_read_from_follower);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->common !== null) {
            if (!is_object($this->common)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('common', TType::STRUCT, 11);
            $xfer += $this->common->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
