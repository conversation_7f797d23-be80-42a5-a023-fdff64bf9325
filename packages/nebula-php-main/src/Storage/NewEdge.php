<?php
namespace Nebula\Storage;

/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class NewEdge
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'key',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Storage\EdgeKey',
        ),
        2 => array(
            'var' => 'props',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Common\Value',
                ),
        ),
    );

    /**
     * @var \Nebula\Storage\EdgeKey
     */
    public $key = null;
    /**
     * @var \Nebula\Common\Value[]
     */
    public $props = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['key'])) {
                $this->key = $vals['key'];
            }
            if (isset($vals['props'])) {
                $this->props = $vals['props'];
            }
        }
    }

    public function getName()
    {
        return 'NewEdge';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRUCT) {
                        $this->key = new \Nebula\Storage\EdgeKey();
                        $xfer += $this->key->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::LST) {
                        $this->props = array();
                        $_size153 = 0;
                        $_etype156 = 0;
                        $xfer += $input->readListBegin($_etype156, $_size153);
                        for ($_i157 = 0; $_i157 < $_size153; ++$_i157) {
                            $elem158 = null;
                            $elem158 = new \Nebula\Common\Value();
                            $xfer += $elem158->read($input);
                            $this->props []= $elem158;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('NewEdge');
        if ($this->key !== null) {
            if (!is_object($this->key)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('key', TType::STRUCT, 1);
            $xfer += $this->key->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->props !== null) {
            if (!is_array($this->props)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('props', TType::LST, 2);
            $output->writeListBegin(TType::STRUCT, count($this->props));
            foreach ($this->props as $iter159) {
                $xfer += $iter159->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
