<?php
namespace Nebula\Storage;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class GetNeighborsRequest
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'column_names',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRING,
            'elem' => array(
                'type' => TType::STRING,
                ),
        ),
        3 => array(
            'var' => 'parts',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I32,
            'vtype' => TType::LST,
            'key' => array(
                'type' => TType::I32,
            ),
            'val' => array(
                'type' => TType::LST,
                'etype' => TType::STRUCT,
                'elem' => array(
                    'type' => TType::STRUCT,
                    'class' => '\Nebula\Common\Row',
                    ),
                ),
        ),
        4 => array(
            'var' => 'traverse_spec',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Storage\TraverseSpec',
        ),
        5 => array(
            'var' => 'common',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Storage\RequestCommon',
        ),
    );

    /**
     * @var int
     */
    public $space_id = null;
    /**
     * @var string[]
     */
    public $column_names = null;
    /**
     * @var array
     */
    public $parts = null;
    /**
     * @var \Nebula\Storage\TraverseSpec
     */
    public $traverse_spec = null;
    /**
     * @var \Nebula\Storage\RequestCommon
     */
    public $common = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space_id'])) {
                $this->space_id = $vals['space_id'];
            }
            if (isset($vals['column_names'])) {
                $this->column_names = $vals['column_names'];
            }
            if (isset($vals['parts'])) {
                $this->parts = $vals['parts'];
            }
            if (isset($vals['traverse_spec'])) {
                $this->traverse_spec = $vals['traverse_spec'];
            }
            if (isset($vals['common'])) {
                $this->common = $vals['common'];
            }
        }
    }

    public function getName()
    {
        return 'GetNeighborsRequest';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::LST) {
                        $this->column_names = array();
                        $_size72 = 0;
                        $_etype75 = 0;
                        $xfer += $input->readListBegin($_etype75, $_size72);
                        for ($_i76 = 0; $_i76 < $_size72; ++$_i76) {
                            $elem77 = null;
                            $xfer += $input->readString($elem77);
                            $this->column_names []= $elem77;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::MAP) {
                        $this->parts = array();
                        $_size78 = 0;
                        $_ktype79 = 0;
                        $_vtype80 = 0;
                        $xfer += $input->readMapBegin($_ktype79, $_vtype80, $_size78);
                        for ($_i82 = 0; $_i82 < $_size78; ++$_i82) {
                            $key83 = 0;
                            $val84 = array();
                            $xfer += $input->readI32($key83);
                            $val84 = array();
                            $_size85 = 0;
                            $_etype88 = 0;
                            $xfer += $input->readListBegin($_etype88, $_size85);
                            for ($_i89 = 0; $_i89 < $_size85; ++$_i89) {
                                $elem90 = null;
                                $elem90 = new \Nebula\Common\Row();
                                $xfer += $elem90->read($input);
                                $val84 []= $elem90;
                            }
                            $xfer += $input->readListEnd();
                            $this->parts[$key83] = $val84;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::STRUCT) {
                        $this->traverse_spec = new \Nebula\Storage\TraverseSpec();
                        $xfer += $this->traverse_spec->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::STRUCT) {
                        $this->common = new \Nebula\Storage\RequestCommon();
                        $xfer += $this->common->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('GetNeighborsRequest');
        if ($this->space_id !== null) {
            $xfer += $output->writeFieldBegin('space_id', TType::I32, 1);
            $xfer += $output->writeI32($this->space_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->column_names !== null) {
            if (!is_array($this->column_names)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('column_names', TType::LST, 2);
            $output->writeListBegin(TType::STRING, count($this->column_names));
            foreach ($this->column_names as $iter91) {
                $xfer += $output->writeString($iter91);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->parts !== null) {
            if (!is_array($this->parts)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('parts', TType::MAP, 3);
            $output->writeMapBegin(TType::I32, TType::LST, count($this->parts));
            foreach ($this->parts as $kiter92 => $viter93) {
                $xfer += $output->writeI32($kiter92);
                $output->writeListBegin(TType::STRUCT, count($viter93));
                foreach ($viter93 as $iter94) {
                    $xfer += $iter94->write($output);
                }
                $output->writeListEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->traverse_spec !== null) {
            if (!is_object($this->traverse_spec)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('traverse_spec', TType::STRUCT, 4);
            $xfer += $this->traverse_spec->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->common !== null) {
            if (!is_object($this->common)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('common', TType::STRUCT, 5);
            $xfer += $this->common->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
