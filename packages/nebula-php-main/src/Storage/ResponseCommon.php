<?php
namespace Nebula\Storage;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ResponseCommon
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'failed_parts',
            'isRequired' => true,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Storage\PartitionResult',
                ),
        ),
        2 => array(
            'var' => 'latency_in_us',
            'isRequired' => true,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'latency_detail_us',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::STRING,
            'vtype' => TType::I32,
            'key' => array(
                'type' => TType::STRING,
            ),
            'val' => array(
                'type' => TType::I32,
                ),
        ),
    );

    /**
     * @var \Nebula\Storage\PartitionResult[]
     */
    public $failed_parts = null;
    /**
     * @var int
     */
    public $latency_in_us = null;
    /**
     * @var array
     */
    public $latency_detail_us = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['failed_parts'])) {
                $this->failed_parts = $vals['failed_parts'];
            }
            if (isset($vals['latency_in_us'])) {
                $this->latency_in_us = $vals['latency_in_us'];
            }
            if (isset($vals['latency_detail_us'])) {
                $this->latency_detail_us = $vals['latency_detail_us'];
            }
        }
    }

    public function getName()
    {
        return 'ResponseCommon';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::LST) {
                        $this->failed_parts = array();
                        $_size0 = 0;
                        $_etype3 = 0;
                        $xfer += $input->readListBegin($_etype3, $_size0);
                        for ($_i4 = 0; $_i4 < $_size0; ++$_i4) {
                            $elem5 = null;
                            $elem5 = new \Nebula\Storage\PartitionResult();
                            $xfer += $elem5->read($input);
                            $this->failed_parts []= $elem5;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->latency_in_us);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::MAP) {
                        $this->latency_detail_us = array();
                        $_size6 = 0;
                        $_ktype7 = 0;
                        $_vtype8 = 0;
                        $xfer += $input->readMapBegin($_ktype7, $_vtype8, $_size6);
                        for ($_i10 = 0; $_i10 < $_size6; ++$_i10) {
                            $key11 = '';
                            $val12 = 0;
                            $xfer += $input->readString($key11);
                            $xfer += $input->readI32($val12);
                            $this->latency_detail_us[$key11] = $val12;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ResponseCommon');
        if ($this->failed_parts !== null) {
            if (!is_array($this->failed_parts)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('failed_parts', TType::LST, 1);
            $output->writeListBegin(TType::STRUCT, count($this->failed_parts));
            foreach ($this->failed_parts as $iter13) {
                $xfer += $iter13->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->latency_in_us !== null) {
            $xfer += $output->writeFieldBegin('latency_in_us', TType::I32, 2);
            $xfer += $output->writeI32($this->latency_in_us);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->latency_detail_us !== null) {
            if (!is_array($this->latency_detail_us)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('latency_detail_us', TType::MAP, 3);
            $output->writeMapBegin(TType::STRING, TType::I32, count($this->latency_detail_us));
            foreach ($this->latency_detail_us as $kiter14 => $viter15) {
                $xfer += $output->writeString($kiter14);
                $xfer += $output->writeI32($viter15);
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
