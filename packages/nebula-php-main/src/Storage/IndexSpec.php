<?php
namespace Nebula\Storage;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class IndexSpec
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'contexts',
            'isRequired' => true,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Storage\IndexQueryContext',
                ),
        ),
        2 => array(
            'var' => 'schema_id',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\SchemaID',
        ),
    );

    /**
     * @var \Nebula\Storage\IndexQueryContext[]
     */
    public $contexts = null;
    /**
     * @var \Nebula\Common\SchemaID
     */
    public $schema_id = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['contexts'])) {
                $this->contexts = $vals['contexts'];
            }
            if (isset($vals['schema_id'])) {
                $this->schema_id = $vals['schema_id'];
            }
        }
    }

    public function getName()
    {
        return 'IndexSpec';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::LST) {
                        $this->contexts = array();
                        $_size305 = 0;
                        $_etype308 = 0;
                        $xfer += $input->readListBegin($_etype308, $_size305);
                        for ($_i309 = 0; $_i309 < $_size305; ++$_i309) {
                            $elem310 = null;
                            $elem310 = new \Nebula\Storage\IndexQueryContext();
                            $xfer += $elem310->read($input);
                            $this->contexts []= $elem310;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRUCT) {
                        $this->schema_id = new \Nebula\Common\SchemaID();
                        $xfer += $this->schema_id->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('IndexSpec');
        if ($this->contexts !== null) {
            if (!is_array($this->contexts)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('contexts', TType::LST, 1);
            $output->writeListBegin(TType::STRUCT, count($this->contexts));
            foreach ($this->contexts as $iter311) {
                $xfer += $iter311->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->schema_id !== null) {
            if (!is_object($this->schema_id)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('schema_id', TType::STRUCT, 2);
            $xfer += $this->schema_id->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
