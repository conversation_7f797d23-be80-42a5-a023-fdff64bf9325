<?php
namespace Nebula\Storage;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class AddPartReq
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'part_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'as_learner',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        4 => array(
            'var' => 'peers',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Common\HostAddr',
                ),
        ),
    );

    /**
     * @var int
     */
    public $space_id = null;
    /**
     * @var int
     */
    public $part_id = null;
    /**
     * @var bool
     */
    public $as_learner = null;
    /**
     * @var \Nebula\Common\HostAddr[]
     */
    public $peers = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space_id'])) {
                $this->space_id = $vals['space_id'];
            }
            if (isset($vals['part_id'])) {
                $this->part_id = $vals['part_id'];
            }
            if (isset($vals['as_learner'])) {
                $this->as_learner = $vals['as_learner'];
            }
            if (isset($vals['peers'])) {
                $this->peers = $vals['peers'];
            }
        }
    }

    public function getName()
    {
        return 'AddPartReq';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->part_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->as_learner);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::LST) {
                        $this->peers = array();
                        $_size347 = 0;
                        $_etype350 = 0;
                        $xfer += $input->readListBegin($_etype350, $_size347);
                        for ($_i351 = 0; $_i351 < $_size347; ++$_i351) {
                            $elem352 = null;
                            $elem352 = new \Nebula\Common\HostAddr();
                            $xfer += $elem352->read($input);
                            $this->peers []= $elem352;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('AddPartReq');
        if ($this->space_id !== null) {
            $xfer += $output->writeFieldBegin('space_id', TType::I32, 1);
            $xfer += $output->writeI32($this->space_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->part_id !== null) {
            $xfer += $output->writeFieldBegin('part_id', TType::I32, 2);
            $xfer += $output->writeI32($this->part_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->as_learner !== null) {
            $xfer += $output->writeFieldBegin('as_learner', TType::BOOL, 3);
            $xfer += $output->writeBool($this->as_learner);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->peers !== null) {
            if (!is_array($this->peers)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('peers', TType::LST, 4);
            $output->writeListBegin(TType::STRUCT, count($this->peers));
            foreach ($this->peers as $iter353) {
                $xfer += $iter353->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
