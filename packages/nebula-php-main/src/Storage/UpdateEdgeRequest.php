<?php
namespace Nebula\Storage;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class UpdateEdgeRequest
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'part_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'edge_key',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Storage\EdgeKey',
        ),
        4 => array(
            'var' => 'updated_props',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Storage\UpdatedProp',
                ),
        ),
        5 => array(
            'var' => 'insertable',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        6 => array(
            'var' => 'return_props',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRING,
            'elem' => array(
                'type' => TType::STRING,
                ),
        ),
        7 => array(
            'var' => 'condition',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        8 => array(
            'var' => 'common',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Storage\RequestCommon',
        ),
    );

    /**
     * @var int
     */
    public $space_id = null;
    /**
     * @var int
     */
    public $part_id = null;
    /**
     * @var \Nebula\Storage\EdgeKey
     */
    public $edge_key = null;
    /**
     * @var \Nebula\Storage\UpdatedProp[]
     */
    public $updated_props = null;
    /**
     * @var bool
     */
    public $insertable = false;
    /**
     * @var string[]
     */
    public $return_props = null;
    /**
     * @var string
     */
    public $condition = null;
    /**
     * @var \Nebula\Storage\RequestCommon
     */
    public $common = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space_id'])) {
                $this->space_id = $vals['space_id'];
            }
            if (isset($vals['part_id'])) {
                $this->part_id = $vals['part_id'];
            }
            if (isset($vals['edge_key'])) {
                $this->edge_key = $vals['edge_key'];
            }
            if (isset($vals['updated_props'])) {
                $this->updated_props = $vals['updated_props'];
            }
            if (isset($vals['insertable'])) {
                $this->insertable = $vals['insertable'];
            }
            if (isset($vals['return_props'])) {
                $this->return_props = $vals['return_props'];
            }
            if (isset($vals['condition'])) {
                $this->condition = $vals['condition'];
            }
            if (isset($vals['common'])) {
                $this->common = $vals['common'];
            }
        }
    }

    public function getName()
    {
        return 'UpdateEdgeRequest';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->part_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRUCT) {
                        $this->edge_key = new \Nebula\Storage\EdgeKey();
                        $xfer += $this->edge_key->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::LST) {
                        $this->updated_props = array();
                        $_size284 = 0;
                        $_etype287 = 0;
                        $xfer += $input->readListBegin($_etype287, $_size284);
                        for ($_i288 = 0; $_i288 < $_size284; ++$_i288) {
                            $elem289 = null;
                            $elem289 = new \Nebula\Storage\UpdatedProp();
                            $xfer += $elem289->read($input);
                            $this->updated_props []= $elem289;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->insertable);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::LST) {
                        $this->return_props = array();
                        $_size290 = 0;
                        $_etype293 = 0;
                        $xfer += $input->readListBegin($_etype293, $_size290);
                        for ($_i294 = 0; $_i294 < $_size290; ++$_i294) {
                            $elem295 = null;
                            $xfer += $input->readString($elem295);
                            $this->return_props []= $elem295;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->condition);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 8:
                    if ($ftype == TType::STRUCT) {
                        $this->common = new \Nebula\Storage\RequestCommon();
                        $xfer += $this->common->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('UpdateEdgeRequest');
        if ($this->space_id !== null) {
            $xfer += $output->writeFieldBegin('space_id', TType::I32, 1);
            $xfer += $output->writeI32($this->space_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->part_id !== null) {
            $xfer += $output->writeFieldBegin('part_id', TType::I32, 2);
            $xfer += $output->writeI32($this->part_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->edge_key !== null) {
            if (!is_object($this->edge_key)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('edge_key', TType::STRUCT, 3);
            $xfer += $this->edge_key->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->updated_props !== null) {
            if (!is_array($this->updated_props)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('updated_props', TType::LST, 4);
            $output->writeListBegin(TType::STRUCT, count($this->updated_props));
            foreach ($this->updated_props as $iter296) {
                $xfer += $iter296->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->insertable !== null) {
            $xfer += $output->writeFieldBegin('insertable', TType::BOOL, 5);
            $xfer += $output->writeBool($this->insertable);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->return_props !== null) {
            if (!is_array($this->return_props)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('return_props', TType::LST, 6);
            $output->writeListBegin(TType::STRING, count($this->return_props));
            foreach ($this->return_props as $iter297) {
                $xfer += $output->writeString($iter297);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->condition !== null) {
            $xfer += $output->writeFieldBegin('condition', TType::STRING, 7);
            $xfer += $output->writeString($this->condition);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->common !== null) {
            if (!is_object($this->common)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('common', TType::STRUCT, 8);
            $xfer += $this->common->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
