<?php
namespace Nebula\Storage;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

interface StorageAdminServiceIf
{
    /**
     * @param \Nebula\Storage\TransLeaderReq $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function transLeader(\Nebula\Storage\TransLeaderReq $req);
    /**
     * @param \Nebula\Storage\AddPartReq $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function addPart(\Nebula\Storage\AddPartReq $req);
    /**
     * @param \Nebula\Storage\AddLearnerReq $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function addLearner(\Nebula\Storage\AddLearnerReq $req);
    /**
     * @param \Nebula\Storage\RemovePartReq $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function removePart(\Nebula\Storage\RemovePartReq $req);
    /**
     * @param \Nebula\Storage\MemberChangeReq $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function memberChange(\Nebula\Storage\MemberChangeReq $req);
    /**
     * @param \Nebula\Storage\CatchUpDataReq $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function waitingForCatchUpData(\Nebula\Storage\CatchUpDataReq $req);
    /**
     * @param \Nebula\Storage\CreateCPRequest $req
     * @return \Nebula\Storage\CreateCPResp
     */
    public function createCheckpoint(\Nebula\Storage\CreateCPRequest $req);
    /**
     * @param \Nebula\Storage\DropCPRequest $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function dropCheckpoint(\Nebula\Storage\DropCPRequest $req);
    /**
     * @param \Nebula\Storage\BlockingSignRequest $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function blockingWrites(\Nebula\Storage\BlockingSignRequest $req);
    /**
     * @param \Nebula\Storage\RebuildIndexRequest $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function rebuildTagIndex(\Nebula\Storage\RebuildIndexRequest $req);
    /**
     * @param \Nebula\Storage\RebuildIndexRequest $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function rebuildEdgeIndex(\Nebula\Storage\RebuildIndexRequest $req);
    /**
     * @param \Nebula\Storage\GetLeaderReq $req
     * @return \Nebula\Storage\GetLeaderPartsResp
     */
    public function getLeaderParts(\Nebula\Storage\GetLeaderReq $req);
    /**
     * @param \Nebula\Storage\CheckPeersReq $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function checkPeers(\Nebula\Storage\CheckPeersReq $req);
    /**
     * @param \Nebula\Storage\AddAdminTaskRequest $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function addAdminTask(\Nebula\Storage\AddAdminTaskRequest $req);
    /**
     * @param \Nebula\Storage\StopAdminTaskRequest $req
     * @return \Nebula\Storage\AdminExecResp
     */
    public function stopAdminTask(\Nebula\Storage\StopAdminTaskRequest $req);
    /**
     * @param \Nebula\Storage\ListClusterInfoReq $req
     * @return \Nebula\Storage\ListClusterInfoResp
     */
    public function listClusterInfo(\Nebula\Storage\ListClusterInfoReq $req);
}
