<?php
namespace Nebula\Storage;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

interface GeneralStorageServiceIf
{
    /**
     * @param \Nebula\Storage\KVGetRequest $req
     * @return \Nebula\Storage\KVGetResponse
     */
    public function get(\Nebula\Storage\KVGetRequest $req);
    /**
     * @param \Nebula\Storage\KVPutRequest $req
     * @return \Nebula\Storage\ExecResponse
     */
    public function put(\Nebula\Storage\KVPutRequest $req);
    /**
     * @param \Nebula\Storage\KVRemoveRequest $req
     * @return \Nebula\Storage\ExecResponse
     */
    public function remove(\Nebula\Storage\KVRemoveRequest $req);
}
