<?php
namespace Nebula\Storage;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class OrderBy
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'prop',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        2 => array(
            'var' => 'direction',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Storage\OrderDirection',
        ),
    );

    /**
     * @var string
     */
    public $prop = null;
    /**
     * @var int
     */
    public $direction = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['prop'])) {
                $this->prop = $vals['prop'];
            }
            if (isset($vals['direction'])) {
                $this->direction = $vals['direction'];
            }
        }
    }

    public function getName()
    {
        return 'OrderBy';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->prop);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->direction);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('OrderBy');
        if ($this->prop !== null) {
            $xfer += $output->writeFieldBegin('prop', TType::STRING, 1);
            $xfer += $output->writeString($this->prop);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->direction !== null) {
            $xfer += $output->writeFieldBegin('direction', TType::I32, 2);
            $xfer += $output->writeI32($this->direction);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
