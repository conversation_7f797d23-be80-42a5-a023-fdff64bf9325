<?php
namespace Nebula\Storage;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class NewVertex
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'id',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Value',
        ),
        2 => array(
            'var' => 'tags',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Storage\NewTag',
                ),
        ),
    );

    /**
     * @var \Nebula\Common\Value
     */
    public $id = null;
    /**
     * @var \Nebula\Storage\NewTag[]
     */
    public $tags = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['id'])) {
                $this->id = $vals['id'];
            }
            if (isset($vals['tags'])) {
                $this->tags = $vals['tags'];
            }
        }
    }

    public function getName()
    {
        return 'NewVertex';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRUCT) {
                        $this->id = new \Nebula\Common\Value();
                        $xfer += $this->id->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::LST) {
                        $this->tags = array();
                        $_size146 = 0;
                        $_etype149 = 0;
                        $xfer += $input->readListBegin($_etype149, $_size146);
                        for ($_i150 = 0; $_i150 < $_size146; ++$_i150) {
                            $elem151 = null;
                            $elem151 = new \Nebula\Storage\NewTag();
                            $xfer += $elem151->read($input);
                            $this->tags []= $elem151;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('NewVertex');
        if ($this->id !== null) {
            if (!is_object($this->id)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('id', TType::STRUCT, 1);
            $xfer += $this->id->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->tags !== null) {
            if (!is_array($this->tags)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('tags', TType::LST, 2);
            $output->writeListBegin(TType::STRUCT, count($this->tags));
            foreach ($this->tags as $iter152) {
                $xfer += $iter152->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
