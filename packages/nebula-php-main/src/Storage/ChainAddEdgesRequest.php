<?php
namespace Nebula\Storage;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ChainAddEdgesRequest
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space_id',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'parts',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I32,
            'vtype' => TType::LST,
            'key' => array(
                'type' => TType::I32,
            ),
            'val' => array(
                'type' => TType::LST,
                'etype' => TType::STRUCT,
                'elem' => array(
                    'type' => TType::STRUCT,
                    'class' => '\Nebula\Storage\NewEdge',
                    ),
                ),
        ),
        3 => array(
            'var' => 'prop_names',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRING,
            'elem' => array(
                'type' => TType::STRING,
                ),
        ),
        4 => array(
            'var' => 'if_not_exists',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        5 => array(
            'var' => 'term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        6 => array(
            'var' => 'edge_version',
            'isRequired' => false,
            'type' => TType::I64,
        ),
    );

    /**
     * @var int
     */
    public $space_id = null;
    /**
     * @var array
     */
    public $parts = null;
    /**
     * @var string[]
     */
    public $prop_names = null;
    /**
     * @var bool
     */
    public $if_not_exists = null;
    /**
     * @var int
     */
    public $term = null;
    /**
     * @var int
     */
    public $edge_version = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space_id'])) {
                $this->space_id = $vals['space_id'];
            }
            if (isset($vals['parts'])) {
                $this->parts = $vals['parts'];
            }
            if (isset($vals['prop_names'])) {
                $this->prop_names = $vals['prop_names'];
            }
            if (isset($vals['if_not_exists'])) {
                $this->if_not_exists = $vals['if_not_exists'];
            }
            if (isset($vals['term'])) {
                $this->term = $vals['term'];
            }
            if (isset($vals['edge_version'])) {
                $this->edge_version = $vals['edge_version'];
            }
        }
    }

    public function getName()
    {
        return 'ChainAddEdgesRequest';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::MAP) {
                        $this->parts = array();
                        $_size473 = 0;
                        $_ktype474 = 0;
                        $_vtype475 = 0;
                        $xfer += $input->readMapBegin($_ktype474, $_vtype475, $_size473);
                        for ($_i477 = 0; $_i477 < $_size473; ++$_i477) {
                            $key478 = 0;
                            $val479 = array();
                            $xfer += $input->readI32($key478);
                            $val479 = array();
                            $_size480 = 0;
                            $_etype483 = 0;
                            $xfer += $input->readListBegin($_etype483, $_size480);
                            for ($_i484 = 0; $_i484 < $_size480; ++$_i484) {
                                $elem485 = null;
                                $elem485 = new \Nebula\Storage\NewEdge();
                                $xfer += $elem485->read($input);
                                $val479 []= $elem485;
                            }
                            $xfer += $input->readListEnd();
                            $this->parts[$key478] = $val479;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::LST) {
                        $this->prop_names = array();
                        $_size486 = 0;
                        $_etype489 = 0;
                        $xfer += $input->readListBegin($_etype489, $_size486);
                        for ($_i490 = 0; $_i490 < $_size486; ++$_i490) {
                            $elem491 = null;
                            $xfer += $input->readString($elem491);
                            $this->prop_names []= $elem491;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->if_not_exists);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->edge_version);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ChainAddEdgesRequest');
        if ($this->space_id !== null) {
            $xfer += $output->writeFieldBegin('space_id', TType::I32, 1);
            $xfer += $output->writeI32($this->space_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->parts !== null) {
            if (!is_array($this->parts)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('parts', TType::MAP, 2);
            $output->writeMapBegin(TType::I32, TType::LST, count($this->parts));
            foreach ($this->parts as $kiter492 => $viter493) {
                $xfer += $output->writeI32($kiter492);
                $output->writeListBegin(TType::STRUCT, count($viter493));
                foreach ($viter493 as $iter494) {
                    $xfer += $iter494->write($output);
                }
                $output->writeListEnd();
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->prop_names !== null) {
            if (!is_array($this->prop_names)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('prop_names', TType::LST, 3);
            $output->writeListBegin(TType::STRING, count($this->prop_names));
            foreach ($this->prop_names as $iter495) {
                $xfer += $output->writeString($iter495);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->if_not_exists !== null) {
            $xfer += $output->writeFieldBegin('if_not_exists', TType::BOOL, 4);
            $xfer += $output->writeBool($this->if_not_exists);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->term !== null) {
            $xfer += $output->writeFieldBegin('term', TType::I64, 5);
            $xfer += $output->writeI64($this->term);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->edge_version !== null) {
            $xfer += $output->writeFieldBegin('edge_version', TType::I64, 6);
            $xfer += $output->writeI64($this->edge_version);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
