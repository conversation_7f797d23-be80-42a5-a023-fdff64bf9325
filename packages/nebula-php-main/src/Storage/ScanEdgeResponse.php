<?php
namespace Nebula\Storage;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class ScanEdgeResponse
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'result',
            'isRequired' => true,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Storage\ResponseCommon',
        ),
        2 => array(
            'var' => 'edge_data',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\DataSet',
        ),
        3 => array(
            'var' => 'has_next',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        4 => array(
            'var' => 'next_cursor',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var \Nebula\Storage\ResponseCommon
     */
    public $result = null;
    /**
     * @var \Nebula\Common\DataSet
     */
    public $edge_data = null;
    /**
     * @var bool
     */
    public $has_next = null;
    /**
     * @var string
     */
    public $next_cursor = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['result'])) {
                $this->result = $vals['result'];
            }
            if (isset($vals['edge_data'])) {
                $this->edge_data = $vals['edge_data'];
            }
            if (isset($vals['has_next'])) {
                $this->has_next = $vals['has_next'];
            }
            if (isset($vals['next_cursor'])) {
                $this->next_cursor = $vals['next_cursor'];
            }
        }
    }

    public function getName()
    {
        return 'ScanEdgeResponse';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRUCT) {
                        $this->result = new \Nebula\Storage\ResponseCommon();
                        $xfer += $this->result->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRUCT) {
                        $this->edge_data = new \Nebula\Common\DataSet();
                        $xfer += $this->edge_data->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->has_next);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->next_cursor);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('ScanEdgeResponse');
        if ($this->result !== null) {
            if (!is_object($this->result)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('result', TType::STRUCT, 1);
            $xfer += $this->result->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->edge_data !== null) {
            if (!is_object($this->edge_data)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('edge_data', TType::STRUCT, 2);
            $xfer += $this->edge_data->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->has_next !== null) {
            $xfer += $output->writeFieldBegin('has_next', TType::BOOL, 3);
            $xfer += $output->writeBool($this->has_next);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->next_cursor !== null) {
            $xfer += $output->writeFieldBegin('next_cursor', TType::STRING, 4);
            $xfer += $output->writeString($this->next_cursor);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
