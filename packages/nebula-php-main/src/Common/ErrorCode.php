<?php
namespace Nebula\Common;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

final class ErrorCode
{
    const SUCCEEDED = 0;

    const E_DISCONNECTED = -1;

    const E_FAIL_TO_CONNECT = -2;

    const E_RPC_FAILURE = -3;

    const E_LEADER_CHANGED = -4;

    const E_SPACE_NOT_FOUND = -5;

    const E_TAG_NOT_FOUND = -6;

    const E_EDGE_NOT_FOUND = -7;

    const E_INDEX_NOT_FOUND = -8;

    const E_EDGE_PROP_NOT_FOUND = -9;

    const E_TAG_PROP_NOT_FOUND = -10;

    const E_ROLE_NOT_FOUND = -11;

    const E_CONFIG_NOT_FOUND = -12;

    const E_GROUP_NOT_FOUND = -13;

    const E_ZONE_NOT_FOUND = -14;

    const E_LISTENER_NOT_FOUND = -15;

    const E_PART_NOT_FOUND = -16;

    const E_KEY_NOT_FOUND = -17;

    const E_USER_NOT_FOUND = -18;

    const E_STATS_NOT_FOUND = -19;

    const E_BACKUP_FAILED = -24;

    const E_BACKUP_EMPTY_TABLE = -25;

    const E_BACKUP_TABLE_FAILED = -26;

    const E_PARTIAL_RESULT = -27;

    const E_REBUILD_INDEX_FAILED = -28;

    const E_INVALID_PASSWORD = -29;

    const E_FAILED_GET_ABS_PATH = -30;

    const E_BAD_USERNAME_PASSWORD = -1001;

    const E_SESSION_INVALID = -1002;

    const E_SESSION_TIMEOUT = -1003;

    const E_SYNTAX_ERROR = -1004;

    const E_EXECUTION_ERROR = -1005;

    const E_STATEMENT_EMPTY = -1006;

    const E_BAD_PERMISSION = -1008;

    const E_SEMANTIC_ERROR = -1009;

    const E_TOO_MANY_CONNECTIONS = -1010;

    const E_PARTIAL_SUCCEEDED = -1011;

    const E_NO_HOSTS = -2001;

    const E_EXISTED = -2002;

    const E_INVALID_HOST = -2003;

    const E_UNSUPPORTED = -2004;

    const E_NOT_DROP = -2005;

    const E_BALANCER_RUNNING = -2006;

    const E_CONFIG_IMMUTABLE = -2007;

    const E_CONFLICT = -2008;

    const E_INVALID_PARM = -2009;

    const E_WRONGCLUSTER = -2010;

    const E_STORE_FAILURE = -2021;

    const E_STORE_SEGMENT_ILLEGAL = -2022;

    const E_BAD_BALANCE_PLAN = -2023;

    const E_BALANCED = -2024;

    const E_NO_RUNNING_BALANCE_PLAN = -2025;

    const E_NO_VALID_HOST = -2026;

    const E_CORRUPTTED_BALANCE_PLAN = -2027;

    const E_NO_INVALID_BALANCE_PLAN = -2028;

    const E_IMPROPER_ROLE = -2030;

    const E_INVALID_PARTITION_NUM = -2031;

    const E_INVALID_REPLICA_FACTOR = -2032;

    const E_INVALID_CHARSET = -2033;

    const E_INVALID_COLLATE = -2034;

    const E_CHARSET_COLLATE_NOT_MATCH = -2035;

    const E_SNAPSHOT_FAILURE = -2040;

    const E_BLOCK_WRITE_FAILURE = -2041;

    const E_REBUILD_INDEX_FAILURE = -2042;

    const E_INDEX_WITH_TTL = -2043;

    const E_ADD_JOB_FAILURE = -2044;

    const E_STOP_JOB_FAILURE = -2045;

    const E_SAVE_JOB_FAILURE = -2046;

    const E_BALANCER_FAILURE = -2047;

    const E_JOB_NOT_FINISHED = -2048;

    const E_TASK_REPORT_OUT_DATE = -2049;

    const E_JOB_NOT_IN_SPACE = -2050;

    const E_INVALID_JOB = -2065;

    const E_BACKUP_BUILDING_INDEX = -2066;

    const E_BACKUP_SPACE_NOT_FOUND = -2067;

    const E_RESTORE_FAILURE = -2068;

    const E_SESSION_NOT_FOUND = -2069;

    const E_LIST_CLUSTER_FAILURE = -2070;

    const E_LIST_CLUSTER_GET_ABS_PATH_FAILURE = -2071;

    const E_GET_META_DIR_FAILURE = -2072;

    const E_QUERY_NOT_FOUND = -2073;

    const E_CONSENSUS_ERROR = -3001;

    const E_KEY_HAS_EXISTS = -3002;

    const E_DATA_TYPE_MISMATCH = -3003;

    const E_INVALID_FIELD_VALUE = -3004;

    const E_INVALID_OPERATION = -3005;

    const E_NOT_NULLABLE = -3006;

    const E_FIELD_UNSET = -3007;

    const E_OUT_OF_RANGE = -3008;

    const E_ATOMIC_OP_FAILED = -3009;

    const E_DATA_CONFLICT_ERROR = -3010;

    const E_WRITE_STALLED = -3011;

    const E_IMPROPER_DATA_TYPE = -3021;

    const E_INVALID_SPACEVIDLEN = -3022;

    const E_INVALID_FILTER = -3031;

    const E_INVALID_UPDATER = -3032;

    const E_INVALID_STORE = -3033;

    const E_INVALID_PEER = -3034;

    const E_RETRY_EXHAUSTED = -3035;

    const E_TRANSFER_LEADER_FAILED = -3036;

    const E_INVALID_STAT_TYPE = -3037;

    const E_INVALID_VID = -3038;

    const E_NO_TRANSFORMED = -3039;

    const E_LOAD_META_FAILED = -3040;

    const E_FAILED_TO_CHECKPOINT = -3041;

    const E_CHECKPOINT_BLOCKED = -3042;

    const E_FILTER_OUT = -3043;

    const E_INVALID_DATA = -3044;

    const E_MUTATE_EDGE_CONFLICT = -3045;

    const E_MUTATE_TAG_CONFLICT = -3046;

    const E_OUTDATED_LOCK = -3047;

    const E_INVALID_TASK_PARA = -3051;

    const E_USER_CANCEL = -3052;

    const E_TASK_EXECUTION_FAILED = -3053;

    const E_PLAN_IS_KILLED = -3060;

    const E_NO_TERM = -3070;

    const E_OUTDATED_TERM = -3071;

    const E_OUTDATED_EDGE = -3072;

    const E_WRITE_WRITE_CONFLICT = -3073;

    const E_CLIENT_SERVER_INCOMPATIBLE = -3061;

    const E_UNKNOWN = -8000;

    static public $__names = array(
        0 => 'SUCCEEDED',
        -1 => 'E_DISCONNECTED',
        -2 => 'E_FAIL_TO_CONNECT',
        -3 => 'E_RPC_FAILURE',
        -4 => 'E_LEADER_CHANGED',
        -5 => 'E_SPACE_NOT_FOUND',
        -6 => 'E_TAG_NOT_FOUND',
        -7 => 'E_EDGE_NOT_FOUND',
        -8 => 'E_INDEX_NOT_FOUND',
        -9 => 'E_EDGE_PROP_NOT_FOUND',
        -10 => 'E_TAG_PROP_NOT_FOUND',
        -11 => 'E_ROLE_NOT_FOUND',
        -12 => 'E_CONFIG_NOT_FOUND',
        -13 => 'E_GROUP_NOT_FOUND',
        -14 => 'E_ZONE_NOT_FOUND',
        -15 => 'E_LISTENER_NOT_FOUND',
        -16 => 'E_PART_NOT_FOUND',
        -17 => 'E_KEY_NOT_FOUND',
        -18 => 'E_USER_NOT_FOUND',
        -19 => 'E_STATS_NOT_FOUND',
        -24 => 'E_BACKUP_FAILED',
        -25 => 'E_BACKUP_EMPTY_TABLE',
        -26 => 'E_BACKUP_TABLE_FAILED',
        -27 => 'E_PARTIAL_RESULT',
        -28 => 'E_REBUILD_INDEX_FAILED',
        -29 => 'E_INVALID_PASSWORD',
        -30 => 'E_FAILED_GET_ABS_PATH',
        -1001 => 'E_BAD_USERNAME_PASSWORD',
        -1002 => 'E_SESSION_INVALID',
        -1003 => 'E_SESSION_TIMEOUT',
        -1004 => 'E_SYNTAX_ERROR',
        -1005 => 'E_EXECUTION_ERROR',
        -1006 => 'E_STATEMENT_EMPTY',
        -1008 => 'E_BAD_PERMISSION',
        -1009 => 'E_SEMANTIC_ERROR',
        -1010 => 'E_TOO_MANY_CONNECTIONS',
        -1011 => 'E_PARTIAL_SUCCEEDED',
        -2001 => 'E_NO_HOSTS',
        -2002 => 'E_EXISTED',
        -2003 => 'E_INVALID_HOST',
        -2004 => 'E_UNSUPPORTED',
        -2005 => 'E_NOT_DROP',
        -2006 => 'E_BALANCER_RUNNING',
        -2007 => 'E_CONFIG_IMMUTABLE',
        -2008 => 'E_CONFLICT',
        -2009 => 'E_INVALID_PARM',
        -2010 => 'E_WRONGCLUSTER',
        -2021 => 'E_STORE_FAILURE',
        -2022 => 'E_STORE_SEGMENT_ILLEGAL',
        -2023 => 'E_BAD_BALANCE_PLAN',
        -2024 => 'E_BALANCED',
        -2025 => 'E_NO_RUNNING_BALANCE_PLAN',
        -2026 => 'E_NO_VALID_HOST',
        -2027 => 'E_CORRUPTTED_BALANCE_PLAN',
        -2028 => 'E_NO_INVALID_BALANCE_PLAN',
        -2030 => 'E_IMPROPER_ROLE',
        -2031 => 'E_INVALID_PARTITION_NUM',
        -2032 => 'E_INVALID_REPLICA_FACTOR',
        -2033 => 'E_INVALID_CHARSET',
        -2034 => 'E_INVALID_COLLATE',
        -2035 => 'E_CHARSET_COLLATE_NOT_MATCH',
        -2040 => 'E_SNAPSHOT_FAILURE',
        -2041 => 'E_BLOCK_WRITE_FAILURE',
        -2042 => 'E_REBUILD_INDEX_FAILURE',
        -2043 => 'E_INDEX_WITH_TTL',
        -2044 => 'E_ADD_JOB_FAILURE',
        -2045 => 'E_STOP_JOB_FAILURE',
        -2046 => 'E_SAVE_JOB_FAILURE',
        -2047 => 'E_BALANCER_FAILURE',
        -2048 => 'E_JOB_NOT_FINISHED',
        -2049 => 'E_TASK_REPORT_OUT_DATE',
        -2050 => 'E_JOB_NOT_IN_SPACE',
        -2065 => 'E_INVALID_JOB',
        -2066 => 'E_BACKUP_BUILDING_INDEX',
        -2067 => 'E_BACKUP_SPACE_NOT_FOUND',
        -2068 => 'E_RESTORE_FAILURE',
        -2069 => 'E_SESSION_NOT_FOUND',
        -2070 => 'E_LIST_CLUSTER_FAILURE',
        -2071 => 'E_LIST_CLUSTER_GET_ABS_PATH_FAILURE',
        -2072 => 'E_GET_META_DIR_FAILURE',
        -2073 => 'E_QUERY_NOT_FOUND',
        -3001 => 'E_CONSENSUS_ERROR',
        -3002 => 'E_KEY_HAS_EXISTS',
        -3003 => 'E_DATA_TYPE_MISMATCH',
        -3004 => 'E_INVALID_FIELD_VALUE',
        -3005 => 'E_INVALID_OPERATION',
        -3006 => 'E_NOT_NULLABLE',
        -3007 => 'E_FIELD_UNSET',
        -3008 => 'E_OUT_OF_RANGE',
        -3009 => 'E_ATOMIC_OP_FAILED',
        -3010 => 'E_DATA_CONFLICT_ERROR',
        -3011 => 'E_WRITE_STALLED',
        -3021 => 'E_IMPROPER_DATA_TYPE',
        -3022 => 'E_INVALID_SPACEVIDLEN',
        -3031 => 'E_INVALID_FILTER',
        -3032 => 'E_INVALID_UPDATER',
        -3033 => 'E_INVALID_STORE',
        -3034 => 'E_INVALID_PEER',
        -3035 => 'E_RETRY_EXHAUSTED',
        -3036 => 'E_TRANSFER_LEADER_FAILED',
        -3037 => 'E_INVALID_STAT_TYPE',
        -3038 => 'E_INVALID_VID',
        -3039 => 'E_NO_TRANSFORMED',
        -3040 => 'E_LOAD_META_FAILED',
        -3041 => 'E_FAILED_TO_CHECKPOINT',
        -3042 => 'E_CHECKPOINT_BLOCKED',
        -3043 => 'E_FILTER_OUT',
        -3044 => 'E_INVALID_DATA',
        -3045 => 'E_MUTATE_EDGE_CONFLICT',
        -3046 => 'E_MUTATE_TAG_CONFLICT',
        -3047 => 'E_OUTDATED_LOCK',
        -3051 => 'E_INVALID_TASK_PARA',
        -3052 => 'E_USER_CANCEL',
        -3053 => 'E_TASK_EXECUTION_FAILED',
        -3060 => 'E_PLAN_IS_KILLED',
        -3070 => 'E_NO_TERM',
        -3071 => 'E_OUTDATED_TERM',
        -3072 => 'E_OUTDATED_EDGE',
        -3073 => 'E_WRITE_WRITE_CONFLICT',
        -3061 => 'E_CLIENT_SERVER_INCOMPATIBLE',
        -8000 => 'E_UNKNOWN',
    );
}

