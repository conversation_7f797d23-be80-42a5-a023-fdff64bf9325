<?php
namespace Nebula\Common;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class HostAddr
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'host',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        2 => array(
            'var' => 'port',
            'isRequired' => false,
            'type' => TType::I32,
        ),
    );

    /**
     * @var string
     */
    public $host = null;
    /**
     * @var int
     */
    public $port = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['host'])) {
                $this->host = $vals['host'];
            }
            if (isset($vals['port'])) {
                $this->port = $vals['port'];
            }
        }
    }

    public function getName()
    {
        return 'HostAddr';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->host);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->port);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('HostAddr');
        if ($this->host !== null) {
            $xfer += $output->writeFieldBegin('host', TType::STRING, 1);
            $xfer += $output->writeString($this->host);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->port !== null) {
            $xfer += $output->writeFieldBegin('port', TType::I32, 2);
            $xfer += $output->writeI32($this->port);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
