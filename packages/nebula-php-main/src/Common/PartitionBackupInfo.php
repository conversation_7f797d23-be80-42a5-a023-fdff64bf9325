<?php
namespace Nebula\Common;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class PartitionBackupInfo
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'info',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::I32,
            'vtype' => TType::STRUCT,
            'key' => array(
                'type' => TType::I32,
            ),
            'val' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Common\LogInfo',
                ),
        ),
    );

    /**
     * @var array
     */
    public $info = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['info'])) {
                $this->info = $vals['info'];
            }
        }
    }

    public function getName()
    {
        return 'PartitionBackupInfo';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::MAP) {
                        $this->info = array();
                        $_size114 = 0;
                        $_ktype115 = 0;
                        $_vtype116 = 0;
                        $xfer += $input->readMapBegin($_ktype115, $_vtype116, $_size114);
                        for ($_i118 = 0; $_i118 < $_size114; ++$_i118) {
                            $key119 = 0;
                            $val120 = new \Nebula\Common\LogInfo();
                            $xfer += $input->readI32($key119);
                            $val120 = new \Nebula\Common\LogInfo();
                            $xfer += $val120->read($input);
                            $this->info[$key119] = $val120;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('PartitionBackupInfo');
        if ($this->info !== null) {
            if (!is_array($this->info)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('info', TType::MAP, 1);
            $output->writeMapBegin(TType::I32, TType::STRUCT, count($this->info));
            foreach ($this->info as $kiter121 => $viter122) {
                $xfer += $output->writeI32($kiter121);
                $xfer += $viter122->write($output);
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
