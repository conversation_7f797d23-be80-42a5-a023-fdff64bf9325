<?php
namespace Nebula\Common;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class DataSet
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'column_names',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRING,
            'elem' => array(
                'type' => TType::STRING,
                ),
        ),
        2 => array(
            'var' => 'rows',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Common\Row',
                ),
        ),
    );

    /**
     * @var string[]
     */
    public $column_names = null;
    /**
     * @var \Nebula\Common\Row[]
     */
    public $rows = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['column_names'])) {
                $this->column_names = $vals['column_names'];
            }
            if (isset($vals['rows'])) {
                $this->rows = $vals['rows'];
            }
        }
    }

    public function getName()
    {
        return 'DataSet';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::LST) {
                        $this->column_names = array();
                        $_size31 = 0;
                        $_etype34 = 0;
                        $xfer += $input->readListBegin($_etype34, $_size31);
                        for ($_i35 = 0; $_i35 < $_size31; ++$_i35) {
                            $elem36 = null;
                            $xfer += $input->readString($elem36);
                            $this->column_names []= $elem36;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::LST) {
                        $this->rows = array();
                        $_size37 = 0;
                        $_etype40 = 0;
                        $xfer += $input->readListBegin($_etype40, $_size37);
                        for ($_i41 = 0; $_i41 < $_size37; ++$_i41) {
                            $elem42 = null;
                            $elem42 = new \Nebula\Common\Row();
                            $xfer += $elem42->read($input);
                            $this->rows []= $elem42;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('DataSet');
        if ($this->column_names !== null) {
            if (!is_array($this->column_names)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('column_names', TType::LST, 1);
            $output->writeListBegin(TType::STRING, count($this->column_names));
            foreach ($this->column_names as $iter43) {
                $xfer += $output->writeString($iter43);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->rows !== null) {
            if (!is_array($this->rows)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('rows', TType::LST, 2);
            $output->writeListBegin(TType::STRUCT, count($this->rows));
            foreach ($this->rows as $iter44) {
                $xfer += $iter44->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
