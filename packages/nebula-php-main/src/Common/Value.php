<?php
namespace Nebula\Common;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class Value
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'nVal',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Common\NullType',
        ),
        2 => array(
            'var' => 'bVal',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
        3 => array(
            'var' => 'iVal',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        4 => array(
            'var' => 'fVal',
            'isRequired' => false,
            'type' => TType::DOUBLE,
        ),
        5 => array(
            'var' => 'sVal',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        6 => array(
            'var' => 'dVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Date',
        ),
        7 => array(
            'var' => 'tVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Time',
        ),
        8 => array(
            'var' => 'dtVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\DateTime',
        ),
        9 => array(
            'var' => 'vVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Vertex',
        ),
        10 => array(
            'var' => 'eVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Edge',
        ),
        11 => array(
            'var' => 'pVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Path',
        ),
        12 => array(
            'var' => 'lVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\NList',
        ),
        13 => array(
            'var' => 'mVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\NMap',
        ),
        14 => array(
            'var' => 'uVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\NSet',
        ),
        15 => array(
            'var' => 'gVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\DataSet',
        ),
        16 => array(
            'var' => 'ggVal',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Geography',
        ),
    );

    /**
     * @var int
     */
    public $nVal = null;
    /**
     * @var bool
     */
    public $bVal = null;
    /**
     * @var int
     */
    public $iVal = null;
    /**
     * @var double
     */
    public $fVal = null;
    /**
     * @var string
     */
    public $sVal = null;
    /**
     * @var \Nebula\Common\Date
     */
    public $dVal = null;
    /**
     * @var \Nebula\Common\Time
     */
    public $tVal = null;
    /**
     * @var \Nebula\Common\DateTime
     */
    public $dtVal = null;
    /**
     * @var \Nebula\Common\Vertex
     */
    public $vVal = null;
    /**
     * @var \Nebula\Common\Edge
     */
    public $eVal = null;
    /**
     * @var \Nebula\Common\Path
     */
    public $pVal = null;
    /**
     * @var \Nebula\Common\NList
     */
    public $lVal = null;
    /**
     * @var \Nebula\Common\NMap
     */
    public $mVal = null;
    /**
     * @var \Nebula\Common\NSet
     */
    public $uVal = null;
    /**
     * @var \Nebula\Common\DataSet
     */
    public $gVal = null;
    /**
     * @var \Nebula\Common\Geography
     */
    public $ggVal = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['nVal'])) {
                $this->nVal = $vals['nVal'];
            }
            if (isset($vals['bVal'])) {
                $this->bVal = $vals['bVal'];
            }
            if (isset($vals['iVal'])) {
                $this->iVal = $vals['iVal'];
            }
            if (isset($vals['fVal'])) {
                $this->fVal = $vals['fVal'];
            }
            if (isset($vals['sVal'])) {
                $this->sVal = $vals['sVal'];
            }
            if (isset($vals['dVal'])) {
                $this->dVal = $vals['dVal'];
            }
            if (isset($vals['tVal'])) {
                $this->tVal = $vals['tVal'];
            }
            if (isset($vals['dtVal'])) {
                $this->dtVal = $vals['dtVal'];
            }
            if (isset($vals['vVal'])) {
                $this->vVal = $vals['vVal'];
            }
            if (isset($vals['eVal'])) {
                $this->eVal = $vals['eVal'];
            }
            if (isset($vals['pVal'])) {
                $this->pVal = $vals['pVal'];
            }
            if (isset($vals['lVal'])) {
                $this->lVal = $vals['lVal'];
            }
            if (isset($vals['mVal'])) {
                $this->mVal = $vals['mVal'];
            }
            if (isset($vals['uVal'])) {
                $this->uVal = $vals['uVal'];
            }
            if (isset($vals['gVal'])) {
                $this->gVal = $vals['gVal'];
            }
            if (isset($vals['ggVal'])) {
                $this->ggVal = $vals['ggVal'];
            }
        }
    }

    public function getName()
    {
        return 'Value';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->nVal);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->bVal);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->iVal);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::DOUBLE) {
                        $xfer += $input->readDouble($this->fVal);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->sVal);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRUCT) {
                        $this->dVal = new \Nebula\Common\Date();
                        $xfer += $this->dVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::STRUCT) {
                        $this->tVal = new \Nebula\Common\Time();
                        $xfer += $this->tVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 8:
                    if ($ftype == TType::STRUCT) {
                        $this->dtVal = new \Nebula\Common\DateTime();
                        $xfer += $this->dtVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 9:
                    if ($ftype == TType::STRUCT) {
                        $this->vVal = new \Nebula\Common\Vertex();
                        $xfer += $this->vVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 10:
                    if ($ftype == TType::STRUCT) {
                        $this->eVal = new \Nebula\Common\Edge();
                        $xfer += $this->eVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 11:
                    if ($ftype == TType::STRUCT) {
                        $this->pVal = new \Nebula\Common\Path();
                        $xfer += $this->pVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 12:
                    if ($ftype == TType::STRUCT) {
                        $this->lVal = new \Nebula\Common\NList();
                        $xfer += $this->lVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 13:
                    if ($ftype == TType::STRUCT) {
                        $this->mVal = new \Nebula\Common\NMap();
                        $xfer += $this->mVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 14:
                    if ($ftype == TType::STRUCT) {
                        $this->uVal = new \Nebula\Common\NSet();
                        $xfer += $this->uVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 15:
                    if ($ftype == TType::STRUCT) {
                        $this->gVal = new \Nebula\Common\DataSet();
                        $xfer += $this->gVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 16:
                    if ($ftype == TType::STRUCT) {
                        $this->ggVal = new \Nebula\Common\Geography();
                        $xfer += $this->ggVal->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('Value');
        if ($this->nVal !== null) {
            $xfer += $output->writeFieldBegin('nVal', TType::I32, 1);
            $xfer += $output->writeI32($this->nVal);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->bVal !== null) {
            $xfer += $output->writeFieldBegin('bVal', TType::BOOL, 2);
            $xfer += $output->writeBool($this->bVal);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->iVal !== null) {
            $xfer += $output->writeFieldBegin('iVal', TType::I64, 3);
            $xfer += $output->writeI64($this->iVal);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->fVal !== null) {
            $xfer += $output->writeFieldBegin('fVal', TType::DOUBLE, 4);
            $xfer += $output->writeDouble($this->fVal);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->sVal !== null) {
            $xfer += $output->writeFieldBegin('sVal', TType::STRING, 5);
            $xfer += $output->writeString($this->sVal);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->dVal !== null) {
            if (!is_object($this->dVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('dVal', TType::STRUCT, 6);
            $xfer += $this->dVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->tVal !== null) {
            if (!is_object($this->tVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('tVal', TType::STRUCT, 7);
            $xfer += $this->tVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->dtVal !== null) {
            if (!is_object($this->dtVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('dtVal', TType::STRUCT, 8);
            $xfer += $this->dtVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->vVal !== null) {
            if (!is_object($this->vVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('vVal', TType::STRUCT, 9);
            $xfer += $this->vVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->eVal !== null) {
            if (!is_object($this->eVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('eVal', TType::STRUCT, 10);
            $xfer += $this->eVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->pVal !== null) {
            if (!is_object($this->pVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('pVal', TType::STRUCT, 11);
            $xfer += $this->pVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->lVal !== null) {
            if (!is_object($this->lVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('lVal', TType::STRUCT, 12);
            $xfer += $this->lVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->mVal !== null) {
            if (!is_object($this->mVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('mVal', TType::STRUCT, 13);
            $xfer += $this->mVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->uVal !== null) {
            if (!is_object($this->uVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('uVal', TType::STRUCT, 14);
            $xfer += $this->uVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->gVal !== null) {
            if (!is_object($this->gVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('gVal', TType::STRUCT, 15);
            $xfer += $this->gVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->ggVal !== null) {
            if (!is_object($this->ggVal)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('ggVal', TType::STRUCT, 16);
            $xfer += $this->ggVal->write($output);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
