<?php
namespace Nebula\Common;

/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class Path
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'src',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Vertex',
        ),
        2 => array(
            'var' => 'steps',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Common\Step',
                ),
        ),
    );

    /**
     * @var \Nebula\Common\Vertex
     */
    public $src = null;
    /**
     * @var \Nebula\Common\Step[]
     */
    public $steps = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['src'])) {
                $this->src = $vals['src'];
            }
            if (isset($vals['steps'])) {
                $this->steps = $vals['steps'];
            }
        }
    }

    public function getName()
    {
        return 'Path';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRUCT) {
                        $this->src = new \Nebula\Common\Vertex();
                        $xfer += $this->src->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::LST) {
                        $this->steps = array();
                        $_size100 = 0;
                        $_etype103 = 0;
                        $xfer += $input->readListBegin($_etype103, $_size100);
                        for ($_i104 = 0; $_i104 < $_size100; ++$_i104) {
                            $elem105 = null;
                            $elem105 = new \Nebula\Common\Step();
                            $xfer += $elem105->read($input);
                            $this->steps []= $elem105;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('Path');
        if ($this->src !== null) {
            if (!is_object($this->src)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('src', TType::STRUCT, 1);
            $xfer += $this->src->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->steps !== null) {
            if (!is_array($this->steps)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('steps', TType::LST, 2);
            $output->writeListBegin(TType::STRUCT, count($this->steps));
            foreach ($this->steps as $iter106) {
                $xfer += $iter106->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
