<?php
namespace Nebula\Common;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class Edge
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'src',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Value',
        ),
        2 => array(
            'var' => 'dst',
            'isRequired' => false,
            'type' => TType::STRUCT,
            'class' => '\Nebula\Common\Value',
        ),
        3 => array(
            'var' => 'type',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        4 => array(
            'var' => 'name',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        5 => array(
            'var' => 'ranking',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        6 => array(
            'var' => 'props',
            'isRequired' => false,
            'type' => TType::MAP,
            'ktype' => TType::STRING,
            'vtype' => TType::STRUCT,
            'key' => array(
                'type' => TType::STRING,
            ),
            'val' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Common\Value',
                ),
        ),
    );

    /**
     * @var \Nebula\Common\Value
     */
    public $src = null;
    /**
     * @var \Nebula\Common\Value
     */
    public $dst = null;
    /**
     * @var int
     */
    public $type = null;
    /**
     * @var string
     */
    public $name = null;
    /**
     * @var int
     */
    public $ranking = null;
    /**
     * @var array
     */
    public $props = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['src'])) {
                $this->src = $vals['src'];
            }
            if (isset($vals['dst'])) {
                $this->dst = $vals['dst'];
            }
            if (isset($vals['type'])) {
                $this->type = $vals['type'];
            }
            if (isset($vals['name'])) {
                $this->name = $vals['name'];
            }
            if (isset($vals['ranking'])) {
                $this->ranking = $vals['ranking'];
            }
            if (isset($vals['props'])) {
                $this->props = $vals['props'];
            }
        }
    }

    public function getName()
    {
        return 'Edge';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::STRUCT) {
                        $this->src = new \Nebula\Common\Value();
                        $xfer += $this->src->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRUCT) {
                        $this->dst = new \Nebula\Common\Value();
                        $xfer += $this->dst->read($input);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->type);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->name);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->ranking);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::MAP) {
                        $this->props = array();
                        $_size82 = 0;
                        $_ktype83 = 0;
                        $_vtype84 = 0;
                        $xfer += $input->readMapBegin($_ktype83, $_vtype84, $_size82);
                        for ($_i86 = 0; $_i86 < $_size82; ++$_i86) {
                            $key87 = '';
                            $val88 = new \Nebula\Common\Value();
                            $xfer += $input->readString($key87);
                            $val88 = new \Nebula\Common\Value();
                            $xfer += $val88->read($input);
                            $this->props[$key87] = $val88;
                        }
                        $xfer += $input->readMapEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('Edge');
        if ($this->src !== null) {
            if (!is_object($this->src)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('src', TType::STRUCT, 1);
            $xfer += $this->src->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->dst !== null) {
            if (!is_object($this->dst)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('dst', TType::STRUCT, 2);
            $xfer += $this->dst->write($output);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->type !== null) {
            $xfer += $output->writeFieldBegin('type', TType::I32, 3);
            $xfer += $output->writeI32($this->type);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->name !== null) {
            $xfer += $output->writeFieldBegin('name', TType::STRING, 4);
            $xfer += $output->writeString($this->name);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->ranking !== null) {
            $xfer += $output->writeFieldBegin('ranking', TType::I64, 5);
            $xfer += $output->writeI64($this->ranking);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->props !== null) {
            if (!is_array($this->props)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('props', TType::MAP, 6);
            $output->writeMapBegin(TType::STRING, TType::STRUCT, count($this->props));
            foreach ($this->props as $kiter89 => $viter90) {
                $xfer += $output->writeString($kiter89);
                $xfer += $viter90->write($output);
            }
            $output->writeMapEnd();
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
