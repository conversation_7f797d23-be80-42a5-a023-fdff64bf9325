<?php
namespace Nebula\Common;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

final class NullType
{
    const __NULL__ = 0;

    const NaN = 1;

    const BAD_DATA = 2;

    const BAD_TYPE = 3;

    const ERR_OVERFLOW = 4;

    const UNKNOWN_PROP = 5;

    const DIV_BY_ZERO = 6;

    const OUT_OF_RANGE = 7;

    static public $__names = array(
        0 => '__NULL__',
        1 => 'NaN',
        2 => 'BAD_DATA',
        3 => 'BAD_TYPE',
        4 => 'ERR_OVERFLOW',
        5 => 'UNKNOWN_PROP',
        6 => 'DIV_BY_ZERO',
        7 => 'OUT_OF_RANGE',
    );
}

