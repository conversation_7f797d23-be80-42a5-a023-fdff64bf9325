<?php
namespace Nebula\Raftex;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class SendSnapshotRequest
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'part',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        4 => array(
            'var' => 'committed_log_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        5 => array(
            'var' => 'committed_log_term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        6 => array(
            'var' => 'leader_addr',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        7 => array(
            'var' => 'leader_port',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        8 => array(
            'var' => 'rows',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRING,
            'elem' => array(
                'type' => TType::STRING,
                ),
        ),
        9 => array(
            'var' => 'total_size',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        10 => array(
            'var' => 'total_count',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        11 => array(
            'var' => 'done',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
    );

    /**
     * @var int
     */
    public $space = null;
    /**
     * @var int
     */
    public $part = null;
    /**
     * @var int
     */
    public $term = null;
    /**
     * @var int
     */
    public $committed_log_id = null;
    /**
     * @var int
     */
    public $committed_log_term = null;
    /**
     * @var string
     */
    public $leader_addr = null;
    /**
     * @var int
     */
    public $leader_port = null;
    /**
     * @var string[]
     */
    public $rows = null;
    /**
     * @var int
     */
    public $total_size = null;
    /**
     * @var int
     */
    public $total_count = null;
    /**
     * @var bool
     */
    public $done = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space'])) {
                $this->space = $vals['space'];
            }
            if (isset($vals['part'])) {
                $this->part = $vals['part'];
            }
            if (isset($vals['term'])) {
                $this->term = $vals['term'];
            }
            if (isset($vals['committed_log_id'])) {
                $this->committed_log_id = $vals['committed_log_id'];
            }
            if (isset($vals['committed_log_term'])) {
                $this->committed_log_term = $vals['committed_log_term'];
            }
            if (isset($vals['leader_addr'])) {
                $this->leader_addr = $vals['leader_addr'];
            }
            if (isset($vals['leader_port'])) {
                $this->leader_port = $vals['leader_port'];
            }
            if (isset($vals['rows'])) {
                $this->rows = $vals['rows'];
            }
            if (isset($vals['total_size'])) {
                $this->total_size = $vals['total_size'];
            }
            if (isset($vals['total_count'])) {
                $this->total_count = $vals['total_count'];
            }
            if (isset($vals['done'])) {
                $this->done = $vals['done'];
            }
        }
    }

    public function getName()
    {
        return 'SendSnapshotRequest';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->part);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->committed_log_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->committed_log_term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->leader_addr);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->leader_port);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 8:
                    if ($ftype == TType::LST) {
                        $this->rows = array();
                        $_size7 = 0;
                        $_etype10 = 0;
                        $xfer += $input->readListBegin($_etype10, $_size7);
                        for ($_i11 = 0; $_i11 < $_size7; ++$_i11) {
                            $elem12 = null;
                            $xfer += $input->readString($elem12);
                            $this->rows []= $elem12;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 9:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->total_size);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 10:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->total_count);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 11:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->done);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('SendSnapshotRequest');
        if ($this->space !== null) {
            $xfer += $output->writeFieldBegin('space', TType::I32, 1);
            $xfer += $output->writeI32($this->space);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->part !== null) {
            $xfer += $output->writeFieldBegin('part', TType::I32, 2);
            $xfer += $output->writeI32($this->part);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->term !== null) {
            $xfer += $output->writeFieldBegin('term', TType::I64, 3);
            $xfer += $output->writeI64($this->term);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->committed_log_id !== null) {
            $xfer += $output->writeFieldBegin('committed_log_id', TType::I64, 4);
            $xfer += $output->writeI64($this->committed_log_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->committed_log_term !== null) {
            $xfer += $output->writeFieldBegin('committed_log_term', TType::I64, 5);
            $xfer += $output->writeI64($this->committed_log_term);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader_addr !== null) {
            $xfer += $output->writeFieldBegin('leader_addr', TType::STRING, 6);
            $xfer += $output->writeString($this->leader_addr);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader_port !== null) {
            $xfer += $output->writeFieldBegin('leader_port', TType::I32, 7);
            $xfer += $output->writeI32($this->leader_port);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->rows !== null) {
            if (!is_array($this->rows)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('rows', TType::LST, 8);
            $output->writeListBegin(TType::STRING, count($this->rows));
            foreach ($this->rows as $iter13) {
                $xfer += $output->writeString($iter13);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->total_size !== null) {
            $xfer += $output->writeFieldBegin('total_size', TType::I64, 9);
            $xfer += $output->writeI64($this->total_size);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->total_count !== null) {
            $xfer += $output->writeFieldBegin('total_count', TType::I64, 10);
            $xfer += $output->writeI64($this->total_count);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->done !== null) {
            $xfer += $output->writeFieldBegin('done', TType::BOOL, 11);
            $xfer += $output->writeBool($this->done);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
