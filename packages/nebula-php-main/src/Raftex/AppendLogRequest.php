<?php
namespace Nebula\Raftex;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class AppendLogRequest
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'part',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'current_term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        4 => array(
            'var' => 'last_log_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        5 => array(
            'var' => 'committed_log_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        6 => array(
            'var' => 'leader_addr',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        7 => array(
            'var' => 'leader_port',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        8 => array(
            'var' => 'last_log_term_sent',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        9 => array(
            'var' => 'last_log_id_sent',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        10 => array(
            'var' => 'log_term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        11 => array(
            'var' => 'log_str_list',
            'isRequired' => false,
            'type' => TType::LST,
            'etype' => TType::STRUCT,
            'elem' => array(
                'type' => TType::STRUCT,
                'class' => '\Nebula\Raftex\LogEntry',
                ),
        ),
        12 => array(
            'var' => 'sending_snapshot',
            'isRequired' => false,
            'type' => TType::BOOL,
        ),
    );

    /**
     * @var int
     */
    public $space = null;
    /**
     * @var int
     */
    public $part = null;
    /**
     * @var int
     */
    public $current_term = null;
    /**
     * @var int
     */
    public $last_log_id = null;
    /**
     * @var int
     */
    public $committed_log_id = null;
    /**
     * @var string
     */
    public $leader_addr = null;
    /**
     * @var int
     */
    public $leader_port = null;
    /**
     * @var int
     */
    public $last_log_term_sent = null;
    /**
     * @var int
     */
    public $last_log_id_sent = null;
    /**
     * @var int
     */
    public $log_term = null;
    /**
     * @var \Nebula\Raftex\LogEntry[]
     */
    public $log_str_list = null;
    /**
     * @var bool
     */
    public $sending_snapshot = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space'])) {
                $this->space = $vals['space'];
            }
            if (isset($vals['part'])) {
                $this->part = $vals['part'];
            }
            if (isset($vals['current_term'])) {
                $this->current_term = $vals['current_term'];
            }
            if (isset($vals['last_log_id'])) {
                $this->last_log_id = $vals['last_log_id'];
            }
            if (isset($vals['committed_log_id'])) {
                $this->committed_log_id = $vals['committed_log_id'];
            }
            if (isset($vals['leader_addr'])) {
                $this->leader_addr = $vals['leader_addr'];
            }
            if (isset($vals['leader_port'])) {
                $this->leader_port = $vals['leader_port'];
            }
            if (isset($vals['last_log_term_sent'])) {
                $this->last_log_term_sent = $vals['last_log_term_sent'];
            }
            if (isset($vals['last_log_id_sent'])) {
                $this->last_log_id_sent = $vals['last_log_id_sent'];
            }
            if (isset($vals['log_term'])) {
                $this->log_term = $vals['log_term'];
            }
            if (isset($vals['log_str_list'])) {
                $this->log_str_list = $vals['log_str_list'];
            }
            if (isset($vals['sending_snapshot'])) {
                $this->sending_snapshot = $vals['sending_snapshot'];
            }
        }
    }

    public function getName()
    {
        return 'AppendLogRequest';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->part);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->current_term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->last_log_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->committed_log_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->leader_addr);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->leader_port);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 8:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->last_log_term_sent);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 9:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->last_log_id_sent);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 10:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->log_term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 11:
                    if ($ftype == TType::LST) {
                        $this->log_str_list = array();
                        $_size0 = 0;
                        $_etype3 = 0;
                        $xfer += $input->readListBegin($_etype3, $_size0);
                        for ($_i4 = 0; $_i4 < $_size0; ++$_i4) {
                            $elem5 = null;
                            $elem5 = new \Nebula\Raftex\LogEntry();
                            $xfer += $elem5->read($input);
                            $this->log_str_list []= $elem5;
                        }
                        $xfer += $input->readListEnd();
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 12:
                    if ($ftype == TType::BOOL) {
                        $xfer += $input->readBool($this->sending_snapshot);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('AppendLogRequest');
        if ($this->space !== null) {
            $xfer += $output->writeFieldBegin('space', TType::I32, 1);
            $xfer += $output->writeI32($this->space);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->part !== null) {
            $xfer += $output->writeFieldBegin('part', TType::I32, 2);
            $xfer += $output->writeI32($this->part);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->current_term !== null) {
            $xfer += $output->writeFieldBegin('current_term', TType::I64, 3);
            $xfer += $output->writeI64($this->current_term);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->last_log_id !== null) {
            $xfer += $output->writeFieldBegin('last_log_id', TType::I64, 4);
            $xfer += $output->writeI64($this->last_log_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->committed_log_id !== null) {
            $xfer += $output->writeFieldBegin('committed_log_id', TType::I64, 5);
            $xfer += $output->writeI64($this->committed_log_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader_addr !== null) {
            $xfer += $output->writeFieldBegin('leader_addr', TType::STRING, 6);
            $xfer += $output->writeString($this->leader_addr);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader_port !== null) {
            $xfer += $output->writeFieldBegin('leader_port', TType::I32, 7);
            $xfer += $output->writeI32($this->leader_port);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->last_log_term_sent !== null) {
            $xfer += $output->writeFieldBegin('last_log_term_sent', TType::I64, 8);
            $xfer += $output->writeI64($this->last_log_term_sent);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->last_log_id_sent !== null) {
            $xfer += $output->writeFieldBegin('last_log_id_sent', TType::I64, 9);
            $xfer += $output->writeI64($this->last_log_id_sent);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->log_term !== null) {
            $xfer += $output->writeFieldBegin('log_term', TType::I64, 10);
            $xfer += $output->writeI64($this->log_term);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->log_str_list !== null) {
            if (!is_array($this->log_str_list)) {
                throw new TProtocolException('Bad type in structure.', TProtocolException::INVALID_DATA);
            }
            $xfer += $output->writeFieldBegin('log_str_list', TType::LST, 11);
            $output->writeListBegin(TType::STRUCT, count($this->log_str_list));
            foreach ($this->log_str_list as $iter6) {
                $xfer += $iter6->write($output);
            }
            $output->writeListEnd();
            $xfer += $output->writeFieldEnd();
        }
        if ($this->sending_snapshot !== null) {
            $xfer += $output->writeFieldBegin('sending_snapshot', TType::BOOL, 12);
            $xfer += $output->writeBool($this->sending_snapshot);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
