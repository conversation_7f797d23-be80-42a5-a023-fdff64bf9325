<?php
namespace Nebula\Raftex;

/**
 * Autogenerated by Thrift Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class LogEntry
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'cluster',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        2 => array(
            'var' => 'log_str',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
    );

    /**
     * @var int
     */
    public $cluster = null;
    /**
     * @var string
     */
    public $log_str = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['cluster'])) {
                $this->cluster = $vals['cluster'];
            }
            if (isset($vals['log_str'])) {
                $this->log_str = $vals['log_str'];
            }
        }
    }

    public function getName()
    {
        return 'LogEntry';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->cluster);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->log_str);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('LogEntry');
        if ($this->cluster !== null) {
            $xfer += $output->writeFieldBegin('cluster', TType::I64, 1);
            $xfer += $output->writeI64($this->cluster);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->log_str !== null) {
            $xfer += $output->writeFieldBegin('log_str', TType::STRING, 2);
            $xfer += $output->writeString($this->log_str);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
