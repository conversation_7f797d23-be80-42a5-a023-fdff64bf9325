<?php
namespace Nebula\Raftex;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class AskForVoteRequest
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'space',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        2 => array(
            'var' => 'part',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        3 => array(
            'var' => 'candidate_addr',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        4 => array(
            'var' => 'candidate_port',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        5 => array(
            'var' => 'term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        6 => array(
            'var' => 'last_log_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        7 => array(
            'var' => 'last_log_term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
    );

    /**
     * @var int
     */
    public $space = null;
    /**
     * @var int
     */
    public $part = null;
    /**
     * @var string
     */
    public $candidate_addr = null;
    /**
     * @var int
     */
    public $candidate_port = null;
    /**
     * @var int
     */
    public $term = null;
    /**
     * @var int
     */
    public $last_log_id = null;
    /**
     * @var int
     */
    public $last_log_term = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['space'])) {
                $this->space = $vals['space'];
            }
            if (isset($vals['part'])) {
                $this->part = $vals['part'];
            }
            if (isset($vals['candidate_addr'])) {
                $this->candidate_addr = $vals['candidate_addr'];
            }
            if (isset($vals['candidate_port'])) {
                $this->candidate_port = $vals['candidate_port'];
            }
            if (isset($vals['term'])) {
                $this->term = $vals['term'];
            }
            if (isset($vals['last_log_id'])) {
                $this->last_log_id = $vals['last_log_id'];
            }
            if (isset($vals['last_log_term'])) {
                $this->last_log_term = $vals['last_log_term'];
            }
        }
    }

    public function getName()
    {
        return 'AskForVoteRequest';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->space);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->part);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->candidate_addr);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->candidate_port);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->last_log_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->last_log_term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('AskForVoteRequest');
        if ($this->space !== null) {
            $xfer += $output->writeFieldBegin('space', TType::I32, 1);
            $xfer += $output->writeI32($this->space);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->part !== null) {
            $xfer += $output->writeFieldBegin('part', TType::I32, 2);
            $xfer += $output->writeI32($this->part);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->candidate_addr !== null) {
            $xfer += $output->writeFieldBegin('candidate_addr', TType::STRING, 3);
            $xfer += $output->writeString($this->candidate_addr);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->candidate_port !== null) {
            $xfer += $output->writeFieldBegin('candidate_port', TType::I32, 4);
            $xfer += $output->writeI32($this->candidate_port);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->term !== null) {
            $xfer += $output->writeFieldBegin('term', TType::I64, 5);
            $xfer += $output->writeI64($this->term);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->last_log_id !== null) {
            $xfer += $output->writeFieldBegin('last_log_id', TType::I64, 6);
            $xfer += $output->writeI64($this->last_log_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->last_log_term !== null) {
            $xfer += $output->writeFieldBegin('last_log_term', TType::I64, 7);
            $xfer += $output->writeI64($this->last_log_term);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
