<?php
namespace Nebula\Raftex;

/**
 * Autogenerated by Thr<PERSON> Compiler (0.15.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
use Thrift\Base\TBase;
use Thrift\Type\TType;
use Thrift\Type\TMessageType;
use Thrift\Exception\TException;
use Thrift\Exception\TProtocolException;
use Thrift\Protocol\TProtocol;
use Thrift\Protocol\TBinaryProtocolAccelerated;
use Thrift\Exception\TApplicationException;

class AppendLogResponse
{
    static public $isValidate = false;

    static public $_TSPEC = array(
        1 => array(
            'var' => 'error_code',
            'isRequired' => false,
            'type' => TType::I32,
            'class' => '\Nebula\Raftex\ErrorCode',
        ),
        2 => array(
            'var' => 'current_term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        3 => array(
            'var' => 'leader_addr',
            'isRequired' => false,
            'type' => TType::STRING,
        ),
        4 => array(
            'var' => 'leader_port',
            'isRequired' => false,
            'type' => TType::I32,
        ),
        5 => array(
            'var' => 'committed_log_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        6 => array(
            'var' => 'last_log_id',
            'isRequired' => false,
            'type' => TType::I64,
        ),
        7 => array(
            'var' => 'last_log_term',
            'isRequired' => false,
            'type' => TType::I64,
        ),
    );

    /**
     * @var int
     */
    public $error_code = null;
    /**
     * @var int
     */
    public $current_term = null;
    /**
     * @var string
     */
    public $leader_addr = null;
    /**
     * @var int
     */
    public $leader_port = null;
    /**
     * @var int
     */
    public $committed_log_id = null;
    /**
     * @var int
     */
    public $last_log_id = null;
    /**
     * @var int
     */
    public $last_log_term = null;

    public function __construct($vals = null)
    {
        if (is_array($vals)) {
            if (isset($vals['error_code'])) {
                $this->error_code = $vals['error_code'];
            }
            if (isset($vals['current_term'])) {
                $this->current_term = $vals['current_term'];
            }
            if (isset($vals['leader_addr'])) {
                $this->leader_addr = $vals['leader_addr'];
            }
            if (isset($vals['leader_port'])) {
                $this->leader_port = $vals['leader_port'];
            }
            if (isset($vals['committed_log_id'])) {
                $this->committed_log_id = $vals['committed_log_id'];
            }
            if (isset($vals['last_log_id'])) {
                $this->last_log_id = $vals['last_log_id'];
            }
            if (isset($vals['last_log_term'])) {
                $this->last_log_term = $vals['last_log_term'];
            }
        }
    }

    public function getName()
    {
        return 'AppendLogResponse';
    }


    public function read($input)
    {
        $xfer = 0;
        $fname = null;
        $ftype = 0;
        $fid = 0;
        $xfer += $input->readStructBegin($fname);
        while (true) {
            $xfer += $input->readFieldBegin($fname, $ftype, $fid);
            if ($ftype == TType::STOP) {
                break;
            }
            switch ($fid) {
                case 1:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->error_code);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 2:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->current_term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 3:
                    if ($ftype == TType::STRING) {
                        $xfer += $input->readString($this->leader_addr);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 4:
                    if ($ftype == TType::I32) {
                        $xfer += $input->readI32($this->leader_port);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 5:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->committed_log_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 6:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->last_log_id);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                case 7:
                    if ($ftype == TType::I64) {
                        $xfer += $input->readI64($this->last_log_term);
                    } else {
                        $xfer += $input->skip($ftype);
                    }
                    break;
                default:
                    $xfer += $input->skip($ftype);
                    break;
            }
            $xfer += $input->readFieldEnd();
        }
        $xfer += $input->readStructEnd();
        return $xfer;
    }

    public function write($output)
    {
        $xfer = 0;
        $xfer += $output->writeStructBegin('AppendLogResponse');
        if ($this->error_code !== null) {
            $xfer += $output->writeFieldBegin('error_code', TType::I32, 1);
            $xfer += $output->writeI32($this->error_code);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->current_term !== null) {
            $xfer += $output->writeFieldBegin('current_term', TType::I64, 2);
            $xfer += $output->writeI64($this->current_term);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader_addr !== null) {
            $xfer += $output->writeFieldBegin('leader_addr', TType::STRING, 3);
            $xfer += $output->writeString($this->leader_addr);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->leader_port !== null) {
            $xfer += $output->writeFieldBegin('leader_port', TType::I32, 4);
            $xfer += $output->writeI32($this->leader_port);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->committed_log_id !== null) {
            $xfer += $output->writeFieldBegin('committed_log_id', TType::I64, 5);
            $xfer += $output->writeI64($this->committed_log_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->last_log_id !== null) {
            $xfer += $output->writeFieldBegin('last_log_id', TType::I64, 6);
            $xfer += $output->writeI64($this->last_log_id);
            $xfer += $output->writeFieldEnd();
        }
        if ($this->last_log_term !== null) {
            $xfer += $output->writeFieldBegin('last_log_term', TType::I64, 7);
            $xfer += $output->writeI64($this->last_log_term);
            $xfer += $output->writeFieldEnd();
        }
        $xfer += $output->writeFieldStop();
        $xfer += $output->writeStructEnd();
        return $xfer;
    }
}
