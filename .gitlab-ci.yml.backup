---
stages:
  - build
  - deploy

variables:
#  image_url: ${CI_REGISTRY}/${CI_PROJECT_PATH}
#  version: ${CI_COMMIT_SHORT_SHA}
#  image_tag: ${CI_REGISTRY}/${CI_PROJECT_PATH}:latest
# new

  IMAGE_NAME: ${CI_REGISTRY}/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}
  IMAGE_TAG: $CI_COMMIT_REF_NAME  # Tags image based on the branch name (e.g., 'main', 'develop')  

build image:
  stage: build
  before_script:
    - cat ${ENV_FILE_CONTENT}
    - cat ${ENV_FILE_CONTENT} > .env
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # - docker build --pull -t ${image_tag} -f Dockerfile .
    # - docker push ${image_tag}
    # - docker rmi ${image_tag}
    - docker build -t $IMAGE_NAME:$CI_COMMIT_SHORT_SHA -t $IMAGE_NAME:$CI_COMMIT_REF_NAME -t $IMAGE_NAME:latest .
    - docker push $IMAGE_NAME:$CI_COMMIT_SHORT_SHA
    - docker push $IMAGE_NAME:$CI_COMMIT_REF_NAME
    - docker push $IMAGE_NAME:latest
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
      when: always
  tags:
    - test

# deploy_to_monolith:
#   rules:
#     - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
#   stage: deploy
#   script:
#     - echo -e ${ENV_MONOLIT} >   /home/<USER>/.env 
#     - scp -o StrictHostKeyChecking=no /home/<USER>/.env gitlab-runner@***********:/home/<USER>/.env
#     - ssh -o StrictHostKeyChecking=no gitlab-runner@*********** "/home/<USER>/deploy.sh"
#   after_script:     
#     - sudo rm -rf $CI_PROJECT_DIR

#   tags:
#     - test

deploy_to_docker_compose:
  stage: deploy
  script:
    - echo "Deploying to remote server"
    - > 
      ssh  -o StrictHostKeyChecking=no -T gitlab-runner@*********** << 'EOF'
        cd /opt/beein-ms/
        sudo /usr/local/bin/docker-compose pull
        sudo /usr/local/bin/docker-compose up -d
      EOF
  rules:
    - if: '$CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "master"'
      when: always
  tags:
    - test
# NOTE: test 02


